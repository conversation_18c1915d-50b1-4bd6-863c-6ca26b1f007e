pipeline {
    agent any
    environment {
        appName =             "zns-end"

        gitRemoteUrl =        "http://*************:8105/superai/superai-end.git"
        gitRemoteBranch =     "*/dev_multi"
        gitIdKey =            "gitlab190-jenkins190-hp"

        zipFileFolder =       "/var/jenkins_home/custom_home/archive_home/"
        zipFileName =         "${appName}-${new Date().format('yyyyMMdd-HHmmss')}.zip"
        zipFilePath =         "${zipFileFolder}${zipFileName}"

        remoteIp =            "*************"
        remoteArchiveFolder = "/home/<USER>/archive_home/"
        remoteArchivePath =   "${remoteArchiveFolder}${zipFileName}"

        remoteTargetFolder  = "/home/<USER>/jar"
    }
    tools {
        jdk 'java_jdk8u442'
        maven 'maven-3.9.9'
    }
    stages {
        stage('代码拉取') {
            steps {
                script {
                    echo "[${env.BUILD_ID}] - 开始SCM代码拉取..."
                    // 核心SCM拉取指令
                    checkout([
                            $class: 'GitSCM',
                            branches: [[name: "${gitRemoteBranch}"]],  // 默认主分支，可按需替换为参数化分支
                            extensions: [
                                    // 清空工作区（可选）
                                    [$class: 'CleanCheckout']
                            ],
                            userRemoteConfigs: [[
                                                        credentialsId: "${gitIdKey}",  // 需提前在Jenkins凭证管理中配置
                                                        url: "${gitRemoteUrl}"
                                                ]]
                    ])

                    // 验证代码拉取结果
                    sh '''
                        echo "当前工作目录："
                        pwd
                        echo "文件列表："
                        ls -lha
                    '''
                }
            }
        }

        // 阶段2: 环境验证
        stage('Env Setup') {
            steps {
                sh 'java -version'  // 验证Java环境
                sh 'mvn -v'         // 验证Maven环境
            }
        }

        // 阶段3: 编译打包
        stage('Build & Package') {
            steps {
                sh 'mvn clean package -Pprod -DskipTests '
                // archiveArtifacts artifacts: 'target/*.jar', fingerprint: true

                sh '''
                    echo "编译成功, 打包开始"
                    cd chatgpt-api/target
                    ls -lha
                    zip -r ${zipFilePath} *.jar | tee zip.log
                    ls -lha ${zipFilePath}
                    echo "打包完成"
                '''
            }
        }
        stage('文件传输') {
            steps {
                withCredentials([usernamePassword(credentialsId: 'tarot-auth-25', usernameVariable: 'REMOTE_USER', passwordVariable: 'REMOTE_PASSWORD')]) {
                    script {
                        echo "[${env.BUILD_ID}] - 文件上传到远程服务器..."
                        sh """
                            echo '压缩文件如下: '
                            ls -lha ${zipFilePath}
                            export SSHPASS="\${REMOTE_PASSWORD}"
                            sshpass -e ssh -o StrictHostKeyChecking=no \${REMOTE_USER}@${remoteIp} 'mkdir -p ${remoteArchiveFolder}'
                            sshpass -e scp -o StrictHostKeyChecking=no ${zipFilePath} \${REMOTE_USER}@${remoteIp}:${remoteArchiveFolder}
                            unset SSHPASS
                        """
                    }
                }
            }
        }
        stage('远程部署') {
            steps {
                withCredentials([usernamePassword(credentialsId: 'tarot-auth-25', usernameVariable: 'REMOTE_USER', passwordVariable: 'REMOTE_PASSWORD')]) {
                    script {
                        echo "[${env.BUILD_ID}] - 执行远程部署..."
                        sh """
                            export SSHPASS="\${REMOTE_PASSWORD}"
                            sshpass -e ssh -o StrictHostKeyChecking=no \${REMOTE_USER}@${remoteIp} '''

                                mkdir -p ${remoteTargetFolder}
                                cd ${remoteTargetFolder}
                                echo '旧文件列表如下'
                                pwd
                                ls -lha

                                echo '旧文件清理'
                                rm -rf *.jar
                                echo '旧文件清理后目录'
                                pwd
                                ls -lha

                                echo '解压新文件'
                                unzip -o ${remoteArchivePath} -d ./

                                echo '新文件列表目录'
                                pwd
                                ls -lha
                            '''
                            unset SSHPASS
                        """
                    }
                }
            }
        }
    }
    post {
        success {
            echo '编译打包上传部署成功'
        }
        failure {
            echo '构建失败，请检查日志'
        }
        aborted {
            echo '构建被用户终止'
        }
        always {
            script {
                echo "[${env.BUILD_ID}] - 任务总耗时: ${currentBuild.durationString}"
            }
        }
    }
}