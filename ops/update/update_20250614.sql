CREATE TABLE `commission_identity` (
   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
   `type` varchar(20) DEFAULT NULL COMMENT '类型',
   `code` varchar(64) DEFAULT NULL COMMENT '编号',
   `status` varchar(10) DEFAULT NULL COMMENT '状态',
   `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
   `end_time` datetime NOT NULL COMMENT '过期时间',
   `percentage` int(5) DEFAULT NULL COMMENT '分佣比例',
   `name` varchar(100) DEFAULT NULL COMMENT '姓名',
   `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
   `wx_mp_url` varchar(255) NULL COMMENT '微信公众号链接',
   `user_info_id` int(11) NOT NULL COMMENT '用户信息ID',
   `open_id` varchar(64) NOT NULL COMMENT 'openId',
   `invite_code` varchar(50) NOT NULL COMMENT '邀请码',
   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=34245 DEFAULT CHARSET=utf8mb4 COMMENT='参与身份';