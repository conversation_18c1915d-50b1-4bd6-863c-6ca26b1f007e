
-- 翻译字典表
-- auto-generated definition
create table tarot_i18n
(
    id          bigint auto_increment
        primary key,
    lang        varchar(20)                        not null comment '语言',
    code        text                               not null comment '编码，同一段文字的不同语言翻译的code必须相同，code可以为中文',
    value       text                               not null comment '翻译值',
    create_time datetime default CURRENT_TIMESTAMP null comment '录入时间',
    update_time datetime default CURRENT_TIMESTAMP null comment '修改时间'
)
    comment '翻译字典表' row_format = DYNAMIC;

alter table product
    add currency varchar(20) null comment '币种(国际代号,全大写)';

alter table wx_pay_order
    add trade_channel varchar(20) null comment '交易渠道';
alter table wx_pay_order
    add currency varchar(20) null comment '交易币种(国际代号,全大写)';

alter table user_base_info
    add users_id bigint null comment 'users表id';

-- auto-generated definition
create table users
(
    id              bigint auto_increment
        primary key,
    fb_id           varchar(100)  null comment 'Facebook ID',
    name            varchar(100)  null comment '用户名',
    email           varchar(100)  null comment '邮箱',
    picture         varchar(255)  null comment '头像URL',
    access_token    varchar(255)  null comment 'Facebook访问令牌',
    last_login_time datetime      null comment '最后登录时间',
    created_at      datetime      null comment '创建时间',
    updated_at      datetime      null comment '更新时间',
    referrer_id     bigint        null comment '推荐人id',
    lucky_coins     bigint        null comment '幸运币',
    google_id       varchar(100)  null comment '谷歌id',
    finb_id         varchar(100)  null comment '浏览器指纹值',
    extra_data      varchar(2000) null comment '额外值',
    constraint uk_email
        unique (email),
    constraint uk_fb_id
        unique (fb_id),
    constraint uk_finb_id
        unique (finb_id)
)
    comment '用户表' row_format = DYNAMIC;


-- alter table users
--     add finb_id varchar(100) null comment '浏览器指纹值';
-- alter table users
--     add extra_data varchar(2000) null comment '额外值';
-- create unique index uk_finb_id
--     on users (finb_id);

ALTER TABLE tarot_spread ADD COLUMN time_set int(11) NULL DEFAULT NULL COMMENT '告警阈值设置' AFTER `model_key`;
ALTER TABLE tarot_reading_record MODIFY COLUMN answer VARCHAR(4000);