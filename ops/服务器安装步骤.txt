# 安装jdk
apt update
apt install openjdk-8-jdk
如出现需要重启内核，按提示重启，全选所有服务即可。

# 创建数据库
创建脚本
vi initdb.sh
添加以下内容：
mysql -u root -p <<EOF
CREATE DATABASE super_gpt CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'super_gpt'@'%' IDENTIFIED BY 'F4etrzBRayCNKePd';
GRANT ALL PRIVILEGES ON super_gpt.* TO 'super_gpt'@'%';
FLUSH PRIVILEGES;
EOF
保存后执行
sh initdb.sh

# 导入初始化数据
mysql -u super_gpt -p super_gpt < /home/<USER>/sql/super_gpt_init.sql

# 创建用户
useradd -m tarot
passwd tarot	#设置密码  Fdsr@2025
usermod -s /bin/bash tarot	#设置默认 Shell

# 创建目录
su - tarot
mkdir bak  bin  data  dist  jar  logs  sql  ssl

# 上传文件
包括：dist.zip和chatgpt-api-0.0.1-SNAPSHOT.jar到对应的目录
解包dist.zip到dist目录下
unzip dist.zip -d dist/

# Nginx配置
vi /etc/nginx/conf.d/tarot.conf

server {
    listen 443 ssl;
    server_name tarot.alwzc.com;

    # SSL 证书配置 - 需要替换为实际证书路径
    ssl_certificate /home/<USER>/ssl/tarot.alwzc.com.pem;
    ssl_certificate_key /home/<USER>/ssl/tarot.alwzc.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;

    # 前端资源服务
    root /home/<USER>/dist;
    index index.html;

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }

    # 代理 API 请求到后端
    location /api {
        proxy_pass http://localhost:3082/super/ai;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 处理前端路由（如使用 Vue Router 的 history 模式）
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 其他优化配置
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name tarot.alwzc.com;
    return 301 https://$host$request_uri;
}
# 验证配置文件是否有错误
nginx -t

#添加到tarot用户组，访问前端资源和证书文件
usermod -aG tarot www-data
systemctl restart nginx   # 重启生效

# 启动脚本
#!/bin/bash

# 基本参数配置
JAR_DIR="/home/<USER>/jar"       # jar包目录
LOG_DIR="/home/<USER>/logs"      # 日志目录
APP_PORT=3082                   # 应用端口
JVM_OPTS="-Xms512m -Xmx768m"    # JVM内存设置（保留部分内存给系统）
JAR_NAME="chatgpt-api-0.0.1-SNAPSHOT.jar"

SPRING_PROFILES_ACTIVE="--spring.profiles.active=prod"

# 确保日志目录存在
mkdir -p $LOG_DIR

# 构建完整jar路径
JAR_PATH="$JAR_DIR/$JAR_NAME"

# 检查jar文件是否存在
if [ ! -f "$JAR_PATH" ]; then
    echo "[ERROR] Jar文件不存在: $JAR_PATH"
    echo "可用jar文件:"
    ls $JAR_DIR/*.jar 2>/dev/null || echo "未找到任何jar文件"
    exit 1
fi

# 检查端口占用
if netstat -tuln | grep -q ":$APP_PORT "; then
    echo "[ERROR] 端口 $APP_PORT 已被占用"
    exit 1
fi

# 启动命令
nohup java $JVM_OPTS -jar $JAR_PATH --server.port=$APP_PORT $SPRING_PROFILES_ACTIVE > $LOG_DIR/zns_end.log 2>&1 &

# 显示启动信息
echo "应用已启动:"
echo "- Jar文件: $JAR_FILE"
echo "- 运行端口: $APP_PORT"
echo "- 日志文件: $LOG_DIR/zns_end.log"
echo "- PID: $!"

# 停止脚本
#!/bin/bash
JAR_FILE=chatgpt-api-0.0.1-SNAPSHOT.jar
PID=$(pgrep -f "$JAR_FILE")

if [ -z "$PID" ]; then
  echo "应用未运行"
else
  kill -15 $PID
  echo "已停止应用(PID: $PID)"
fi

# 修改为越南时区
# 确认时区文件是否存在
ls /usr/share/zoneinfo/Asia/Ho_Chi_Minh
# 加载时区数据
mysql_tzinfo_to_sql /usr/share/zoneinfo | mysql -u root -p mysql
mysql -u root -p
SET GLOBAL time_zone = 'Asia/Ho_Chi_Minh';
SET SESSION time_zone = 'Asia/Ho_Chi_Minh';

# 修改配置中的默认时区和绑定地址 永久生效
vi /etc/mysql/mariadb.conf.d/50-server.cnf
[mysqld]
default_time_zone = 'Asia/Ho_Chi_Minh'
bind-address            = 0.0.0.0
# 修改完成后重启数据库
systemctl restart mariadb

-Duser.timezone=Asia/Ho_Chi_Minh
jdbc配置中修改：serverTimezone=Asia/Ho_Chi_Minh