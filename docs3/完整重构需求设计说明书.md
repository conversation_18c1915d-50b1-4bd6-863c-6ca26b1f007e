# 超级智能社(SuperAI)完整重构需求设计说明书

## 1. 重构概述

### 1.1 重构背景
基于现有超级智能社项目的深度分析，结合61张数据表的完整业务逻辑，在保持核心业务需求不变的前提下，对系统进行全面重构，提升架构规范性、代码可维护性和系统扩展性。

### 1.2 文档关系说明
本文档是docs3目录下所有重构文档的核心串联文档，整合了以下关键文档：

#### 1.2.1 需求文档
- **重构原始需求清单.md**: 包含28个详细需求点，是重构的基础依据和最新需求

#### 1.2.2 设计文档
- **重构目录结构设计.md**: 详细的代码目录结构设计，是编码实施的指导
- **重构数据库设计.sql**: 完整的数据库表结构设计，约25张表

#### 1.2.3 实施文档
- **历史数据迁移方案.md**: 完整的数据迁移策略和脚本
- **接口对应关系说明.md**: 新旧接口对应关系，供前端改造使用

#### 1.2.4 技术集成文档
- **pay-java-parent集成说明.md**: 统一支付框架集成详细说明
- **Spring-AI集成详细说明.md**: Spring AI框架集成详细说明
- **国际化注解翻译详细实现.md**: 国际化功能详细实现说明

#### 1.2.5 管理文档
- **README.md**: 文档索引和项目导航
- **数据库导出表设计.sql**: 原有61张表结构，用于对比分析

### 1.3 重构目标
- **保持业务连续性**: 100%复刻原有功能的所有业务逻辑
- **架构标准化**: 采用标准Java开发规范，提升代码复用性
- **多元化支持**: 支持多币种、多语种、多时区、多种登录方式
- **性能优化**: 建立Redis和本地缓存机制，优化数据查询效率
- **配置管理**: 数据库配置化管理，支持动态开关和排序控制
- **全球化部署**: 全球一套服务，不同地区可自由访问使用

### 1.3 重构范围
- **包路径**: 保持现有com.hncboy.chatgpt，在此基础上重构
- **数据库**: 基于现有61张表结构，优化和新增必要表
- **核心功能**: 用户管理、AI对话、支付系统、塔罗牌、绘画功能等
- **新增功能**: 多渠道登录、统一支付、AI模型通道优化、内容审核等

## 2. 技术架构重构

### 2.1 核心技术集成说明

#### 2.1.1 JustAuth第三方登录集成
JustAuth是一个第三方授权登录的工具类库，支持各大平台的授权登录：
- **集成版本**: JustAuth 1.16.x
- **支持平台**: 微信、谷歌、Facebook、GitHub等
- **核心特性**: 统一的OAuth认证流程，简化第三方登录开发
- **数据存储**: user_joint_login表存储授权信息，user_joint_config表存储平台配置
- **与Sa-Token集成**: 统一会话管理和权限控制

#### 2.1.2 pay-java-parent统一支付集成
pay-java-parent是一个统一的支付SDK，支持多种支付方式：
- **集成版本**: pay-java-parent 2.13.x
- **支付宝集成**: 使用com.egzosn.pay-java-ali包的AliPayServiceImpl官方实现
- **微信支付集成**: 使用com.egzosn.pay-java-wx包的WxPayServiceImpl官方实现，多商户按biz_scene区分
- **越南支付**: 保持现有Momo和SePay实现
- **核心特性**: 统一的支付接口，简化多渠道支付开发
- **多币种支持**: 支持产品币种和支付币种分离，自动汇率转换
- **配置管理**: pay_channel_config表管理多商户配置

#### 2.1.3 Spring AI框架集成
Spring AI是Spring生态的AI集成框架：
- **集成版本**: Spring AI 1.0.x
- **核心特性**: 统一的AI模型调用接口，支持多种AI服务
- **模型支持**: OpenAI、Claude、Gemini等主流AI模型
- **与现有业务集成**: 保持现有AI功能不变，增强扩展性
- **通道管理**: channel_router系列表管理AI模型通道选择

#### 2.1.4 国际化注解翻译实现
基于数据库驱动的国际化方案：
- **按值翻译**: 直接用文本内容查询翻译，有就翻译，没有就返回原文
- **参数化翻译**: 支持{{count}}等动态参数替换
- **注解翻译**: 通过@I18n注解实现字段自动翻译
- **工具类支持**: I18nUtil提供编程式调用接口
- **缓存优化**: Redis缓存翻译结果，提升性能

#### 2.1.5 POINTS概念修正
- **概念澄清**: POINTS是塔罗币，不是积分
- **修订原则**: 修订中文描述，保留英文单词POINTS
- **区分概念**: 积分(points)和塔罗币(POINTS)是两个不同概念
- **应用范围**: 所有文档和注释中的POINTS中文描述修正为"塔罗币"

### 2.2 技术栈升级
```yaml
基础框架:
  - Spring Boot: 2.7.x (保持现有)
  - MyBatis-Plus: 3.5.x (保持现有)
  - MySQL: 8.0 (保持现有)

新增组件:
  - 第三方登录: JustAuth 1.16.x (新增，整合到user_joint_login表)
  - 支付集成: pay-java-parent 2.13.x (替换现有支付)
  - 缓存框架: Redis 6.x + Caffeine (增强现有缓存)
  - 国际化: 数据库存储 + 按值翻译 (新增)
  - Spring AI: 1.0.x (新增AI功能增强)

保持组件:
  - 权限认证: Sa-Token 1.34.x (保持现有，无需替换)

保持组件:
  - Web框架: Spring MVC
  - 数据库连接池: HikariCP
  - JSON处理: Jackson
  - 日志框架: Logback
```

### 2.2 包结构重构设计 - 按业务场景划分
```
com.hncboy.chatgpt
├── common                          # 公共模块 (保持现有)
│   ├── constant                    # 常量定义 (保持)
│   ├── enums                       # 枚举类 (增强)
│   ├── exception                   # 异常处理 (保持)
│   ├── util                        # 工具类 (保持)
│   └── validation                  # 参数校验 (增强)
├── db                             # 数据库相关操作的直接实现
│   ├── entity                     # 数据库表映射 (按需重新梳理表结构)
│   │   ├── user                   # 用户相关实体 (7张表)
│   │   ├── pay                    # 支付相关实体 (2张表)
│   │   ├── ai                     # AI相关实体 (9张表)
│   │   ├── tarot                  # 塔罗牌相关实体 (3张表)
│   │   ├── system                 # 系统功能相关实体 (6张表)
│   │   └── withdraw               # 提现功能相关实体 (2张表)
│   ├── service                    # 数据库表数据管理服务 (MyBatis-Plus标准CRUD)
│   │   ├── user                   # 用户相关Service (db包下保留Service)
│   │   ├── pay                    # 支付相关Service
│   │   ├── ai                     # AI相关Service
│   │   ├── tarot                  # 塔罗牌相关Service
│   │   ├── system                 # 系统功能相关Service
│   │   └── withdraw               # 提现功能相关Service
│   └── mapper                     # MyBatis映射器 (保持现有结构)
├── framework                      # 框架核心 (重构)
│   ├── auth                       # 认证授权 (JustAuth集成，Sa-Token保持)
│   ├── cache                      # 缓存框架 (新增Redis+Caffeine)
│   ├── pay                        # 支付框架 (新增pay-java-parent)
│   ├── i18n                       # 国际化框架 (新增按值翻译)
│   ├── ai                         # AI框架 (重构)
│   │   ├── chat                   # AI对话
│   │   ├── draw                   # AI绘画
│   │   ├── music                  # AI生成音乐
│   │   ├── write                  # AI写作
│   │   ├── prompter               # AI提示词管理
│   │   ├── channel                # AI模型通道管理
│   │   ├── filter                 # AI内容过滤
│   │   └── dify                   # Dify对接工具包
│   ├── withdraw                   # 提现功能模块
│   ├── security                   # 安全框架 (重构)
│   └── web                        # Web框架 (保持现有)
├── biz                            # 业务模块 (按产品类型划分)
│   ├── tarot                      # 塔罗牌业务
│   │   ├── controller             # 塔罗牌控制器
│   │   ├── worker                 # 塔罗牌业务逻辑 (Service改为Worker)
│   │   └── domain                 # 塔罗牌领域对象
│   ├── zns                        # 智能社业务
│   │   ├── controller             # 智能社控制器
│   │   ├── worker                 # 智能社业务逻辑 (Service改为Worker)
│   │   └── domain                 # 智能社领域对象
│   └── chatoi                     # AI对话业务
│       ├── controller             # AI对话控制器
│       ├── worker                 # AI对话业务逻辑 (Service改为Worker)
│       └── domain                 # AI对话领域对象
├── admin                          # 管理后台 (保持现有)
├── api                            # API接口 (保持结构，重构内容)
│   ├── controller                 # 控制器层
│   ├── worker                     # 业务逻辑层 (Service改为Worker)
│   └── mapper                     # 数据访问层
├── base                           # 基础模块 (保持现有)
└── ChatgptApplication.java       # 主启动类 (保持)
```

## 3. 数据库重构设计

### 3.1 表结构优化策略

基于对现有61张表的深度分析，按照业务逻辑相关性和数据访问模式，制定合理的表合并和优化策略：

#### 3.1.1 命名规范统一
- **advertisement → adv**: 所有广告相关命名使用adv缩写
- **business_scene → biz_scene**: 所有业务场景字段统一使用biz_scene
- **统一字段**: create_by/create_time/update_by/update_time标准字段

#### 3.1.2 AI表按能力类型合并
- **AI消息表**: chat_message + draw_message + write_message → ai_message
- **AI房间表**: chat_room + draw_room → ai_room
- **AI智能体**: chat_agent + write_agent → ai_agent
- **AI分类**: category_info + write_category → ai_category
- **能力类型**: 通过ability_type字段区分CHAT/DRAW/WRITE/MUSIC

#### 3.1.3 AI通道表重构
- **表重构对应关系**:
  - site_info → ai_router_config (路由配置表)
  - channel_config → ai_router (路由信息表)
  - model → ai_model (模型信息表)
- **优化效果**: 更清晰的通道路由管理，支持故障转移和负载均衡

#### 3.1.4 提现功能表重构
- **表重构对应关系**:
  - transfer_info → withdraw_application (提现申请表)
  - transaction → withdraw_transaction (提现交易记录表，关联银行交易流水)
- **优化效果**: 独立的提现功能模块，支持完整的提现流程管理

#### 3.1.4 支付订单表合并
- **原表**: al_orders, wx_pay_order, se_pay_order, qr_payment (4张表)
- **合并后**: pay_order (1张表)
- **多币种支持**: product_currency(产品币种) + pay_currency(支付币种) + exchange_rate(汇率)

#### 3.1.5 分佣功能保留
- **commission_identity表**: 完整保留分佣身份管理
- **相关字段**: commission_id、parent_id、referrer_id等分佣相关字段全部保留

## 4. 核心功能重构设计

### 4.1 用户功能重构

#### 4.1.1 用户信息管理重构
**现状分析**:
- 现有user_base_info表作为主用户表
- users表存储第三方登录信息
- wx_user_info表存储微信用户信息

**重构方案**:
```java
// 保持现有表结构，增强业务逻辑
@Component
public class UserWorker {
    
    /**
     * 统一用户信息获取 - 整合多表数据
     */
    @Override
    public UserInfoVO getUserInfo(Integer userId) {
        // 1. 获取基础用户信息
        UserBaseInfo baseInfo = userBaseInfoMapper.selectById(userId);
        
        // 2. 获取第三方登录信息
        Users thirdPartyInfo = null;
        if (baseInfo.getUsersId() != null) {
            thirdPartyInfo = usersMapper.selectById(baseInfo.getUsersId());
        }
        
        // 3. 获取微信用户信息
        WxUserInfo wxInfo = wxUserInfoMapper.selectByUserId(userId);
        
        // 4. 整合用户信息
        return buildUserInfoVO(baseInfo, thirdPartyInfo, wxInfo);
    }
}
```

#### 3.1.2 多渠道登录重构
**新增表结构**:
```sql
-- 联合登录表 (新增)
CREATE TABLE `user_joint_login` (
  `id` bigint AUTO_INCREMENT PRIMARY KEY,
  `user_id` int NOT NULL COMMENT '关联user_base_info.id',
  `login_type` varchar(20) NOT NULL COMMENT '登录类型',
  `third_party_id` varchar(100) NOT NULL COMMENT '第三方唯一标识',
  `third_party_data` text COMMENT '第三方数据(JSON)',
  `access_token` varchar(500) COMMENT '访问令牌',
  `refresh_token` varchar(500) COMMENT '刷新令牌',
  `expire_time` datetime COMMENT '令牌过期时间',
  `status` tinyint DEFAULT 1 COMMENT '状态(0:禁用 1:启用)',
  `bind_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `last_use_time` datetime COMMENT '最后使用时间',
  UNIQUE KEY `uk_type_third_id` (`login_type`, `third_party_id`),
  KEY `idx_user_id` (`user_id`)
) COMMENT '用户联合登录表';

-- 联合登录配置表 (新增)
CREATE TABLE `user_joint_config` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `login_type` varchar(20) NOT NULL COMMENT '登录类型',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `client_id` varchar(255) NOT NULL COMMENT '客户端ID',
  `client_secret` varchar(255) NOT NULL COMMENT '客户端密钥',
  `redirect_uri` varchar(255) COMMENT '回调地址',
  `scope` varchar(255) COMMENT '授权范围',
  `config_params` text COMMENT '配置参数(JSON)',
  `enabled` tinyint DEFAULT 1 COMMENT '是否启用',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `environment` varchar(20) DEFAULT 'prod' COMMENT '适用环境',
  UNIQUE KEY `uk_type_env` (`login_type`, `environment`)
) COMMENT '联合登录配置表';
```

**支持的登录方式**:
1. **微信开放平台**: 服务号 + 订阅号
2. **账号密码**: 传统用户名密码登录
3. **手机号验证码**: 国内/国际手机号 + 短信验证码
4. **邮箱验证码**: 邮箱 + 邮件验证码
5. **Google账号**: Google OAuth2登录
6. **Facebook账号**: Facebook OAuth2登录
7. **浏览器指纹**: 设备指纹识别登录

#### 3.1.3 用户注销功能重构
**业务逻辑**:
```java
@Override
@Transactional(rollbackFor = Exception.class)
public Boolean deactivateUser(Integer userId, UserDeactivateDTO deactivateDTO) {
    // 1. 检查用户是否存在
    UserBaseInfo userInfo = userBaseInfoMapper.selectById(userId);
    if (userInfo == null) {
        throw new ServiceException("用户不存在");
    }
    
    // 2. 检查是否可以注销 - 跨业务场景检查
    if (!canDeactivate(userId)) {
        throw new ServiceException("用户在其他业务场景有未完成事项，无法注销");
    }
    
    // 3. 标记用户为注销状态，但保留历史数据
    userInfo.setStatus(2); // 2=注销状态
    userInfo.setUpdateTime(LocalDateTime.now());
    userBaseInfoMapper.updateById(userInfo);
    
    // 4. 处理关联数据 - 软删除或状态标记
    handleRelatedDataOnDeactivation(userId);
    
    // 5. 记录注销日志
    logUserDeactivation(userId, deactivateDTO.getReason());
    
    return true;
}

/**
 * 检查用户是否可以注销
 */
private boolean canDeactivate(Integer userId) {
    // 检查是否有未完成的订单
    if (hasUnfinishedOrders(userId)) {
        return false;
    }
    
    // 检查是否有未完成的绘画任务
    if (hasUnfinishedDrawTasks(userId)) {
        return false;
    }
    
    // 检查是否有分佣关系
    if (hasCommissionRelations(userId)) {
        return false;
    }
    
    return true;
}
```

### 3.2 支付功能重构

#### 3.2.1 统一支付架构 - 直接使用pay-java-parent官方实现
**重构策略**: 直接配置使用pay-java-parent官方支持的支付方式，无需重写现有代码

```java
// 统一支付业务逻辑 - 直接使用官方API
@Component
@Slf4j
public class UnifiedPaymentWorker {

    /**
     * 支付宝支付 - 直接使用pay-java-parent官方实现
     */
    public PaymentResultVO createAlipayOrder(CreateOrderDTO orderDTO) {
        // 1. 构建支付宝配置
        AliPayConfig aliPayConfig = buildAlipayConfig();

        // 2. 创建支付服务 - 使用官方实现
        PayService payService = new AliPayServiceImpl(aliPayConfig);

        // 3. 构建支付请求 - 使用官方请求对象
        AliPayOrderRequest request = AliPayOrderRequest.builder()
            .subject(orderDTO.getProductName())
            .outTradeNo(orderDTO.getOrderNo())
            .totalAmount(orderDTO.getAmount().toString())
            .body(orderDTO.getDescription())
            .build();

        // 4. 调用官方支付接口
        String payUrl = payService.toPay(request);

        // 5. 保存订单到统一订单表
        savePaymentOrder(orderDTO, "ALIPAY", payUrl);

        return PaymentResultVO.builder()
            .orderNo(orderDTO.getOrderNo())
            .payUrl(payUrl)
            .qrCode(generateQrCode(payUrl))
            .build();
    }

    /**
     * 微信支付 - 直接使用pay-java-parent官方实现
     */
    public PaymentResultVO createWechatPayOrder(CreateOrderDTO orderDTO) {
        // 1. 构建微信支付配置
        WxPayConfig wxPayConfig = buildWechatPayConfig();

        // 2. 创建支付服务 - 使用官方实现
        PayService payService = new WxPayServiceImpl(wxPayConfig);

        // 3. 构建支付请求 - 使用官方请求对象
        WxPayOrderRequest request = WxPayOrderRequest.builder()
            .body(orderDTO.getProductName())
            .outTradeNo(orderDTO.getOrderNo())
            .totalFee(orderDTO.getAmount().multiply(new BigDecimal("100")).intValue()) // 转为分
            .spbillCreateIp(orderDTO.getClientIp())
            .build();

        // 4. 调用官方支付接口
        String payUrl = payService.toPay(request);

        // 5. 保存订单到统一订单表
        savePaymentOrder(orderDTO, "WECHAT", payUrl);

        return PaymentResultVO.builder()
            .orderNo(orderDTO.getOrderNo())
            .payUrl(payUrl)
            .qrCode(generateQrCode(payUrl))
            .build();
    }

    /**
     * 统一支付回调处理 - 使用官方回调处理
     */
    public Boolean handlePaymentCallback(String channel, HttpServletRequest request) {
        try {
            PayService payService = getPayService(channel);

            // 使用官方回调验证
            PayResponse payResponse = payService.callback(request);

            if (payResponse.isSuccess()) {
                // 更新订单状态
                updateOrderStatus(payResponse.getOutTradeNo(), "PAID");

                // 执行业务逻辑 (发货、增加积分等)
                executeBusinessLogic(payResponse);

                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("支付回调处理失败: channel={}", channel, e);
            return false;
        }
    }

    /**
     * 构建支付宝配置 - 从数据库读取
     */
    private AliPayConfig buildAlipayConfig() {
        PaymentChannelConfig config = getChannelConfig("ALIPAY");

        return AliPayConfig.builder()
            .appId(config.getAppId())
            .privateKey(config.getPrivateKey())
            .publicKey(config.getPublicKey())
            .serverUrl(config.getApiUrl())
            .signType("RSA2")
            .build();
    }

    /**
     * 构建微信支付配置 - 从数据库读取
     */
    private WxPayConfig buildWechatPayConfig() {
        PaymentChannelConfig config = getChannelConfig("WECHAT");

        return WxPayConfig.builder()
            .appId(config.getAppId())
            .mchId(config.getMerchantId())
            .mchKey(config.getAppSecret())
            .notifyUrl(config.getNotifyUrl())
            .build();
    }
}
```

**重构优势**:
1. **零代码重写**: 直接使用pay-java-parent官方成熟实现
2. **快速集成**: 只需配置参数，无需开发支付逻辑
3. **官方维护**: 享受官方更新和bug修复
4. **标准化**: 使用业界标准的支付接口
5. **减少风险**: 避免自研支付代码的安全风险

#### 3.2.2 支付渠道配置管理 - 支持pay-java-parent官方配置
**配置表设计**: 存储pay-java-parent所需的官方配置参数

```sql
CREATE TABLE `payment_channel_config` (
  `id` bigint AUTO_INCREMENT PRIMARY KEY,
  `channel_code` varchar(20) NOT NULL COMMENT '渠道代码(ALIPAY/WECHAT/SEPAY/MOMO)',
  `channel_name` varchar(100) NOT NULL COMMENT '渠道名称',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式(SCAN/H5/APP)',

  -- pay-java-parent官方配置字段
  `app_id` varchar(100) COMMENT '应用ID(支付宝AppId/微信AppId)',
  `merchant_id` varchar(100) COMMENT '商户号(支付宝PID/微信MchId)',
  `app_secret` varchar(255) COMMENT '应用密钥(微信MchKey)',
  `private_key` text COMMENT '应用私钥(支付宝/微信)',
  `public_key` text COMMENT '平台公钥(支付宝)',
  `cert_path` varchar(255) COMMENT '证书路径(微信)',

  -- 通用配置
  `api_url` varchar(255) COMMENT 'API地址(支付宝网关)',
  `notify_url` varchar(255) COMMENT '异步通知地址',
  `return_url` varchar(255) COMMENT '同步返回地址',
  `sign_type` varchar(10) DEFAULT 'RSA2' COMMENT '签名类型',

  -- 业务配置
  `supported_currencies` varchar(255) COMMENT '支持币种["CNY","USD","VND"]',
  `enabled` tinyint DEFAULT 1 COMMENT '是否启用',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `environment` varchar(20) DEFAULT 'prod' COMMENT '环境(dev/test/prod)',
  `fee_rate` decimal(5,4) DEFAULT 0 COMMENT '费率',
  `min_amount` decimal(12,2) DEFAULT 0.01 COMMENT '最小金额',
  `max_amount` decimal(12,2) DEFAULT 50000 COMMENT '最大金额',

  -- 扩展配置 (JSON格式存储pay-java-parent特殊配置)
  `extra_config` text COMMENT '扩展配置(JSON格式)',

  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  UNIQUE KEY `uk_channel_env` (`channel_code`, `environment`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_sort_order` (`sort_order`)
) COMMENT '支付渠道配置表-兼容pay-java-parent';
```

**配置示例**:
```sql
-- 支付宝配置 (pay-java-parent官方参数)
INSERT INTO `payment_channel_config` VALUES (
  1, 'ALIPAY', '支付宝', 'SCAN',
  'your_alipay_app_id',           -- app_id
  'your_alipay_pid',              -- merchant_id
  NULL,                           -- app_secret (支付宝不需要)
  'your_alipay_private_key',      -- private_key
  'alipay_public_key',            -- public_key
  NULL,                           -- cert_path (支付宝不需要)
  'https://openapi.alipay.com/gateway.do', -- api_url
  'https://yourdomain.com/notify/alipay',  -- notify_url
  'https://yourdomain.com/return/alipay',  -- return_url
  'RSA2',                         -- sign_type
  '["CNY","USD"]',                -- supported_currencies
  1, 1, 'prod', 0.006, 0.01, 50000,
  '{"timeout":"15m","product_code":"FAST_INSTANT_TRADE_PAY"}', -- extra_config
  NOW(), NOW()
);

-- 微信支付配置 (pay-java-parent官方参数)
INSERT INTO `payment_channel_config` VALUES (
  2, 'WECHAT', '微信支付', 'SCAN',
  'your_wechat_app_id',           -- app_id
  'your_wechat_mch_id',           -- merchant_id
  'your_wechat_mch_key',          -- app_secret (微信叫mch_key)
  'your_wechat_private_key',      -- private_key
  NULL,                           -- public_key (微信不需要)
  '/path/to/wechat/cert.p12',     -- cert_path
  'https://api.mch.weixin.qq.com', -- api_url
  'https://yourdomain.com/notify/wechat', -- notify_url
  'https://yourdomain.com/return/wechat', -- return_url
  'MD5',                          -- sign_type
  '["CNY"]',                      -- supported_currencies
  1, 2, 'prod', 0.006, 0.01, 50000,
  '{"trade_type":"NATIVE","timeout":"15m"}', -- extra_config
  NOW(), NOW()
);
```

### 3.3 AI模型通道选择优化

#### 3.3.1 基于现有三表优化
**现有表结构分析**:
- `model`: 存储AI模型定义
- `channel_config`: 存储模型与站点的映射配置
- `site_info`: 存储API站点的详细信息

**优化方案**:
```java
@Component
@Slf4j
public class OptimizedChannelSelectorWorker {
    
    /**
     * 智能通道选择算法 - 基于现有三表优化
     */
    @Cacheable(value = "optimal_channel", key = "#modelId + '_' + #request.hashCode()")
    public ChannelInfo selectOptimalChannel(String modelId, ChatRequest request) {
        // 1. 从model表获取模型信息
        Model model = modelMapper.selectByModelId(modelId);
        if (model == null || model.getStatus() != 1) {
            throw new ServiceException("模型不可用: " + modelId);
        }
        
        // 2. 从channel_config表获取该模型的所有通道
        List<ChannelConfig> channels = channelConfigMapper.selectByModelId(model.getId());
        
        // 3. 过滤可用通道
        channels = filterAvailableChannels(channels);
        
        // 4. 基于多因素评分选择最优通道
        ChannelConfig selectedChannel = selectByAdvancedScore(channels, request);
        
        // 5. 获取站点信息
        SiteInfo siteInfo = siteInfoMapper.selectById(selectedChannel.getSiteId());
        
        // 6. 构建通道信息
        return buildChannelInfo(model, selectedChannel, siteInfo);
    }
    
    /**
     * 高级评分算法
     */
    private ChannelConfig selectByAdvancedScore(List<ChannelConfig> channels, ChatRequest request) {
        Map<ChannelConfig, Double> scores = new HashMap<>();
        
        for (ChannelConfig channel : channels) {
            double score = calculateAdvancedScore(channel, request);
            scores.put(channel, score);
        }
        
        return scores.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElseThrow(() -> new ServiceException("无法选择合适的通道"));
    }
    
    /**
     * 多因素评分计算
     */
    private double calculateAdvancedScore(ChannelConfig channel, ChatRequest request) {
        double score = 0;
        
        // 1. 优先级权重 (30%)
        score += (100 - channel.getPriority()) * 0.3;
        
        // 2. 成本权重 (25%)
        score += calculateCostScore(channel) * 0.25;
        
        // 3. 性能权重 (20%)
        score += calculatePerformanceScore(channel) * 0.2;
        
        // 4. 可用性权重 (15%)
        score += calculateAvailabilityScore(channel) * 0.15;
        
        // 5. 负载权重 (10%)
        score += calculateLoadScore(channel) * 0.1;
        
        return score;
    }
}
```

## 4. 缓存架构重构

### 4.1 多级缓存设计
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    /**
     * L1缓存 - Caffeine本地缓存
     */
    @Bean
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .recordStats());
        return cacheManager;
    }
    
    /**
     * L2缓存 - Redis分布式缓存
     */
    @Bean
    public CacheManager redisCacheManager(RedisConnectionFactory factory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(factory)
            .cacheDefaults(config)
            .build();
    }
}

/**
 * 缓存管理业务逻辑
 */
@Component
public class CacheManagerWorker {
    
    @Autowired
    private CacheManager caffeineCacheManager;
    
    @Autowired
    private CacheManager redisCacheManager;
    
    /**
     * 清空指定缓存
     */
    public void clearCache(String cacheName) {
        // 清空本地缓存
        Cache caffeineCache = caffeineCacheManager.getCache(cacheName);
        if (caffeineCache != null) {
            caffeineCache.clear();
        }
        
        // 清空Redis缓存
        Cache redisCache = redisCacheManager.getCache(cacheName);
        if (redisCache != null) {
            redisCache.clear();
        }
        
        log.info("已清空缓存: {}", cacheName);
    }
    
    /**
     * 刷新指定缓存
     */
    public void refreshCache(String cacheName, String key) {
        // 先清除
        evictCache(cacheName, key);
        
        // 触发重新加载 (通过调用相应的服务方法)
        triggerCacheReload(cacheName, key);
        
        log.info("已刷新缓存: {}:{}", cacheName, key);
    }
}
```

## 4.8 Spring AI集成方案 - 覆盖现有AI功能

### 4.8.1 Spring AI功能分析
Spring AI 1.0提供了完整的AI集成能力，可以覆盖现有系统的大部分AI功能：

#### 支持的AI模型提供商
- **OpenAI**: ChatGPT-3.5, ChatGPT-4, GPT-4o, DALL-E 3
- **Anthropic**: Claude 3 (Haiku, Sonnet, Opus)
- **Google**: Gemini Pro, Gemini Pro Vision
- **Microsoft**: Azure OpenAI Service
- **Amazon**: Bedrock (Claude, Llama2, Titan)
- **Ollama**: 本地模型部署

#### 核心功能特性
```yaml
Chat Model API:
  - 同步和流式对话
  - 函数调用支持
  - 多轮对话管理
  - 角色扮演支持

Image Model API:
  - 文生图功能 (DALL-E, Midjourney等)
  - 图像理解和分析
  - 图像编辑和变换

Audio Model API:
  - 文本转语音 (TTS)
  - 语音转文本 (STT)
  - 语音克隆

Embedding Model API:
  - 文本向量化
  - 语义搜索
  - 相似度计算

Vector Database:
  - 支持17种向量数据库
  - 向量存储和检索
  - RAG (检索增强生成)
```

### 4.8.2 现有功能覆盖分析

#### AI对话功能 - 完全覆盖
```java
// 现有实现 -> Spring AI实现
@Component
public class SpringAIChatWorker {

    @Autowired
    private ChatClient chatClient; // Spring AI的ChatClient

    @Autowired
    private ChannelSelectorWorker channelSelectorWorker; // 保留现有通道选择

    /**
     * AI对话 - 使用Spring AI ChatClient
     */
    public ChatResponse chat(ChatRequest request) {
        // 1. 选择最优通道 (保留现有逻辑)
        ChannelInfo channelInfo = channelSelectorWorker.selectOptimalChannel(
            request.getModelId(), request);

        // 2. 构建Spring AI ChatClient
        ChatClient client = buildChatClient(channelInfo);

        // 3. 发送对话请求
        ChatResponse response = client.prompt()
            .user(request.getContent())
            .system(request.getSystemPrompt())
            .call()
            .chatResponse();

        // 4. 保存对话记录 (保留现有逻辑)
        saveChatMessage(request, response);

        return response;
    }

    /**
     * 流式对话 - Spring AI原生支持
     */
    public Flux<ChatResponse> streamChat(ChatRequest request) {
        ChannelInfo channelInfo = channelSelectorWorker.selectOptimalChannel(
            request.getModelId(), request);

        ChatClient client = buildChatClient(channelInfo);

        return client.prompt()
            .user(request.getContent())
            .stream()
            .chatResponse();
    }
}
```

#### 文生图功能 - 完全覆盖
```java
// 现有Midjourney实现 -> Spring AI实现
@Component
public class SpringAIImageWorker {

    @Autowired
    private ImageClient imageClient; // Spring AI的ImageClient

    /**
     * 文生图 - 使用Spring AI ImageClient
     */
    public ImageResponse generateImage(ImageRequest request) {
        // 1. 敏感词检测 (保留现有逻辑)
        String filteredPrompt = sensitiveWordFilter.filter(request.getPrompt());

        // 2. 使用Spring AI生成图片
        ImageResponse response = imageClient.call(
            ImagePrompt.builder()
                .prompt(filteredPrompt)
                .options(ImageOptions.builder()
                    .model("dall-e-3")
                    .width(1024)
                    .height(1024)
                    .quality("hd")
                    .build())
                .build());

        // 3. 保存任务记录 (保留现有逻辑)
        saveImageTask(request, response);

        return response;
    }
}
```

### 4.8.3 AI Agent和MCP集成可行性

#### AI Agent支持
Spring AI支持函数调用，可以轻松实现AI Agent：

```java
@Component
public class WeatherAgent {

    @Function("获取天气信息")
    public String getCurrentWeather(String location) {
        return weatherService.getWeather(location);
    }

    @Function("发送邮件")
    public String sendEmail(String to, String subject, String content) {
        return emailService.send(to, subject, content);
    }
}

// 使用Agent
public String chatWithAgent(String userMessage) {
    return chatClient.prompt()
        .user(userMessage)
        .function("getCurrentWeather", weatherAgent::getCurrentWeather)
        .function("sendEmail", weatherAgent::sendEmail)
        .call()
        .content();
}
```

### 4.8.4 集成优势

#### 技术优势
- ✅ **标准化**: 使用Spring生态标准AI框架
- ✅ **多模态**: 支持文本、图像、音频多模态AI
- ✅ **向量搜索**: 内置RAG和向量数据库支持
- ✅ **函数调用**: 原生支持AI Agent和工具调用
- ✅ **流式处理**: 原生支持流式响应

#### 兼容性保证
- ✅ **渐进迁移**: 现有功能可以逐步迁移，不影响业务
- ✅ **数据兼容**: 保留现有数据表和业务逻辑
- ✅ **接口兼容**: 保持现有API接口不变
- ✅ **配置兼容**: 保留现有通道选择和配置管理

## 5. 国际化支持重构 - 基于数据库存储 + 注解翻译

### 5.1 数据库国际化设计 (参考RuoYi-Vue-Plus)
基于数据库存储国际化信息，支持动态标签注入和{{count}}格式的参数化翻译

#### 5.1.1 国际化注解设计
```java
/**
 * 国际化翻译注解 - 参考RuoYi-Vue-Plus Translation注解
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
@Documented
@JacksonAnnotationsInside
@JsonSerialize(using = I18nTranslationHandler.class)
public @interface I18nTranslation {

    /**
     * 翻译键 (对应i18n_message表的message_key)
     */
    String key();

    /**
     * 参数映射字段 (支持{{count}}等动态参数)
     */
    String[] paramFields() default {};

    /**
     * 默认值 (当翻译不存在时使用)
     */
    String defaultValue() default "";

    /**
     * 是否缓存翻译结果
     */
    boolean cached() default true;
}

/**
 * 国际化翻译类型注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
public @interface I18nTranslationType {
    String type();
}
```

#### 5.1.2 Jackson序列化处理器
```java
/**
 * 国际化翻译处理器 - 参考RuoYi-Vue-Plus TranslationHandler
 */
@Slf4j
public class I18nTranslationHandler extends JsonSerializer<Object> implements ContextualSerializer {

    /**
     * 全局翻译实现类映射器
     */
    public static final Map<String, I18nTranslationInterface> TRANSLATION_MAPPER = new ConcurrentHashMap<>();

    private I18nTranslation i18nTranslation;

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        try {
            // 1. 获取当前用户语言环境
            String locale = getCurrentLocale();

            // 2. 获取翻译键
            String messageKey = i18nTranslation.key();

            // 3. 构建参数Map
            Map<String, Object> params = buildParams(gen.getCurrentValue(), i18nTranslation.paramFields());

            // 4. 获取翻译结果
            String translatedMessage = getTranslatedMessage(messageKey, locale, params);

            // 5. 如果翻译不存在，使用默认值
            if (StringUtils.isBlank(translatedMessage)) {
                translatedMessage = StringUtils.isNotBlank(i18nTranslation.defaultValue())
                    ? i18nTranslation.defaultValue()
                    : String.valueOf(value);
            }

            // 6. 输出翻译结果
            gen.writeString(translatedMessage);

        } catch (Exception e) {
            log.error("国际化翻译失败: key={}", i18nTranslation.key(), e);
            gen.writeString(String.valueOf(value));
        }
    }

    /**
     * 获取翻译消息，支持{{param}}格式的参数替换
     */
    private String getTranslatedMessage(String messageKey, String locale, Map<String, Object> params) {
        // 1. 从缓存或数据库获取翻译模板
        String template = I18nMessageService.getMessage(messageKey, locale);

        if (StringUtils.isBlank(template)) {
            return null;
        }

        // 2. 替换参数 {{count}} -> 实际值
        String result = template;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            String value = String.valueOf(entry.getValue());
            result = result.replace(placeholder, value);
        }

        return result;
    }

    /**
     * 构建参数Map
     */
    private Map<String, Object> buildParams(Object currentObject, String[] paramFields) {
        Map<String, Object> params = new HashMap<>();

        for (String field : paramFields) {
            try {
                Object value = ReflectUtils.invokeGetter(currentObject, field);
                params.put(field, value);
            } catch (Exception e) {
                log.warn("获取参数字段失败: field={}", field, e);
                params.put(field, "");
            }
        }

        return params;
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        I18nTranslation translation = property.getAnnotation(I18nTranslation.class);
        if (Objects.nonNull(translation)) {
            this.i18nTranslation = translation;
            return this;
        }
        return prov.findValueSerializer(property.getType(), property);
    }
}
```

#### 5.1.3 国际化服务实现
```java
/**
 * 国际化消息服务
 */
@Service
@Slf4j
public class I18nMessageService {

    @Autowired
    private I18nMessageMapper i18nMessageMapper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 获取翻译消息 - 支持缓存
     */
    @Cacheable(value = "i18n_messages", key = "#messageKey + '_' + #locale")
    public String getMessage(String messageKey, String locale) {
        // 1. 先查询指定语言的翻译
        I18nMessage message = i18nMessageMapper.selectByKeyAndLocale(messageKey, locale);

        if (message != null) {
            return message.getMessageValue();
        }

        // 2. 如果没有找到，尝试查询默认语言(中文)
        if (!"zh_CN".equals(locale)) {
            message = i18nMessageMapper.selectByKeyAndLocale(messageKey, "zh_CN");
            if (message != null) {
                return message.getMessageValue();
            }
        }

        // 3. 都没有找到，返回null
        return null;
    }

    /**
     * 批量获取翻译消息
     */
    public Map<String, String> getMessages(List<String> messageKeys, String locale) {
        Map<String, String> result = new HashMap<>();

        for (String key : messageKeys) {
            String message = getMessage(key, locale);
            if (message != null) {
                result.put(key, message);
            }
        }

        return result;
    }

    /**
     * 刷新缓存
     */
    @CacheEvict(value = "i18n_messages", allEntries = true)
    public void refreshCache() {
        log.info("刷新国际化消息缓存");
    }
}
```

### 5.2 使用示例
```java
/**
 * 用户信息VO - 支持国际化翻译
 */
@Data
public class UserInfoVO {

    private Integer id;

    private String name;

    @I18nTranslation(key = "user.status", paramFields = {"statusCode"})
    private String statusText; // 将根据statusCode翻译为对应语言的状态文本

    private Integer statusCode; // 状态码，用于翻译参数

    @I18nTranslation(key = "user.points.message", paramFields = {"points"})
    private String pointsMessage; // "您有{{points}}个积分" -> "You have {{points}} points"

    private Integer points; // 积分数量，用于翻译参数

    @I18nTranslation(key = "user.vip.expire", paramFields = {"days"})
    private String vipExpireMessage; // "VIP还有{{days}}天到期" -> "VIP expires in {{days}} days"

    private Integer days; // 剩余天数，用于翻译参数
}
```

### 5.3 国际化消息数据示例
```sql
-- 用户状态翻译
INSERT INTO i18n_message VALUES
('user.status', 'zh_CN', '正常', 'user', '用户状态-正常'),
('user.status', 'en_US', 'Normal', 'user', 'User Status-Normal'),
('user.status', 'vi_VN', 'Bình thường', 'user', 'Trạng thái người dùng-Bình thường'),

-- 积分消息翻译 (支持参数)
('user.points.message', 'zh_CN', '您有{{points}}个积分', 'user', '积分消息'),
('user.points.message', 'en_US', 'You have {{points}} points', 'user', 'Points message'),
('user.points.message', 'vi_VN', 'Bạn có {{points}} điểm', 'user', 'Thông báo điểm'),

-- VIP到期消息翻译 (支持参数)
('user.vip.expire', 'zh_CN', 'VIP还有{{days}}天到期', 'user', 'VIP到期提醒'),
('user.vip.expire', 'en_US', 'VIP expires in {{days}} days', 'user', 'VIP expiration reminder'),
('user.vip.expire', 'vi_VN', 'VIP hết hạn sau {{days}} ngày', 'user', 'Nhắc nhở hết hạn VIP');
```

### 5.2 多币种支持
```java
@Service
public class CurrencyService {
    
    private static final Map<String, String> CURRENCY_SYMBOLS = Map.of(
        "CNY", "¥",
        "USD", "$",
        "VND", "₫",
        "EUR", "€"
    );
    
    /**
     * 格式化金额显示
     */
    public String formatAmount(BigDecimal amount, String currency) {
        String symbol = CURRENCY_SYMBOLS.getOrDefault(currency, currency);
        
        // 根据币种设置小数位数
        int scale = "VND".equals(currency) ? 0 : 2;
        
        DecimalFormat formatter = new DecimalFormat();
        formatter.setMaximumFractionDigits(scale);
        formatter.setMinimumFractionDigits(scale);
        
        return symbol + formatter.format(amount);
    }
    
    /**
     * 汇率转换
     */
    public BigDecimal convertCurrency(BigDecimal amount, String fromCurrency, String toCurrency) {
        if (fromCurrency.equals(toCurrency)) {
            return amount;
        }
        
        // 获取汇率 (可以从缓存或第三方API获取)
        BigDecimal exchangeRate = getExchangeRate(fromCurrency, toCurrency);
        
        return amount.multiply(exchangeRate);
    }
}
```

## 6. 重构实施计划

### 6.1 阶段划分
```yaml
第一阶段 (基础重构):
  - 用户认证系统重构 (JustAuth + Sa-Token)
  - 缓存架构升级 (Redis + Caffeine)
  - 国际化支持添加
  - 时间: 2周

第二阶段 (支付重构):
  - 统一支付架构 (pay-java-parent)
  - 支付渠道配置管理
  - 多币种支持
  - 时间: 2周

第三阶段 (AI优化):
  - AI模型通道选择优化
  - 故障转移机制增强
  - 性能监控完善
  - 时间: 1周

第四阶段 (功能完善):
  - 内容审核系统
  - 绘画功能优化
  - 系统监控完善
  - 时间: 1周
```

### 6.2 风险控制
- **数据备份**: 重构前完整备份数据库
- **灰度发布**: 分模块逐步上线
- **回滚方案**: 每个阶段都有完整回滚方案
- **监控告警**: 实时监控系统运行状态
