# Spring AI集成详细说明

## 1. 集成概述

### 1.1 为什么选择Spring AI
Spring AI是Spring生态系统中的官方AI框架，提供了完整的AI集成能力，可以完美覆盖现有系统的AI功能：

```yaml
现有功能覆盖度:
  AI对话: 100% (ChatClient完全覆盖)
  文生图: 100% (ImageClient支持DALL-E等)
  流式响应: 100% (原生支持)
  多模型支持: 100% (支持OpenAI/Claude/Gemini等)
  函数调用: 100% (原生支持AI Agent)

新增功能:
  向量搜索: RAG检索增强生成
  多模态AI: 图像理解、语音处理
  AI Agent: 工具调用和任务执行
  MCP协议: 模型上下文协议支持
```

### 1.2 集成策略
采用渐进式集成，保持现有业务逻辑不变：

```yaml
保持不变:
  - 现有数据表结构 (chat_room, chat_message等)
  - 现有通道选择逻辑 (model, channel_config, site_info)
  - 现有API接口
  - 现有业务流程

Spring AI增强:
  - 使用ChatClient替换原生HTTP调用
  - 使用ImageClient替换Midjourney集成
  - 新增向量搜索和RAG功能
  - 新增AI Agent和工具调用
```

## 2. 依赖配置

### 2.1 Maven依赖
```xml
<!-- Spring AI 核心依赖 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>

<!-- OpenAI 支持 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-openai-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>

<!-- 向量数据库支持 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-redis-store-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>

<!-- 函数调用支持 -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-function-calling-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2.2 配置文件
```yaml
# application.yml
spring:
  ai:
    openai:
      # 配置将从数据库动态读取，这里只是示例
      api-key: ${OPENAI_API_KEY:your-api-key}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      chat:
        enabled: true
        options:
          model: gpt-3.5-turbo
          temperature: 0.7
          max-tokens: 2048
      image:
        enabled: true
        options:
          model: dall-e-3
          quality: hd
          size: 1024x1024
    vectorstore:
      redis:
        uri: redis://localhost:6379
        index-name: ai-vectors
        prefix: "ai:vector:"
```

## 3. 核心实现

### 3.1 Spring AI聊天服务
```java
@Service
@Slf4j
public class SpringAIChatService implements ChatService {
    
    @Autowired
    private ChannelSelectorService channelSelectorService; // 保留现有通道选择
    
    @Autowired
    private ChatMessageService chatMessageService; // 保留现有消息存储
    
    /**
     * AI对话 - 使用Spring AI ChatClient
     */
    @Override
    public ChatResponse chat(ChatRequest request) {
        try {
            // 1. 选择最优通道 (保留现有逻辑)
            ChannelInfo channelInfo = channelSelectorService.selectOptimalChannel(
                request.getModelId(), request);
            
            // 2. 构建Spring AI ChatClient
            ChatClient chatClient = buildChatClient(channelInfo);
            
            // 3. 构建对话上下文 (保留现有逻辑)
            List<Message> messageHistory = buildMessageHistory(request.getChatRoomId());
            
            // 4. 发送对话请求
            ChatResponse response = chatClient.prompt()
                .system(request.getSystemPrompt())
                .user(request.getContent())
                .messages(messageHistory)
                .call()
                .chatResponse();
            
            // 5. 保存对话记录 (保留现有逻辑)
            saveChatMessage(request, response, channelInfo);
            
            // 6. 更新通道统计 (保留现有逻辑)
            updateChannelStats(channelInfo, true, response.getMetadata().getUsage());
            
            return response;
            
        } catch (Exception e) {
            log.error("AI对话失败: {}", request, e);
            // 故障转移 (保留现有逻辑)
            return handleChatFailover(request, e);
        }
    }
    
    /**
     * 流式对话 - Spring AI原生支持
     */
    @Override
    public Flux<ChatResponse> streamChat(ChatRequest request) {
        ChannelInfo channelInfo = channelSelectorService.selectOptimalChannel(
            request.getModelId(), request);
        
        ChatClient chatClient = buildChatClient(channelInfo);
        
        return chatClient.prompt()
            .system(request.getSystemPrompt())
            .user(request.getContent())
            .stream()
            .chatResponse()
            .doOnNext(response -> {
                // 实时保存流式响应 (保留现有逻辑)
                saveStreamResponse(request, response);
            })
            .doOnError(error -> {
                // 错误处理 (保留现有逻辑)
                handleStreamError(request, error);
            });
    }
    
    /**
     * 构建ChatClient - 基于通道信息
     */
    private ChatClient buildChatClient(ChannelInfo channelInfo) {
        SiteInfo siteInfo = channelInfo.getSiteInfo();
        
        // 构建OpenAI配置
        OpenAiChatModel chatModel = OpenAiChatModel.builder()
            .apiKey(siteInfo.getApiKey())
            .baseUrl(siteInfo.getSiteUrl())
            .modelName(channelInfo.getModel().getModelId())
            .temperature(0.7f)
            .maxTokens(channelInfo.getChannelConfig().getMaxTokensPerRequest())
            .build();
        
        return ChatClient.builder(chatModel)
            .defaultSystem("你是一个智能助手")
            .build();
    }
    
    /**
     * 构建消息历史 - 保留现有逻辑
     */
    private List<Message> buildMessageHistory(Long chatRoomId) {
        List<ChatMessage> history = chatMessageService.getRecentMessages(chatRoomId, 10);
        
        return history.stream()
            .map(msg -> {
                if (msg.getMessageType() == 1) { // 用户消息
                    return new UserMessage(msg.getContent());
                } else { // AI回复
                    return new AssistantMessage(msg.getContent());
                }
            })
            .collect(Collectors.toList());
    }
}
```

### 3.2 Spring AI图像服务
```java
@Service
@Slf4j
public class SpringAIImageService {
    
    @Autowired
    private ImageClient imageClient;
    
    @Autowired
    private SensitiveWordFilter sensitiveWordFilter; // 保留现有敏感词过滤
    
    @Autowired
    private MidjourneyTaskService midjourneyTaskService; // 保留现有任务管理
    
    /**
     * 文生图 - 使用Spring AI ImageClient
     */
    public ImageResponse generateImage(ImageGenerationRequest request) {
        try {
            // 1. 敏感词检测 (保留现有逻辑)
            String filteredPrompt = sensitiveWordFilter.filter(request.getPrompt());
            if (!filteredPrompt.equals(request.getPrompt())) {
                throw new ServiceException("提示词包含敏感内容");
            }
            
            // 2. 创建任务记录 (保留现有逻辑)
            MidjourneyTask task = createImageTask(request, filteredPrompt);
            
            // 3. 使用Spring AI生成图片
            ImageResponse response = imageClient.call(
                new ImagePrompt(filteredPrompt,
                    ImageOptionsBuilder.builder()
                        .withModel("dall-e-3")
                        .withWidth(1024)
                        .withHeight(1024)
                        .withQuality("hd")
                        .withResponseFormat("url")
                        .build()));
            
            // 4. 更新任务状态 (保留现有逻辑)
            updateImageTask(task, response);
            
            // 5. 扣减用户绘画次数 (保留现有逻辑)
            deductDrawPoints(request.getUserId());
            
            return response;
            
        } catch (Exception e) {
            log.error("图像生成失败: {}", request, e);
            throw new ServiceException("图像生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 图像理解 - Spring AI新增功能
     */
    public String analyzeImage(String imageUrl, String question) {
        ChatClient chatClient = ChatClient.builder(chatModel)
            .build();
            
        return chatClient.prompt()
            .user(userSpec -> userSpec
                .text(question)
                .media(MimeTypeUtils.IMAGE_JPEG, imageUrl))
            .call()
            .content();
    }
}
```

### 3.3 向量搜索和RAG
```java
@Service
@Slf4j
public class SpringAIVectorService {
    
    @Autowired
    private EmbeddingClient embeddingClient;
    
    @Autowired
    private VectorStore vectorStore;
    
    @Autowired
    private ChatClient chatClient;
    
    /**
     * 文档向量化存储
     */
    public void storeDocuments(List<String> documents, String category) {
        List<Document> docs = documents.stream()
            .map(content -> new Document(content, 
                Map.of("category", category, "timestamp", System.currentTimeMillis())))
            .collect(Collectors.toList());
        
        // 生成向量并存储
        vectorStore.add(docs);
        
        log.info("存储文档向量: count={}, category={}", documents.size(), category);
    }
    
    /**
     * RAG检索增强生成
     */
    public String ragChat(String question, String category) {
        // 1. 向量检索相关文档
        SearchRequest searchRequest = SearchRequest.query(question)
            .withTopK(5)
            .withSimilarityThreshold(0.7);
            
        if (StringUtils.isNotBlank(category)) {
            searchRequest = searchRequest.withFilterExpression("category == '" + category + "'");
        }
        
        List<Document> relevantDocs = vectorStore.similaritySearch(searchRequest);
        
        if (relevantDocs.isEmpty()) {
            return chatClient.prompt()
                .user(question)
                .call()
                .content();
        }
        
        // 2. 构建上下文
        String context = relevantDocs.stream()
            .map(Document::getContent)
            .collect(Collectors.joining("\n\n"));
        
        // 3. 生成回答
        String systemPrompt = """
            你是一个智能助手。请基于以下上下文信息回答用户的问题。
            如果上下文中没有相关信息，请说明并给出你的一般性建议。
            
            上下文信息：
            %s
            """.formatted(context);
        
        return chatClient.prompt()
            .system(systemPrompt)
            .user(question)
            .call()
            .content();
    }
    
    /**
     * 语义搜索
     */
    public List<Document> semanticSearch(String query, int topK) {
        return vectorStore.similaritySearch(
            SearchRequest.query(query).withTopK(topK));
    }
}
```

### 3.4 AI Agent实现
```java
@Component
@Slf4j
public class ChatAgentService {
    
    @Autowired
    private ChatClient chatClient;
    
    @Autowired
    private WeatherService weatherService;
    
    @Autowired
    private EmailService emailService;
    
    @Autowired
    private UserService userService;
    
    /**
     * 带工具调用的AI对话
     */
    public String chatWithAgent(String userMessage, Integer userId) {
        return chatClient.prompt()
            .user(userMessage)
            .function("getCurrentWeather", "获取指定城市的当前天气", this::getCurrentWeather)
            .function("sendEmail", "发送邮件", this::sendEmail)
            .function("getUserInfo", "获取用户信息", () -> getUserInfo(userId))
            .function("searchKnowledge", "搜索知识库", this::searchKnowledge)
            .call()
            .content();
    }
    
    @Function("获取天气信息")
    public String getCurrentWeather(String location) {
        try {
            WeatherInfo weather = weatherService.getWeather(location);
            return String.format("城市：%s，天气：%s，温度：%s°C，湿度：%s%%", 
                location, weather.getDescription(), weather.getTemperature(), weather.getHumidity());
        } catch (Exception e) {
            return "抱歉，无法获取" + location + "的天气信息";
        }
    }
    
    @Function("发送邮件")
    public String sendEmail(String to, String subject, String content) {
        try {
            emailService.send(to, subject, content);
            return "邮件已成功发送到 " + to;
        } catch (Exception e) {
            return "邮件发送失败：" + e.getMessage();
        }
    }
    
    @Function("获取用户信息")
    public String getUserInfo(Integer userId) {
        try {
            UserInfoVO userInfo = userService.getUserInfo(userId);
            return String.format("用户：%s，积分：%d，VIP状态：%s", 
                userInfo.getName(), userInfo.getPoints(), 
                userInfo.getVipEndTime() != null ? "是" : "否");
        } catch (Exception e) {
            return "无法获取用户信息";
        }
    }
    
    @Function("搜索知识库")
    public String searchKnowledge(String query) {
        try {
            List<Document> docs = vectorService.semanticSearch(query, 3);
            if (docs.isEmpty()) {
                return "知识库中没有找到相关信息";
            }
            
            return docs.stream()
                .map(Document::getContent)
                .collect(Collectors.joining("\n\n"));
        } catch (Exception e) {
            return "知识库搜索失败";
        }
    }
}
```

## 4. 配置管理

### 4.1 动态配置
```java
@Service
public class SpringAIConfigService {
    
    @Autowired
    private SiteInfoService siteInfoService;
    
    /**
     * 动态构建ChatClient
     */
    public ChatClient buildChatClient(String channelCode) {
        SiteInfo siteInfo = siteInfoService.getByChannelCode(channelCode);
        
        OpenAiChatModel chatModel = OpenAiChatModel.builder()
            .apiKey(siteInfo.getApiKey())
            .baseUrl(siteInfo.getSiteUrl())
            .build();
        
        return ChatClient.builder(chatModel).build();
    }
    
    /**
     * 动态构建ImageClient
     */
    public ImageClient buildImageClient(String channelCode) {
        SiteInfo siteInfo = siteInfoService.getByChannelCode(channelCode);
        
        return OpenAiImageClient.builder()
            .apiKey(siteInfo.getApiKey())
            .baseUrl(siteInfo.getSiteUrl())
            .build();
    }
}
```

## 5. 集成优势总结

### 5.1 功能增强
- ✅ **AI对话**: 更稳定的ChatClient，支持流式响应
- ✅ **文生图**: 支持DALL-E 3，图像质量更高
- ✅ **多模态**: 新增图像理解、语音处理能力
- ✅ **向量搜索**: RAG检索增强生成，知识库问答
- ✅ **AI Agent**: 工具调用，任务执行能力

### 5.2 技术优势
- ✅ **标准化**: Spring生态官方AI框架
- ✅ **性能优化**: 连接池、缓存、重试机制
- ✅ **扩展性**: 轻松集成新的AI模型和功能
- ✅ **维护性**: 官方维护，减少自研代码

### 5.3 业务连续性
- ✅ **零中断**: 渐进式迁移，不影响现有业务
- ✅ **数据兼容**: 保留所有现有数据表和逻辑
- ✅ **接口兼容**: API接口保持不变
- ✅ **配置兼容**: 保留现有通道选择机制

通过Spring AI集成，我们可以在保持现有业务逻辑不变的前提下，大幅提升AI功能的稳定性和扩展性，同时为未来的AI Agent和MCP协议集成奠定基础。
