# 重构原始需求清单（最新版本）

## 一、用户功能需求

### 1. 用户信息表设计
**需求描述**: 创建用户信息表，用于存储用户各种维度的信息，主键ID是唯一用户标识
**实现方案**: 
- 基于现有user_base_info表结构优化设计
- 支持多币种/多语种/多时区字段
- 添加用户来源渠道标识
- 支持软删除机制
- **保留分佣功能**: 保留commission_id、parent_id等分佣相关字段

### 2. JustAuth第三方登录集成
**需求描述**: 使用 JustAuth(https://gitee.com/justauth) 开源组件，实现统一的第三方鉴权登录
**支持登录方式**:
- 微信开放平台(服务号+订阅号)
- 账号密码登录
- 手机号(国内/国际)加验证码
- 邮箱加验证码登录
- 谷歌账号(Google OAuth)
- Facebook账号(Facebook OAuth)
- 浏览器指纹识别登录
**实现方案**:
- 集成JustAuth 1.16.x组件，提供统一的OAuth认证能力
- 创建user_joint_login表存储第三方授权信息和令牌
- 创建user_joint_config表存储各平台的接入配置(client_id/secret等)
- 支持登录方式的动态开关和排序控制
- 与Sa-Token集成，统一会话管理

### 3. 账号注销功能
**需求描述**: 支持账号注销功能，注销后再次登录时能找到历史用户信息
**实现方案**:
- 使用软删除机制(deleted字段)
- 注销后保留历史数据
- 再次登录时跳过新用户赠品逻辑
- 跨业务场景注销限制检查

### 4. 用户登录历史记录
**需求描述**: 记录用户登录历史，追加登录来源和客户端信息
**实现方案**:
- 基于sys_logininfor表增强为user_login_history
- 记录使用了哪个账号或联合账号登录
- 记录客户端信息(浏览器、操作系统、设备类型)
- 记录IP地址和登录地点
- 记录登录方式(微信/手机/邮箱等)
- 支持登录行为分析和安全监控

## 二、支付功能需求

### 5. pay-java-parent统一支付框架
**需求描述**: 基于 pay-java-parent(https://gitee.com/egzosn/pay-java-parent) 组件，实现支付功能的快速接入
**支持支付方式**:
- 支付宝: 使用com.egzosn.pay-java-ali包的AliPayServiceImpl官方实现
- 微信支付: 使用com.egzosn.pay-java-wx包的WxPayServiceImpl官方实现，多商户按biz_scene区分
- 越南momo支付(v2/v3版本): 保持现有实现
- 越南sepay: 保持现有实现
**实现方案**:
- 集成pay-java-parent 2.13.x组件
- 创建统一支付订单表(pay_order)
- 创建支付渠道配置表(pay_channel_config)
- 支持多商户配置，按biz_scene区分
- **多币种支持**: 支付订单表包含product_currency和pay_currency两个字段，支持汇率转换

### 6. 订单表合并
**需求描述**: 将AlOrders.java和Orders.java等多个支付表合并到统一支付订单表
**实现方案**:
- 合并al_orders + wx_pay_order + se_pay_order + qr_payment → pay_order
- 支持区分支付渠道和支付方式
- 支持biz_scene标识(tarot/zns/chatoi)
- 提供数据迁移方案

### 7. 充值记录业务场景扩展
**需求描述**: recharge_log优化后扩展业务场景字段
**实现方案**:
- 添加biz_scene字段区分tarot/zns/chatoi
- 支持不同业务场景的充值统计
- 关联对应的支付订单
- 支持充值来源分析

## 三、AI功能需求

### 8. AI表按能力类型合并
**需求描述**: AI相关的xxx_message、xxx_room、xxx_agent表按能力类型合并
**实现方案**:
- ai_message表合并chat_message + draw_message + write_message
- ai_room表合并chat_room + draw_room
- ai_agent表合并chat_agent + write_agent
- 通过ability_type字段区分CHAT/DRAW/WRITE/MUSIC能力类型
- 保留各自特有字段，通用字段统一

### 9. AI提示词表重构
**需求描述**: prompter_info按AI能力类型划分，更名为ai_prompter，增加来源字段
**实现方案**:
- 表名改为ai_prompter
- 增加ability_type字段区分CHAT/DRAW/WRITE/MUSIC能力类型
- 增加source字段区分导入(IMPORT)和系统内部管理(SYSTEM)
- 保持原有industry、key_word等字段
- 迁移到framework.ai.prompter模块

### 10. AI分类信息表优化
**需求描述**: category_info若是AI相关改为ai_category，考虑与write_category合并
**实现方案**:
- 分析category_info源码确定是否AI相关
- 如果是AI相关改为ai_category
- 如果write_category可以合并则合并表
- 使用ability_type字段区分不同AI能力类型
- 统一AI相关分类管理

### 11. AI模型通道功能模块化
**需求描述**: site_info、channel_config、model三张表用于AI大模型通道最优费率选择
**表重构对应关系**:
- site_info → ai_router_config (路由配置表)
- channel_config → ai_router (路由信息表)
- model → ai_model (模型信息表)
**实现方案**:
- 将此功能放入framework.ai.channel子模块
- 封闭并工具化此功能，提供统一的通道选择接口
- 支持基于成本、性能、可用性的最优通道选择
- 支持故障转移和负载均衡

### 12. AI敏感词功能归类
**需求描述**: sensitive_word表用于AI内容过滤，归入AI部分
**实现方案**:
- 表名改为ai_sensitive_word，归入framework.ai.filter
- 提供统一的敏感词过滤接口
- 支持不同ability_type的敏感词管理
- 支持动态敏感词管理和更新

### 13. Spring AI集成
**需求描述**: AI对话、文生图等功能使用Spring AI + 不支持的功能保持原有或定制化实现
**实现方案**:
- 集成Spring AI 1.0.x框架
- 保持现有业务逻辑不变
- 考虑AI Agent、MCP等工具组件扩展
- 分析现有业务覆盖可行性

## 四、系统功能需求

### 14. 国际化工具化支持
**需求描述**: 国际化支持工具化，方便源码直接调用获取翻译结果，支持按字段值内容翻译
**实现方案**:
- 提供I18nUtil工具类，支持代码直接调用
- 支持translate(key, language, params)方法
- 支持按值翻译：直接用值查询翻译，有就翻译，没有就原文返回
- 支持批量翻译和缓存机制
- 支持动态参数替换如{{count}}个数
- 集成到framework.i18n模块
- 使用注解方式实现字段翻译

### 15. 缓存优化
**需求描述**: 建立Redis和本地缓存机制优化数据查询效率
**实现方案**:
- Redis + Caffeine多级缓存
- 提供缓存清空和刷新接口
- 缓存预热机制
- 缓存统计和监控

### 16. 广告位管理功能
**需求描述**: promotion_info表增强为广告位管理，添加业务场景和类型字段
**实现方案**:
- 表名改为adv_info
- 添加biz_scene字段(tarot/zns/chatoi)
- 添加adv_type字段(移动端首页海报/弹窗广告/横幅广告等)
- 添加detail_url字段作为广告详情链接
- poster_url改为adv_image_url(广告图片)
- 支持多业务场景的广告位管理

### 17. 分享功能分析
**需求描述**: 分析share_info表的已有编码实现，确定用途
**实现方案**:
- 分析share_info源码实现确定业务用途
- 如果是分享链接管理，增强分享统计功能
- 如果是内容分享，支持多种分享类型
- 添加biz_scene字段支持不同模块的分享

### 18. 提现功能模块化
**需求描述**: 基于transfer_info表(提现申请)和transaction表(银行交易记录)实现提现功能模块化
**表重构对应关系**:
- transfer_info → withdraw_application (提现申请表)
- transaction → withdraw_transaction (提现交易记录表，关联银行交易流水)
**实现方案**:
- 创建framework.withdraw模块，独立提现功能
- withdraw_application存储提现申请信息
- withdraw_transaction存储银行交易流水和处理状态
- 支持提现申请、审核、批量处理等功能

### 19. 塔罗国际化整合
**需求描述**: tarot_i18n并入国际化部分，不需要专门的表
**实现方案**:
- 将tarot_i18n表数据迁移到统一的国际化表
- 删除tarot_i18n表
- 塔罗相关翻译通过统一的国际化系统管理
- 支持塔罗牌名称、含义等内容的多语言

## 五、技术规范需求

### 20. 命名规范调整
**需求描述**: 统一命名规范，使用合理缩写
**实现方案**:
- advertisement → adv (所有相关命名)
- business_scene → biz_scene (所有相关表及业务逻辑)
- Payment → Pay (所有相关命名)

### 21. 表设计字段统一
**需求描述**: 统一表设计的创建和更新字段
**实现方案**:
- 统一使用create_by/create_time/update_by/update_time字段
- 字段定义：create_by varchar(50) default '', create_time timestamp default current_timestamp()
- update_by varchar(50) default '', update_time timestamp default current_timestamp() on update current_timestamp()

### 22. POINTS概念修正
**需求描述**: POINTS是塔罗币，不是积分，修订中文描述，保留英文单词
**实现方案**:
- 用户积分日志表中的POINTS类型中文描述改为"塔罗币"
- 签到奖励类型POINTS中文描述改为"塔罗币"
- 所有文档和注释中的POINTS中文描述修正为"塔罗币"
- 保留POINTS英文单词，只修改中文描述
- 区分积分(points)和塔罗币(POINTS)两个概念

### 23. 业务场景术语修正
**需求描述**: zns是智能社，不是紫微斗数，修订所有相关文档描述
**实现方案**:
- 统一zns术语为"智能社"
- 更新所有文档中的相关描述
- 确保业务场景标识的准确性

### 24. 编码模式调整
**需求描述**: 非db下的service和impl模式统一改为worker模式
**实现方案**:
- xxxService改为xxxWorker
- 去掉实现类，直接在Worker中处理
- 简化编码复杂度
- 保持依赖注入机制

### 25. 包路径重构
**需求描述**: 保持com.hncboy.chatgpt包路径，按业务场景划分
**实现方案**:
- 创建新的包结构
- 按业务场景划分(tarot/zns/chatoi)
- 统一使用Worker模式
- 保持现有技术栈和开发规范

## 六、数据迁移需求

### 26. 历史数据迁移
**需求描述**: 提供完整的历史数据迁移方案
**实现方案**:
- 分析现有数据结构
- 设计迁移脚本
- 数据完整性校验
- 回滚机制设计

### 27. 接口兼容性
**需求描述**: 提供历史接口和新接口的对应关系
**实现方案**:
- 梳理现有接口清单
- 设计新接口结构
- 提供接口映射关系
- 制定接口迁移计划

### 28. 编码模式统一
**需求描述**: 非db下的Service一律改为Worker，简化实现
**实现方案**:
- 所有非db包下的XxxService改为XxxWorker
- 去掉Service接口和Impl实现类的复杂结构
- 直接在Worker中实现业务逻辑
- 保持依赖注入和Spring管理机制
- 简化代码结构，提高开发效率

### 29. 文档一致性保证
**需求描述**: 确保所有重构文档内容严格一致，无重复错乱现象
**实现方案**:
- 数据库设计文件中的表设计必须与需求说明完全对应
- 重构目录结构设计文档中不能有重复内容
- 完整重构需求设计说明书中的包结构树必须与重构目录结构设计文档保持一致
- 所有文档都要按最新需求更新，无陈旧内容
- 严格验证并修订所有文档的一致性

### 30. 业务能力说明完善
**需求描述**: 为所有表添加明确的业务能力说明，让人理解表的用途
**实现方案**:
- 重点分析已有编码实现
- 为每个表添加详细的业务用途说明
- 说明表在系统中的作用和使用场景
- 提供表之间的关联关系说明
- 更新数据库设计文档
