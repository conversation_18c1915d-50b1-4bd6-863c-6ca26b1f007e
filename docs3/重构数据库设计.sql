-- =====================================================
-- 超级智能社重构数据库设计（最新版本）
-- 基于原有61张表深入分析，按实际业务需求重新梳理表结构
--
-- 命名规范：
-- - advertisement → adv (广告相关)
-- - business_scene → biz_scene (业务场景)
-- - 统一字段：create_by/create_time/update_by/update_time
--
-- 表合并策略(按需合并，不强制压缩):
-- 支付订单: al_orders + wx_pay_order + se_pay_order + qr_payment → pay_order
-- 用户信息: users + wx_user_info → user_joint_login
-- 签到记录: user_check_in_record + app_sign → user_check_in_record
-- AI消息表: chat_message + draw_message + write_message → ai_message (按能力类型区分)
-- AI房间表: chat_room + draw_room → ai_room (按能力类型区分)
-- AI智能体: chat_agent + write_agent → ai_agent (按能力类型区分)
-- AI分类信息: category_info + write_category → ai_category (统一AI分类管理)
-- AI通道重构: site_info + channel_config + model → channel_router_config + channel_router + channel_model
--
-- 保留独立表:
-- - 分佣相关: commission_identity (参与身份表)
-- - 塔罗牌: tarot_* 系列表 (业务独立性强)
-- - 系统管理: 必要的系统功能表
-- =====================================================

-- =====================================================
-- 1. 用户相关表 (6张表)
-- =====================================================

-- 用户基础信息表 (保留原user_base_info结构，增强多语言支持，保留分佣功能)
CREATE TABLE `user_base_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `commission_id` int(11) DEFAULT NULL COMMENT '分佣身份ID(关联commission_identity.id)',
    `account` varchar(64) NOT NULL COMMENT '账号(手机号或其他账号)',
    `user_type` varchar(50) DEFAULT 'zns' COMMENT '用户类型 zns:智能社 tarot:塔罗 chatoi:对话',
    `name` varchar(15) DEFAULT NULL COMMENT '用户名',
    `nick_name` varchar(50) DEFAULT NULL COMMENT '昵称',
    `parent_id` int(11) DEFAULT NULL COMMENT '上级用户id（邀请人id，用于分佣）',
    `password` varchar(128) DEFAULT NULL COMMENT '密码',
    `email` varchar(128) DEFAULT NULL COMMENT '邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `avatar_url` varchar(300) DEFAULT NULL COMMENT '头像URL',
    `vip_end_time` datetime DEFAULT NULL COMMENT 'VIP到期时间',
    `points` int(11) DEFAULT 0 COMMENT '积分(区别于POINTS塔罗币)',
    `tarot_coins` int(11) DEFAULT 0 COMMENT 'POINTS塔罗币(用于塔罗解读)',
    `draw_num` int(11) DEFAULT 0 COMMENT '绘画次数',
    `music_num` int(11) DEFAULT 0 COMMENT '音乐次数',
    `write_num` int(11) DEFAULT 0 COMMENT '写作次数',
    `use_num` int(11) DEFAULT 0 COMMENT '剩余使用次数(充值)',
    `free_num` int(11) DEFAULT 0 COMMENT '免费使用次数(赠送)',
    `daily_free_time` int(11) DEFAULT NULL COMMENT '每日免费次数',
    `login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
    `first_status` varchar(1) DEFAULT '1' COMMENT '是否首次登录 1:是 0:否',
    `ip` varchar(28) DEFAULT NULL COMMENT 'IP地址',
    `address` varchar(255) DEFAULT NULL COMMENT '地址',
    `status` tinyint(2) DEFAULT 0 COMMENT '状态 0:正常 1:禁用 2:注销',
    `language` varchar(10) DEFAULT 'zh_CN' COMMENT '用户语言偏好',
    `currency` varchar(10) DEFAULT 'CNY' COMMENT '用户币种偏好',
    `timezone` varchar(50) DEFAULT 'Asia/Shanghai' COMMENT '用户时区',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_account_type` (`account`, `user_type`),
    KEY `idx_user_type` (`user_type`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_commission_id` (`commission_id`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 用户联合登录表 (合并users表和wx_user_info表，保留推荐关系)
CREATE TABLE `user_joint_login` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID(关联user_base_info.id)',
    `login_type` varchar(20) NOT NULL COMMENT '登录类型 WECHAT:微信 GOOGLE:谷歌 FACEBOOK:脸书 PHONE:手机 EMAIL:邮箱 FINGERPRINT:指纹',
    `third_party_id` varchar(100) NOT NULL COMMENT '第三方唯一标识',
    `third_party_username` varchar(100) DEFAULT NULL COMMENT '第三方用户名',
    `third_party_email` varchar(100) DEFAULT NULL COMMENT '第三方邮箱',
    `third_party_avatar` varchar(300) DEFAULT NULL COMMENT '第三方头像',
    `access_token` varchar(500) DEFAULT NULL COMMENT '访问令牌',
    `refresh_token` varchar(500) DEFAULT NULL COMMENT '刷新令牌',
    `expires_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
    `union_id` varchar(100) DEFAULT NULL COMMENT '联合ID(微信unionId等)',
    `app_id` varchar(100) DEFAULT NULL COMMENT '应用ID',
    `referrer_id` bigint(20) DEFAULT NULL COMMENT '推荐人ID(从users表迁移)',
    `lucky_coins` bigint(20) DEFAULT NULL COMMENT '幸运币(从users表迁移)',
    `extra_data` text COMMENT '额外数据(JSON格式)',
    `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
    `login_count` int(11) DEFAULT 0 COMMENT '登录次数',
    `status` tinyint(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_login_type_third_id` (`login_type`, `third_party_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_login_type` (`login_type`),
    KEY `idx_union_id` (`union_id`),
    KEY `idx_referrer_id` (`referrer_id`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户联合登录表';

-- 用户签到记录表 (合并user_check_in_record和app_sign)
CREATE TABLE `user_check_in_record` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `check_in_date` varchar(8) NOT NULL COMMENT '签到日期 yyyyMMDD',
    `week` varchar(20) DEFAULT NULL COMMENT '签到星期',
    `type` varchar(32) NOT NULL COMMENT '签到类型 TAROT:塔罗牌 ZNS:智能社 CHATOI:对话',
    `is_make_up` varchar(1) DEFAULT '0' COMMENT '是否补签 0:否 1:是',
    `awarded` int(11) NOT NULL DEFAULT 0 COMMENT '奖励数量',
    `award_type` varchar(20) DEFAULT 'POINTS' COMMENT '奖励类型 POINTS:积分 COINS:代币',
    `continuous_days` int(11) DEFAULT 1 COMMENT '连续签到天数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_user_date_type` (`user_id`, `check_in_date`, `type`),
    KEY `idx_check_in_date` (`check_in_date`),
    KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户签到记录表';

-- 用户积分日志表 (保留原结构，修正POINTS中文描述)
CREATE TABLE `user_points_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `rel_order` varchar(255) NOT NULL COMMENT '关联订单',
    `points` int(11) DEFAULT NULL COMMENT '变动数量',
    `points_type` varchar(20) DEFAULT NULL COMMENT '变动类型 EARN:获得 CONSUME:消费',
    `currency_type` varchar(20) DEFAULT 'POINTS' COMMENT '币种类型 POINTS:塔罗币 COINS:积分',
    `business_type` varchar(20) DEFAULT NULL COMMENT '业务类型 SIGN:签到 PAY:支付 REFUND:退款 TAROT:塔罗解读',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_points_type` (`points_type`),
    KEY `idx_currency_type` (`currency_type`),
    KEY `idx_business_type` (`business_type`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分日志表';

-- 用户配置表 (保留原结构，增强配置类型)
CREATE TABLE `user_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `config_type` varchar(50) NOT NULL COMMENT '配置类型',
    `config_key` varchar(100) NOT NULL COMMENT '配置键',
    `config_value` text COMMENT '配置值',
    `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_user_type_key` (`user_id`, `config_type`, `config_key`),
    KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配置表';

-- 分佣身份表 (保留原commission_identity结构)
CREATE TABLE `commission_identity` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `type` varchar(20) DEFAULT NULL COMMENT '类型',
    `code` varchar(64) DEFAULT NULL COMMENT '编号',
    `status` varchar(10) DEFAULT NULL COMMENT '状态',
    `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    `end_time` datetime NOT NULL COMMENT '过期时间',
    `percentage` int(5) DEFAULT NULL COMMENT '分佣比例',
    `name` varchar(100) DEFAULT NULL COMMENT '姓名',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `wx_mp_url` varchar(255) DEFAULT NULL COMMENT '微信公众号链接',
    `user_info_id` int(11) NOT NULL COMMENT '用户信息ID',
    `open_id` varchar(64) NOT NULL COMMENT 'openId',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_info_id` (`user_info_id`),
    KEY `idx_open_id` (`open_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣身份表';

-- 第三方登录配置表 (用户相关，JustAuth配置)
CREATE TABLE `user_joint_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `login_type` varchar(20) NOT NULL COMMENT '登录类型 WECHAT GOOGLE FACEBOOK',
    `config_name` varchar(50) NOT NULL COMMENT '配置名称',
    `client_id` varchar(100) NOT NULL COMMENT '客户端ID',
    `client_secret` varchar(255) NOT NULL COMMENT '客户端密钥',
    `redirect_uri` varchar(255) NOT NULL COMMENT '回调地址',
    `scope` varchar(255) DEFAULT NULL COMMENT '授权范围',
    `config_params` text COMMENT '其他配置参数(JSON)',
    `status` tinyint(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_login_type` (`login_type`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方登录配置表';

-- =====================================================
-- 2. 支付相关表 (3张表)
-- =====================================================

-- 统一支付订单表 (合并al_orders, wx_pay_order, se_pay_order, qr_payment，支持多币种)
CREATE TABLE `pay_order` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_no` varchar(64) NOT NULL COMMENT '商户订单号',
    `third_party_order_no` varchar(64) DEFAULT NULL COMMENT '第三方订单号',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `product_id` bigint(20) DEFAULT NULL COMMENT '商品ID',
    `product_type` varchar(20) DEFAULT NULL COMMENT '商品类型',
    `biz_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `pay_channel` varchar(20) NOT NULL COMMENT '支付渠道 ALIPAY:支付宝 WECHAT:微信 MOMO:越南momo SEPAY:越南sepay',
    `pay_method` varchar(20) DEFAULT NULL COMMENT '支付方式 QR:扫码 H5:网页 APP:应用',
    `product_currency` varchar(10) DEFAULT 'CNY' COMMENT '产品币种(商品定价币种)',
    `pay_currency` varchar(10) DEFAULT 'CNY' COMMENT '支付币种(实际支付币种)',
    `product_amount` decimal(15,2) NOT NULL COMMENT '产品金额(产品币种)',
    `pay_amount` decimal(15,2) NOT NULL COMMENT '支付金额(支付币种)',
    `exchange_rate` decimal(10,4) DEFAULT 1.0000 COMMENT '汇率(产品币种到支付币种)',
    `paid_amount` decimal(15,2) DEFAULT NULL COMMENT '实际支付金额',
    `title` varchar(255) DEFAULT NULL COMMENT '订单标题',
    `description` varchar(500) DEFAULT NULL COMMENT '订单描述',
    `status` varchar(20) DEFAULT 'PENDING' COMMENT '订单状态 PENDING:待支付 PAID:已支付 CANCELLED:已取消 EXPIRED:已过期 REFUNDED:已退款',
    `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
    `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
    `notify_url` varchar(255) DEFAULT NULL COMMENT '异步通知地址',
    `return_url` varchar(255) DEFAULT NULL COMMENT '同步跳转地址',
    `pay_params` text COMMENT '支付参数(JSON)',
    `pay_result` text COMMENT '支付结果(JSON)',
    `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_order_no` (`order_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_third_party_order_no` (`third_party_order_no`),
    KEY `idx_biz_scene` (`biz_scene`),
    KEY `idx_pay_channel` (`pay_channel`),
    KEY `idx_product_currency` (`product_currency`),
    KEY `idx_pay_currency` (`pay_currency`),
    KEY `idx_status` (`status`),
    KEY `idx_pay_time` (`pay_time`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一支付订单表';

-- 支付渠道配置表 (新增，支持多商户配置)
CREATE TABLE `pay_channel_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `channel_code` varchar(20) NOT NULL COMMENT '渠道编码 ALIPAY WECHAT MOMO SEPAY',
    `channel_name` varchar(50) NOT NULL COMMENT '渠道名称',
    `business_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot zns chatoi',
    `merchant_id` varchar(100) NOT NULL COMMENT '商户ID',
    `app_id` varchar(100) DEFAULT NULL COMMENT '应用ID',
    `private_key` text COMMENT '私钥',
    `public_key` text COMMENT '公钥',
    `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
    `secret_key` varchar(255) DEFAULT NULL COMMENT '密钥',
    `gateway_url` varchar(255) DEFAULT NULL COMMENT '网关地址',
    `notify_url` varchar(255) DEFAULT NULL COMMENT '异步通知地址',
    `return_url` varchar(255) DEFAULT NULL COMMENT '同步跳转地址',
    `config_params` text COMMENT '其他配置参数(JSON)',
    `status` tinyint(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_channel_scene` (`channel_code`, `business_scene`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付渠道配置表';

-- 第三方登录配置表 (新增，支持JustAuth配置)
CREATE TABLE `user_joint_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `login_type` varchar(20) NOT NULL COMMENT '登录类型 WECHAT GOOGLE FACEBOOK',
    `config_name` varchar(50) NOT NULL COMMENT '配置名称',
    `client_id` varchar(100) NOT NULL COMMENT '客户端ID',
    `client_secret` varchar(255) NOT NULL COMMENT '客户端密钥',
    `redirect_uri` varchar(255) NOT NULL COMMENT '回调地址',
    `scope` varchar(255) DEFAULT NULL COMMENT '授权范围',
    `config_params` text COMMENT '其他配置参数(JSON)',
    `status` tinyint(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_login_type` (`login_type`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方登录配置表';

-- =====================================================
-- 3. AI相关表 (7张表) - 按能力类型合并优化
-- =====================================================

-- =====================================================
-- 4. AI相关表 (6张表) - 按能力类型合并优化
-- =====================================================

-- AI统一消息表 (合并chat_message + draw_message + write_message)
CREATE TABLE `ai_message` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `parent_msg_id` bigint(20) DEFAULT NULL COMMENT '父消息ID',
    `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
    `room_id` bigint(20) DEFAULT NULL COMMENT '房间ID',
    `ability_type` varchar(20) NOT NULL COMMENT '能力类型 CHAT:对话 DRAW:绘画 WRITE:写作 MUSIC:音乐',
    `message_type` int(11) NOT NULL COMMENT '消息类型 1:请求 2:回复',
    `content` text NOT NULL COMMENT '消息内容',
    `prompt` varchar(2000) DEFAULT NULL COMMENT '提示词(绘画专用)',
    `task_id` varchar(64) DEFAULT NULL COMMENT '任务ID(绘画专用)',
    `action` varchar(20) DEFAULT NULL COMMENT '事件(绘画专用)',
    `inputs` varchar(5000) DEFAULT NULL COMMENT '请求参数(写作专用,JSON格式)',
    `topic` varchar(2000) DEFAULT NULL COMMENT '主题(写作专用)',
    `model_gid` varchar(128) DEFAULT NULL COMMENT '模型ID',
    `agent_id` int(11) DEFAULT NULL COMMENT '智能体ID',
    `agent_name` varchar(64) DEFAULT NULL COMMENT '智能体名称',
    `agent_title` varchar(100) DEFAULT NULL COMMENT '应用标题',
    `site_id` int(11) DEFAULT NULL COMMENT '站点ID',
    `site_name` varchar(64) DEFAULT NULL COMMENT '站点名称',
    `site_url` varchar(100) DEFAULT NULL COMMENT '站点URL',
    `total_tokens` bigint(20) DEFAULT NULL COMMENT '累计Tokens',
    `consume` int(11) DEFAULT NULL COMMENT '消耗点数',
    `status` int(5) DEFAULT 0 COMMENT '状态 0:初始化 1:完成',
    `progress` int(3) DEFAULT NULL COMMENT '进度(绘画专用)',
    `image_url` varchar(1000) DEFAULT NULL COMMENT '图片URL(绘画专用)',
    `ip` varchar(255) DEFAULT NULL COMMENT 'IP',
    `open_id` varchar(64) DEFAULT NULL COMMENT '用户openId',
    `first_char_time` datetime DEFAULT NULL COMMENT '第一个字符出现时间',
    `remark` longtext COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_room` (`user_id`, `room_id`),
    KEY `idx_ability_type` (`ability_type`),
    KEY `idx_message_type` (`message_type`),
    KEY `idx_task_id` (`task_id`),
    KEY `idx_agent_id` (`agent_id`),
    KEY `idx_site_id` (`site_id`),
    KEY `idx_open_id` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI统一消息表';

-- AI统一房间表 (合并chat_room + draw_room)
CREATE TABLE `ai_room` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `title` varchar(255) NOT NULL COMMENT '房间名称',
    `description` varchar(2550) DEFAULT NULL COMMENT '房间简介',
    `ability_type` varchar(20) NOT NULL COMMENT '能力类型 CHAT:对话 DRAW:绘画 MUSIC:音乐',
    `sys_content` longtext COMMENT '系统回答',
    `role_id` int(11) DEFAULT NULL COMMENT '角色ID',
    `image_url` varchar(500) DEFAULT NULL COMMENT '房间图片',
    `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
    `open_id` varchar(64) NOT NULL COMMENT '微信用户ID',
    `conversation_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
    `ip` varchar(64) DEFAULT NULL COMMENT 'IP',
    `open` varchar(3) DEFAULT NULL COMMENT '是否公开',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_open_id` (`open_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_ability_type` (`ability_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI统一房间表';

-- AI统一智能体表 (合并chat_agent + write_agent)
CREATE TABLE `ai_agent` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `title` varchar(100) DEFAULT NULL COMMENT '标题',
    `description` varchar(5000) DEFAULT NULL COMMENT '描述',
    `ability_type` varchar(20) NOT NULL COMMENT '能力类型 CHAT:对话 WRITE:写作',
    `tag` varchar(20) DEFAULT NULL COMMENT '分类',
    `status` int(2) DEFAULT 0 COMMENT '状态 0:启用 1:临时停用 2:内置应用 9:下架',
    `gid` varchar(128) DEFAULT '' COMMENT '模型ID',
    `model_name` varchar(64) DEFAULT NULL COMMENT '模型名称',
    `agent_name` varchar(128) DEFAULT '' COMMENT '智能体名称',
    `use_cnt` int(11) DEFAULT NULL COMMENT '使用次数',
    `sys_content` text COMMENT '系统回答',
    `input_example` varchar(5000) DEFAULT NULL COMMENT '输入提示',
    `charge` int(3) DEFAULT 0 COMMENT '是否收费',
    `img_url` varchar(1000) DEFAULT NULL COMMENT '图标',
    `hot` int(3) DEFAULT NULL COMMENT '是否热门',
    `feat_recs` int(3) DEFAULT NULL COMMENT '是否精选推荐 0:否 1:是',
    `max_token` int(255) DEFAULT NULL COMMENT '回复数',
    `temperature` double(11,2) DEFAULT NULL COMMENT '随机数',
    `num_contexts` int(11) DEFAULT NULL COMMENT '上下文数量',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark` varchar(50) DEFAULT '' COMMENT '备注',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    PRIMARY KEY (`id`),
    KEY `idx_ability_type` (`ability_type`),
    KEY `idx_tag` (`tag`),
    KEY `idx_status` (`status`),
    KEY `idx_gid` (`gid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI统一智能体表';

-- AI分类信息表 (合并category_info + write_category，专用于AI相关分类)
CREATE TABLE `ai_category` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(64) DEFAULT NULL COMMENT '类别名称',
    `code` varchar(64) DEFAULT NULL COMMENT '代码',
    `ability_type` varchar(20) DEFAULT 'CHAT' COMMENT 'AI能力类型 CHAT:对话 DRAW:绘画 WRITE:写作 MUSIC:音乐',
    `sort` int(5) DEFAULT NULL COMMENT '排序',
    `status` int(5) DEFAULT NULL COMMENT '状态 1:启用 0:禁用',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_name_code_ability` (`name`, `code`, `ability_type`),
    KEY `idx_ability_type` (`ability_type`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分类信息表';

-- AI提示词表 (原prompter_info重构，按能力类型划分)
CREATE TABLE `ai_prompter` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `industry` varchar(10) DEFAULT NULL COMMENT '行业代码',
    `industry_name` varchar(100) DEFAULT NULL COMMENT '行业名称',
    `key_word` varchar(100) DEFAULT NULL COMMENT '关键词',
    `ability_type` varchar(20) NOT NULL DEFAULT 'CHAT' COMMENT '能力类型 CHAT:对话 WRITE:写作 DRAW:绘画 MUSIC:音乐',
    `source` varchar(20) NOT NULL DEFAULT 'SYSTEM' COMMENT '来源 IMPORT:导入 SYSTEM:系统内部管理',
    `prompt_content` text COMMENT '提示词内容',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_ability_type` (`ability_type`),
    KEY `idx_source` (`source`),
    KEY `idx_key_industry` (`key_word`, `industry_name`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI提示词表';

-- AI路由配置表 (原site_info重构，用于AI模型付费通道选择)
CREATE TABLE `ai_router_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(64) DEFAULT NULL COMMENT '通道名称',
    `url` varchar(1000) DEFAULT NULL COMMENT '通道地址',
    `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
    `cost_per_token` decimal(10,6) DEFAULT 0.000000 COMMENT '每Token成本',
    `max_tokens` int(11) DEFAULT 4096 COMMENT '最大Token数',
    `timeout` int(11) DEFAULT 30 COMMENT '超时时间(秒)',
    `status` int(3) DEFAULT 0 COMMENT '状态 0:启用 1:禁用',
    `sort` int(5) DEFAULT 0 COMMENT '排序',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI路由配置表';

-- AI路由表 (原channel_config重构，用于AI模型通道路由)
CREATE TABLE `ai_router` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `model_id` int(11) NOT NULL COMMENT '模型ID(关联ai_model.id)',
    `config_id` int(11) NOT NULL COMMENT '配置ID(关联ai_router_config.id)',
    `status` int(3) DEFAULT 0 COMMENT '状态 0:启用 1:禁用',
    `weight` int(3) DEFAULT 1 COMMENT '权重',
    `priority` int(3) DEFAULT 1 COMMENT '优先级',
    `failure_count` int(11) DEFAULT 0 COMMENT '失败次数',
    `last_failure_time` datetime DEFAULT NULL COMMENT '最后失败时间',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_model_id` (`model_id`),
    KEY `idx_config_id` (`config_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI路由表';

-- AI模型表 (原model表重构，用于AI模型信息管理)
CREATE TABLE `ai_model` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `model_gid` varchar(64) NOT NULL COMMENT '模型全局ID',
    `model_name` varchar(100) NOT NULL COMMENT '模型名称',
    `model_type` varchar(20) DEFAULT 'CHAT' COMMENT '模型类型 CHAT:对话 DRAW:绘画 WRITE:写作',
    `provider` varchar(50) DEFAULT NULL COMMENT '提供商 OPENAI CLAUDE GEMINI',
    `version` varchar(20) DEFAULT NULL COMMENT '版本',
    `max_tokens` int(11) DEFAULT 4096 COMMENT '最大Token数',
    `context_window` int(11) DEFAULT 4096 COMMENT '上下文窗口',
    `input_cost` decimal(10,6) DEFAULT 0.000000 COMMENT '输入成本(每1K Token)',
    `output_cost` decimal(10,6) DEFAULT 0.000000 COMMENT '输出成本(每1K Token)',
    `status` int(3) DEFAULT 0 COMMENT '状态 0:启用 1:禁用',
    `description` varchar(500) DEFAULT NULL COMMENT '模型描述',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_model_gid` (`model_gid`),
    KEY `idx_model_type` (`model_type`),
    KEY `idx_provider` (`provider`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型表';

-- =====================================================
-- 5. 塔罗牌相关表 (3张表) - 保留原有结构
-- =====================================================

-- 塔罗牌阵表 (保留原tarot_spread结构)
CREATE TABLE `tarot_spread` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(100) DEFAULT NULL COMMENT '牌阵名称',
    `description` varchar(500) DEFAULT NULL COMMENT '牌阵描述',
    `card_count` int(11) DEFAULT NULL COMMENT '牌数',
    `image_url` varchar(255) DEFAULT NULL COMMENT '牌阵图片',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='塔罗牌阵表';

-- 塔罗牌义表 (保留原tarot_card_meaning结构)
CREATE TABLE `tarot_card_meaning` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name` varchar(100) DEFAULT NULL COMMENT '塔罗牌名称',
    `meaning` text COMMENT '牌的基本含义',
    `guidance_text` text COMMENT '指引文字',
    `advice` text COMMENT '正位建议',
    `discouraged` text COMMENT '逆位警示',
    `card_front_url` varchar(255) DEFAULT NULL COMMENT '塔罗牌正面图片',
    `card_back_url` varchar(255) DEFAULT NULL COMMENT '塔罗牌背面图片',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='塔罗牌义表';

-- 塔罗解读记录表 (保留原tarot_reading_record结构)
CREATE TABLE `tarot_reading_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `spread_id` bigint(20) DEFAULT NULL COMMENT '牌阵ID',
    `question` varchar(500) DEFAULT NULL COMMENT '问题',
    `draw_result` text COMMENT '抽牌结果(JSON格式)',
    `answer` text COMMENT 'AI解读结果',
    `interpretation_mode` int(2) DEFAULT 0 COMMENT '解读模式 0:抽牌 1:自选',
    `status` int(2) DEFAULT 0 COMMENT '解读状态 0:未回答 1:已回答',
    `open_id` varchar(64) DEFAULT NULL COMMENT '微信openId',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_spread_id` (`spread_id`),
    KEY `idx_open_id` (`open_id`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='塔罗解读记录表';

-- =====================================================
-- 6. 系统功能相关表 (8张表)
-- =====================================================

-- 统一国际化表 (合并tarot_i18n，支持所有模块的国际化)
CREATE TABLE `i18n_message` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `lang` varchar(20) NOT NULL COMMENT '语言代码 zh_CN en_US vi_VN',
    `code` text NOT NULL COMMENT '编码，同一段文字的不同语言翻译的code必须相同',
    `value` text NOT NULL COMMENT '翻译值',
    `module` varchar(50) DEFAULT 'COMMON' COMMENT '模块 COMMON:通用 TAROT:塔罗 AI:AI CHAT:对话',
    `category` varchar(50) DEFAULT NULL COMMENT '分类',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_lang_code` (`lang`, `code`(100)),
    KEY `idx_module` (`module`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一国际化表';

-- 用户登录历史表 (基于sys_logininfor增强)
CREATE TABLE `user_login_history` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
    `login_account` varchar(100) DEFAULT NULL COMMENT '登录账号',
    `login_type` varchar(20) DEFAULT NULL COMMENT '登录类型 WECHAT:微信 PHONE:手机 EMAIL:邮箱',
    `joint_login_id` bigint(20) DEFAULT NULL COMMENT '联合登录ID',
    `client_type` varchar(20) DEFAULT NULL COMMENT '客户端类型 WEB:网页 MOBILE:手机 APP:应用',
    `browser` varchar(100) DEFAULT NULL COMMENT '浏览器',
    `os` varchar(100) DEFAULT NULL COMMENT '操作系统',
    `device` varchar(100) DEFAULT NULL COMMENT '设备信息',
    `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `location` varchar(255) DEFAULT NULL COMMENT '登录地点',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `logout_time` datetime DEFAULT NULL COMMENT '退出时间',
    `session_duration` int(11) DEFAULT NULL COMMENT '会话时长(秒)',
    `status` varchar(10) DEFAULT 'SUCCESS' COMMENT '登录状态 SUCCESS:成功 FAILED:失败',
    `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_login_type` (`login_type`),
    KEY `idx_login_time` (`login_time`),
    KEY `idx_ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录历史表';

-- 广告位管理表 (原promotion_info增强，使用adv缩写)
CREATE TABLE `adv_info` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `title` varchar(100) NOT NULL COMMENT '广告标题',
    `description` varchar(500) DEFAULT NULL COMMENT '广告描述',
    `biz_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `adv_type` varchar(50) NOT NULL COMMENT '广告类型 MOBILE_BANNER:移动端横幅 HOME_POSTER:首页海报 POPUP:弹窗广告',
    `adv_image_url` varchar(200) DEFAULT NULL COMMENT '广告图片URL',
    `logo_url` varchar(200) DEFAULT NULL COMMENT 'LOGO图标URL',
    `h5_url` varchar(200) DEFAULT NULL COMMENT 'H5活动页面URL',
    `detail_url` varchar(200) DEFAULT NULL COMMENT '广告详情链接',
    `related_product_id` int(11) DEFAULT NULL COMMENT '关联产品ID',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `status` int(2) DEFAULT 0 COMMENT '状态 0:启用 1:禁用',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `click_count` int(11) DEFAULT 0 COMMENT '点击次数',
    `view_count` int(11) DEFAULT 0 COMMENT '展示次数',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_biz_scene` (`biz_scene`),
    KEY `idx_adv_type` (`adv_type`),
    KEY `idx_status` (`status`),
    KEY `idx_start_end_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告位管理表';

-- 充值记录表 (原recharge_log增强业务场景)
CREATE TABLE `recharge_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `order_no` varchar(64) DEFAULT NULL COMMENT '关联订单号',
    `biz_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `recharge_type` varchar(20) DEFAULT NULL COMMENT '充值类型 POINTS:塔罗币 COINS:积分 VIP:会员',
    `amount` decimal(15,2) NOT NULL COMMENT '充值金额',
    `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
    `recharge_count` int(11) DEFAULT NULL COMMENT '充值数量',
    `bonus_count` int(11) DEFAULT 0 COMMENT '赠送数量',
    `total_count` int(11) DEFAULT NULL COMMENT '总数量',
    `pay_channel` varchar(20) DEFAULT NULL COMMENT '支付渠道',
    `status` int(2) DEFAULT 0 COMMENT '状态 0:待处理 1:成功 2:失败',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_order_no` (`order_no`),
    KEY `idx_biz_scene` (`biz_scene`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';

-- AI敏感词表 (原sensitive_word，归入AI模块)
CREATE TABLE `ai_sensitive_word` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `word` varchar(255) NOT NULL COMMENT '敏感词内容',
    `word_type` varchar(20) DEFAULT 'COMMON' COMMENT '敏感词类型 COMMON:通用 POLITICAL:政治 VIOLENCE:暴力 SEXUAL:色情',
    `ability_type` varchar(20) DEFAULT 'ALL' COMMENT '适用能力类型 ALL:全部 CHAT:对话 DRAW:绘画 WRITE:写作',
    `filter_level` int(2) DEFAULT 1 COMMENT '过滤级别 1:低 2:中 3:高',
    `replacement` varchar(255) DEFAULT '***' COMMENT '替换内容',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 2:停用',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) DEFAULT 0 COMMENT '是否删除 0:否 1:是',
    PRIMARY KEY (`id`),
    KEY `idx_word` (`word`),
    KEY `idx_word_type` (`word_type`),
    KEY `idx_ability_type` (`ability_type`),
    KEY `idx_status` (`status`),
    KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI敏感词表';

-- 分享信息表 (原share_info，分析后确定为分享链接管理)
CREATE TABLE `share_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '分享用户ID',
    `business_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `share_type` varchar(20) NOT NULL COMMENT '分享类型 READING:解读结果 CHAT:对话内容 DRAW:绘画作品',
    `content_id` bigint(20) DEFAULT NULL COMMENT '内容ID',
    `share_title` varchar(255) DEFAULT NULL COMMENT '分享标题',
    `share_description` varchar(500) DEFAULT NULL COMMENT '分享描述',
    `share_image` varchar(255) DEFAULT NULL COMMENT '分享图片',
    `share_url` varchar(500) DEFAULT NULL COMMENT '分享链接',
    `share_code` varchar(32) DEFAULT NULL COMMENT '分享码',
    `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
    `like_count` int(11) DEFAULT 0 COMMENT '点赞次数',
    `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:有效 0:失效',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_share_code` (`share_code`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_business_scene` (`business_scene`),
    KEY `idx_share_type` (`share_type`),
    KEY `idx_content_id` (`content_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分享信息表';

-- =====================================================
-- 7. 提现功能模块 (2张表) - 独立功能模块
-- =====================================================

-- 提现申请表 (原transfer_info重构，独立成提现功能模块)
CREATE TABLE `withdraw_application` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `application_no` varchar(64) NOT NULL COMMENT '申请单号',
    `withdraw_type` varchar(20) DEFAULT 'CASH' COMMENT '提现类型 CASH:现金 POINTS:积分转现',
    `amount` decimal(15,2) NOT NULL COMMENT '提现金额',
    `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
    `account_type` varchar(20) DEFAULT NULL COMMENT '账户类型 ALIPAY:支付宝 WECHAT:微信 BANK:银行卡',
    `account_info` text COMMENT '账户信息(JSON格式)',
    `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
    `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `status` varchar(20) DEFAULT 'PENDING' COMMENT '状态 PENDING:待审核 APPROVED:已通过 REJECTED:已拒绝 PROCESSED:已处理',
    `audit_user` varchar(50) DEFAULT NULL COMMENT '审核人',
    `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
    `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
    `process_time` datetime DEFAULT NULL COMMENT '处理时间',
    `transaction_no` varchar(64) DEFAULT NULL COMMENT '交易流水号',
    `fee` decimal(15,2) DEFAULT 0.00 COMMENT '手续费',
    `actual_amount` decimal(15,2) DEFAULT NULL COMMENT '实际到账金额',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_application_no` (`application_no`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现申请表';

-- 提现交易记录表 (原transaction重构，关联银行交易流水)
CREATE TABLE `withdraw_transaction` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `application_id` bigint(20) NOT NULL COMMENT '提现申请ID',
    `user_id` int(11) NOT NULL COMMENT '用户ID',
    `transaction_no` varchar(64) NOT NULL COMMENT '交易流水号',
    `transaction_type` varchar(20) DEFAULT 'WITHDRAW' COMMENT '交易类型 WITHDRAW:提现 REFUND:退款',
    `amount` decimal(15,2) NOT NULL COMMENT '交易金额',
    `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
    `channel` varchar(20) DEFAULT NULL COMMENT '交易渠道',
    `channel_transaction_no` varchar(64) DEFAULT NULL COMMENT '渠道交易号',
    `status` varchar(20) DEFAULT 'PROCESSING' COMMENT '状态 PROCESSING:处理中 SUCCESS:成功 FAILED:失败',
    `error_code` varchar(50) DEFAULT NULL COMMENT '错误码',
    `error_message` varchar(255) DEFAULT NULL COMMENT '错误信息',
    `callback_data` text COMMENT '回调数据(JSON格式)',
    `process_time` datetime DEFAULT NULL COMMENT '处理时间',
    `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_transaction_no` (`transaction_no`),
    KEY `idx_application_id` (`application_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_channel_transaction_no` (`channel_transaction_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现交易记录表';

-- =====================================================
-- 8. 其他业务表 (保留必要的业务功能表)
-- =====================================================

-- 首页配置表 (保留原home_config，用于首页内容配置)
CREATE TABLE `home_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `biz_scene` varchar(20) NOT NULL COMMENT '业务场景 tarot:塔罗 zns:智能社 chatoi:对话',
    `config_type` varchar(50) NOT NULL COMMENT '配置类型 BANNER:轮播图 NOTICE:公告 FEATURE:功能推荐',
    `title` varchar(255) DEFAULT NULL COMMENT '标题',
    `content` text COMMENT '内容',
    `image_url` varchar(255) DEFAULT NULL COMMENT '图片URL',
    `link_url` varchar(255) DEFAULT NULL COMMENT '链接URL',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序',
    `status` int(2) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    `start_time` datetime DEFAULT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `create_by` varchar(50) DEFAULT '' COMMENT '创建者',
    `create_time` timestamp DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `update_by` varchar(50) DEFAULT '' COMMENT '更新者',
    `update_time` timestamp DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_biz_scene` (`biz_scene`),
    KEY `idx_config_type` (`config_type`),
    KEY `idx_status` (`status`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='首页配置表';

-- 异常日志表 (保留原exception_log，用于系统异常记录)
CREATE TABLE `exception_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
    `module` varchar(50) DEFAULT NULL COMMENT '模块名称',
    `method` varchar(100) DEFAULT NULL COMMENT '方法名称',
    `exception_type` varchar(100) DEFAULT NULL COMMENT '异常类型',
    `exception_message` text COMMENT '异常信息',
    `stack_trace` longtext COMMENT '异常堆栈',
    `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
    `request_method` varchar(10) DEFAULT NULL COMMENT '请求方法',
    `request_params` text COMMENT '请求参数',
    `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
    `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `status` int(2) DEFAULT 0 COMMENT '处理状态 0:未处理 1:已处理',
    `handle_user` varchar(50) DEFAULT NULL COMMENT '处理人',
    `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
    `handle_remark` varchar(255) DEFAULT NULL COMMENT '处理备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_module` (`module`),
    KEY `idx_exception_type` (`exception_type`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常日志表';

-- =====================================================
-- 表结构总结
-- =====================================================
-- 用户相关表: 6张 (user_base_info, user_joint_login, user_check_in_record, user_points_log, user_config, commission_identity, user_joint_config)
-- 支付相关表: 2张 (pay_order, pay_channel_config)
-- AI相关表: 6张 (ai_message, ai_room, ai_agent, category_info, ai_prompter, site_info, channel_config)
-- 塔罗牌相关表: 3张 (tarot_spread, tarot_card_meaning, tarot_reading_record)
-- 系统功能表: 8张 (i18n_message, user_login_history, advertisement_info, recharge_log, ai_sensitive_word, share_info, home_config, exception_log)
-- 提现功能表: 2张 (withdraw_application, withdraw_transaction)
--
-- 总计: 约27张表 (基于61张表深入分析，按需重新梳理)
