# 接口对应关系说明

## 1. 概述

### 1.1 接口重构原则
- **统一入口**: 支付功能统一由支付模块提供，自动识别业务场景
- **向后兼容**: 保持原有接口可用，新增统一接口
- **业务场景**: 按tarot/zns/chatoi业务场景划分
- **RESTful规范**: 统一使用RESTful API设计规范

### 1.2 接口版本管理
- **v1**: 原有接口，保持兼容
- **v2**: 重构后统一接口
- **渐进迁移**: 支持新旧接口并存

## 2. 用户认证接口

### 2.1 登录接口

#### 2.1.1 微信登录
**原接口**:
```
POST /api/wx/login
参数: {
    "code": "微信授权码",
    "state": "状态参数"
}
```

**新接口**:
```
POST /api/v2/auth/login/wechat
参数: {
    "code": "微信授权码", 
    "state": "状态参数",
    "businessScene": "tarot|zns|chatoi"  // 新增业务场景
}
```

#### 2.1.2 手机号登录
**原接口**: 无

**新接口**:
```
POST /api/v2/auth/login/phone
参数: {
    "phone": "手机号",
    "verifyCode": "验证码",
    "businessScene": "tarot|zns|chatoi"
}
```

#### 2.1.3 邮箱登录
**原接口**: 
```
POST /api/user/login
参数: {
    "email": "邮箱",
    "password": "密码"
}
```

**新接口**:
```
POST /api/v2/auth/login/email
参数: {
    "email": "邮箱",
    "password": "密码",
    "businessScene": "tarot|zns|chatoi"
}
```

#### 2.1.4 第三方登录
**原接口**: 无

**新接口**:
```
POST /api/v2/auth/login/{provider}
路径参数: provider = google|facebook|fingerprint
参数: {
    "code": "授权码",
    "state": "状态参数", 
    "businessScene": "tarot|zns|chatoi"
}
```

### 2.2 用户信息接口

#### 2.2.1 获取用户信息
**原接口**:
```
GET /api/user/info
响应: {
    "id": 1,
    "name": "用户名",
    "email": "邮箱",
    "points": 100
}
```

**新接口**:
```
GET /api/v2/user/profile
响应: {
    "id": 1,
    "account": "账号",
    "name": "用户名", 
    "nickname": "昵称",
    "email": "邮箱",
    "phone": "手机号",
    "avatarUrl": "头像",
    "points": 100,
    "tarotCoins": 50,
    "useNum": 10,
    "freeNum": 5,
    "language": "zh_CN",
    "currency": "CNY",
    "timezone": "Asia/Shanghai",
    "loginMethods": ["WECHAT", "EMAIL"]  // 新增：已绑定的登录方式
}
```

#### 2.2.2 更新用户信息
**原接口**:
```
PUT /api/user/update
参数: {
    "name": "新用户名",
    "email": "新邮箱"
}
```

**新接口**:
```
PUT /api/v2/user/profile
参数: {
    "name": "新用户名",
    "nickname": "新昵称", 
    "email": "新邮箱",
    "phone": "新手机号",
    "language": "语言偏好",
    "currency": "币种偏好",
    "timezone": "时区偏好"
}
```

## 3. 支付接口

### 3.1 统一支付接口

#### 3.1.1 创建支付订单
**原接口** (分散在各业务模块):
```
POST /api/tarot/pay/create     // 塔罗支付
POST /api/zns/pay/create       // 智能社支付  
POST /api/chatoi/pay/create    // 对话支付
```

**新接口** (统一入口):
```
POST /api/v2/pay/order/create
参数: {
    "businessScene": "tarot|zns|chatoi",  // 业务场景
    "payChannel": "ALIPAY|WECHAT|MOMO|SEPAY",  // 支付渠道
    "payMethod": "QR|H5|APP",  // 支付方式
    "productId": 1,
    "productType": "VIP|POINTS|COINS",
    "amount": 100.00,
    "currency": "CNY|VND",
    "title": "商品标题",
    "description": "商品描述",
    "returnUrl": "同步跳转地址",
    "notifyUrl": "异步通知地址"
}

响应: {
    "success": true,
    "orderNo": "订单号",
    "payUrl": "支付链接",
    "qrCode": "二维码内容",
    "expireTime": "过期时间"
}
```

#### 3.1.2 查询支付状态
**原接口**:
```
GET /api/pay/status/{orderNo}
```

**新接口**:
```
GET /api/v2/pay/order/{orderNo}/status
响应: {
    "orderNo": "订单号",
    "status": "PENDING|PAID|CANCELLED|EXPIRED|REFUNDED",
    "amount": 100.00,
    "paidAmount": 100.00,
    "payTime": "支付时间",
    "businessScene": "业务场景"
}
```

#### 3.1.3 支付回调接口
**原接口** (分散):
```
POST /api/pay/alipay/notify
POST /api/pay/wechat/notify  
POST /api/pay/sepay/notify
```

**新接口** (统一):
```
POST /api/v2/pay/callback/{channel}
路径参数: channel = alipay|wechat|momo|sepay
```

### 3.2 支付历史接口

#### 3.2.1 支付历史查询
**原接口**: 无统一接口

**新接口**:
```
GET /api/v2/pay/orders
参数: {
    "businessScene": "tarot|zns|chatoi",  // 可选
    "payChannel": "ALIPAY|WECHAT|MOMO|SEPAY",  // 可选
    "status": "PENDING|PAID|CANCELLED",  // 可选
    "pageNum": 1,
    "pageSize": 20
}

响应: {
    "total": 100,
    "list": [{
        "orderNo": "订单号",
        "businessScene": "业务场景",
        "payChannel": "支付渠道", 
        "amount": 100.00,
        "status": "状态",
        "payTime": "支付时间",
        "createTime": "创建时间"
    }]
}
```

## 4. AI对话接口

### 4.1 聊天接口

#### 4.1.1 发送消息
**原接口**:
```
POST /api/chat/send
参数: {
    "content": "消息内容",
    "roomId": 1
}
```

**新接口**:
```
POST /api/v2/chat/message/send
参数: {
    "content": "消息内容",
    "roomId": 1,
    "modelName": "gpt-3.5-turbo",  // 新增
    "systemPrompt": "系统提示词",  // 新增
    "businessScene": "zns|chatoi"  // 新增
}
```

#### 4.1.2 流式对话
**原接口**: 无

**新接口**:
```
POST /api/v2/chat/message/stream
参数: 同发送消息接口
响应: Server-Sent Events流式响应
```

#### 4.1.3 聊天历史
**原接口**:
```
GET /api/chat/history/{roomId}
```

**新接口**:
```
GET /api/v2/chat/room/{roomId}/messages
参数: {
    "pageNum": 1,
    "pageSize": 20
}
```

## 5. 塔罗牌接口

### 5.1 塔罗解读接口

#### 5.1.1 塔罗解读
**原接口**:
```
POST /api/tarot/reading
参数: {
    "spreadId": 1,
    "question": "问题",
    "selectedCards": [1,2,3]
}
```

**新接口**:
```
POST /api/v2/tarot/reading/perform
参数: {
    "spreadId": 1,
    "question": "问题", 
    "selectedCards": [1,2,3],
    "readingType": "GENERAL|LOVE|CAREER",  // 新增
    "language": "zh_CN|en_US"  // 新增
}
```

#### 5.1.2 解读历史
**原接口**:
```
GET /api/tarot/history
```

**新接口**:
```
GET /api/v2/tarot/reading/history
参数: {
    "pageNum": 1,
    "pageSize": 20,
    "readingType": "GENERAL|LOVE|CAREER"  // 可选
}
```

## 6. 国际化接口

### 6.1 翻译接口

#### 6.1.1 获取翻译
**原接口**: 无

**新接口**:
```
GET /api/v2/i18n/translate
参数: {
    "text": "待翻译文本",
    "language": "zh_CN|en_US|vi_VN",
    "params": {"count": 5}  // 可选，动态参数
}

响应: {
    "originalText": "原文",
    "translatedText": "译文", 
    "language": "目标语言"
}
```

#### 6.1.2 批量翻译
**原接口**: 无

**新接口**:
```
POST /api/v2/i18n/translate/batch
参数: {
    "texts": ["文本1", "文本2"],
    "language": "zh_CN|en_US|vi_VN"
}
```

## 7. 缓存管理接口

### 7.1 缓存操作接口

#### 7.1.1 清空缓存
**原接口**: 无

**新接口**:
```
DELETE /api/v2/cache/clear
参数: {
    "cacheNames": ["user", "pay", "tarot"],  // 可选，指定缓存名称
    "pattern": "user:*"  // 可选，按模式清空
}
```

#### 7.1.2 刷新缓存
**原接口**: 无

**新接口**:
```
POST /api/v2/cache/refresh
参数: {
    "cacheNames": ["user", "pay", "tarot"]
}
```

## 8. 迁移建议

### 8.1 前端迁移步骤
1. **阶段1**: 新功能使用v2接口
2. **阶段2**: 逐步将现有功能迁移到v2接口
3. **阶段3**: 废弃v1接口

### 8.2 接口兼容性
- v1接口保持6个月兼容期
- 提供接口迁移工具和文档
- 新增功能只提供v2接口
- 重要变更提前通知
