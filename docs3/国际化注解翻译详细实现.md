# 国际化注解翻译详细实现

## 1. 设计概述

### 1.1 设计理念
参考RuoYi-Vue-Plus的数据翻译实现，采用**注解 + Jackson序列化**的方式，在数据序列化期间动态进行国际化翻译。

### 1.2 核心特性
- ✅ **数据库存储**: 国际化信息存储在数据库中，支持动态管理
- ✅ **注解驱动**: 通过注解标记需要翻译的字段
- ✅ **按值翻译**: 支持直接用字段值查找翻译，无需配置key
- ✅ **参数化翻译**: 支持`{{count}}`格式的动态参数注入
- ✅ **Jackson集成**: 在JSON序列化期间自动翻译
- ✅ **缓存优化**: 翻译结果缓存，提升性能
- ✅ **多语言支持**: 支持中文、英文、越南语等多语言
- ✅ **智能回退**: 有翻译就翻译，没有就返回原文

## 2. 核心注解设计

### 2.1 翻译注解
```java
/**
 * 国际化翻译注解 - 参考RuoYi-Vue-Plus Translation注解
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
@Documented
@JacksonAnnotationsInside
@JsonSerialize(using = I18nTranslationHandler.class)
public @interface I18nTranslation {

    /**
     * 翻译键 (对应i18n_message表的message_key)
     * 如果为空，则使用字段值作为翻译键
     */
    String key() default "";

    /**
     * 是否按值翻译 (直接用字段值查找翻译)
     */
    boolean byValue() default false;

    /**
     * 参数映射字段 (支持{{count}}等动态参数)
     * 例如: {"count", "days", "amount"}
     */
    String[] paramFields() default {};

    /**
     * 默认值 (当翻译不存在时使用)
     */
    String defaultValue() default "";

    /**
     * 是否缓存翻译结果
     */
    boolean cached() default true;

    /**
     * 翻译类型 (用于扩展不同的翻译策略)
     */
    TranslationType type() default TranslationType.MESSAGE;
}

/**
 * 翻译类型枚举
 */
public enum TranslationType {
    MESSAGE,        // 普通消息翻译
    DICT,          // 字典翻译
    USER_DEFINED   // 用户自定义翻译
}
```

### 2.2 翻译类型注解
```java
/**
 * 国际化翻译类型注解 - 标记整个类的翻译类型
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
public @interface I18nTranslationType {
    /**
     * 翻译类型标识
     */
    String type();
    
    /**
     * 是否启用翻译
     */
    boolean enabled() default true;
}
```

## 3. Jackson序列化处理器

### 3.1 核心翻译处理器
```java
/**
 * 国际化翻译处理器 - 参考RuoYi-Vue-Plus TranslationHandler
 */
@Slf4j
public class I18nTranslationHandler extends JsonSerializer<Object> implements ContextualSerializer {
    
    /**
     * 全局翻译实现类映射器
     */
    public static final Map<String, I18nTranslationInterface> TRANSLATION_MAPPER = new ConcurrentHashMap<>();
    
    private I18nTranslation i18nTranslation;
    
    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        try {
            // 1. 获取当前用户语言环境
            String locale = getCurrentLocale();

            // 2. 确定翻译键
            String messageKey;
            if (i18nTranslation.byValue() || StringUtils.isBlank(i18nTranslation.key())) {
                // 按值翻译：直接用字段值作为翻译键
                messageKey = String.valueOf(value);
            } else {
                // 按键翻译：使用配置的key
                messageKey = i18nTranslation.key();
            }

            // 3. 构建参数Map
            Map<String, Object> params = buildParams(gen.getCurrentValue(), i18nTranslation.paramFields());

            // 4. 根据翻译类型选择处理策略
            String translatedMessage = translateByType(messageKey, locale, params, i18nTranslation.type());

            // 5. 智能回退：有翻译就翻译，没有就返回原文
            if (StringUtils.isBlank(translatedMessage)) {
                if (StringUtils.isNotBlank(i18nTranslation.defaultValue())) {
                    translatedMessage = i18nTranslation.defaultValue();
                } else {
                    // 没有翻译就返回原文
                    translatedMessage = String.valueOf(value);
                }
            }

            // 6. 输出翻译结果
            gen.writeString(translatedMessage);

        } catch (Exception e) {
            log.error("国际化翻译失败: value={}, byValue={}", value, i18nTranslation.byValue(), e);
            // 异常时返回原文
            gen.writeString(String.valueOf(value));
        }
    }
    
    /**
     * 根据翻译类型进行翻译
     */
    private String translateByType(String messageKey, String locale, Map<String, Object> params, TranslationType type) {
        switch (type) {
            case MESSAGE:
                return translateMessage(messageKey, locale, params);
            case DICT:
                return translateDict(messageKey, locale, params);
            case USER_DEFINED:
                return translateUserDefined(messageKey, locale, params);
            default:
                return translateMessage(messageKey, locale, params);
        }
    }
    
    /**
     * 普通消息翻译，支持{{param}}格式的参数替换
     */
    private String translateMessage(String messageKey, String locale, Map<String, Object> params) {
        // 1. 从缓存或数据库获取翻译模板
        String template = getTranslationTemplate(messageKey, locale);
        
        if (StringUtils.isBlank(template)) {
            return null;
        }
        
        // 2. 替换参数 {{count}} -> 实际值
        String result = template;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            String value = formatParamValue(entry.getValue(), locale);
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    /**
     * 字典翻译 (用于状态码、类型码等)
     */
    private String translateDict(String dictType, String locale, Map<String, Object> params) {
        // 从字典表获取翻译
        String dictValue = params.get("value") != null ? params.get("value").toString() : "";
        return DictTranslationService.translate(dictType, dictValue, locale);
    }
    
    /**
     * 用户自定义翻译
     */
    private String translateUserDefined(String key, String locale, Map<String, Object> params) {
        I18nTranslationInterface translator = TRANSLATION_MAPPER.get(key);
        if (translator != null) {
            return translator.translate(key, locale, params);
        }
        return null;
    }
    
    /**
     * 构建参数Map
     */
    private Map<String, Object> buildParams(Object currentObject, String[] paramFields) {
        Map<String, Object> params = new HashMap<>();
        
        if (currentObject == null || paramFields.length == 0) {
            return params;
        }
        
        for (String field : paramFields) {
            try {
                Object value = getFieldValue(currentObject, field);
                params.put(field, value);
            } catch (Exception e) {
                log.warn("获取参数字段失败: field={}", field, e);
                params.put(field, "");
            }
        }
        
        return params;
    }
    
    /**
     * 获取字段值 (支持嵌套字段)
     */
    private Object getFieldValue(Object obj, String fieldPath) throws Exception {
        if (obj == null) {
            return null;
        }
        
        String[] fields = fieldPath.split("\\.");
        Object current = obj;
        
        for (String field : fields) {
            if (current == null) {
                return null;
            }
            
            // 使用反射获取字段值
            Field declaredField = current.getClass().getDeclaredField(field);
            declaredField.setAccessible(true);
            current = declaredField.get(current);
        }
        
        return current;
    }
    
    /**
     * 格式化参数值 (根据语言环境格式化数字、日期等)
     */
    private String formatParamValue(Object value, String locale) {
        if (value == null) {
            return "";
        }
        
        // 数字格式化
        if (value instanceof Number) {
            return NumberFormatUtil.format((Number) value, locale);
        }
        
        // 日期格式化
        if (value instanceof Date || value instanceof LocalDateTime) {
            return DateFormatUtil.format(value, locale);
        }
        
        return String.valueOf(value);
    }
    
    /**
     * 获取当前用户语言环境
     */
    private String getCurrentLocale() {
        // 1. 从请求头获取
        HttpServletRequest request = getCurrentRequest();
        if (request != null) {
            String acceptLanguage = request.getHeader("Accept-Language");
            if (StringUtils.isNotBlank(acceptLanguage)) {
                return parseLocale(acceptLanguage);
            }
        }
        
        // 2. 从用户配置获取
        Integer userId = getCurrentUserId();
        if (userId != null) {
            UserBaseInfo userInfo = userService.getById(userId);
            if (userInfo != null && StringUtils.isNotBlank(userInfo.getLanguage())) {
                return userInfo.getLanguage();
            }
        }
        
        // 3. 默认中文
        return "zh_CN";
    }
    
    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        I18nTranslation translation = property.getAnnotation(I18nTranslation.class);
        if (Objects.nonNull(translation)) {
            this.i18nTranslation = translation;
            return this;
        }
        return prov.findValueSerializer(property.getType(), property);
    }
}
```

## 4. 翻译服务实现

### 4.1 国际化消息服务
```java
/**
 * 国际化消息服务
 */
@Service
@Slf4j
public class I18nMessageService {
    
    @Autowired
    private I18nMessageMapper i18nMessageMapper;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    /**
     * 获取翻译消息 - 支持缓存
     */
    @Cacheable(value = "i18n_messages", key = "#messageKey + '_' + #locale")
    public String getMessage(String messageKey, String locale) {
        // 1. 先查询指定语言的翻译
        I18nMessage message = i18nMessageMapper.selectByKeyAndLocale(messageKey, locale);
        
        if (message != null) {
            return message.getMessageValue();
        }
        
        // 2. 如果没有找到，尝试查询默认语言(中文)
        if (!"zh_CN".equals(locale)) {
            message = i18nMessageMapper.selectByKeyAndLocale(messageKey, "zh_CN");
            if (message != null) {
                return message.getMessageValue();
            }
        }
        
        // 3. 都没有找到，返回null
        return null;
    }
    
    /**
     * 批量获取翻译消息
     */
    public Map<String, String> getMessages(List<String> messageKeys, String locale) {
        // 1. 先从缓存批量获取
        List<String> cacheKeys = messageKeys.stream()
            .map(key -> "i18n_messages::" + key + "_" + locale)
            .collect(Collectors.toList());
        
        List<String> cachedValues = redisTemplate.opsForValue().multiGet(cacheKeys);
        
        Map<String, String> result = new HashMap<>();
        List<String> missedKeys = new ArrayList<>();
        
        for (int i = 0; i < messageKeys.size(); i++) {
            String key = messageKeys.get(i);
            String cachedValue = cachedValues.get(i);
            
            if (cachedValue != null) {
                result.put(key, cachedValue);
            } else {
                missedKeys.add(key);
            }
        }
        
        // 2. 从数据库查询缓存未命中的
        if (!missedKeys.isEmpty()) {
            List<I18nMessage> messages = i18nMessageMapper.selectByKeysAndLocale(missedKeys, locale);
            
            for (I18nMessage message : messages) {
                result.put(message.getMessageKey(), message.getMessageValue());
                
                // 更新缓存
                String cacheKey = "i18n_messages::" + message.getMessageKey() + "_" + locale;
                redisTemplate.opsForValue().set(cacheKey, message.getMessageValue(), 1, TimeUnit.HOURS);
            }
        }
        
        return result;
    }
    
    /**
     * 刷新缓存
     */
    @CacheEvict(value = "i18n_messages", allEntries = true)
    public void refreshCache() {
        log.info("刷新国际化消息缓存");
    }
    
    /**
     * 预热缓存
     */
    @PostConstruct
    public void warmUpCache() {
        List<String> locales = Arrays.asList("zh_CN", "en_US", "vi_VN");
        
        for (String locale : locales) {
            List<I18nMessage> messages = i18nMessageMapper.selectByLocale(locale);
            
            for (I18nMessage message : messages) {
                String cacheKey = "i18n_messages::" + message.getMessageKey() + "_" + locale;
                redisTemplate.opsForValue().set(cacheKey, message.getMessageValue(), 2, TimeUnit.HOURS);
            }
            
            log.info("预热国际化缓存完成: locale={}, count={}", locale, messages.size());
        }
    }
}
```

## 5. 使用示例

### 5.1 VO类定义
```java
/**
 * 用户信息VO - 支持国际化翻译
 */
@Data
@I18nTranslationType(type = "user")
public class UserInfoVO {
    
    private Integer id;
    
    private String name;
    
    /**
     * 用户状态翻译
     * 数据库中存储: 
     * - user.status.0 -> "正常" / "Normal" / "Bình thường"
     * - user.status.1 -> "禁用" / "Disabled" / "Bị vô hiệu hóa"
     */
    @I18nTranslation(key = "user.status", paramFields = {"status"})
    private String statusText;
    
    private Integer status; // 状态码，用于翻译参数
    
    /**
     * 积分消息翻译 (支持参数)
     * 数据库中存储:
     * - user.points.message -> "您有{{points}}个积分" / "You have {{points}} points" / "Bạn có {{points}} điểm"
     */
    @I18nTranslation(key = "user.points.message", paramFields = {"points"})
    private String pointsMessage;
    
    private Integer points; // 积分数量，用于翻译参数
    
    /**
     * VIP到期消息翻译 (支持参数)
     * 数据库中存储:
     * - user.vip.expire -> "VIP还有{{days}}天到期" / "VIP expires in {{days}} days" / "VIP hết hạn sau {{days}} ngày"
     */
    @I18nTranslation(key = "user.vip.expire", paramFields = {"days"})
    private String vipExpireMessage;
    
    private Integer days; // 剩余天数，用于翻译参数
    
    /**
     * 复杂参数翻译 (支持多个参数)
     * 数据库中存储:
     * - user.usage.summary -> "本月使用{{chatCount}}次对话，{{drawCount}}次绘画，消耗{{totalPoints}}积分"
     */
    @I18nTranslation(key = "user.usage.summary", paramFields = {"chatCount", "drawCount", "totalPoints"})
    private String usageSummary;
    
    private Integer chatCount;
    private Integer drawCount; 
    private Integer totalPoints;
}
```

### 5.2 控制器使用
```java
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @GetMapping("/info")
    public BaseResponse<UserInfoVO> getUserInfo() {
        Integer userId = StpUtil.getLoginIdAsInt();
        
        // 获取用户基础信息
        UserBaseInfo userInfo = userService.getById(userId);
        
        // 构建VO对象
        UserInfoVO vo = new UserInfoVO();
        vo.setId(userInfo.getId());
        vo.setName(userInfo.getName());
        vo.setStatus(userInfo.getStatus());
        vo.setPoints(userInfo.getUseNum() + userInfo.getFreeNum());
        
        // 计算VIP剩余天数
        if (userInfo.getVipEndTime() != null) {
            long days = ChronoUnit.DAYS.between(LocalDateTime.now(), userInfo.getVipEndTime());
            vo.setDays((int) Math.max(0, days));
        }
        
        // 设置使用统计
        vo.setChatCount(getUserChatCount(userId));
        vo.setDrawCount(getUserDrawCount(userId));
        vo.setTotalPoints(getUserTotalPoints(userId));
        
        // 返回时会自动进行国际化翻译
        return BaseResponse.success(vo);
    }
}
```

### 5.3 返回结果示例
```json
// 中文用户 (zh_CN)
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "张三",
    "statusText": "正常",
    "pointsMessage": "您有1500个积分",
    "vipExpireMessage": "VIP还有30天到期",
    "usageSummary": "本月使用50次对话，10次绘画，消耗800积分"
  }
}

// 英文用户 (en_US)
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "张三",
    "statusText": "Normal",
    "pointsMessage": "You have 1,500 points",
    "vipExpireMessage": "VIP expires in 30 days",
    "usageSummary": "Used 50 chats, 10 drawings this month, consumed 800 points"
  }
}

// 越南语用户 (vi_VN)
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "张三",
    "statusText": "Bình thường",
    "pointsMessage": "Bạn có 1.500 điểm",
    "vipExpireMessage": "VIP hết hạn sau 30 ngày",
    "usageSummary": "Đã sử dụng 50 cuộc trò chuyện, 10 bản vẽ trong tháng này, tiêu thụ 800 điểm"
  }
}
```

## 6. 扩展功能

### 6.1 自定义翻译器
```java
/**
 * 自定义翻译接口
 */
public interface I18nTranslationInterface {
    String translate(String key, String locale, Map<String, Object> params);
}

/**
 * 时间相对翻译器
 */
@Component
public class RelativeTimeTranslator implements I18nTranslationInterface {
    
    @Override
    public String translate(String key, String locale, Map<String, Object> params) {
        LocalDateTime time = (LocalDateTime) params.get("time");
        if (time == null) {
            return "";
        }
        
        Duration duration = Duration.between(time, LocalDateTime.now());
        long minutes = duration.toMinutes();
        
        if (minutes < 1) {
            return getLocalizedMessage("time.just_now", locale);
        } else if (minutes < 60) {
            return getLocalizedMessage("time.minutes_ago", locale, minutes);
        } else if (minutes < 1440) {
            return getLocalizedMessage("time.hours_ago", locale, minutes / 60);
        } else {
            return getLocalizedMessage("time.days_ago", locale, minutes / 1440);
        }
    }
    
    // 注册翻译器
    @PostConstruct
    public void register() {
        I18nTranslationHandler.TRANSLATION_MAPPER.put("relative_time", this);
    }
}
```

通过这套国际化注解翻译系统，我们可以实现：
- ✅ **数据库驱动**: 翻译内容存储在数据库中，支持动态管理
- ✅ **注解简化**: 通过注解标记，使用简单
- ✅ **参数化支持**: 支持`{{param}}`格式的动态参数
- ✅ **性能优化**: 多级缓存，高性能翻译
- ✅ **扩展性强**: 支持自定义翻译器和翻译策略
