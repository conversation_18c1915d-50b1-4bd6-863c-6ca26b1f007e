# 超级智能社重构文档中心

## 📋 文档概述

本目录包含超级智能社项目完整重构的所有设计文档，涵盖需求分析、架构设计、数据库设计、接口设计、迁移方案等全方位内容。

## 📚 文档目录

### 1. 需求设计文档

#### 1.1 [重构原始需求清单.md](./重构原始需求清单.md)
- **内容**: 完整的重构需求清单，包含29个详细需求点（最新版本）
- **用途**: 需求跟踪和验收标准
- **包含**: 用户功能、支付功能、AI功能、系统功能、技术规范、数据迁移等需求
- **重点**: JustAuth集成、pay-java-parent具体包名、AI通道表重构、POINTS概念修正、Service改Worker

#### 1.2 [完整重构需求设计说明书.md](./完整重构需求设计说明书.md)
- **内容**: 详细的重构需求设计说明
- **用途**: 开发团队技术实现指南
- **包含**: 技术栈、架构原则、功能模块设计

#### 1.3 [重构需求总结.md](./重构需求总结.md)
- **内容**: 重构需求的高度总结和概览
- **用途**: 项目管理和进度跟踪
- **包含**: 核心目标、实施计划、风险控制

### 2. 架构设计文档

#### 2.1 [重构目录结构设计.md](./重构目录结构设计.md)
- **内容**: 完整的代码目录结构设计
- **用途**: 代码组织和模块划分指南
- **包含**: 包结构、类设计、方法设计

#### 2.2 [系统架构设计.md](./系统架构设计.md)
- **内容**: 系统整体架构设计
- **用途**: 技术选型和架构决策参考
- **包含**: 分层架构、技术栈、设计模式

### 3. 数据库设计文档

#### 3.1 [数据库导出表设计.sql](./数据库导出表设计.sql)
- **内容**: 原有61张表的完整结构
- **用途**: 现状分析和迁移参考
- **包含**: 所有表结构、字段定义、索引设计

#### 3.2 [重构数据库设计.sql](./重构数据库设计.sql)
- **内容**: 基于61张表深入分析，按需重新梳理的表结构设计（约25张表）
- **用途**: 新数据库结构实施
- **包含**: 命名规范统一、AI表合并、通道表重构、多币种支付、分佣功能保留

#### 3.3 [数据库设计说明书.md](./数据库设计说明书.md)
- **内容**: 数据库设计的详细说明
- **用途**: 数据库开发和维护指南
- **包含**: 设计原则、表关系、业务规则

### 4. 接口设计文档

#### 4.1 [接口对应关系说明.md](./接口对应关系说明.md)
- **内容**: 新旧接口的对应关系
- **用途**: 前端改造和接口迁移
- **包含**: 接口映射、参数变更、版本管理

#### 4.2 [API接口设计规范.md](./API接口设计规范.md)
- **内容**: RESTful API设计规范
- **用途**: 接口开发标准化
- **包含**: 命名规范、响应格式、错误处理

### 5. 迁移实施文档

#### 5.1 [历史数据迁移方案.md](./历史数据迁移方案.md)
- **内容**: 完整的数据迁移方案
- **用途**: 数据迁移实施指南
- **包含**: 迁移脚本、校验方案、回滚机制

#### 5.2 [代码迁移指南.md](./代码迁移指南.md)
- **内容**: 代码重构和迁移指南
- **用途**: 开发团队实施参考
- **包含**: 重构步骤、注意事项、质量标准

### 6. 技术实现文档

#### 6.1 [缓存设计方案.md](./缓存设计方案.md)
- **内容**: Redis + Caffeine多级缓存设计
- **用途**: 缓存系统实现指南
- **包含**: 缓存策略、性能优化、监控方案

#### 6.2 [国际化实现方案.md](./国际化实现方案.md)
- **内容**: 多语言国际化实现方案
- **用途**: 国际化功能开发指南
- **包含**: 翻译机制、注解使用、数据库设计

#### 6.3 [支付系统设计.md](./支付系统设计.md)
- **内容**: 统一支付系统设计
- **用途**: 支付功能开发指南
- **包含**: 多渠道支付、配置管理、回调处理

#### 6.4 [认证系统设计.md](./认证系统设计.md)
- **内容**: JustAuth + Sa-Token认证系统
- **用途**: 认证功能开发指南
- **包含**: 多渠道登录、权限管理、会话控制

#### 6.5 [文档组织总结.md](./文档组织总结.md)
- **内容**: 完整的文档更新状态和重构目标校准
- **用途**: 项目管理和质量控制
- **包含**: 文档更新状态、重构目标确认、实施计划、验收标准

## 🎯 重构核心目标

### 数据库优化
- **深入分析61张表**: 按实际业务需求重新梳理表结构，约25张表
- **命名规范统一**: advertisement→adv, business_scene→biz_scene
- **AI表按能力类型合并**: ai_message/ai_room/ai_agent按ability_type区分
- **AI通道表重构**: site_info→ai_router_config, channel_config→ai_router, model→ai_model
- **统一支付订单**: 合并4个支付表为pay_order表，支持多币种
- **分佣功能保留**: 完整保留commission_identity表和相关分佣逻辑

### 架构升级
- **业务场景划分**: 按tarot/zns/chatoi业务场景组织代码
- **Worker模式**: 简化Service+Impl为Worker模式
- **多级缓存**: Redis + Caffeine提升性能

### 功能增强
- **JustAuth集成**: 统一第三方登录，支持微信/谷歌/Facebook/手机/邮箱/指纹
- **pay-java-parent集成**: 使用com.egzosn.pay-java-ali和com.egzosn.pay-java-wx官方包
- **Spring AI集成**: AI功能框架化，保持现有业务逻辑
- **国际化工具化**: 按值翻译+参数化翻译+注解翻译
- **POINTS概念修正**: POINTS是塔罗币，修订中文描述，保留英文单词

## 📊 项目统计

### 数据库变更
- **原有表数**: 61张表
- **重构后表数**: 约25张表
- **核心合并**: 支付订单4合1、AI消息3合1、AI房间2合1、AI智能体2合1
- **表重构**: AI通道表重构、提现功能表重构、广告位表增强、充值记录增强
- **命名统一**: adv缩写、biz_scene统一、标准字段统一
- **功能保留**: 分佣功能、塔罗牌业务、系统管理

### 代码结构
- **包路径**: 保持com.hncboy.chatgpt
- **业务模块**: 3个业务场景(tarot/zns/chatoi)
- **框架模块**: 7个核心框架(auth/cache/pay/i18n/ai/weixin/web)
- **编码模式**: 非db包下Service一律改为Worker模式，简化实现

### 技术栈
- **后端框架**: Spring Boot 3.x
- **数据库**: MySQL 8.0
- **缓存**: Redis + Caffeine
- **认证**: Sa-Token + JustAuth
- **支付**: pay-java-parent
- **AI**: Spring AI + Dify

## 🚀 实施路径

### 阶段一：基础架构 (2周)
1. 数据库表结构设计和创建
2. 基础框架搭建(auth/cache/pay/i18n)
3. 核心工具类和枚举类开发

### 阶段二：业务迁移 (6周)
1. 用户认证系统重构 (1周)
2. 支付系统重构 (1周)
3. tarot业务迁移 (2周)
4. zns业务迁移 (1周)
5. chatoi业务迁移 (1周)

### 阶段三：数据迁移 (1周)
1. 历史数据迁移脚本开发
2. 数据迁移执行和校验
3. 新旧系统并行运行

### 阶段四：测试上线 (1周)
1. 功能测试和性能测试
2. 接口兼容性测试
3. 灰度发布和全量上线

## 📞 联系方式

如有任何问题或建议，请联系项目团队：

- **项目经理**: [项目经理联系方式]
- **技术负责人**: [技术负责人联系方式]
- **文档维护**: [文档维护人联系方式]

## 📝 更新日志

- **2024-01-01**: 初始版本，完成所有设计文档
- **待更新**: 根据开发进度持续更新

---

> 📌 **重要提醒**: 本重构项目严格按照最新需求执行，包括：
> - 命名规范统一（adv缩写、biz_scene统一）
> - AI表按能力类型合并，移除重复的chat_*表
> - AI通道表重构：site_info→ai_router_config, channel_config→ai_router, model→ai_model
> - 提现功能表重构：transfer_info→withdraw_application, transaction→withdraw_transaction
> - POINTS概念修正：POINTS是塔罗币，修订中文描述，保留英文单词
> - 编码模式统一：非db包下Service一律改为Worker
> - JustAuth、pay-java-parent具体包名、Spring AI等技术集成
> - 100%复刻原有业务逻辑，确保业务功能不受影响
