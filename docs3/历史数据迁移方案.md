# 历史数据迁移方案

## 1. 迁移概述

### 1.1 迁移目标
将现有61张表的数据迁移到重构后的25张表中，确保数据完整性和业务连续性。

### 1.2 迁移策略
- **分阶段迁移**: 按业务模块分批次迁移
- **数据校验**: 每个阶段完成后进行数据完整性校验
- **回滚机制**: 提供完整的数据回滚方案
- **业务不中断**: 采用双写模式确保业务连续性

## 2. 表合并迁移方案

### 2.1 用户信息合并迁移

#### 2.1.1 users表 + wx_user_info表 → user_joint_login表

**迁移脚本**:
```sql
-- 第一步：迁移users表数据到user_joint_login
INSERT INTO user_joint_login (
    user_id, login_type, third_party_id, third_party_username, 
    third_party_email, third_party_avatar, union_id, 
    extra_data, last_login_time, create_time, update_time
)
SELECT 
    u.id as user_id,
    CASE 
        WHEN u.fb_id IS NOT NULL THEN 'FACEBOOK'
        WHEN u.google_id IS NOT NULL THEN 'GOOGLE'
        WHEN u.finb_id IS NOT NULL THEN 'FINGERPRINT'
        ELSE 'EMAIL'
    END as login_type,
    COALESCE(u.fb_id, u.google_id, u.finb_id, u.email) as third_party_id,
    u.name as third_party_username,
    u.email as third_party_email,
    u.picture as third_party_avatar,
    NULL as union_id,
    JSON_OBJECT(
        'referrer_id', u.referrer_id,
        'lucky_coins', u.lucky_coins,
        'extra_data', u.extra_data
    ) as extra_data,
    u.last_login_time,
    u.created_at as create_time,
    u.updated_at as update_time
FROM users u
WHERE u.id IS NOT NULL;

-- 第二步：迁移wx_user_info表数据到user_joint_login
INSERT INTO user_joint_login (
    user_id, login_type, third_party_id, third_party_username,
    third_party_email, third_party_avatar, union_id, app_id,
    extra_data, create_time, update_time
)
SELECT 
    ubi.id as user_id,
    'WECHAT' as login_type,
    wx.open_id as third_party_id,
    wx.nick_name as third_party_username,
    NULL as third_party_email,
    wx.avatar_url as third_party_avatar,
    wx.union_id,
    wx.app_id,
    JSON_OBJECT(
        'gender', wx.gender,
        'language', wx.language,
        'city', wx.city,
        'province', wx.province,
        'country', wx.country,
        'subscribe_scene', wx.subscribe_scene,
        'qr_scene', wx.qr_scene,
        'vip_end_time', wx.vip_end_time,
        'apply_num', wx.apply_num,
        'parent_id', wx.parent_id,
        'vip', wx.vip
    ) as extra_data,
    wx.create_time,
    wx.update_time
FROM wx_user_info wx
LEFT JOIN user_base_info ubi ON ubi.open_id = wx.open_id
WHERE ubi.id IS NOT NULL;
```

#### 2.1.2 数据校验脚本
```sql
-- 校验迁移数据完整性
SELECT 
    '原users表记录数' as description,
    COUNT(*) as count
FROM users
UNION ALL
SELECT 
    '原wx_user_info表记录数' as description,
    COUNT(*) as count
FROM wx_user_info
UNION ALL
SELECT 
    '新user_joint_login表记录数' as description,
    COUNT(*) as count
FROM user_joint_login;

-- 校验关键字段是否正确迁移
SELECT 
    login_type,
    COUNT(*) as count
FROM user_joint_login
GROUP BY login_type;
```

### 2.2 支付订单合并迁移

#### 2.2.1 多个支付表 → pay_order表

**迁移脚本**:
```sql
-- 迁移支付宝订单
INSERT INTO pay_order (
    order_no, user_id, product_id, product_type, business_scene,
    pay_channel, currency, amount, title, description, status,
    pay_time, expire_time, create_time, update_time
)
SELECT 
    al.orders_id as order_no,
    CAST(al.user_id AS UNSIGNED) as user_id,
    al.product_id,
    al.product_type,
    CASE 
        WHEN al.product_type = 'tarot' THEN 'tarot'
        ELSE 'zns'
    END as business_scene,
    'ALIPAY' as pay_channel,
    'CNY' as currency,
    al.product_price as amount,
    al.product_name as title,
    al.package_info as description,
    CASE al.state
        WHEN 1 THEN 'PAID'
        WHEN 0 THEN 'PENDING'
        ELSE 'CANCELLED'
    END as status,
    al.pay_time,
    al.expires_time as expire_time,
    al.created_time as create_time,
    al.update_time
FROM al_orders al;

-- 迁移微信支付订单
INSERT INTO pay_order (
    order_no, third_party_order_no, user_id, product_id, product_type,
    business_scene, pay_channel, currency, amount, title, description,
    status, pay_time, expire_time, notify_url, pay_params, pay_result,
    client_ip, create_time, update_time
)
SELECT 
    wx.out_trade_no as order_no,
    wx.transaction_id as third_party_order_no,
    CAST(wx.user_id AS UNSIGNED) as user_id,
    wx.goods_id as product_id,
    wx.product_type,
    CASE 
        WHEN wx.product_type = 'tarot' THEN 'tarot'
        ELSE 'zns'
    END as business_scene,
    'WECHAT' as pay_channel,
    COALESCE(wx.currency, 'CNY') as currency,
    wx.total_fee as amount,
    wx.body as title,
    wx.body as description,
    CASE wx.status
        WHEN 1 THEN 'PAID'
        WHEN 0 THEN 'PENDING'
        ELSE 'CANCELLED'
    END as status,
    wx.time_end as pay_time,
    wx.expires_time as expire_time,
    wx.notify_url,
    JSON_OBJECT(
        'trade_type', wx.trade_type,
        'openid', wx.openid,
        'is_subscribe', wx.is_subscribe,
        'trade_channel', wx.trade_channel
    ) as pay_params,
    wx.original_message as pay_result,
    wx.spbill_create_ip as client_ip,
    wx.create_time,
    wx.update_time
FROM wx_pay_order wx;

-- 迁移SE支付订单
INSERT INTO pay_order (
    order_no, user_id, product_id, product_type, business_scene,
    pay_channel, currency, amount, title, description, status,
    pay_time, expire_time, pay_params, client_ip, create_time, update_time
)
SELECT 
    se.order_no,
    CAST(se.user_id AS UNSIGNED) as user_id,
    se.product_id,
    se.product_type,
    CASE 
        WHEN se.product_type = 'tarot' THEN 'tarot'
        ELSE 'zns'
    END as business_scene,
    'SEPAY' as pay_channel,
    'VND' as currency,
    se.amount,
    se.body as title,
    se.description,
    CASE se.status
        WHEN 1 THEN 'PAID'
        WHEN 0 THEN 'PENDING'
        ELSE 'CANCELLED'
    END as status,
    se.time_end as pay_time,
    se.expires_time as expire_time,
    JSON_OBJECT(
        'unique_id', se.unique_id,
        'account_number', se.account_number,
        'bank_name', se.bank_name,
        'qr_code_url', se.qr_code_url,
        'gateway', se.gateway,
        'code', se.code,
        'se_pay_id', se.se_pay_id
    ) as pay_params,
    se.ip_address as client_ip,
    se.create_time,
    se.update_time
FROM se_pay_order se;
```

### 2.3 签到记录合并迁移

#### 2.3.1 user_check_in_record表 + app_sign表 → user_check_in_record表

**迁移脚本**:
```sql
-- 先备份原表数据
CREATE TABLE user_check_in_record_backup AS SELECT * FROM user_check_in_record;
CREATE TABLE app_sign_backup AS SELECT * FROM app_sign;

-- 清空目标表
TRUNCATE TABLE user_check_in_record;

-- 迁移原user_check_in_record数据
INSERT INTO user_check_in_record (
    user_id, check_in_date, week, type, is_make_up, awarded,
    award_type, continuous_days, create_time, update_time
)
SELECT 
    user_id,
    check_in_date,
    week,
    type,
    is_make_up,
    awarded,
    'POINTS' as award_type,
    1 as continuous_days,
    create_time,
    update_time
FROM user_check_in_record_backup;

-- 迁移app_sign数据
INSERT INTO user_check_in_record (
    user_id, check_in_date, week, type, is_make_up, awarded,
    award_type, continuous_days, create_time, update_time
)
SELECT 
    CAST(SUBSTRING(user_id, 1, 10) AS UNSIGNED) as user_id,
    DATE_FORMAT(check_date, '%Y%m%d') as check_in_date,
    DAYNAME(check_date) as week,
    'ZNS' as type,
    repair_check as is_make_up,
    10 as awarded,
    'POINTS' as award_type,
    1 as continuous_days,
    NOW() as create_time,
    NOW() as update_time
FROM app_sign_backup
WHERE user_id IS NOT NULL AND check_date IS NOT NULL;
```

## 3. 数据完整性校验

### 3.1 校验脚本
```sql
-- 创建数据校验视图
CREATE VIEW migration_validation AS
SELECT 
    'users' as table_name,
    COUNT(*) as original_count,
    (SELECT COUNT(*) FROM user_joint_login WHERE login_type != 'WECHAT') as migrated_count
FROM users
UNION ALL
SELECT 
    'wx_user_info' as table_name,
    COUNT(*) as original_count,
    (SELECT COUNT(*) FROM user_joint_login WHERE login_type = 'WECHAT') as migrated_count
FROM wx_user_info
UNION ALL
SELECT 
    'al_orders' as table_name,
    COUNT(*) as original_count,
    (SELECT COUNT(*) FROM pay_order WHERE pay_channel = 'ALIPAY') as migrated_count
FROM al_orders
UNION ALL
SELECT 
    'wx_pay_order' as table_name,
    COUNT(*) as original_count,
    (SELECT COUNT(*) FROM pay_order WHERE pay_channel = 'WECHAT') as migrated_count
FROM wx_pay_order
UNION ALL
SELECT 
    'se_pay_order' as table_name,
    COUNT(*) as original_count,
    (SELECT COUNT(*) FROM pay_order WHERE pay_channel = 'SEPAY') as migrated_count
FROM se_pay_order;

-- 查看校验结果
SELECT * FROM migration_validation;
```

## 4. 回滚方案

### 4.1 数据回滚脚本
```sql
-- 回滚用户联合登录数据
DROP TABLE IF EXISTS user_joint_login;
RENAME TABLE user_joint_login_backup TO user_joint_login;

-- 回滚支付订单数据
DROP TABLE IF EXISTS pay_order;
RENAME TABLE pay_order_backup TO pay_order;

-- 回滚签到记录数据
DROP TABLE IF EXISTS user_check_in_record;
RENAME TABLE user_check_in_record_backup TO user_check_in_record;
```

## 5. 迁移执行计划

### 5.1 迁移时间安排
1. **准备阶段** (1天): 数据备份、脚本测试
2. **用户数据迁移** (2小时): 迁移用户相关表
3. **支付数据迁移** (3小时): 迁移支付相关表
4. **其他数据迁移** (2小时): 迁移其他业务表
5. **数据校验** (1小时): 完整性校验
6. **业务测试** (2小时): 功能测试验证

### 5.2 风险控制
- 迁移前完整备份所有数据
- 分批次迁移，每批次完成后校验
- 准备快速回滚方案
- 业务低峰期执行迁移
- 迁移过程中监控系统状态
