package com.hncboy.chatgpt.framework.pay.controller;

import com.hncboy.chatgpt.framework.pay.worker.UnifiedPayWorker;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 统一支付回调控制器
 * 
 * 功能特点:
 * 1. 统一的支付回调处理入口
 * 2. 支持所有支付渠道的回调
 * 3. 自动识别支付渠道和业务场景
 * 4. 完整的回调验证和处理
 * 5. 统一的响应格式
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@RequiredArgsConstructor
@Tag(name = "统一支付回调相关接口")
@RestController
@RequestMapping("/pay/callback")
public class PayCallbackController {

    private final UnifiedPayWorker unifiedPayWorker;

    @Operation(summary = "支付宝支付回调", description = "处理支付宝支付回调通知")
    @PostMapping("/alipay")
    public String alipayCallback(HttpServletRequest request, @RequestBody String callbackData) {
        try {
            log.info("收到支付宝支付回调: data={}", callbackData);
            
            String result = unifiedPayWorker.handlePayCallback("ALIPAY", "common", callbackData);
            
            log.info("支付宝支付回调处理完成: result={}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("支付宝支付回调处理失败: data={}", callbackData, e);
            return "FAIL";
        }
    }

    @Operation(summary = "微信支付回调", description = "处理微信支付回调通知")
    @PostMapping("/wechat")
    public String wechatCallback(HttpServletRequest request, @RequestBody String callbackData) {
        try {
            log.info("收到微信支付回调: data={}", callbackData);
            
            String result = unifiedPayWorker.handlePayCallback("WECHAT", "common", callbackData);
            
            log.info("微信支付回调处理完成: result={}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("微信支付回调处理失败: data={}", callbackData, e);
            return "FAIL";
        }
    }

    @Operation(summary = "SE支付回调", description = "处理SE支付回调通知")
    @PostMapping("/sepay")
    public String sepayCallback(HttpServletRequest request, @RequestBody String callbackData) {
        try {
            log.info("收到SE支付回调: data={}", callbackData);
            
            String result = unifiedPayWorker.handlePayCallback("SEPAY", "common", callbackData);
            
            log.info("SE支付回调处理完成: result={}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("SE支付回调处理失败: data={}", callbackData, e);
            return "FAIL";
        }
    }

    @Operation(summary = "Momo支付回调", description = "处理Momo支付回调通知")
    @PostMapping("/momo")
    public String momoCallback(HttpServletRequest request, @RequestBody String callbackData) {
        try {
            log.info("收到Momo支付回调: data={}", callbackData);
            
            String result = unifiedPayWorker.handlePayCallback("MOMO", "common", callbackData);
            
            log.info("Momo支付回调处理完成: result={}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("Momo支付回调处理失败: data={}", callbackData, e);
            return "FAIL";
        }
    }

    @Operation(summary = "通用支付回调", description = "通用的支付回调处理接口")
    @PostMapping("/{channel}")
    public String genericCallback(@PathVariable String channel, 
                                @RequestParam(defaultValue = "common") String businessScene,
                                HttpServletRequest request, 
                                @RequestBody String callbackData) {
        try {
            log.info("收到通用支付回调: channel={}, scene={}, data={}", channel, businessScene, callbackData);
            
            String result = unifiedPayWorker.handlePayCallback(channel.toUpperCase(), businessScene, callbackData);
            
            log.info("通用支付回调处理完成: channel={}, scene={}, result={}", channel, businessScene, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("通用支付回调处理失败: channel={}, scene={}, data={}", channel, businessScene, callbackData, e);
            return "FAIL";
        }
    }

    @Operation(summary = "支付回调健康检查", description = "检查支付回调服务是否正常")
    @GetMapping("/health")
    public String healthCheck() {
        return "OK";
    }

    // ==================== 业务场景专用回调 ====================

    @Operation(summary = "塔罗牌业务支付回调")
    @PostMapping("/tarot/{channel}")
    public String tarotCallback(@PathVariable String channel, 
                              HttpServletRequest request, 
                              @RequestBody String callbackData) {
        try {
            log.info("收到塔罗牌支付回调: channel={}, data={}", channel, callbackData);
            
            String result = unifiedPayWorker.handlePayCallback(channel.toUpperCase(), "tarot", callbackData);
            
            log.info("塔罗牌支付回调处理完成: channel={}, result={}", channel, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("塔罗牌支付回调处理失败: channel={}, data={}", channel, callbackData, e);
            return "FAIL";
        }
    }

    @Operation(summary = "AI对话业务支付回调")
    @PostMapping("/chatoi/{channel}")
    public String chatoiCallback(@PathVariable String channel, 
                               HttpServletRequest request, 
                               @RequestBody String callbackData) {
        try {
            log.info("收到AI对话支付回调: channel={}, data={}", channel, callbackData);
            
            String result = unifiedPayWorker.handlePayCallback(channel.toUpperCase(), "chatoi", callbackData);
            
            log.info("AI对话支付回调处理完成: channel={}, result={}", channel, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("AI对话支付回调处理失败: channel={}, data={}", channel, callbackData, e);
            return "FAIL";
        }
    }

    @Operation(summary = "紫微斗数业务支付回调")
    @PostMapping("/zns/{channel}")
    public String znsCallback(@PathVariable String channel, 
                            HttpServletRequest request, 
                            @RequestBody String callbackData) {
        try {
            log.info("收到紫微斗数支付回调: channel={}, data={}", channel, callbackData);
            
            String result = unifiedPayWorker.handlePayCallback(channel.toUpperCase(), "zns", callbackData);
            
            log.info("紫微斗数支付回调处理完成: channel={}, result={}", channel, result);
            
            return result;
            
        } catch (Exception e) {
            log.error("紫微斗数支付回调处理失败: channel={}, data={}", channel, callbackData, e);
            return "FAIL";
        }
    }
}
