package com.hncboy.chatgpt.framework.payment.controller;

import com.hncboy.chatgpt.front.framework.annotation.ApiAdminRestController;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.framework.payment.domain.dto.CreateOrderDTO;
import com.hncboy.chatgpt.framework.payment.domain.vo.PaymentOrderVO;
import com.hncboy.chatgpt.framework.payment.domain.vo.PaymentResultVO;
import com.hncboy.chatgpt.framework.payment.service.UnifiedPaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 统一支付控制器
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@AllArgsConstructor
@Tag(name = "统一支付相关接口")
@ApiAdminRestController("/payment")
@Slf4j
public class PaymentController {

    private final UnifiedPaymentService unifiedPaymentService;

    @Operation(summary = "创建支付订单")
    @PostMapping("/create")
    public R<PaymentResultVO> createPaymentOrder(@Validated @RequestBody CreateOrderDTO createOrderDTO) {
        PaymentResultVO result = unifiedPaymentService.createPaymentOrder(createOrderDTO);
        return R.data(result);
    }

    @Operation(summary = "支付宝支付")
    @PostMapping("/alipay/create")
    public R<PaymentResultVO> createAlipayOrder(@Validated @RequestBody CreateOrderDTO createOrderDTO) {
        PaymentResultVO result = unifiedPaymentService.createAlipayOrder(createOrderDTO);
        return R.data(result);
    }

    @Operation(summary = "微信支付")
    @PostMapping("/wechat/create")
    public R<PaymentResultVO> createWechatPayOrder(@Validated @RequestBody CreateOrderDTO createOrderDTO) {
        PaymentResultVO result = unifiedPaymentService.createWechatPayOrder(createOrderDTO);
        return R.data(result);
    }

    @Operation(summary = "SE支付")
    @PostMapping("/sepay/create")
    public R<PaymentResultVO> createSePayOrder(@Validated @RequestBody CreateOrderDTO createOrderDTO) {
        PaymentResultVO result = unifiedPaymentService.createSePayOrder(createOrderDTO);
        return R.data(result);
    }

    @Operation(summary = "Momo支付")
    @PostMapping("/momo/create")
    public R<PaymentResultVO> createMomoPayOrder(@Validated @RequestBody CreateOrderDTO createOrderDTO) {
        PaymentResultVO result = unifiedPaymentService.createMomoPayOrder(createOrderDTO);
        return R.data(result);
    }

    @Operation(summary = "查询支付订单")
    @GetMapping("/query/{orderNo}")
    public R<PaymentOrderVO> queryPaymentOrder(@PathVariable String orderNo) {
        PaymentOrderVO result = unifiedPaymentService.queryPaymentOrder(orderNo);
        return R.data(result);
    }

    @Operation(summary = "取消支付订单")
    @PostMapping("/cancel/{orderNo}")
    public R<Boolean> cancelPaymentOrder(@PathVariable String orderNo) {
        Boolean result = unifiedPaymentService.cancelPaymentOrder(orderNo);
        return R.data(result);
    }

    @Operation(summary = "申请退款")
    @PostMapping("/refund/{orderNo}")
    public R<Boolean> refundPaymentOrder(@PathVariable String orderNo,
                                       @RequestParam java.math.BigDecimal refundAmount,
                                       @RequestParam String reason) {
        Boolean result = unifiedPaymentService.refundPaymentOrder(orderNo, refundAmount, reason);
        return R.data(result);
    }

    @Operation(summary = "同步支付状态")
    @PostMapping("/sync/{orderNo}")
    public R<Boolean> syncPaymentStatus(@PathVariable String orderNo) {
        Boolean result = unifiedPaymentService.syncPaymentStatus(orderNo);
        return R.data(result);
    }
}
