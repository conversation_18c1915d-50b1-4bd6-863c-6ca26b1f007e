package com.hncboy.chatgpt.framework.pay.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付结果VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
public class PayResultVO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 第三方订单号
     */
    private String thirdPartyOrderNo;

    /**
     * 支付状态
     */
    private String status;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 实际支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 支付渠道
     */
    private String payChannel;

    /**
     * 支付方式
     */
    private String payMethod;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 是否支付成功
     */
    private boolean success;

    /**
     * 支付参数 (用于前端调起支付)
     */
    private String payParams;

    /**
     * 支付URL (用于跳转支付)
     */
    private String payUrl;

    /**
     * 二维码URL (用于扫码支付)
     */
    private String qrCodeUrl;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 扩展信息
     */
    private String extraInfo;
}
