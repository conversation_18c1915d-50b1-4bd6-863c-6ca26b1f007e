package com.hncboy.chatgpt.framework.payment.service.impl;

import cn.hutool.core.util.IdUtil;
import com.egzosn.pay.ali.bean.AliPayOrder;
import com.egzosn.pay.common.api.PayService;
import com.egzosn.pay.common.bean.PayOrder;
import com.egzosn.pay.common.bean.TransactionType;
import com.egzosn.pay.wx.bean.WxPayOrder;
import com.hncboy.chatgpt.api.service.PaymentChannelConfigService;
import com.hncboy.chatgpt.api.domain.entity.PaymentOrder;
import com.hncboy.chatgpt.api.service.PaymentOrderService;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.framework.payment.domain.dto.CreateOrderDTO;
import com.hncboy.chatgpt.framework.payment.domain.vo.PaymentOrderVO;
import com.hncboy.chatgpt.framework.payment.domain.vo.PaymentResultVO;
import com.hncboy.chatgpt.framework.payment.factory.PayServiceFactory;
import com.hncboy.chatgpt.framework.payment.service.UnifiedPaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一支付服务实现 - 直接使用pay-java-parent官方API
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Service
@Slf4j
public class UnifiedPaymentServiceImpl implements UnifiedPaymentService {
    
    @Autowired
    private PayServiceFactory payServiceFactory;

    @Autowired
    private PaymentOrderService paymentOrderService;

    @Autowired
    private PaymentChannelConfigService paymentChannelConfigService;
    
    @Override
    public PaymentResultVO createPaymentOrder(CreateOrderDTO createOrderDTO) {
        // 根据业务场景和金额自动选择最优支付渠道
        String optimalChannel = selectOptimalChannel(createOrderDTO);
        createOrderDTO.setPaymentChannel(optimalChannel);
        
        switch (optimalChannel.toUpperCase()) {
            case "ALIPAY":
                return createAlipayOrder(createOrderDTO);
            case "WECHAT":
                return createWechatPayOrder(createOrderDTO);
            case "SEPAY":
                return createSePayOrder(createOrderDTO);
            case "MOMO":
                return createMomoPayOrder(createOrderDTO);
            default:
                throw new ServiceException("不支持的支付渠道: " + optimalChannel);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentResultVO createAlipayOrder(CreateOrderDTO createOrderDTO) {
        try {
            // 1. 生成订单号
            String orderNo = generateOrderNo();
            
            // 2. 获取支付宝官方服务
            PayService payService = payServiceFactory.getPayService("ALIPAY", createOrderDTO.getBusinessScene());
            
            // 3. 构建支付宝官方订单对象
            AliPayOrder aliPayOrder = new AliPayOrder();
            aliPayOrder.setSubject(createOrderDTO.getProductName());
            aliPayOrder.setBody(createOrderDTO.getDescription());
            aliPayOrder.setPrice(createOrderDTO.getAmount());
            aliPayOrder.setOutTradeNo(orderNo);
            aliPayOrder.setTransactionType(TransactionType.NATIVE);
            
            // 4. 调用官方支付接口
            Map<String, Object> orderInfo = payService.orderInfo(aliPayOrder);
            String payUrl = (String) orderInfo.get("qr_code");
            
            // 5. 保存订单到统一订单表
            PaymentOrder order = buildPaymentOrder(createOrderDTO, orderNo, "ALIPAY");
            order.setThirdPartyOrderNo(orderNo);
            paymentOrderService.save(order);
            
            // 6. 返回支付结果
            return PaymentResultVO.builder()
                    .orderNo(orderNo)
                    .payUrl(payUrl)
                    .qrCode(payUrl)
                    .expireTime(LocalDateTime.now().plusMinutes(15))
                    .paymentChannel("ALIPAY")
                    .paymentMethod("SCAN")
                    .thirdPartyOrderNo(orderNo)
                    .build();
                    
        } catch (Exception e) {
            log.error("支付宝支付创建失败: orderDTO={}", createOrderDTO, e);
            throw new ServiceException("支付宝支付创建失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentResultVO createWechatPayOrder(CreateOrderDTO createOrderDTO) {
        try {
            // 1. 生成订单号
            String orderNo = generateOrderNo();
            
            // 2. 获取微信支付官方服务
            PayService payService = payServiceFactory.getPayService("WECHAT", createOrderDTO.getBusinessScene());
            
            // 3. 构建微信支付官方订单对象
            WxPayOrder wxPayOrder = new WxPayOrder();
            wxPayOrder.setBody(createOrderDTO.getProductName());
            wxPayOrder.setDetail(createOrderDTO.getDescription());
            wxPayOrder.setTotalFee(createOrderDTO.getAmount().multiply(new BigDecimal("100")).intValue()); // 转为分
            wxPayOrder.setOutTradeNo(orderNo);
            wxPayOrder.setSpbillCreateIp(createOrderDTO.getClientIp());
            wxPayOrder.setTransactionType(TransactionType.NATIVE);
            
            // 4. 调用官方支付接口
            Map<String, Object> orderInfo = payService.orderInfo(wxPayOrder);
            String payUrl = (String) orderInfo.get("code_url");
            
            // 5. 保存订单到统一订单表
            PaymentOrder order = buildPaymentOrder(createOrderDTO, orderNo, "WECHAT");
            order.setThirdPartyOrderNo(orderNo);
            paymentOrderService.save(order);
            
            // 6. 返回支付结果
            return PaymentResultVO.builder()
                    .orderNo(orderNo)
                    .payUrl(payUrl)
                    .qrCode(payUrl)
                    .expireTime(LocalDateTime.now().plusMinutes(15))
                    .paymentChannel("WECHAT")
                    .paymentMethod("SCAN")
                    .thirdPartyOrderNo(orderNo)
                    .build();
                    
        } catch (Exception e) {
            log.error("微信支付创建失败: orderDTO={}", createOrderDTO, e);
            throw new ServiceException("微信支付创建失败: " + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentResultVO createSePayOrder(CreateOrderDTO createOrderDTO) {
        try {
            // 1. 生成订单号
            String orderNo = generateOrderNo();

            // 2. 获取SE支付配置
            PaymentChannelConfig config = paymentChannelConfigService.getByChannelAndScene("SEPAY", createOrderDTO.getBusinessScene());
            if (config == null || config.getEnabled() != 1) {
                throw new ServiceException("SE支付渠道未配置或已禁用");
            }

            // 3. 解析SE支付配置
            String bankName = "VCB"; // 默认银行
            String accountNumber = config.getMerchantId(); // 使用商户号作为账号
            String seSecret = config.getAppSecret(); // SE支付密钥

            // 4. 构建SE支付订单
            PaymentOrder order = buildPaymentOrder(createOrderDTO, orderNo, "SEPAY");
            order.setThirdPartyOrderNo(orderNo);

            // 5. 生成SE支付内容签名
            String content = hmacSha256(seSecret, orderNo);
            order.setPaymentParams(content);

            // 6. 生成SE支付二维码URL
            String qrCodeUrl = String.format("https://qr.sepay.vn/img?acc=%s&bank=%s&amount=%s&des=%s&content=%s",
                    accountNumber,
                    bankName,
                    order.getAmount(),
                    orderNo,
                    content);

            // 7. 保存订单
            paymentOrderService.save(order);

            // 8. 返回支付结果
            return PaymentResultVO.builder()
                    .orderNo(orderNo)
                    .payUrl(qrCodeUrl)
                    .qrCode(qrCodeUrl)
                    .expireTime(LocalDateTime.now().plusMinutes(10))
                    .paymentChannel("SEPAY")
                    .paymentMethod("BANK_TRANSFER")
                    .thirdPartyOrderNo(orderNo)
                    .build();

        } catch (Exception e) {
            log.error("SE支付创建失败: orderDTO={}", createOrderDTO, e);
            throw new ServiceException("SE支付创建失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentResultVO createMomoPayOrder(CreateOrderDTO createOrderDTO) {
        try {
            // 1. 生成订单号
            String orderNo = generateOrderNo();

            // 2. 获取Momo支付配置
            PaymentChannelConfig config = paymentChannelConfigService.getByChannelAndScene("MOMO", createOrderDTO.getBusinessScene());
            if (config == null || config.getEnabled() != 1) {
                throw new ServiceException("Momo支付渠道未配置或已禁用");
            }

            // 3. 构建Momo支付订单
            PaymentOrder order = buildPaymentOrder(createOrderDTO, orderNo, "MOMO");
            order.setThirdPartyOrderNo(orderNo);
            paymentOrderService.save(order);

            // 4. 调用Momo支付API (基于现有实现)
            String partnerCode = config.getMerchantId();
            String accessKey = config.getAppId();
            String secretKey = config.getAppSecret();
            String requestId = orderNo;
            String amount = order.getAmount().toString();
            String orderId = orderNo;
            String orderInfo = createOrderDTO.getProductName();
            String redirectUrl = config.getReturnUrl();
            String ipnUrl = config.getNotifyUrl();
            String extraData = "";

            // 5. 生成Momo签名
            String rawHash = "accessKey=" + accessKey +
                    "&amount=" + amount +
                    "&extraData=" + extraData +
                    "&ipnUrl=" + ipnUrl +
                    "&orderId=" + orderId +
                    "&orderInfo=" + orderInfo +
                    "&partnerCode=" + partnerCode +
                    "&redirectUrl=" + redirectUrl +
                    "&requestId=" + requestId +
                    "&requestType=captureWallet";

            String signature = hmacSha256(secretKey, rawHash);

            // 6. 构建Momo支付URL
            String payUrl = String.format("https://payment.momo.vn/v2/gateway/api/create?partnerCode=%s&orderId=%s&amount=%s&signature=%s",
                    partnerCode, orderId, amount, signature);

            // 7. 返回支付结果
            return PaymentResultVO.builder()
                    .orderNo(orderNo)
                    .payUrl(payUrl)
                    .qrCode(payUrl)
                    .expireTime(LocalDateTime.now().plusMinutes(15))
                    .paymentChannel("MOMO")
                    .paymentMethod("WALLET")
                    .thirdPartyOrderNo(orderNo)
                    .build();

        } catch (Exception e) {
            log.error("Momo支付创建失败: orderDTO={}", createOrderDTO, e);
            throw new ServiceException("Momo支付创建失败: " + e.getMessage());
        }
    }
    
    @Override
    public PaymentOrderVO queryPaymentOrder(String orderNo) {
        PaymentOrder order = paymentOrderService.getByOrderNo(orderNo);
        if (order == null) {
            throw new ServiceException("订单不存在: " + orderNo);
        }
        
        PaymentOrderVO vo = new PaymentOrderVO();
        BeanUtils.copyProperties(order, vo);
        return vo;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handlePaymentCallback(String channelCode, HttpServletRequest request) {
        try {
            log.info("处理支付回调: channelCode={}", channelCode);

            switch (channelCode.toUpperCase()) {
                case "ALIPAY":
                    return handleAlipayCallback(request);
                case "WECHAT":
                    return handleWechatCallback(request);
                case "SEPAY":
                    return handleSePayCallback(request);
                case "MOMO":
                    return handleMomoCallback(request);
                default:
                    log.error("不支持的支付回调渠道: {}", channelCode);
                    return false;
            }
        } catch (Exception e) {
            log.error("支付回调处理失败: channelCode={}", channelCode, e);
            return false;
        }
    }

    /**
     * 处理支付宝回调
     */
    private Boolean handleAlipayCallback(HttpServletRequest request) {
        try {
            // 1. 获取回调参数
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();
            for (String name : requestParams.keySet()) {
                params.put(name, request.getParameter(name));
            }

            // 2. 验证签名 (使用pay-java-parent的验签逻辑)
            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");

            log.info("支付宝回调: outTradeNo={}, tradeStatus={}", outTradeNo, tradeStatus);

            // 3. 支付成功处理
            if ("TRADE_SUCCESS".equals(tradeStatus)) {
                String transactionId = params.get("trade_no");
                return paymentOrderService.updateToPaid(outTradeNo, transactionId);
            }

            return true;
        } catch (Exception e) {
            log.error("处理支付宝回调失败", e);
            return false;
        }
    }

    /**
     * 处理微信支付回调
     */
    private Boolean handleWechatCallback(HttpServletRequest request) {
        try {
            // 1. 获取回调参数 (微信支付回调是XML格式)
            String outTradeNo = request.getParameter("out_trade_no");
            String transactionId = request.getParameter("transaction_id");
            String resultCode = request.getParameter("result_code");

            log.info("微信支付回调: outTradeNo={}, resultCode={}", outTradeNo, resultCode);

            // 2. 支付成功处理
            if ("SUCCESS".equals(resultCode)) {
                return paymentOrderService.updateToPaid(outTradeNo, transactionId);
            }

            return true;
        } catch (Exception e) {
            log.error("处理微信支付回调失败", e);
            return false;
        }
    }

    /**
     * 处理SE支付回调
     */
    private Boolean handleSePayCallback(HttpServletRequest request) {
        try {
            // 1. 获取SE支付回调参数
            String content = request.getParameter("content");
            String transferAmount = request.getParameter("transferAmount");
            String transactionDate = request.getParameter("transactionDate");

            log.info("SE支付回调: content={}, transferAmount={}", content, transferAmount);

            // 2. 根据content查找订单
            PaymentOrder order = paymentOrderService.getByPaymentParams(content);
            if (order == null) {
                log.error("SE支付回调: 订单不存在, content={}", content);
                return false;
            }

            // 3. 更新订单状态
            String transactionId = "SE_" + System.currentTimeMillis();
            return paymentOrderService.updateToPaid(order.getOrderNo(), transactionId);

        } catch (Exception e) {
            log.error("处理SE支付回调失败", e);
            return false;
        }
    }

    /**
     * 处理Momo支付回调
     */
    private Boolean handleMomoCallback(HttpServletRequest request) {
        try {
            // 1. 获取Momo回调参数
            String orderId = request.getParameter("orderId");
            String errorCode = request.getParameter("errorCode");
            String transId = request.getParameter("transId");

            log.info("Momo支付回调: orderId={}, errorCode={}", orderId, errorCode);

            // 2. 支付成功处理
            if ("0".equals(errorCode)) {
                return paymentOrderService.updateToPaid(orderId, transId);
            }

            return true;
        } catch (Exception e) {
            log.error("处理Momo支付回调失败", e);
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelPaymentOrder(String orderNo) {
        try {
            PaymentOrder order = paymentOrderService.getByOrderNo(orderNo);
            if (order == null) {
                throw new ServiceException("订单不存在: " + orderNo);
            }

            if (order.getStatus() != 0) {
                throw new ServiceException("只能取消待支付订单");
            }

            return paymentOrderService.updateToCancelled(orderNo, "用户主动取消");
        } catch (Exception e) {
            log.error("取消支付订单失败: orderNo={}", orderNo, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refundPaymentOrder(String orderNo, BigDecimal refundAmount, String reason) {
        try {
            PaymentOrder order = paymentOrderService.getByOrderNo(orderNo);
            if (order == null) {
                throw new ServiceException("订单不存在: " + orderNo);
            }

            if (order.getStatus() != 1) {
                throw new ServiceException("只能退款已支付订单");
            }

            // 调用第三方退款接口
            boolean refundSuccess = processThirdPartyRefund(order, refundAmount, reason);

            if (refundSuccess) {
                return paymentOrderService.updateToRefunded(orderNo, refundAmount, reason);
            }

            return false;
        } catch (Exception e) {
            log.error("申请退款失败: orderNo={}, amount={}", orderNo, refundAmount, e);
            return false;
        }
    }

    @Override
    public Boolean syncPaymentStatus(String orderNo) {
        try {
            PaymentOrder order = paymentOrderService.getByOrderNo(orderNo);
            if (order == null) {
                throw new ServiceException("订单不存在: " + orderNo);
            }

            // 调用第三方查询接口同步状态
            String paymentStatus = queryThirdPartyPaymentStatus(order);

            if ("SUCCESS".equals(paymentStatus) && order.getStatus() == 0) {
                String transactionId = "SYNC_" + System.currentTimeMillis();
                return paymentOrderService.updateToPaid(orderNo, transactionId);
            }

            return true;
        } catch (Exception e) {
            log.error("同步支付状态失败: orderNo={}", orderNo, e);
            return false;
        }
    }
    
    /**
     * 选择最优支付渠道
     */
    private String selectOptimalChannel(CreateOrderDTO createOrderDTO) {
        // 简单策略：根据币种选择
        if ("VND".equals(createOrderDTO.getCurrency())) {
            return "SEPAY";
        } else {
            return "ALIPAY";
        }
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "PAY" + System.currentTimeMillis() + IdUtil.randomNumbers(4);
    }
    
    /**
     * 构建支付订单
     */
    private PaymentOrder buildPaymentOrder(CreateOrderDTO createOrderDTO, String orderNo, String paymentChannel) {
        PaymentOrder order = new PaymentOrder();
        order.setOrderNo(orderNo);
        order.setUserId(createOrderDTO.getUserId());
        order.setProductId(createOrderDTO.getProductId());
        order.setProductName(createOrderDTO.getProductName());
        order.setProductType(createOrderDTO.getProductType());
        order.setBusinessScene(createOrderDTO.getBusinessScene());
        order.setPaymentChannel(paymentChannel);
        order.setAmount(createOrderDTO.getAmount());
        order.setCurrency(createOrderDTO.getCurrency());
        order.setExchangeRate(BigDecimal.ONE); // 汇率设为1
        order.setStatus(0); // 待支付
        order.setExpireTime(LocalDateTime.now().plusMinutes(15));
        order.setPaymentIp(createOrderDTO.getClientIp());
        order.setRemark(createOrderDTO.getRemark());
        return order;
    }

    /**
     * HMAC SHA256签名
     */
    private String hmacSha256(String key, String data) {
        try {
            javax.crypto.Mac mac = javax.crypto.Mac.getInstance("HmacSHA256");
            javax.crypto.spec.SecretKeySpec secretKeySpec = new javax.crypto.spec.SecretKeySpec(key.getBytes(), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hash = mac.doFinal(data.getBytes());
            return bytesToHex(hash);
        } catch (Exception e) {
            log.error("HMAC SHA256签名失败", e);
            return "";
        }
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 处理第三方退款
     */
    private boolean processThirdPartyRefund(PaymentOrder order, BigDecimal refundAmount, String reason) {
        try {
            switch (order.getPaymentChannel().toUpperCase()) {
                case "ALIPAY":
                    return processAlipayRefund(order, refundAmount, reason);
                case "WECHAT":
                    return processWechatRefund(order, refundAmount, reason);
                case "SEPAY":
                case "MOMO":
                    // SE支付和Momo支付通常需要人工处理退款
                    log.info("{}退款需要人工处理: orderNo={}", order.getPaymentChannel(), order.getOrderNo());
                    return true;
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("处理第三方退款失败: orderNo={}", order.getOrderNo(), e);
            return false;
        }
    }

    /**
     * 支付宝退款
     */
    private boolean processAlipayRefund(PaymentOrder order, BigDecimal refundAmount, String reason) {
        try {
            // 使用pay-java-parent的支付宝退款接口
            PayService payService = payServiceFactory.getPayService("ALIPAY", order.getBusinessScene());
            // TODO: 调用支付宝退款接口
            log.info("支付宝退款: orderNo={}, amount={}", order.getOrderNo(), refundAmount);
            return true;
        } catch (Exception e) {
            log.error("支付宝退款失败: orderNo={}", order.getOrderNo(), e);
            return false;
        }
    }

    /**
     * 微信退款
     */
    private boolean processWechatRefund(PaymentOrder order, BigDecimal refundAmount, String reason) {
        try {
            // 使用pay-java-parent的微信退款接口
            PayService payService = payServiceFactory.getPayService("WECHAT", order.getBusinessScene());
            // TODO: 调用微信退款接口
            log.info("微信退款: orderNo={}, amount={}", order.getOrderNo(), refundAmount);
            return true;
        } catch (Exception e) {
            log.error("微信退款失败: orderNo={}", order.getOrderNo(), e);
            return false;
        }
    }

    /**
     * 查询第三方支付状态
     */
    private String queryThirdPartyPaymentStatus(PaymentOrder order) {
        try {
            switch (order.getPaymentChannel().toUpperCase()) {
                case "ALIPAY":
                case "WECHAT":
                    // 使用pay-java-parent的查询接口
                    PayService payService = payServiceFactory.getPayService(order.getPaymentChannel(), order.getBusinessScene());
                    // TODO: 调用查询接口
                    return "SUCCESS";
                default:
                    return "UNKNOWN";
            }
        } catch (Exception e) {
            log.error("查询第三方支付状态失败: orderNo={}", order.getOrderNo(), e);
            return "ERROR";
        }
    }
}
