package com.hncboy.chatgpt.framework.ai.chat.worker;

import com.hncboy.chatgpt.db.entity.chat.ChatMessage;
import com.hncboy.chatgpt.framework.cache.manager.MultiLevelCacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Spring AI聊天业务逻辑Worker
 * 
 * 功能特点:
 * 1. 集成Spring AI框架，支持多种AI模型
 * 2. 支持流式和非流式对话
 * 3. 智能上下文管理和历史记录处理
 * 4. 高可用性设计，支持模型切换和降级
 * 5. 性能优化，缓存模型配置和常用响应
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SpringAIChatWorker {

    private final MultiLevelCacheManager cacheManager;

    // 缓存前缀
    private static final String MODEL_CACHE_PREFIX = "ai:model:";
    private static final String RESPONSE_CACHE_PREFIX = "ai:response:";
    private static final long MODEL_CACHE_TTL = 3600; // 1小时
    private static final long RESPONSE_CACHE_TTL = 1800; // 30分钟

    /**
     * 生成AI响应 (非流式)
     *
     * @param userInput 用户输入
     * @param messageHistory 消息历史
     * @param systemPrompt 系统提示词
     * @param modelName 模型名称
     * @return AI响应
     */
    public String generateResponse(String userInput, List<ChatMessage> messageHistory, 
                                 String systemPrompt, String modelName) {
        try {
            log.info("生成AI响应: model={}, inputLength={}, historySize={}", 
                    modelName, userInput.length(), messageHistory.size());

            // 1. 检查缓存 (对于相同的输入和上下文)
            String cacheKey = buildResponseCacheKey(userInput, messageHistory, systemPrompt, modelName);
            Object cached = cacheManager.get(cacheKey);
            if (cached != null) {
                log.debug("AI响应缓存命中: key={}", cacheKey);
                return cached.toString();
            }

            // 2. 构建对话上下文
            String context = buildConversationContext(messageHistory, systemPrompt);

            // 3. 调用AI模型生成响应
            String response = callAIModel(userInput, context, modelName);

            // 4. 缓存响应结果 (对于通用问题)
            if (isCacheableResponse(userInput, response)) {
                cacheManager.set(cacheKey, response, RESPONSE_CACHE_TTL);
            }

            log.info("AI响应生成成功: model={}, responseLength={}", modelName, response.length());
            return response;

        } catch (Exception e) {
            log.error("生成AI响应失败: model={}, input={}", modelName, userInput, e);
            return handleAIError(e, userInput);
        }
    }

    /**
     * 生成AI流式响应
     *
     * @param userInput 用户输入
     * @param messageHistory 消息历史
     * @param systemPrompt 系统提示词
     * @param modelName 模型名称
     * @return 流式响应
     */
    public Flux<String> generateStreamResponse(String userInput, List<ChatMessage> messageHistory,
                                             String systemPrompt, String modelName) {
        try {
            log.info("生成AI流式响应: model={}, inputLength={}, historySize={}", 
                    modelName, userInput.length(), messageHistory.size());

            // 1. 构建对话上下文
            String context = buildConversationContext(messageHistory, systemPrompt);

            // 2. 调用AI模型生成流式响应
            return callAIModelStream(userInput, context, modelName)
                    .doOnNext(chunk -> log.debug("AI流式响应块: {}", chunk))
                    .doOnComplete(() -> log.info("AI流式响应完成: model={}", modelName))
                    .doOnError(e -> log.error("AI流式响应失败: model={}", modelName, e))
                    .onErrorResume(e -> handleAIStreamError(e, userInput));

        } catch (Exception e) {
            log.error("启动AI流式响应失败: model={}, input={}", modelName, userInput, e);
            return Flux.error(new RuntimeException("AI流式响应失败: " + e.getMessage()));
        }
    }

    /**
     * 获取可用的AI模型列表
     *
     * @return 模型列表
     */
    public String[] getAvailableModels() {
        try {
            // 从缓存获取
            String cacheKey = MODEL_CACHE_PREFIX + "available";
            Object cached = cacheManager.get(cacheKey);
            if (cached instanceof String[]) {
                return (String[]) cached;
            }

            // 查询可用模型
            String[] models = queryAvailableModels();
            
            // 缓存结果
            cacheManager.set(cacheKey, models, MODEL_CACHE_TTL);
            
            return models;

        } catch (Exception e) {
            log.error("获取可用AI模型失败", e);
            return getDefaultModels();
        }
    }

    /**
     * 检查模型是否可用
     *
     * @param modelName 模型名称
     * @return 是否可用
     */
    public boolean isModelAvailable(String modelName) {
        try {
            // 从缓存获取模型状态
            String cacheKey = MODEL_CACHE_PREFIX + "status:" + modelName;
            Object cached = cacheManager.get(cacheKey);
            if (cached instanceof Boolean) {
                return (Boolean) cached;
            }

            // 检查模型状态
            boolean available = checkModelStatus(modelName);
            
            // 缓存状态 (较短的缓存时间)
            cacheManager.set(cacheKey, available, 300); // 5分钟
            
            return available;

        } catch (Exception e) {
            log.warn("检查模型可用性失败: model={}", modelName, e);
            return false;
        }
    }

    /**
     * 获取模型配置信息
     *
     * @param modelName 模型名称
     * @return 模型配置
     */
    public ModelConfig getModelConfig(String modelName) {
        try {
            // 从缓存获取
            String cacheKey = MODEL_CACHE_PREFIX + "config:" + modelName;
            Object cached = cacheManager.get(cacheKey);
            if (cached instanceof ModelConfig) {
                return (ModelConfig) cached;
            }

            // 查询模型配置
            ModelConfig config = queryModelConfig(modelName);
            
            // 缓存配置
            cacheManager.set(cacheKey, config, MODEL_CACHE_TTL);
            
            return config;

        } catch (Exception e) {
            log.error("获取模型配置失败: model={}", modelName, e);
            return getDefaultModelConfig(modelName);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 构建对话上下文
     */
    private String buildConversationContext(List<ChatMessage> messageHistory, String systemPrompt) {
        StringBuilder context = new StringBuilder();
        
        // 添加系统提示词
        if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
            context.append("System: ").append(systemPrompt).append("\n\n");
        }

        // 添加历史消息 (最近的几条)
        int maxHistory = 10; // 最多保留10条历史消息
        int startIndex = Math.max(0, messageHistory.size() - maxHistory);
        
        for (int i = startIndex; i < messageHistory.size(); i++) {
            ChatMessage message = messageHistory.get(i);
            String role = message.getMessageType() == 1 ? "User" : "Assistant";
            context.append(role).append(": ").append(message.getContent()).append("\n");
        }

        return context.toString();
    }

    /**
     * 调用AI模型 (非流式)
     */
    private String callAIModel(String userInput, String context, String modelName) {
        // 这里应该调用实际的Spring AI接口
        // 暂时模拟AI响应
        
        try {
            // 模拟AI处理时间
            Thread.sleep(1000 + (int)(Math.random() * 2000));
            
            // 模拟AI响应
            return generateMockResponse(userInput, context, modelName);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("AI调用被中断", e);
        }
    }

    /**
     * 调用AI模型 (流式)
     */
    private Flux<String> callAIModelStream(String userInput, String context, String modelName) {
        // 这里应该调用实际的Spring AI流式接口
        // 暂时模拟流式响应
        
        return Flux.interval(Duration.ofMillis(100))
                .take(20)
                .map(i -> generateMockStreamChunk(userInput, i.intValue()))
                .onErrorResume(e -> Flux.just("抱歉，AI服务暂时不可用。"));
    }

    /**
     * 生成模拟响应
     */
    private String generateMockResponse(String userInput, String context, String modelName) {
        // 根据用户输入生成相应的模拟响应
        if (userInput.contains("你好") || userInput.contains("hello")) {
            return "你好！我是AI助手，很高兴为您服务。有什么我可以帮助您的吗？";
        } else if (userInput.contains("天气")) {
            return "抱歉，我无法获取实时天气信息。建议您查看天气预报应用或网站获取准确的天气信息。";
        } else if (userInput.contains("时间")) {
            return "当前时间是 " + java.time.LocalDateTime.now().toString() + "。";
        } else {
            return "感谢您的问题。基于您的输入，我理解您想了解关于\"" + userInput + "\"的信息。" +
                   "作为AI助手，我会尽力为您提供有用的回答和建议。请问您还有其他问题吗？";
        }
    }

    /**
     * 生成模拟流式响应块
     */
    private String generateMockStreamChunk(String userInput, int chunkIndex) {
        String[] chunks = {
            "感谢", "您的", "问题", "。", "我", "正在", "思考", "如何", "最好", "地",
            "回答", "您", "关于", "\"" + userInput + "\"", "的", "询问", "。", "请", "稍等", "..."
        };
        
        if (chunkIndex < chunks.length) {
            return chunks[chunkIndex];
        } else {
            return "";
        }
    }

    /**
     * 构建响应缓存键
     */
    private String buildResponseCacheKey(String userInput, List<ChatMessage> messageHistory, 
                                       String systemPrompt, String modelName) {
        // 简化的缓存键生成逻辑
        int contextHash = (systemPrompt + userInput + modelName).hashCode();
        return RESPONSE_CACHE_PREFIX + Math.abs(contextHash);
    }

    /**
     * 判断响应是否可缓存
     */
    private boolean isCacheableResponse(String userInput, String response) {
        // 简单的缓存策略：常见问题可以缓存
        return userInput.length() < 100 && 
               (userInput.contains("你好") || userInput.contains("hello") || 
                userInput.contains("什么是") || userInput.contains("how to"));
    }

    /**
     * 处理AI错误
     */
    private String handleAIError(Exception e, String userInput) {
        log.error("AI调用失败，启用降级响应: input={}", userInput, e);
        
        // 降级响应
        return "抱歉，AI服务暂时不可用。我们正在努力修复问题，请稍后再试。" +
               "如果您有紧急问题，请联系客服获得帮助。";
    }

    /**
     * 处理AI流式错误
     */
    private Flux<String> handleAIStreamError(Exception e, String userInput) {
        log.error("AI流式调用失败，启用降级响应: input={}", userInput, e);
        
        return Flux.just("抱歉，", "AI", "服务", "暂时", "不可用", "。", "请", "稍后", "再试", "。");
    }

    /**
     * 查询可用模型
     */
    private String[] queryAvailableModels() {
        // 这里应该查询实际可用的模型
        return new String[]{
            "gpt-3.5-turbo", "gpt-4", "claude-3", "gemini-pro", "qwen-max"
        };
    }

    /**
     * 获取默认模型列表
     */
    private String[] getDefaultModels() {
        return new String[]{"gpt-3.5-turbo"};
    }

    /**
     * 检查模型状态
     */
    private boolean checkModelStatus(String modelName) {
        // 这里应该检查实际的模型状态
        // 暂时返回true
        return true;
    }

    /**
     * 查询模型配置
     */
    private ModelConfig queryModelConfig(String modelName) {
        // 这里应该查询实际的模型配置
        ModelConfig config = new ModelConfig();
        config.setModelName(modelName);
        config.setMaxTokens(2000);
        config.setTemperature(0.7);
        config.setTopP(0.9);
        return config;
    }

    /**
     * 获取默认模型配置
     */
    private ModelConfig getDefaultModelConfig(String modelName) {
        ModelConfig config = new ModelConfig();
        config.setModelName(modelName);
        config.setMaxTokens(1000);
        config.setTemperature(0.5);
        config.setTopP(0.8);
        return config;
    }

    /**
     * 模型配置类
     */
    public static class ModelConfig {
        private String modelName;
        private Integer maxTokens;
        private Double temperature;
        private Double topP;

        // Getters and Setters
        public String getModelName() { return modelName; }
        public void setModelName(String modelName) { this.modelName = modelName; }
        public Integer getMaxTokens() { return maxTokens; }
        public void setMaxTokens(Integer maxTokens) { this.maxTokens = maxTokens; }
        public Double getTemperature() { return temperature; }
        public void setTemperature(Double temperature) { this.temperature = temperature; }
        public Double getTopP() { return topP; }
        public void setTopP(Double topP) { this.topP = topP; }
    }
}
