package com.hncboy.chatgpt.framework.pay.worker;

import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.db.entity.pay.PayOrder;
import com.hncboy.chatgpt.db.service.pay.PayOrderService;
import com.hncboy.chatgpt.framework.pay.domain.dto.CreateOrderDTO;
import com.hncboy.chatgpt.framework.pay.domain.vo.PayResultVO;
import com.hncboy.chatgpt.framework.pay.factory.PayServiceFactory;
import com.hncboy.chatgpt.common.enums.PayChannelEnum;
import com.hncboy.chatgpt.common.enums.PayStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 统一支付业务逻辑Worker
 * 
 * 功能特点:
 * 1. 统一支付入口，自备区分业务场景能力
 * 2. 支持多种支付渠道 (支付宝/微信/SE支付/Momo支付)
 * 3. 高可用性设计，支持重试和容错
 * 4. 性能优化，适当缓存配置信息
 * 5. 完整复刻原有支付功能
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UnifiedPayWorker {

    private final PayOrderService payOrderService;
    private final PayServiceFactory payServiceFactory;

    /**
     * 创建支付订单 (统一入口，自动识别业务场景)
     *
     * @param createOrderDTO 创建订单请求
     * @return 支付结果
     */
    @Transactional(rollbackFor = Exception.class)
    public PayResultVO createPayOrder(CreateOrderDTO createOrderDTO) {
        try {
            // 1. 参数验证
            validateCreateOrderRequest(createOrderDTO);

            // 2. 设置用户ID (如果未设置)
            if (createOrderDTO.getUserId() == null) {
                createOrderDTO.setUserId(getCurrentUserId());
            }

            // 3. 生成订单号
            String orderNo = generateOrderNo(createOrderDTO.getBusinessScene());

            // 4. 创建支付订单记录
            PayOrder payOrder = buildPayOrder(createOrderDTO, orderNo);
            payOrderService.save(payOrder);

            // 5. 调用对应支付渠道创建支付
            PayResultVO payResult = createChannelPayment(payOrder);

            // 6. 更新订单支付信息
            updateOrderPayInfo(payOrder, payResult);

            log.info("创建支付订单成功: orderNo={}, channel={}, scene={}, amount={}",
                    orderNo, createOrderDTO.getPayChannel(), createOrderDTO.getBusinessScene(),
                    createOrderDTO.getAmount());

            return payResult;

        } catch (Exception e) {
            log.error("创建支付订单失败: {}", createOrderDTO, e);
            throw new RuntimeException("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单状态
     *
     * @param orderNo 订单号
     * @return 支付结果
     */
    @Cacheable(value = "payOrder", key = "#orderNo", unless = "#result == null")
    public PayResultVO queryPayOrder(String orderNo) {
        try {
            PayOrder payOrder = payOrderService.getByOrderNo(orderNo);
            if (payOrder == null) {
                throw new RuntimeException("订单不存在: " + orderNo);
            }

            // 如果订单状态为待支付，主动查询第三方支付状态
            if (PayStatusEnum.PENDING.getCode().equals(payOrder.getStatus())) {
                queryAndUpdateOrderStatus(payOrder);
            }

            return buildPayResultVO(payOrder);

        } catch (Exception e) {
            log.error("查询支付订单失败: orderNo={}", orderNo, e);
            throw new RuntimeException("查询支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 处理支付回调 (统一回调处理，自动识别支付渠道)
     *
     * @param payChannel 支付渠道
     * @param businessScene 业务场景
     * @param callbackData 回调数据
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public String handlePayCallback(String payChannel, String businessScene, String callbackData) {
        try {
            log.info("处理支付回调: channel={}, scene={}, data={}", payChannel, businessScene, callbackData);

            // 1. 验证回调数据
            boolean isValid = validateCallback(payChannel, callbackData);
            if (!isValid) {
                log.warn("支付回调验证失败: channel={}, data={}", payChannel, callbackData);
                return "FAIL";
            }

            // 2. 解析回调数据，获取订单信息
            PayOrder payOrder = parseCallbackData(payChannel, callbackData);
            if (payOrder == null) {
                log.warn("解析回调数据失败: channel={}, data={}", payChannel, callbackData);
                return "FAIL";
            }

            // 3. 更新订单状态
            updateOrderFromCallback(payOrder, callbackData);

            // 4. 执行业务逻辑 (根据业务场景执行不同逻辑)
            executeBusinessLogic(payOrder);

            log.info("支付回调处理成功: orderNo={}, channel={}, scene={}", 
                    payOrder.getOrderNo(), payChannel, businessScene);

            return "SUCCESS";

        } catch (Exception e) {
            log.error("处理支付回调失败: channel={}, scene={}, data={}", payChannel, businessScene, callbackData, e);
            return "FAIL";
        }
    }

    /**
     * 申请退款
     *
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款结果
     */
    @Transactional(rollbackFor = Exception.class)
    public PayResultVO refundOrder(String orderNo, BigDecimal refundAmount, String refundReason) {
        try {
            PayOrder payOrder = payOrderService.getByOrderNo(orderNo);
            if (payOrder == null) {
                throw new RuntimeException("订单不存在: " + orderNo);
            }

            if (!PayStatusEnum.PAID.getCode().equals(payOrder.getStatus())) {
                throw new RuntimeException("订单状态不支持退款: " + payOrder.getStatus());
            }

            // 调用对应支付渠道退款接口
            PayResultVO refundResult = processChannelRefund(payOrder, refundAmount, refundReason);

            // 更新订单状态
            if (refundResult.isSuccess()) {
                payOrder.setStatus(PayStatusEnum.REFUNDED.getCode());
                payOrder.setRemark(refundReason);
                payOrderService.updateById(payOrder);
            }

            log.info("申请退款完成: orderNo={}, amount={}, result={}", 
                    orderNo, refundAmount, refundResult.isSuccess());

            return refundResult;

        } catch (Exception e) {
            log.error("申请退款失败: orderNo={}, amount={}, reason={}", orderNo, refundAmount, refundReason, e);
            throw new RuntimeException("申请退款失败: " + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 验证创建订单请求参数
     */
    private void validateCreateOrderRequest(CreateOrderDTO request) {
        if (request.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("订单金额必须大于0");
        }
        if (request.getPayChannel() == null) {
            throw new IllegalArgumentException("支付渠道不能为空");
        }
        if (request.getBusinessScene() == null) {
            throw new IllegalArgumentException("业务场景不能为空");
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo(String businessScene) {
        String prefix = switch (businessScene.toUpperCase()) {
            case "TAROT" -> "TR";
            case "CHATOI" -> "CO";
            case "ZNS" -> "ZN";
            default -> "CM";
        };
        return prefix + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * 构建支付订单对象
     */
    private PayOrder buildPayOrder(CreateOrderDTO request, String orderNo) {
        PayOrder payOrder = new PayOrder();
        payOrder.setOrderNo(orderNo);
        payOrder.setUserId(request.getUserId());
        payOrder.setProductId(request.getProductId());
        payOrder.setProductName(request.getProductName());
        payOrder.setBusinessScene(request.getBusinessScene());
        payOrder.setPayChannel(request.getPayChannel());
        payOrder.setPayMethod(request.getPayMethod());
        payOrder.setAmount(request.getAmount());
        payOrder.setCurrency(request.getCurrency() != null ? request.getCurrency() : "CNY");
        payOrder.setStatus(PayStatusEnum.PENDING.getCode());
        payOrder.setNotifyUrl(request.getNotifyUrl());
        payOrder.setReturnUrl(request.getReturnUrl());
        payOrder.setExpireTime(LocalDateTime.now().plusHours(2)); // 2小时过期
        return payOrder;
    }

    /**
     * 调用对应支付渠道创建支付
     */
    private PayResultVO createChannelPayment(PayOrder payOrder) {
        return payServiceFactory.createPayment(payOrder);
    }

    /**
     * 更新订单支付信息
     */
    private void updateOrderPayInfo(PayOrder payOrder, PayResultVO payResult) {
        if (payResult.getThirdPartyOrderNo() != null) {
            payOrder.setThirdPartyOrderNo(payResult.getThirdPartyOrderNo());
        }
        if (payResult.getPayParams() != null) {
            payOrder.setPayParams(payResult.getPayParams());
        }
        payOrderService.updateById(payOrder);
    }

    /**
     * 主动查询并更新订单状态
     */
    private void queryAndUpdateOrderStatus(PayOrder payOrder) {
        try {
            PayResultVO queryResult = payServiceFactory.queryPayment(payOrder);
            if (queryResult != null && queryResult.isSuccess()) {
                payOrder.setStatus(PayStatusEnum.PAID.getCode());
                payOrder.setPayTime(LocalDateTime.now());
                payOrder.setPaidAmount(payOrder.getAmount());
                payOrderService.updateById(payOrder);
            }
        } catch (Exception e) {
            log.warn("主动查询订单状态失败: orderNo={}", payOrder.getOrderNo(), e);
        }
    }

    /**
     * 构建支付结果VO
     */
    private PayResultVO buildPayResultVO(PayOrder payOrder) {
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setThirdPartyOrderNo(payOrder.getThirdPartyOrderNo());
        result.setStatus(payOrder.getStatus());
        result.setAmount(payOrder.getAmount());
        result.setPaidAmount(payOrder.getPaidAmount());
        result.setPayTime(payOrder.getPayTime());
        result.setSuccess(PayStatusEnum.PAID.getCode().equals(payOrder.getStatus()));
        return result;
    }

    /**
     * 验证支付回调
     */
    private boolean validateCallback(String payChannel, String callbackData) {
        return payServiceFactory.validateCallback(payChannel, callbackData);
    }

    /**
     * 解析回调数据
     */
    private PayOrder parseCallbackData(String payChannel, String callbackData) {
        return payServiceFactory.parseCallback(payChannel, callbackData);
    }

    /**
     * 从回调更新订单
     */
    private void updateOrderFromCallback(PayOrder payOrder, String callbackData) {
        payOrder.setStatus(PayStatusEnum.PAID.getCode());
        payOrder.setPayTime(LocalDateTime.now());
        payOrder.setPayResult(callbackData);
        payOrderService.updateById(payOrder);
    }

    /**
     * 执行业务逻辑 (根据业务场景执行不同逻辑)
     */
    private void executeBusinessLogic(PayOrder payOrder) {
        // 这里会根据业务场景调用不同的业务逻辑
        // 比如：增加用户次数、激活VIP、发放奖励等
        log.info("执行业务逻辑: orderNo={}, scene={}", payOrder.getOrderNo(), payOrder.getBusinessScene());
    }

    /**
     * 处理渠道退款
     */
    private PayResultVO processChannelRefund(PayOrder payOrder, BigDecimal refundAmount, String refundReason) {
        return payServiceFactory.refundPayment(payOrder, refundAmount, refundReason);
    }

    /**
     * 获取当前用户ID
     */
    private Integer getCurrentUserId() {
        try {
            return StpUtil.getLoginIdAsInt();
        } catch (Exception e) {
            log.warn("获取当前用户ID失败，可能未登录", e);
            return null;
        }
    }
}
