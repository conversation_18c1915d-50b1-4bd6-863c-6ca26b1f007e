package com.hncboy.chatgpt.framework.auth.domain;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 认证用户信息
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
public class AuthUserInfo {

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 用户状态
     */
    private Integer status;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 登录类型
     */
    private String loginType;

    /**
     * 第三方用户ID
     */
    private String thirdPartyId;

    /**
     * 第三方用户名
     */
    private String thirdPartyUsername;

    /**
     * 第三方邮箱
     */
    private String thirdPartyEmail;

    /**
     * 第三方头像
     */
    private String thirdPartyAvatar;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌过期时间
     */
    private LocalDateTime tokenExpireTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveTime;

    /**
     * 扩展属性
     */
    private String extraAttributes;
}
