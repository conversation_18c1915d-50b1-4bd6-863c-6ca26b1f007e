package com.hncboy.chatgpt.framework.auth.controller;

import com.hncboy.chatgpt.front.framework.annotation.ApiAdminRestController;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.framework.auth.domain.LoginResultVO;
import com.hncboy.chatgpt.framework.auth.domain.AuthUserInfo;
import com.hncboy.chatgpt.framework.auth.worker.AuthWorker;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;

/**
 * 统一认证控制器 (重构后)
 *
 * 功能特点:
 * 1. 支持多种登录方式 (微信/手机/邮箱)
 * 2. 基于Sa-Token的统一认证体系
 * 3. 自动用户创建和关联
 * 4. 完整的用户信息管理
 * 5. 统一的异常处理和响应格式
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@RequiredArgsConstructor
@Tag(name = "统一认证相关接口")
@ApiAdminRestController("/auth")
@Validated
public class AuthController {

    private final AuthWorker authWorker;

    @Operation(summary = "微信登录", description = "通过微信授权码进行登录")
    @PostMapping("/wechat/login")
    public R<LoginResultVO> wechatLogin(@RequestParam @NotBlank(message = "授权码不能为空") String code,
                                      @RequestParam(required = false) String state) {
        try {
            log.info("微信登录请求: code={}, state={}", code, state);

            LoginResultVO result = authWorker.wechatLogin(code, state);

            log.info("微信登录成功: userId={}, loginType={}", result.getUserId(), result.getLoginType());

            return R.data(result);

        } catch (Exception e) {
            log.error("微信登录失败: code={}, state={}", code, state, e);
            return R.fail("微信登录失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取微信登录授权URL", description = "获取微信登录的授权链接")
    @GetMapping("/wechat/authorize-url")
    public R<String> getWechatAuthorizeUrl(@RequestParam(required = false) String state) {
        try {
            log.info("获取微信登录授权URL请求: state={}", state);

            // 这里应该生成实际的微信授权URL
            String authorizeUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?" +
                                "appid=YOUR_APP_ID&redirect_uri=YOUR_REDIRECT_URI&response_type=code&scope=snsapi_userinfo" +
                                (state != null ? "&state=" + state : "");

            log.info("获取微信登录授权URL成功");

            return R.data(authorizeUrl);

        } catch (Exception e) {
            log.error("获取微信登录授权URL失败: state={}", state, e);
            return R.fail("获取授权URL失败: " + e.getMessage());
        }
    }

    @Operation(summary = "手机号登录", description = "通过手机号和验证码进行登录")
    @PostMapping("/phone/login")
    public R<LoginResultVO> phoneLogin(@RequestParam @NotBlank(message = "手机号不能为空") String phone,
                                     @RequestParam @NotBlank(message = "验证码不能为空") String code) {
        try {
            log.info("手机号登录请求: phone={}", phone);

            LoginResultVO result = authWorker.phoneLogin(phone, code);

            log.info("手机号登录成功: userId={}, phone={}", result.getUserId(), phone);

            return R.data(result);

        } catch (Exception e) {
            log.error("手机号登录失败: phone={}", phone, e);
            return R.fail("手机号登录失败: " + e.getMessage());
        }
    }

    @Operation(summary = "邮箱登录", description = "通过邮箱和密码进行登录")
    @PostMapping("/email/login")
    public R<LoginResultVO> emailLogin(@RequestParam @NotBlank(message = "邮箱不能为空") String email,
                                     @RequestParam @NotBlank(message = "密码不能为空") String password) {
        try {
            log.info("邮箱登录请求: email={}", email);

            LoginResultVO result = authWorker.emailLogin(email, password);

            log.info("邮箱登录成功: userId={}, email={}", result.getUserId(), email);

            return R.data(result);

        } catch (Exception e) {
            log.error("邮箱登录失败: email={}", email, e);
            return R.fail("邮箱登录失败: " + e.getMessage());
        }
    }

    @Operation(summary = "退出登录", description = "退出当前用户登录状态")
    @PostMapping("/logout")
    public R<String> logout() {
        try {
            log.info("用户退出登录请求");

            authWorker.logout();

            log.info("用户退出登录成功");

            return R.data("退出登录成功");

        } catch (Exception e) {
            log.error("退出登录失败", e);
            return R.fail("退出登录失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/user/info")
    public R<AuthUserInfo> getCurrentUser() {
        try {
            log.info("获取当前用户信息请求");

            AuthUserInfo userInfo = authWorker.getCurrentUser();

            log.info("获取当前用户信息成功: userId={}", userInfo.getUserId());

            return R.data(userInfo);

        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return R.fail("获取用户信息失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查登录状态", description = "检查当前用户是否已登录")
    @GetMapping("/check")
    public R<Boolean> checkLogin() {
        try {
            boolean isLogin = authWorker.isLogin();

            log.info("检查登录状态: isLogin={}", isLogin);

            return R.data(isLogin);

        } catch (Exception e) {
            log.error("检查登录状态失败", e);
            return R.data(false);
        }
    }
}
