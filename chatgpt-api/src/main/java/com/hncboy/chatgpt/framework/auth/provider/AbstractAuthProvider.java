package com.hncboy.chatgpt.framework.auth.provider;

import com.hncboy.chatgpt.db.entity.user.UserJointLogin;
import com.hncboy.chatgpt.framework.auth.domain.LoginResultVO;
import me.zhyd.oauth.model.AuthResponse;

/**
 * 抽象认证提供者
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public abstract class AbstractAuthProvider {

    /**
     * 获取登录类型
     *
     * @return 登录类型
     */
    public abstract String getLoginType();

    /**
     * 获取授权URL
     *
     * @param state 状态参数
     * @return 授权URL
     */
    public abstract String getAuthorizeUrl(String state);

    /**
     * 处理授权回调
     *
     * @param code 授权码
     * @param state 状态参数
     * @return 登录结果
     */
    public abstract LoginResultVO handleCallback(String code, String state);

    /**
     * 构建用户联合登录记录
     *
     * @param authResponse JustAuth响应
     * @param userId 用户ID
     * @return 用户联合登录记录
     */
    protected UserJointLogin buildUserJointLogin(AuthResponse authResponse, Integer userId) {
        UserJointLogin jointLogin = new UserJointLogin();
        jointLogin.setUserId(userId);
        jointLogin.setLoginType(getLoginType());
        jointLogin.setThirdPartyId(authResponse.getData().getUuid());
        jointLogin.setThirdPartyUsername(authResponse.getData().getUsername());
        jointLogin.setThirdPartyEmail(authResponse.getData().getEmail());
        jointLogin.setThirdPartyAvatar(authResponse.getData().getAvatar());

        // 检查Token是否存在
        if (authResponse.getData().getToken() != null) {
            jointLogin.setAccessToken(authResponse.getData().getToken().getAccessToken());
            jointLogin.setRefreshToken(authResponse.getData().getToken().getRefreshToken());
        }

        jointLogin.setStatus(1);
        jointLogin.setIsPrimary(0);
        return jointLogin;
    }
}
