package com.hncboy.chatgpt.framework.auth.provider;

import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.db.entity.user.UserJointLogin;
import com.hncboy.chatgpt.db.service.user.UserJointLoginService;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.framework.auth.config.JustAuthConfig;
import com.hncboy.chatgpt.framework.auth.domain.LoginResultVO;
import com.hncboy.chatgpt.framework.auth.domain.AuthUserInfo;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.request.AuthWeChatOpenRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 微信认证提供者
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Component
@Slf4j
public class WechatAuthProvider extends AbstractAuthProvider {

    @Autowired
    private JustAuthConfig justAuthConfig;

    @Autowired
    private UserJointLoginService userJointLoginService;

    @Autowired
    private UserBaseInfoService userBaseInfoService;

    @Override
    public String getLoginType() {
        return "WECHAT";
    }

    @Override
    public String getAuthorizeUrl(String state) {
        AuthRequest authRequest = getAuthRequest();
        return authRequest.authorize(state);
    }

    @Override
    public LoginResultVO handleCallback(String code, String state) {
        try {
            AuthRequest authRequest = getAuthRequest();
            AuthCallback authCallback = AuthCallback.builder()
                    .code(code)
                    .state(state)
                    .build();

            AuthResponse response = authRequest.login(authCallback);
            
            if (!response.ok()) {
                throw new RuntimeException("微信登录失败: " + response.getMsg());
            }

            // 查找或创建用户
            UserJointLogin existingLogin = userJointLoginService.getByTypeAndThirdPartyId(
                    getLoginType(), response.getData().getUuid());

            Integer userId;
            if (existingLogin != null) {
                // 已存在，更新登录信息
                userId = existingLogin.getUserId();
                updateExistingLogin(existingLogin, response);
            } else {
                // 新用户，创建用户记录
                userId = createNewUser(response);
            }

            // 执行登录
            StpUtil.login(userId);

            return LoginResultVO.builder()
                    .userId(userId)
                    .token(StpUtil.getTokenValue())
                    .loginType(getLoginType())
                    .build();

        } catch (Exception e) {
            log.error("微信登录回调处理失败", e);
            throw new RuntimeException("微信登录失败: " + e.getMessage());
        }
    }

    /**
     * 微信认证 (供AuthWorker调用)
     *
     * @param code 微信授权码
     * @param state 状态参数
     * @return 认证用户信息
     */
    public AuthUserInfo authenticate(String code, String state) {
        try {
            log.info("微信认证开始: code={}, state={}", code, state);

            AuthRequest authRequest = getAuthRequest();
            AuthCallback authCallback = AuthCallback.builder()
                    .code(code)
                    .state(state)
                    .build();

            AuthResponse response = authRequest.login(authCallback);

            if (!response.ok()) {
                throw new RuntimeException("微信登录失败: " + response.getMsg());
            }

            // 构建认证用户信息
            AuthUserInfo authUserInfo = new AuthUserInfo();
            authUserInfo.setLoginType(getLoginType());
            authUserInfo.setThirdPartyId(response.getData().getUuid());
            authUserInfo.setThirdPartyUsername(response.getData().getUsername());
            authUserInfo.setThirdPartyEmail(response.getData().getEmail());
            authUserInfo.setThirdPartyAvatar(response.getData().getAvatar());

            if (response.getData().getToken() != null) {
                authUserInfo.setAccessToken(response.getData().getToken().getAccessToken());
                authUserInfo.setRefreshToken(response.getData().getToken().getRefreshToken());
            }

            authUserInfo.setLoginTime(LocalDateTime.now());

            log.info("微信认证成功: openId={}, nickname={}",
                    authUserInfo.getThirdPartyId(), authUserInfo.getThirdPartyUsername());

            return authUserInfo;

        } catch (Exception e) {
            log.error("微信认证失败: code={}, state={}", code, state, e);
            throw new RuntimeException("微信认证失败: " + e.getMessage());
        }
    }

    /**
     * 获取AuthRequest
     */
    private AuthRequest getAuthRequest() {
        AuthConfig config = justAuthConfig.getWechatConfig();
        return new AuthWeChatOpenRequest(config);
    }

    /**
     * 更新已存在的登录信息
     */
    private void updateExistingLogin(UserJointLogin existingLogin, AuthResponse response) {
        existingLogin.setThirdPartyUsername(response.getData().getUsername());
        existingLogin.setThirdPartyAvatar(response.getData().getAvatar());

        // 检查Token是否存在
        if (response.getData().getToken() != null) {
            existingLogin.setAccessToken(response.getData().getToken().getAccessToken());
            existingLogin.setRefreshToken(response.getData().getToken().getRefreshToken());
        }

        existingLogin.setLastUseTime(LocalDateTime.now());
        userJointLoginService.updateById(existingLogin);
    }

    /**
     * 创建新用户
     */
    private Integer createNewUser(AuthResponse response) {
        // 1. 创建基础用户信息
        UserBaseInfo userInfo = new UserBaseInfo();
        userInfo.setName(response.getData().getUsername());
        userInfo.setAvatar(response.getData().getAvatar());
        userInfo.setStatus(1);
        userInfo.setLanguage("zh_CN");
        userInfo.setCurrency("CNY");
        userInfo.setTimezone("Asia/Shanghai");
        userBaseInfoService.insertUserBaseInfo(userInfo);

        // 2. 创建联合登录记录
        UserJointLogin jointLogin = buildUserJointLogin(response, userInfo.getId());
        jointLogin.setWechatOpenId(response.getData().getUuid());
        jointLogin.setIsPrimary(1); // 第一个登录方式设为主要方式
        userJointLoginService.save(jointLogin);

        return userInfo.getId();
    }
}
