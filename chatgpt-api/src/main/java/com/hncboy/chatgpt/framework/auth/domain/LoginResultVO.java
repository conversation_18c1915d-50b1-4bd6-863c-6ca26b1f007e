package com.hncboy.chatgpt.framework.auth.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录结果VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(title = "登录结果VO")
public class LoginResultVO {

    @Schema(title = "用户ID")
    private Integer userId;

    @Schema(title = "访问令牌")
    private String token;

    @Schema(title = "登录类型")
    private String loginType;

    @Schema(title = "用户名")
    private String username;

    @Schema(title = "头像")
    private String avatar;

    @Schema(title = "是否新用户")
    private Boolean isNewUser;

    @Schema(title = "令牌过期时间(秒)")
    private Long tokenTimeout;

    @Schema(title = "昵称")
    private String nickname;

    @Schema(title = "邮箱")
    private String email;

    @Schema(title = "手机号")
    private String phone;

    @Schema(title = "用户状态")
    private Integer status;

    @Schema(title = "用户类型")
    private String userType;

    @Schema(title = "语言偏好")
    private String language;

    @Schema(title = "时区")
    private String timezone;

    @Schema(title = "币种偏好")
    private String currency;

    @Schema(title = "刷新令牌")
    private String refreshToken;

    @Schema(title = "是否首次登录")
    private Boolean isFirstLogin;
}
