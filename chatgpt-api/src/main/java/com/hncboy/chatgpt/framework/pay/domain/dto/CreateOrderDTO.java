package com.hncboy.chatgpt.framework.pay.domain.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 创建支付订单DTO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
public class CreateOrderDTO {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空")
    private Long productId;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;

    /**
     * 业务场景 (tarot/chatoi/zns/common)
     */
    @NotBlank(message = "业务场景不能为空")
    private String businessScene;

    /**
     * 支付渠道 (ALIPAY/WECHAT/SEPAY/MOMO)
     */
    @NotBlank(message = "支付渠道不能为空")
    private String payChannel;

    /**
     * 支付方式 (APP/H5/NATIVE/JSAPI/BANK_TRANSFER/WALLET)
     */
    @NotBlank(message = "支付方式不能为空")
    private String payMethod;

    /**
     * 订单金额
     */
    @NotNull(message = "订单金额不能为空")
    @Positive(message = "订单金额必须大于0")
    private BigDecimal amount;

    /**
     * 币种 (CNY/USD/VND)
     */
    private String currency = "CNY";

    /**
     * 通知URL
     */
    private String notifyUrl;

    /**
     * 返回URL
     */
    private String returnUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 扩展参数 (JSON格式)
     */
    private String extraParams;
}
