package com.hncboy.chatgpt.framework.payment.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付结果VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Builder
@Schema(title = "支付结果VO")
public class PaymentResultVO {

    @Schema(title = "订单号")
    private String orderNo;

    @Schema(title = "支付URL")
    private String payUrl;

    @Schema(title = "二维码")
    private String qrCode;

    @Schema(title = "过期时间")
    private LocalDateTime expireTime;

    @Schema(title = "支付渠道")
    private String paymentChannel;

    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "第三方订单号")
    private String thirdPartyOrderNo;
}
