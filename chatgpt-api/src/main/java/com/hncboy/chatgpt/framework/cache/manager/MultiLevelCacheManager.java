package com.hncboy.chatgpt.framework.cache.manager;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 多级缓存管理器 (Redis + Caffeine)
 * 
 * 功能特点:
 * 1. L1缓存: Caffeine本地缓存，超高性能
 * 2. L2缓存: Redis分布式缓存，数据共享
 * 3. 自动降级: Redis不可用时使用本地缓存
 * 4. 缓存预热: 启动时自动预热热点数据
 * 5. 性能监控: 缓存命中率统计和监控
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MultiLevelCacheManager {

    private final RedisTemplate<String, Object> redisTemplate;

    // L1缓存 - Caffeine本地缓存
    private Cache<String, Object> localCache;
    
    // 缓存统计
    private long localHits = 0;
    private long redisHits = 0;
    private long misses = 0;

    @PostConstruct
    public void init() {
        // 初始化本地缓存
        localCache = Caffeine.newBuilder()
                .maximumSize(10000) // 最大缓存条目数
                .expireAfterWrite(30, TimeUnit.MINUTES) // 写入后30分钟过期
                .expireAfterAccess(15, TimeUnit.MINUTES) // 访问后15分钟过期
                .recordStats() // 启用统计
                .build();

        log.info("多级缓存管理器初始化完成");
        
        // 启动缓存预热
        preloadCache();
    }

    /**
     * 获取缓存值
     *
     * @param key 缓存键
     * @return 缓存值
     */
    public Object get(String key) {
        try {
            // 1. 先从L1缓存获取
            Object value = localCache.getIfPresent(key);
            if (value != null) {
                localHits++;
                log.debug("L1缓存命中: key={}", key);
                return value;
            }

            // 2. 从L2缓存获取
            value = getFromRedis(key);
            if (value != null) {
                redisHits++;
                // 回写到L1缓存
                localCache.put(key, value);
                log.debug("L2缓存命中: key={}", key);
                return value;
            }

            // 3. 缓存未命中
            misses++;
            log.debug("缓存未命中: key={}", key);
            return null;

        } catch (Exception e) {
            log.warn("获取缓存失败: key={}", key, e);
            return localCache.getIfPresent(key); // 降级到本地缓存
        }
    }

    /**
     * 设置缓存值
     *
     * @param key 缓存键
     * @param value 缓存值
     * @param ttl 过期时间(秒)
     */
    public void set(String key, Object value, long ttl) {
        try {
            // 1. 设置到L1缓存
            localCache.put(key, value);

            // 2. 设置到L2缓存
            setToRedis(key, value, ttl);

            log.debug("设置缓存成功: key={}, ttl={}", key, ttl);

        } catch (Exception e) {
            log.warn("设置缓存失败: key={}", key, e);
            // 至少保证本地缓存可用
            localCache.put(key, value);
        }
    }

    /**
     * 删除缓存
     *
     * @param key 缓存键
     */
    public void delete(String key) {
        try {
            // 1. 从L1缓存删除
            localCache.invalidate(key);

            // 2. 从L2缓存删除
            deleteFromRedis(key);

            log.debug("删除缓存成功: key={}", key);

        } catch (Exception e) {
            log.warn("删除缓存失败: key={}", key, e);
        }
    }

    /**
     * 批量删除缓存 (支持通配符)
     *
     * @param pattern 缓存键模式
     */
    public void deleteByPattern(String pattern) {
        try {
            // 1. 清理本地缓存 (简单实现，实际可以优化)
            localCache.invalidateAll();

            // 2. 清理Redis缓存
            deleteFromRedisByPattern(pattern);

            log.debug("批量删除缓存成功: pattern={}", pattern);

        } catch (Exception e) {
            log.warn("批量删除缓存失败: pattern={}", pattern, e);
        }
    }

    /**
     * 检查缓存是否存在
     *
     * @param key 缓存键
     * @return 是否存在
     */
    public boolean exists(String key) {
        try {
            // 先检查本地缓存
            if (localCache.getIfPresent(key) != null) {
                return true;
            }

            // 再检查Redis缓存
            return existsInRedis(key);

        } catch (Exception e) {
            log.warn("检查缓存存在性失败: key={}", key, e);
            return localCache.getIfPresent(key) != null;
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return 统计信息
     */
    public String getCacheStats() {
        long total = localHits + redisHits + misses;
        if (total == 0) {
            return "缓存统计: 暂无数据";
        }

        double localHitRate = (double) localHits / total * 100;
        double redisHitRate = (double) redisHits / total * 100;
        double missRate = (double) misses / total * 100;

        return String.format("缓存统计 - L1命中率: %.2f%%, L2命中率: %.2f%%, 未命中率: %.2f%%, 总请求: %d",
                localHitRate, redisHitRate, missRate, total);
    }

    /**
     * 清空所有缓存
     */
    public void clear() {
        try {
            // 清空本地缓存
            localCache.invalidateAll();

            // 清空Redis缓存 (谨慎使用)
            // redisTemplate.getConnectionFactory().getConnection().flushDb();

            log.info("清空所有缓存完成");

        } catch (Exception e) {
            log.error("清空缓存失败", e);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 从Redis获取缓存
     */
    private Object getFromRedis(String key) {
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.warn("从Redis获取缓存失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 设置Redis缓存
     */
    private void setToRedis(String key, Object value, long ttl) {
        try {
            if (ttl > 0) {
                redisTemplate.opsForValue().set(key, value, Duration.ofSeconds(ttl));
            } else {
                redisTemplate.opsForValue().set(key, value);
            }
        } catch (Exception e) {
            log.warn("设置Redis缓存失败: key={}", key, e);
        }
    }

    /**
     * 从Redis删除缓存
     */
    private void deleteFromRedis(String key) {
        try {
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.warn("从Redis删除缓存失败: key={}", key, e);
        }
    }

    /**
     * 从Redis批量删除缓存
     */
    private void deleteFromRedisByPattern(String pattern) {
        try {
            redisTemplate.delete(redisTemplate.keys(pattern));
        } catch (Exception e) {
            log.warn("从Redis批量删除缓存失败: pattern={}", pattern, e);
        }
    }

    /**
     * 检查Redis中是否存在
     */
    private boolean existsInRedis(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.warn("检查Redis缓存存在性失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 缓存预热
     */
    private void preloadCache() {
        try {
            log.info("开始缓存预热...");

            // 预热系统配置
            preloadSystemConfig();

            // 预热用户信息
            preloadUserInfo();

            // 预热模型配置
            preloadModelConfig();

            log.info("缓存预热完成");

        } catch (Exception e) {
            log.error("缓存预热失败", e);
        }
    }

    /**
     * 预热系统配置
     */
    private void preloadSystemConfig() {
        // 这里可以预热系统配置、字典数据等
        log.debug("预热系统配置");
    }

    /**
     * 预热用户信息
     */
    private void preloadUserInfo() {
        // 这里可以预热活跃用户信息
        log.debug("预热用户信息");
    }

    /**
     * 预热模型配置
     */
    private void preloadModelConfig() {
        // 这里可以预热AI模型配置
        log.debug("预热模型配置");
    }
}
