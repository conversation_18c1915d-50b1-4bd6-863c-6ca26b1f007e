package com.hncboy.chatgpt.framework.pay.controller;

import com.hncboy.chatgpt.front.framework.annotation.ApiAdminRestController;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.framework.pay.worker.UnifiedPayWorker;
import com.hncboy.chatgpt.framework.pay.domain.dto.CreateOrderDTO;
import com.hncboy.chatgpt.framework.pay.domain.vo.PayResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 统一支付控制器
 * 
 * 功能特点:
 * 1. 统一支付入口，不按业务场景分Controller
 * 2. 自备区分业务场景能力，支持所有业务类型
 * 3. 支持多种支付渠道和支付方式
 * 4. 完整的订单管理和状态查询
 * 5. 统一的回调处理和退款功能
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@RequiredArgsConstructor
@Tag(name = "统一支付相关接口")
@ApiAdminRestController("/pay")
public class PayController {

    private final UnifiedPayWorker unifiedPayWorker;

    @Operation(summary = "创建支付订单", description = "支持所有业务场景的统一支付入口")
    @PostMapping("/create")
    public R<PayResultVO> createOrder(@Validated @RequestBody CreateOrderDTO createOrderDTO) {
        try {
            log.info("创建支付订单请求: {}", createOrderDTO);
            
            PayResultVO result = unifiedPayWorker.createPayOrder(createOrderDTO);
            
            log.info("创建支付订单成功: orderNo={}, channel={}, scene={}", 
                    result.getOrderNo(), createOrderDTO.getPayChannel(), createOrderDTO.getBusinessScene());
            
            return R.data(result);
            
        } catch (Exception e) {
            log.error("创建支付订单失败: {}", createOrderDTO, e);
            return R.fail("创建支付订单失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询支付订单状态")
    @GetMapping("/query/{orderNo}")
    public R<PayResultVO> queryOrder(@PathVariable String orderNo) {
        try {
            log.info("查询支付订单状态: orderNo={}", orderNo);
            
            PayResultVO result = unifiedPayWorker.queryPayOrder(orderNo);
            
            log.info("查询支付订单成功: orderNo={}, status={}", orderNo, result.getStatus());
            
            return R.data(result);
            
        } catch (Exception e) {
            log.error("查询支付订单失败: orderNo={}", orderNo, e);
            return R.fail("查询支付订单失败: " + e.getMessage());
        }
    }

    @Operation(summary = "申请退款")
    @PostMapping("/refund")
    public R<PayResultVO> refundOrder(@RequestParam String orderNo,
                                    @RequestParam BigDecimal refundAmount,
                                    @RequestParam(required = false) String refundReason) {
        try {
            log.info("申请退款请求: orderNo={}, amount={}, reason={}", orderNo, refundAmount, refundReason);
            
            PayResultVO result = unifiedPayWorker.refundOrder(orderNo, refundAmount, refundReason);
            
            log.info("申请退款完成: orderNo={}, success={}", orderNo, result.isSuccess());
            
            return R.data(result);
            
        } catch (Exception e) {
            log.error("申请退款失败: orderNo={}, amount={}, reason={}", orderNo, refundAmount, refundReason, e);
            return R.fail("申请退款失败: " + e.getMessage());
        }
    }

    // ==================== 业务场景快捷接口 ====================

    @Operation(summary = "塔罗牌业务支付", description = "塔罗牌业务专用支付接口")
    @PostMapping("/tarot")
    public R<PayResultVO> tarotPay(@RequestParam String payChannel,
                                 @RequestParam String payMethod,
                                 @RequestParam Long productId,
                                 @RequestParam String productName,
                                 @RequestParam BigDecimal amount) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setBusinessScene("tarot");
        createOrderDTO.setPayChannel(payChannel);
        createOrderDTO.setPayMethod(payMethod);
        createOrderDTO.setProductId(productId);
        createOrderDTO.setProductName(productName);
        createOrderDTO.setAmount(amount);
        createOrderDTO.setCurrency("CNY");
        
        return createOrder(createOrderDTO);
    }

    @Operation(summary = "AI对话业务支付", description = "ChatOI业务专用支付接口")
    @PostMapping("/chatoi")
    public R<PayResultVO> chatoiPay(@RequestParam String payChannel,
                                  @RequestParam String payMethod,
                                  @RequestParam Long productId,
                                  @RequestParam String productName,
                                  @RequestParam BigDecimal amount) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setBusinessScene("chatoi");
        createOrderDTO.setPayChannel(payChannel);
        createOrderDTO.setPayMethod(payMethod);
        createOrderDTO.setProductId(productId);
        createOrderDTO.setProductName(productName);
        createOrderDTO.setAmount(amount);
        createOrderDTO.setCurrency("CNY");
        
        return createOrder(createOrderDTO);
    }

    @Operation(summary = "紫微斗数业务支付", description = "ZNS业务专用支付接口")
    @PostMapping("/zns")
    public R<PayResultVO> znsPay(@RequestParam String payChannel,
                               @RequestParam String payMethod,
                               @RequestParam Long productId,
                               @RequestParam String productName,
                               @RequestParam BigDecimal amount) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setBusinessScene("zns");
        createOrderDTO.setPayChannel(payChannel);
        createOrderDTO.setPayMethod(payMethod);
        createOrderDTO.setProductId(productId);
        createOrderDTO.setProductName(productName);
        createOrderDTO.setAmount(amount);
        createOrderDTO.setCurrency("CNY");
        
        return createOrder(createOrderDTO);
    }

    @Operation(summary = "通用业务支付", description = "通用业务支付接口")
    @PostMapping("/common")
    public R<PayResultVO> commonPay(@RequestParam String payChannel,
                                  @RequestParam String payMethod,
                                  @RequestParam Long productId,
                                  @RequestParam String productName,
                                  @RequestParam BigDecimal amount) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setBusinessScene("common");
        createOrderDTO.setPayChannel(payChannel);
        createOrderDTO.setPayMethod(payMethod);
        createOrderDTO.setProductId(productId);
        createOrderDTO.setProductName(productName);
        createOrderDTO.setAmount(amount);
        createOrderDTO.setCurrency("CNY");
        
        return createOrder(createOrderDTO);
    }

    // ==================== 支付渠道快捷接口 ====================

    @Operation(summary = "支付宝支付", description = "支付宝支付快捷接口")
    @PostMapping("/alipay")
    public R<PayResultVO> alipay(@RequestParam String businessScene,
                               @RequestParam String payMethod,
                               @RequestParam Long productId,
                               @RequestParam String productName,
                               @RequestParam BigDecimal amount) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setBusinessScene(businessScene);
        createOrderDTO.setPayChannel("ALIPAY");
        createOrderDTO.setPayMethod(payMethod);
        createOrderDTO.setProductId(productId);
        createOrderDTO.setProductName(productName);
        createOrderDTO.setAmount(amount);
        createOrderDTO.setCurrency("CNY");
        
        return createOrder(createOrderDTO);
    }

    @Operation(summary = "微信支付", description = "微信支付快捷接口")
    @PostMapping("/wechat")
    public R<PayResultVO> wechatPay(@RequestParam String businessScene,
                                  @RequestParam String payMethod,
                                  @RequestParam Long productId,
                                  @RequestParam String productName,
                                  @RequestParam BigDecimal amount) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setBusinessScene(businessScene);
        createOrderDTO.setPayChannel("WECHAT");
        createOrderDTO.setPayMethod(payMethod);
        createOrderDTO.setProductId(productId);
        createOrderDTO.setProductName(productName);
        createOrderDTO.setAmount(amount);
        createOrderDTO.setCurrency("CNY");
        
        return createOrder(createOrderDTO);
    }

    @Operation(summary = "SE支付", description = "越南SE支付快捷接口")
    @PostMapping("/sepay")
    public R<PayResultVO> sepay(@RequestParam String businessScene,
                              @RequestParam Long productId,
                              @RequestParam String productName,
                              @RequestParam BigDecimal amount) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setBusinessScene(businessScene);
        createOrderDTO.setPayChannel("SEPAY");
        createOrderDTO.setPayMethod("BANK_TRANSFER");
        createOrderDTO.setProductId(productId);
        createOrderDTO.setProductName(productName);
        createOrderDTO.setAmount(amount);
        createOrderDTO.setCurrency("VND");
        
        return createOrder(createOrderDTO);
    }

    @Operation(summary = "Momo支付", description = "越南Momo支付快捷接口")
    @PostMapping("/momo")
    public R<PayResultVO> momoPay(@RequestParam String businessScene,
                                @RequestParam Long productId,
                                @RequestParam String productName,
                                @RequestParam BigDecimal amount) {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();
        createOrderDTO.setBusinessScene(businessScene);
        createOrderDTO.setPayChannel("MOMO");
        createOrderDTO.setPayMethod("WALLET");
        createOrderDTO.setProductId(productId);
        createOrderDTO.setProductName(productName);
        createOrderDTO.setAmount(amount);
        createOrderDTO.setCurrency("VND");
        
        return createOrder(createOrderDTO);
    }
}
