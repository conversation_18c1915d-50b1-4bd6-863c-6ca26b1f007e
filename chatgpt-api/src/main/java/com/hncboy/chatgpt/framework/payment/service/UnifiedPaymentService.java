package com.hncboy.chatgpt.framework.payment.service;

import com.hncboy.chatgpt.framework.payment.domain.dto.CreateOrderDTO;
import com.hncboy.chatgpt.framework.payment.domain.vo.PaymentResultVO;
import com.hncboy.chatgpt.framework.payment.domain.vo.PaymentOrderVO;

import javax.servlet.http.HttpServletRequest;

/**
 * 统一支付服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface UnifiedPaymentService {

    /**
     * 创建支付订单 - 自动选择最优支付渠道
     *
     * @param createOrderDTO 创建订单请求
     * @return 支付结果
     */
    PaymentResultVO createPaymentOrder(CreateOrderDTO createOrderDTO);

    /**
     * 支付宝支付 - 直接使用pay-java-parent官方实现
     *
     * @param createOrderDTO 创建订单请求
     * @return 支付结果
     */
    PaymentResultVO createAlipayOrder(CreateOrderDTO createOrderDTO);

    /**
     * 微信支付 - 直接使用pay-java-parent官方实现
     *
     * @param createOrderDTO 创建订单请求
     * @return 支付结果
     */
    PaymentResultVO createWechatPayOrder(CreateOrderDTO createOrderDTO);

    /**
     * SE支付 - 保持现有实现
     *
     * @param createOrderDTO 创建订单请求
     * @return 支付结果
     */
    PaymentResultVO createSePayOrder(CreateOrderDTO createOrderDTO);

    /**
     * Momo支付 - 保持现有实现
     *
     * @param createOrderDTO 创建订单请求
     * @return 支付结果
     */
    PaymentResultVO createMomoPayOrder(CreateOrderDTO createOrderDTO);

    /**
     * 查询支付订单状态
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    PaymentOrderVO queryPaymentOrder(String orderNo);

    /**
     * 统一支付回调处理 - 支持所有渠道
     *
     * @param channelCode 渠道代码
     * @param request HTTP请求
     * @return 处理结果
     */
    Boolean handlePaymentCallback(String channelCode, HttpServletRequest request);

    /**
     * 取消支付订单
     *
     * @param orderNo 订单号
     * @return 是否成功
     */
    Boolean cancelPaymentOrder(String orderNo);

    /**
     * 申请退款 - 使用pay-java-parent官方退款接口
     *
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @param reason 退款原因
     * @return 是否成功
     */
    Boolean refundPaymentOrder(String orderNo, java.math.BigDecimal refundAmount, String reason);

    /**
     * 同步支付状态 - 使用pay-java-parent官方查询接口
     *
     * @param orderNo 订单号
     * @return 是否成功
     */
    Boolean syncPaymentStatus(String orderNo);
}
