package com.hncboy.chatgpt.framework.payment.controller;

import com.hncboy.chatgpt.framework.payment.service.UnifiedPaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 支付回调控制器
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@AllArgsConstructor
@Tag(name = "支付回调相关接口")
@RestController
@RequestMapping("/api/payment/callback")
@Slf4j
public class PaymentCallbackController {

    private final UnifiedPaymentService unifiedPaymentService;

    @Operation(summary = "支付宝回调 - 塔罗牌业务")
    @PostMapping("/alipay/tarot")
    public String alipayTarotCallback(HttpServletRequest request) {
        log.info("收到支付宝塔罗牌业务回调");
        Boolean result = unifiedPaymentService.handlePaymentCallback("ALIPAY", request);
        return result ? "success" : "fail";
    }

    @Operation(summary = "支付宝回调 - 紫微斗数业务")
    @PostMapping("/alipay/zns")
    public String alipayZnsCallback(HttpServletRequest request) {
        log.info("收到支付宝紫微斗数业务回调");
        Boolean result = unifiedPaymentService.handlePaymentCallback("ALIPAY", request);
        return result ? "success" : "fail";
    }

    @Operation(summary = "支付宝回调 - AI对话业务")
    @PostMapping("/alipay/chatoi")
    public String alipayChatOiCallback(HttpServletRequest request) {
        log.info("收到支付宝AI对话业务回调");
        Boolean result = unifiedPaymentService.handlePaymentCallback("ALIPAY", request);
        return result ? "success" : "fail";
    }

    @Operation(summary = "微信支付回调 - 塔罗牌业务")
    @PostMapping("/wechat/tarot")
    public String wechatTarotCallback(HttpServletRequest request) {
        log.info("收到微信支付塔罗牌业务回调");
        Boolean result = unifiedPaymentService.handlePaymentCallback("WECHAT", request);
        return result ? "success" : "fail";
    }

    @Operation(summary = "微信支付回调 - 紫微斗数业务")
    @PostMapping("/wechat/zns")
    public String wechatZnsCallback(HttpServletRequest request) {
        log.info("收到微信支付紫微斗数业务回调");
        Boolean result = unifiedPaymentService.handlePaymentCallback("WECHAT", request);
        return result ? "success" : "fail";
    }

    @Operation(summary = "微信支付回调 - AI对话业务")
    @PostMapping("/wechat/chatoi")
    public String wechatChatOiCallback(HttpServletRequest request) {
        log.info("收到微信支付AI对话业务回调");
        Boolean result = unifiedPaymentService.handlePaymentCallback("WECHAT", request);
        return result ? "success" : "fail";
    }

    @Operation(summary = "SE支付回调")
    @PostMapping("/sepay/{businessScene}")
    public String sepayCallback(@PathVariable String businessScene, HttpServletRequest request) {
        log.info("收到SE支付回调: businessScene={}", businessScene);
        Boolean result = unifiedPaymentService.handlePaymentCallback("SEPAY", request);
        return result ? "success" : "fail";
    }

    @Operation(summary = "Momo支付回调")
    @PostMapping("/momo/{businessScene}")
    public String momoCallback(@PathVariable String businessScene, HttpServletRequest request) {
        log.info("收到Momo支付回调: businessScene={}", businessScene);
        Boolean result = unifiedPaymentService.handlePaymentCallback("MOMO", request);
        return result ? "success" : "fail";
    }
}
