package com.hncboy.chatgpt.framework.ai.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.biz.chatoi.domain.dto.ChatRequestDTO;
import com.hncboy.chatgpt.biz.chatoi.domain.vo.ChatResponseVO;
import com.hncboy.chatgpt.framework.ai.service.SpringAIChatService;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatRoomDO;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.service.ChatMessageService;
import com.hncboy.chatgpt.front.service.ChatRoomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * Spring AI聊天服务实现 - 集成现有通道选择逻辑
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Service
@Slf4j
public class SpringAIChatServiceImpl implements SpringAIChatService {

    @Autowired
    private UserBaseInfoService userBaseInfoService;

    @Autowired
    private ChatRoomService chatRoomService;

    @Autowired
    private ChatMessageService chatMessageService;

    // TODO: 注入Spring AI ChatClient
    // @Autowired
    // private ChatClient chatClient;

    // TODO: 注入现有通道选择服务
    // @Autowired
    // private ChannelSelectorService channelSelectorService;
    
    @Override
    public ChatResponseVO chat(ChatRequestDTO request) {
        try {
            // 1. 验证聊天室是否存在
            ChatRoomDO chatRoom = chatRoomService.getById(request.getChatRoomId());
            if (chatRoom == null) {
                throw new RuntimeException("聊天室不存在");
            }

            // 2. 获取当前用户ID
            Integer userId = StpUtil.getLoginIdAsInt();

            // 3. 验证用户权限和次数 (基于现有逻辑)
            UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
            if (userInfo == null) {
                throw new RuntimeException("用户不存在");
            }

            // 4. 检查用户剩余次数
            int remainingCount = userInfo.getUseNum() + userInfo.getFreeNum();
            if (remainingCount <= 0) {
                throw new RuntimeException("对话次数不足");
            }

            // 5. 保存用户消息 (基于现有ChatMessageService逻辑)
            ChatMessageDO userMessage = new ChatMessageDO();
            userMessage.setChatRoomId(request.getChatRoomId());
            userMessage.setOpenId(userId.toString());
            userMessage.setContent(request.getContent());
            userMessage.setMessageType(1); // 用户消息
            userMessage.setCreateTime(new Date());
            userMessage.setUpdateTime(new Date());
            chatMessageService.save(userMessage);

            // 6. 构建对话历史 (基于现有逻辑)
            List<ChatMessageDO> messageHistory = getMessageHistory(request.getChatRoomId());

            // 7. 调用AI模型 (这里模拟Spring AI调用)
            String aiResponse = callAIModel(request, messageHistory);

            // 8. 保存AI回复
            ChatMessageDO aiMessage = new ChatMessageDO();
            aiMessage.setChatRoomId(request.getChatRoomId());
            aiMessage.setOpenId(userId.toString());
            aiMessage.setContent(aiResponse);
            aiMessage.setMessageType(2); // AI回复
            aiMessage.setCreateTime(new Date());
            aiMessage.setUpdateTime(new Date());
            chatMessageService.save(aiMessage);

            // 9. 扣减用户次数 (基于现有逻辑)
            deductUserCount(userId);

            // 10. 构建响应
            ChatResponseVO responseVO = new ChatResponseVO();
            responseVO.setMessageId(aiMessage.getId());
            responseVO.setChatRoomId(request.getChatRoomId());
            responseVO.setContent(aiResponse);
            responseVO.setModelName(getModelName(request.getModelId()));
            responseVO.setTokenCount(calculateTokenCount(aiResponse));
            responseVO.setResponseTime(LocalDateTime.now());

            log.info("AI对话完成: chatRoomId={}, userId={}", request.getChatRoomId(), userId);
            return responseVO;

        } catch (Exception e) {
            log.error("AI对话失败: {}", request, e);
            throw new RuntimeException("AI对话失败: " + e.getMessage());
        }
    }
    
    @Override
    public Flux<String> streamChat(ChatRequestDTO request) {
        try {
            // 1. 验证聊天室和用户 (复用chat方法的逻辑)
            ChatRoomDO chatRoom = chatRoomService.getById(request.getChatRoomId());
            if (chatRoom == null) {
                return Flux.error(new RuntimeException("聊天室不存在"));
            }

            Integer userId = StpUtil.getLoginIdAsInt();
            UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
            if (userInfo == null) {
                return Flux.error(new RuntimeException("用户不存在"));
            }

            int remainingCount = userInfo.getUseNum() + userInfo.getFreeNum();
            if (remainingCount <= 0) {
                return Flux.error(new RuntimeException("对话次数不足"));
            }

            // 2. 保存用户消息
            ChatMessageDO userMessage = new ChatMessageDO();
            userMessage.setChatRoomId(request.getChatRoomId());
            userMessage.setOpenId(userId.toString());
            userMessage.setContent(request.getContent());
            userMessage.setMessageType(1);
            userMessage.setCreateTime(new Date());
            userMessage.setUpdateTime(new Date());
            chatMessageService.save(userMessage);

            // 3. 获取对话历史
            List<ChatMessageDO> messageHistory = getMessageHistory(request.getChatRoomId());

            // 4. 模拟流式响应 (基于现有业务逻辑)
            String fullResponse = callAIModel(request, messageHistory);
            String[] chunks = fullResponse.split("(?<=\\p{Punct})|(?=\\p{Punct})|\\s+");

            StringBuilder responseBuilder = new StringBuilder();

            return Flux.fromArray(chunks)
                    .delayElements(java.time.Duration.ofMillis(100)) // 模拟流式延迟
                    .doOnNext(chunk -> {
                        responseBuilder.append(chunk);
                        log.debug("流式响应片段: {}", chunk);
                    })
                    .doOnComplete(() -> {
                        // 5. 保存完整的AI回复
                        ChatMessageDO aiMessage = new ChatMessageDO();
                        aiMessage.setChatRoomId(request.getChatRoomId());
                        aiMessage.setOpenId(userId.toString());
                        aiMessage.setContent(responseBuilder.toString());
                        aiMessage.setMessageType(2);
                        aiMessage.setCreateTime(new Date());
                        aiMessage.setUpdateTime(new Date());
                        chatMessageService.save(aiMessage);

                        // 6. 扣减用户次数
                        deductUserCount(userId);

                        log.info("流式对话完成: chatRoomId={}, userId={}", request.getChatRoomId(), userId);
                    })
                    .doOnError(error -> {
                        log.error("流式对话错误: chatRoomId={}, error={}", request.getChatRoomId(), error.getMessage());
                    });

        } catch (Exception e) {
            log.error("流式对话失败: {}", request, e);
            return Flux.error(new RuntimeException("流式对话失败: " + e.getMessage()));
        }
    }
    
    @Override
    public String chatWithAgent(String userMessage, Integer userId) {
        try {
            // TODO: 实现AI Agent功能
            // return chatClient.prompt()
            //     .user(userMessage)
            //     .function("getCurrentWeather", weatherAgent::getCurrentWeather)
            //     .function("sendEmail", emailAgent::sendEmail)
            //     .function("getUserInfo", () -> getUserInfo(userId))
            //     .function("searchKnowledge", vectorService::searchKnowledge)
            //     .call()
            //     .content();
            
            log.info("AI Agent对话: userId={}, message={}", userId, userMessage);
            return "AI Agent功能开发中，用户消息: " + userMessage;
            
        } catch (Exception e) {
            log.error("AI Agent对话失败: userId={}, message={}", userId, userMessage, e);
            throw new RuntimeException("AI Agent对话失败: " + e.getMessage());
        }
    }
    
    @Override
    public String analyzeImage(String imageUrl, String question) {
        try {
            // TODO: 实现图像理解功能
            // return chatClient.prompt()
            //     .user(userSpec -> userSpec
            //         .text(question)
            //         .media(MimeTypeUtils.IMAGE_JPEG, imageUrl))
            //     .call()
            //     .content();
            
            log.info("图像理解: imageUrl={}, question={}", imageUrl, question);
            return "图像理解功能开发中，问题: " + question;
            
        } catch (Exception e) {
            log.error("图像理解失败: imageUrl={}, question={}", imageUrl, question, e);
            throw new RuntimeException("图像理解失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取对话历史 (基于现有ChatMessageService逻辑)
     */
    private List<ChatMessageDO> getMessageHistory(Long chatRoomId) {
        LambdaQueryWrapper<ChatMessageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMessageDO::getChatRoomId, chatRoomId)
                .orderByAsc(ChatMessageDO::getCreateTime)
                .last("LIMIT 20"); // 限制最近20条消息
        return chatMessageService.list(queryWrapper);
    }

    /**
     * 调用AI模型 (模拟Spring AI调用)
     */
    private String callAIModel(ChatRequestDTO request, List<ChatMessageDO> messageHistory) {
        try {
            // TODO: 这里应该调用Spring AI ChatClient
            // 1. 选择最优通道
            // 2. 构建消息上下文
            // 3. 调用AI模型
            // 4. 返回响应

            // 模拟AI响应
            String response = "这是基于现有业务逻辑的AI响应，用户问题：" + request.getContent();

            // 根据历史消息调整响应
            if (!messageHistory.isEmpty()) {
                response += "（基于" + messageHistory.size() + "条历史消息）";
            }

            return response;
        } catch (Exception e) {
            log.error("调用AI模型失败", e);
            return "抱歉，AI服务暂时不可用，请稍后再试。";
        }
    }

    /**
     * 扣减用户次数 (基于现有业务逻辑)
     */
    private void deductUserCount(Integer userId) {
        try {
            UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
            if (userInfo != null) {
                // 优先扣减免费次数，再扣减付费次数
                if (userInfo.getFreeNum() > 0) {
                    userInfo.setFreeNum(userInfo.getFreeNum() - 1);
                } else if (userInfo.getUseNum() > 0) {
                    userInfo.setUseNum(userInfo.getUseNum() - 1);
                }
                userBaseInfoService.updateById(userInfo);
                log.info("扣减用户次数成功: userId={}, 剩余免费次数={}, 剩余付费次数={}",
                        userId, userInfo.getFreeNum(), userInfo.getUseNum());
            }
        } catch (Exception e) {
            log.error("扣减用户次数失败: userId={}", userId, e);
        }
    }

    /**
     * 获取模型名称
     */
    private String getModelName(Integer modelId) {
        // TODO: 根据modelId查询模型名称
        if (modelId == null) {
            return "gpt-3.5-turbo";
        }
        return "model-" + modelId;
    }

    /**
     * 计算Token数量
     */
    private Integer calculateTokenCount(String content) {
        if (content == null) {
            return 0;
        }
        // 简单估算：中文按字符数，英文按单词数
        return content.length();
    }

    /**
     * 获取用户信息 - 用于AI Agent
     */
    private String getUserInfo(Integer userId) {
        try {
            UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
            if (userInfo != null) {
                return String.format("用户：%s，积分：%d，状态：%s",
                    userInfo.getName(),
                    userInfo.getUseNum() + userInfo.getFreeNum(),
                    userInfo.getStatus() == 0 ? "正常" : "禁用");
            }
            return "用户信息不存在";
        } catch (Exception e) {
            log.error("获取用户信息失败: userId={}", userId, e);
            return "无法获取用户信息";
        }
    }
}
