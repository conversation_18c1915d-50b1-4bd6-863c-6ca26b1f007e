package com.hncboy.chatgpt.framework.auth.config;

import me.zhyd.oauth.config.AuthConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * JustAuth配置
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Configuration
public class JustAuthConfig {

    @Value("${auth.wechat.client-id:}")
    private String wechatClientId;

    @Value("${auth.wechat.client-secret:}")
    private String wechatClientSecret;

    @Value("${auth.wechat.redirect-uri:}")
    private String wechatRedirectUri;

    @Value("${auth.google.client-id:}")
    private String googleClientId;

    @Value("${auth.google.client-secret:}")
    private String googleClientSecret;

    @Value("${auth.google.redirect-uri:}")
    private String googleRedirectUri;

    @Value("${auth.facebook.client-id:}")
    private String facebookClientId;

    @Value("${auth.facebook.client-secret:}")
    private String facebookClientSecret;

    @Value("${auth.facebook.redirect-uri:}")
    private String facebookRedirectUri;

    /**
     * 获取微信配置
     */
    public AuthConfig getWechatConfig() {
        return AuthConfig.builder()
                .clientId(wechatClientId)
                .clientSecret(wechatClientSecret)
                .redirectUri(wechatRedirectUri)
                .build();
    }

    /**
     * 获取Google配置
     */
    public AuthConfig getGoogleConfig() {
        return AuthConfig.builder()
                .clientId(googleClientId)
                .clientSecret(googleClientSecret)
                .redirectUri(googleRedirectUri)
                .build();
    }

    /**
     * 获取Facebook配置
     */
    public AuthConfig getFacebookConfig() {
        return AuthConfig.builder()
                .clientId(facebookClientId)
                .clientSecret(facebookClientSecret)
                .redirectUri(facebookRedirectUri)
                .build();
    }
}
