package com.hncboy.chatgpt.framework.payment.factory;

import com.egzosn.pay.ali.api.AliPayConfigStorage;
import com.egzosn.pay.ali.api.AliPayService;
import com.egzosn.pay.common.api.PayService;
import com.egzosn.pay.wx.api.WxPayConfigStorage;
import com.egzosn.pay.wx.api.WxPayService;
import com.hncboy.chatgpt.api.domain.entity.PaymentChannelConfig;
import com.hncboy.chatgpt.api.service.PaymentChannelConfigService;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * 支付服务工厂 - 基于pay-java-parent官方实现
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Component
@Slf4j
public class PayServiceFactory {
    
    @Autowired
    private PaymentChannelConfigService channelConfigService;
    
    /**
     * 获取支付服务 - 基于pay-java-parent官方实现
     *
     * @param channelCode 渠道代码
     * @param businessScene 业务场景
     * @return 支付服务
     */
    @Cacheable(value = "payment_config", key = "#channelCode + '_' + #businessScene")
    public PayService getPayService(String channelCode, String businessScene) {
        PaymentChannelConfig config = channelConfigService.getByChannelAndScene(channelCode, businessScene);
        
        if (config == null || config.getEnabled() != 1) {
            throw new ServiceException("支付渠道未配置或已禁用: " + channelCode);
        }
        
        switch (channelCode.toUpperCase()) {
            case "ALIPAY":
                return createAlipayService(config);
            case "WECHAT":
                return createWechatPayService(config);
            default:
                throw new ServiceException("不支持的支付渠道: " + channelCode);
        }
    }
    
    /**
     * 创建支付宝支付服务 - 使用pay-java-parent官方配置
     */
    private PayService createAlipayService(PaymentChannelConfig config) {
        try {
            AliPayConfigStorage aliPayConfig = new AliPayConfigStorage();
            aliPayConfig.setAppId(config.getAppId());
            aliPayConfig.setPid(config.getMerchantId());
            aliPayConfig.setKeyPrivate(config.getPrivateKey());
            aliPayConfig.setKeyPublic(config.getPublicKey());
            aliPayConfig.setSignType(config.getSignType());
            aliPayConfig.setNotifyUrl(config.getNotifyUrl());
            aliPayConfig.setReturnUrl(config.getReturnUrl());
            aliPayConfig.setInputCharset("UTF-8");
            
            return new AliPayService(aliPayConfig);
        } catch (Exception e) {
            log.error("创建支付宝支付服务失败: {}", config.getChannelCode(), e);
            throw new ServiceException("支付宝配置错误: " + e.getMessage());
        }
    }
    
    /**
     * 创建微信支付服务 - 使用pay-java-parent官方配置
     */
    private PayService createWechatPayService(PaymentChannelConfig config) {
        try {
            WxPayConfigStorage wxPayConfig = new WxPayConfigStorage();
            wxPayConfig.setAppId(config.getAppId());
            wxPayConfig.setMchId(config.getMerchantId());
            wxPayConfig.setKeyPrivate(config.getPrivateKey());
            wxPayConfig.setSecretKey(config.getAppSecret());
            wxPayConfig.setNotifyUrl(config.getNotifyUrl());
            wxPayConfig.setReturnUrl(config.getReturnUrl());
            wxPayConfig.setSignType(config.getSignType());
            wxPayConfig.setInputCharset("UTF-8");
            
            // 设置证书路径（如果有）
            if (config.getCertPath() != null) {
                wxPayConfig.setCertStoreType("PKCS12");
                wxPayConfig.setKeystorePath(config.getCertPath());
                wxPayConfig.setStorePassword(config.getMerchantId());
            }
            
            return new WxPayService(wxPayConfig);
        } catch (Exception e) {
            log.error("创建微信支付服务失败: {}", config.getChannelCode(), e);
            throw new ServiceException("微信支付配置错误: " + e.getMessage());
        }
    }
}
