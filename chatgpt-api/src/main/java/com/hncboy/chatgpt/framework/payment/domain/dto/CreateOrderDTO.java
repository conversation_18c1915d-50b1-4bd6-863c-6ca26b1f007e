package com.hncboy.chatgpt.framework.payment.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 创建订单DTO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Schema(title = "创建订单DTO")
public class CreateOrderDTO {

    @Schema(title = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @Schema(title = "产品ID")
    @NotNull(message = "产品ID不能为空")
    private Long productId;

    @Schema(title = "产品名称")
    @NotBlank(message = "产品名称不能为空")
    private String productName;

    @Schema(title = "产品类型")
    private String productType;

    @Schema(title = "业务场景")
    @NotBlank(message = "业务场景不能为空")
    private String businessScene;

    @Schema(title = "支付渠道")
    private String paymentChannel;

    @Schema(title = "订单金额")
    @NotNull(message = "订单金额不能为空")
    private BigDecimal amount;

    @Schema(title = "币种")
    private String currency = "CNY";

    @Schema(title = "客户端IP")
    private String clientIp;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "备注")
    private String remark;
}
