package com.hncboy.chatgpt.framework.ai.service;

import com.hncboy.chatgpt.biz.chatoi.domain.dto.ChatRequestDTO;
import com.hncboy.chatgpt.biz.chatoi.domain.vo.ChatResponseVO;
import reactor.core.publisher.Flux;

/**
 * Spring AI聊天服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface SpringAIChatService {

    /**
     * AI对话 - 使用Spring AI ChatClient
     *
     * @param request 对话请求
     * @return 对话响应
     */
    ChatResponseVO chat(ChatRequestDTO request);

    /**
     * 流式对话 - Spring AI原生支持
     *
     * @param request 对话请求
     * @return 流式响应
     */
    Flux<String> streamChat(ChatRequestDTO request);

    /**
     * 带工具调用的AI对话
     *
     * @param userMessage 用户消息
     * @param userId 用户ID
     * @return 对话响应
     */
    String chatWithAgent(String userMessage, Integer userId);

    /**
     * 图像理解 - Spring AI新增功能
     *
     * @param imageUrl 图片URL
     * @param question 问题
     * @return 分析结果
     */
    String analyzeImage(String imageUrl, String question);
}
