package com.hncboy.chatgpt.framework.pay.factory;

import com.hncboy.chatgpt.db.entity.pay.PayOrder;
import com.hncboy.chatgpt.framework.pay.domain.vo.PayResultVO;
import com.hncboy.chatgpt.common.enums.PayChannelEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 支付服务工厂
 * 
 * 功能特点:
 * 1. 统一的支付服务入口
 * 2. 支持多种支付渠道 (支付宝/微信/SE支付/Momo支付)
 * 3. 自动路由到对应的支付实现
 * 4. 统一的异常处理和降级策略
 * 5. 支持支付回调验证和解析
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PayServiceFactory {

    /**
     * 创建支付
     *
     * @param payOrder 支付订单
     * @return 支付结果
     */
    public PayResultVO createPayment(PayOrder payOrder) {
        try {
            log.info("创建支付: orderNo={}, channel={}, amount={}", 
                    payOrder.getOrderNo(), payOrder.getPayChannel(), payOrder.getAmount());

            PayChannelEnum channel = PayChannelEnum.getByCode(payOrder.getPayChannel());
            if (channel == null) {
                throw new RuntimeException("不支持的支付渠道: " + payOrder.getPayChannel());
            }

            PayResultVO result = switch (channel) {
                case ALIPAY -> createAlipayPayment(payOrder);
                case WECHAT -> createWechatPayment(payOrder);
                case SEPAY -> createSepayPayment(payOrder);
                case MOMO -> createMomoPayment(payOrder);
                default -> throw new RuntimeException("暂不支持的支付渠道: " + channel.getName());
            };

            log.info("创建支付成功: orderNo={}, channel={}, success={}", 
                    payOrder.getOrderNo(), payOrder.getPayChannel(), result.isSuccess());

            return result;

        } catch (Exception e) {
            log.error("创建支付失败: orderNo={}, channel={}", 
                    payOrder.getOrderNo(), payOrder.getPayChannel(), e);
            return createFailureResult(payOrder, e.getMessage());
        }
    }

    /**
     * 查询支付状态
     *
     * @param payOrder 支付订单
     * @return 支付结果
     */
    public PayResultVO queryPayment(PayOrder payOrder) {
        try {
            log.info("查询支付状态: orderNo={}, channel={}", 
                    payOrder.getOrderNo(), payOrder.getPayChannel());

            PayChannelEnum channel = PayChannelEnum.getByCode(payOrder.getPayChannel());
            if (channel == null) {
                throw new RuntimeException("不支持的支付渠道: " + payOrder.getPayChannel());
            }

            PayResultVO result = switch (channel) {
                case ALIPAY -> queryAlipayPayment(payOrder);
                case WECHAT -> queryWechatPayment(payOrder);
                case SEPAY -> querySepayPayment(payOrder);
                case MOMO -> queryMomoPayment(payOrder);
                default -> throw new RuntimeException("暂不支持的支付渠道: " + channel.getName());
            };

            log.info("查询支付状态成功: orderNo={}, channel={}, status={}", 
                    payOrder.getOrderNo(), payOrder.getPayChannel(), result.getStatus());

            return result;

        } catch (Exception e) {
            log.error("查询支付状态失败: orderNo={}, channel={}", 
                    payOrder.getOrderNo(), payOrder.getPayChannel(), e);
            return createFailureResult(payOrder, e.getMessage());
        }
    }

    /**
     * 申请退款
     *
     * @param payOrder 支付订单
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款结果
     */
    public PayResultVO refundPayment(PayOrder payOrder, BigDecimal refundAmount, String refundReason) {
        try {
            log.info("申请退款: orderNo={}, channel={}, amount={}, reason={}", 
                    payOrder.getOrderNo(), payOrder.getPayChannel(), refundAmount, refundReason);

            PayChannelEnum channel = PayChannelEnum.getByCode(payOrder.getPayChannel());
            if (channel == null) {
                throw new RuntimeException("不支持的支付渠道: " + payOrder.getPayChannel());
            }

            PayResultVO result = switch (channel) {
                case ALIPAY -> refundAlipayPayment(payOrder, refundAmount, refundReason);
                case WECHAT -> refundWechatPayment(payOrder, refundAmount, refundReason);
                case SEPAY -> refundSepayPayment(payOrder, refundAmount, refundReason);
                case MOMO -> refundMomoPayment(payOrder, refundAmount, refundReason);
                default -> throw new RuntimeException("暂不支持的支付渠道: " + channel.getName());
            };

            log.info("申请退款完成: orderNo={}, channel={}, success={}", 
                    payOrder.getOrderNo(), payOrder.getPayChannel(), result.isSuccess());

            return result;

        } catch (Exception e) {
            log.error("申请退款失败: orderNo={}, channel={}, amount={}", 
                    payOrder.getOrderNo(), payOrder.getPayChannel(), refundAmount, e);
            return createFailureResult(payOrder, e.getMessage());
        }
    }

    /**
     * 验证支付回调
     *
     * @param payChannel 支付渠道
     * @param callbackData 回调数据
     * @return 是否验证通过
     */
    public boolean validateCallback(String payChannel, String callbackData) {
        try {
            log.info("验证支付回调: channel={}", payChannel);

            PayChannelEnum channel = PayChannelEnum.getByCode(payChannel);
            if (channel == null) {
                log.warn("不支持的支付渠道: {}", payChannel);
                return false;
            }

            boolean valid = switch (channel) {
                case ALIPAY -> validateAlipayCallback(callbackData);
                case WECHAT -> validateWechatCallback(callbackData);
                case SEPAY -> validateSepayCallback(callbackData);
                case MOMO -> validateMomoCallback(callbackData);
                default -> false;
            };

            log.info("验证支付回调完成: channel={}, valid={}", payChannel, valid);
            return valid;

        } catch (Exception e) {
            log.error("验证支付回调失败: channel={}", payChannel, e);
            return false;
        }
    }

    /**
     * 解析支付回调
     *
     * @param payChannel 支付渠道
     * @param callbackData 回调数据
     * @return 支付订单信息
     */
    public PayOrder parseCallback(String payChannel, String callbackData) {
        try {
            log.info("解析支付回调: channel={}", payChannel);

            PayChannelEnum channel = PayChannelEnum.getByCode(payChannel);
            if (channel == null) {
                throw new RuntimeException("不支持的支付渠道: " + payChannel);
            }

            PayOrder payOrder = switch (channel) {
                case ALIPAY -> parseAlipayCallback(callbackData);
                case WECHAT -> parseWechatCallback(callbackData);
                case SEPAY -> parseSepayCallback(callbackData);
                case MOMO -> parseMomoCallback(callbackData);
                default -> throw new RuntimeException("暂不支持的支付渠道: " + channel.getName());
            };

            log.info("解析支付回调成功: channel={}, orderNo={}", payChannel, payOrder.getOrderNo());
            return payOrder;

        } catch (Exception e) {
            log.error("解析支付回调失败: channel={}", payChannel, e);
            return null;
        }
    }

    // ==================== 私有方法 - 支付宝 ====================

    private PayResultVO createAlipayPayment(PayOrder payOrder) {
        // 这里应该调用支付宝SDK创建支付
        // 暂时模拟实现
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setThirdPartyOrderNo("ALI" + System.currentTimeMillis());
        result.setStatus("PENDING");
        result.setAmount(payOrder.getAmount());
        result.setPayChannel("ALIPAY");
        result.setPayMethod(payOrder.getPayMethod());
        result.setSuccess(true);
        result.setPayParams("{\"app_id\":\"2021001234567890\",\"method\":\"alipay.trade.app.pay\"}");
        return result;
    }

    private PayResultVO queryAlipayPayment(PayOrder payOrder) {
        // 模拟查询支付宝支付状态
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setThirdPartyOrderNo(payOrder.getThirdPartyOrderNo());
        result.setStatus("PAID");
        result.setAmount(payOrder.getAmount());
        result.setPaidAmount(payOrder.getAmount());
        result.setSuccess(true);
        return result;
    }

    private PayResultVO refundAlipayPayment(PayOrder payOrder, BigDecimal refundAmount, String refundReason) {
        // 模拟支付宝退款
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setAmount(refundAmount);
        result.setSuccess(true);
        return result;
    }

    private boolean validateAlipayCallback(String callbackData) {
        // 模拟支付宝回调验证
        return true;
    }

    private PayOrder parseAlipayCallback(String callbackData) {
        // 模拟解析支付宝回调
        PayOrder payOrder = new PayOrder();
        payOrder.setOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        payOrder.setStatus("PAID");
        return payOrder;
    }

    // ==================== 私有方法 - 微信支付 ====================

    private PayResultVO createWechatPayment(PayOrder payOrder) {
        // 模拟微信支付
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setThirdPartyOrderNo("WX" + System.currentTimeMillis());
        result.setStatus("PENDING");
        result.setAmount(payOrder.getAmount());
        result.setPayChannel("WECHAT");
        result.setSuccess(true);
        return result;
    }

    private PayResultVO queryWechatPayment(PayOrder payOrder) {
        // 模拟查询微信支付状态
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setStatus("PAID");
        result.setSuccess(true);
        return result;
    }

    private PayResultVO refundWechatPayment(PayOrder payOrder, BigDecimal refundAmount, String refundReason) {
        // 模拟微信退款
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setAmount(refundAmount);
        result.setSuccess(true);
        return result;
    }

    private boolean validateWechatCallback(String callbackData) {
        return true;
    }

    private PayOrder parseWechatCallback(String callbackData) {
        PayOrder payOrder = new PayOrder();
        payOrder.setOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        payOrder.setStatus("PAID");
        return payOrder;
    }

    // ==================== 私有方法 - SE支付 ====================

    private PayResultVO createSepayPayment(PayOrder payOrder) {
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setThirdPartyOrderNo("SE" + System.currentTimeMillis());
        result.setStatus("PENDING");
        result.setAmount(payOrder.getAmount());
        result.setPayChannel("SEPAY");
        result.setSuccess(true);
        return result;
    }

    private PayResultVO querySepayPayment(PayOrder payOrder) {
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setStatus("PAID");
        result.setSuccess(true);
        return result;
    }

    private PayResultVO refundSepayPayment(PayOrder payOrder, BigDecimal refundAmount, String refundReason) {
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setAmount(refundAmount);
        result.setSuccess(true);
        return result;
    }

    private boolean validateSepayCallback(String callbackData) {
        return true;
    }

    private PayOrder parseSepayCallback(String callbackData) {
        PayOrder payOrder = new PayOrder();
        payOrder.setOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        payOrder.setStatus("PAID");
        return payOrder;
    }

    // ==================== 私有方法 - Momo支付 ====================

    private PayResultVO createMomoPayment(PayOrder payOrder) {
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setThirdPartyOrderNo("MOMO" + System.currentTimeMillis());
        result.setStatus("PENDING");
        result.setAmount(payOrder.getAmount());
        result.setPayChannel("MOMO");
        result.setSuccess(true);
        return result;
    }

    private PayResultVO queryMomoPayment(PayOrder payOrder) {
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setStatus("PAID");
        result.setSuccess(true);
        return result;
    }

    private PayResultVO refundMomoPayment(PayOrder payOrder, BigDecimal refundAmount, String refundReason) {
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setAmount(refundAmount);
        result.setSuccess(true);
        return result;
    }

    private boolean validateMomoCallback(String callbackData) {
        return true;
    }

    private PayOrder parseMomoCallback(String callbackData) {
        PayOrder payOrder = new PayOrder();
        payOrder.setOrderNo("TEST_ORDER_" + System.currentTimeMillis());
        payOrder.setStatus("PAID");
        return payOrder;
    }

    // ==================== 工具方法 ====================

    private PayResultVO createFailureResult(PayOrder payOrder, String errorMessage) {
        PayResultVO result = new PayResultVO();
        result.setOrderNo(payOrder.getOrderNo());
        result.setStatus("FAILED");
        result.setAmount(payOrder.getAmount());
        result.setPayChannel(payOrder.getPayChannel());
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
