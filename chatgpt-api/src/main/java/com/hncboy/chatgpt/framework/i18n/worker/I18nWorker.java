package com.hncboy.chatgpt.framework.i18n.worker;

import com.hncboy.chatgpt.framework.cache.manager.MultiLevelCacheManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 国际化业务逻辑Worker
 * 
 * 功能特点:
 * 1. 数据库驱动的国际化系统
 * 2. 按值翻译: 直接用字段值查找翻译
 * 3. 参数化翻译: 支持{{param}}格式的动态参数
 * 4. 智能回退: 有翻译就翻译，没有就返回原文
 * 5. 多级缓存: Redis + 本地缓存优化性能
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class I18nWorker {

    private final MultiLevelCacheManager cacheManager;
    
    // 参数化翻译的正则表达式
    private static final Pattern PARAM_PATTERN = Pattern.compile("\\{\\{([^}]+)\\}\\}");
    
    // 缓存前缀
    private static final String CACHE_PREFIX = "i18n:";
    private static final long CACHE_TTL = 3600; // 1小时

    /**
     * 按值翻译 (直接用字段值查找翻译)
     *
     * @param value 原始值
     * @param language 目标语言
     * @return 翻译后的值
     */
    public String translateByValue(String value, String language) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }

        // 默认语言直接返回
        if ("zh_CN".equals(language)) {
            return value;
        }

        try {
            // 构建缓存键
            String cacheKey = CACHE_PREFIX + "value:" + language + ":" + value.hashCode();
            
            // 先从缓存获取
            Object cached = cacheManager.get(cacheKey);
            if (cached != null) {
                return cached.toString();
            }

            // 从数据库查询翻译 (这里模拟数据库查询)
            String translation = queryTranslationFromDatabase(value, language);
            
            if (translation != null && !translation.equals(value)) {
                // 缓存翻译结果
                cacheManager.set(cacheKey, translation, CACHE_TTL);
                log.debug("翻译成功: {} -> {} ({})", value, translation, language);
                return translation;
            }

            // 没有翻译，缓存原值避免重复查询
            cacheManager.set(cacheKey, value, CACHE_TTL);
            return value;

        } catch (Exception e) {
            log.warn("翻译失败: value={}, language={}", value, language, e);
            return value; // 翻译失败返回原值
        }
    }

    /**
     * 参数化翻译 (支持{{param}}格式的动态参数)
     *
     * @param template 翻译模板
     * @param language 目标语言
     * @param params 参数映射
     * @return 翻译并替换参数后的文本
     */
    public String translateWithParams(String template, String language, Map<String, Object> params) {
        if (template == null || template.trim().isEmpty()) {
            return template;
        }

        try {
            // 1. 先翻译模板
            String translatedTemplate = translateByValue(template, language);

            // 2. 替换参数
            if (params != null && !params.isEmpty()) {
                translatedTemplate = replaceParameters(translatedTemplate, params, language);
            }

            return translatedTemplate;

        } catch (Exception e) {
            log.warn("参数化翻译失败: template={}, language={}, params={}", template, language, params, e);
            return template;
        }
    }

    /**
     * 批量翻译
     *
     * @param values 原始值列表
     * @param language 目标语言
     * @return 翻译后的值映射
     */
    public Map<String, String> batchTranslate(String[] values, String language) {
        Map<String, String> result = new HashMap<>();
        
        if (values == null || values.length == 0) {
            return result;
        }

        for (String value : values) {
            result.put(value, translateByValue(value, language));
        }

        return result;
    }

    /**
     * 获取支持的语言列表
     *
     * @return 语言列表
     */
    public String[] getSupportedLanguages() {
        try {
            // 从缓存获取
            String cacheKey = CACHE_PREFIX + "languages";
            Object cached = cacheManager.get(cacheKey);
            if (cached instanceof String[]) {
                return (String[]) cached;
            }

            // 从数据库查询支持的语言
            String[] languages = querySupportedLanguagesFromDatabase();
            
            // 缓存结果
            cacheManager.set(cacheKey, languages, CACHE_TTL * 24); // 24小时
            
            return languages;

        } catch (Exception e) {
            log.warn("获取支持的语言列表失败", e);
            return new String[]{"zh_CN", "en_US", "vi_VN"}; // 默认支持的语言
        }
    }

    /**
     * 清除翻译缓存
     *
     * @param language 语言 (可选，为空则清除所有)
     */
    public void clearTranslationCache(String language) {
        try {
            if (language != null && !language.trim().isEmpty()) {
                // 清除指定语言的缓存
                cacheManager.deleteByPattern(CACHE_PREFIX + "*:" + language + ":*");
                log.info("清除翻译缓存完成: language={}", language);
            } else {
                // 清除所有翻译缓存
                cacheManager.deleteByPattern(CACHE_PREFIX + "*");
                log.info("清除所有翻译缓存完成");
            }
        } catch (Exception e) {
            log.error("清除翻译缓存失败: language={}", language, e);
        }
    }

    /**
     * 预热翻译缓存
     *
     * @param language 目标语言
     */
    public void preloadTranslationCache(String language) {
        try {
            log.info("开始预热翻译缓存: language={}", language);

            // 预热常用翻译
            preloadCommonTranslations(language);

            // 预热业务翻译
            preloadBusinessTranslations(language);

            log.info("预热翻译缓存完成: language={}", language);

        } catch (Exception e) {
            log.error("预热翻译缓存失败: language={}", language, e);
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 从数据库查询翻译
     */
    private String queryTranslationFromDatabase(String value, String language) {
        // 这里应该查询数据库中的翻译表
        // 模拟数据库查询逻辑
        
        // 示例翻译映射
        Map<String, Map<String, String>> translations = getExampleTranslations();
        
        Map<String, String> langTranslations = translations.get(language);
        if (langTranslations != null) {
            return langTranslations.get(value);
        }
        
        return null;
    }

    /**
     * 查询支持的语言列表
     */
    private String[] querySupportedLanguagesFromDatabase() {
        // 这里应该查询数据库中支持的语言
        return new String[]{"zh_CN", "en_US", "vi_VN", "ja_JP", "ko_KR"};
    }

    /**
     * 替换参数
     */
    private String replaceParameters(String template, Map<String, Object> params, String language) {
        Matcher matcher = PARAM_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String paramName = matcher.group(1);
            Object paramValue = params.get(paramName);
            
            if (paramValue != null) {
                // 如果参数值也需要翻译
                String translatedValue = paramValue.toString();
                if (paramValue instanceof String) {
                    translatedValue = translateByValue((String) paramValue, language);
                }
                matcher.appendReplacement(result, translatedValue);
            } else {
                // 参数不存在，保持原样
                matcher.appendReplacement(result, matcher.group(0));
            }
        }
        
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 预热常用翻译
     */
    private void preloadCommonTranslations(String language) {
        String[] commonWords = {
            "成功", "失败", "错误", "警告", "信息",
            "确定", "取消", "保存", "删除", "编辑",
            "用户", "密码", "登录", "注册", "退出"
        };

        for (String word : commonWords) {
            translateByValue(word, language);
        }
    }

    /**
     * 预热业务翻译
     */
    private void preloadBusinessTranslations(String language) {
        String[] businessWords = {
            "塔罗牌", "占卜", "解读", "牌阵", "运势",
            "AI对话", "智能助手", "模型", "聊天", "消息",
            "支付", "订单", "充值", "余额", "会员"
        };

        for (String word : businessWords) {
            translateByValue(word, language);
        }
    }

    /**
     * 获取示例翻译数据
     */
    private Map<String, Map<String, String>> getExampleTranslations() {
        Map<String, Map<String, String>> translations = new HashMap<>();
        
        // 英文翻译
        Map<String, String> enTranslations = new HashMap<>();
        enTranslations.put("成功", "Success");
        enTranslations.put("失败", "Failed");
        enTranslations.put("用户", "User");
        enTranslations.put("塔罗牌", "Tarot");
        enTranslations.put("AI对话", "AI Chat");
        enTranslations.put("支付", "Payment");
        translations.put("en_US", enTranslations);
        
        // 越南语翻译
        Map<String, String> viTranslations = new HashMap<>();
        viTranslations.put("成功", "Thành công");
        viTranslations.put("失败", "Thất bại");
        viTranslations.put("用户", "Người dùng");
        viTranslations.put("塔罗牌", "Tarot");
        viTranslations.put("AI对话", "Trò chuyện AI");
        viTranslations.put("支付", "Thanh toán");
        translations.put("vi_VN", viTranslations);
        
        return translations;
    }
}
