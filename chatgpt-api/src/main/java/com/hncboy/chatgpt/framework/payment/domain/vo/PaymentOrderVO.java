package com.hncboy.chatgpt.framework.payment.domain.vo;

import com.hncboy.chatgpt.common.i18n.I18nTranslation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付订单VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Schema(title = "支付订单VO")
public class PaymentOrderVO {

    @Schema(title = "订单ID")
    private Long id;

    @Schema(title = "订单号")
    private String orderNo;

    @Schema(title = "产品名称")
    private String productName;

    @Schema(title = "业务场景")
    @I18nTranslation(byValue = true)
    private String businessScene;

    @Schema(title = "支付渠道")
    @I18nTranslation(byValue = true)
    private String paymentChannel;

    @Schema(title = "订单金额")
    private BigDecimal amount;

    @Schema(title = "实付金额")
    private BigDecimal paidAmount;

    @Schema(title = "币种")
    private String currency;

    @Schema(title = "订单状态")
    @I18nTranslation(key = "payment.status", paramFields = {"status"})
    private String statusText;

    @Schema(title = "状态码")
    private Integer status;

    @Schema(title = "支付时间")
    private LocalDateTime payTime;

    @Schema(title = "创建时间")
    private LocalDateTime createTime;
}
