package com.hncboy.chatgpt.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.api.domain.entity.PaymentOrder;
import com.hncboy.chatgpt.api.mapper.PaymentOrderMapper;
import com.hncboy.chatgpt.api.service.PaymentOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一支付订单服务实现
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Service
@Slf4j
public class PaymentOrderServiceImpl extends ServiceImpl<PaymentOrderMapper, PaymentOrder> implements PaymentOrderService {

    @Override
    @Cacheable(value = "payment_order", key = "#orderNo")
    public PaymentOrder getByOrderNo(String orderNo) {
        return baseMapper.selectByOrderNo(orderNo);
    }

    @Override
    public List<PaymentOrder> getByUserId(Integer userId) {
        return baseMapper.selectByUserId(userId);
    }

    @Override
    public List<PaymentOrder> getByBusinessScene(String businessScene) {
        return baseMapper.selectByBusinessScene(businessScene);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "payment_order", key = "#orderNo")
    public Boolean updateToPaid(String orderNo, String thirdPartyTransactionId) {
        try {
            LambdaUpdateWrapper<PaymentOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PaymentOrder::getOrderNo, orderNo)
                    .eq(PaymentOrder::getStatus, 0) // 只能从待支付状态更新
                    .set(PaymentOrder::getStatus, 1) // 已支付
                    .set(PaymentOrder::getThirdPartyTransactionId, thirdPartyTransactionId)
                    .set(PaymentOrder::getPayTime, LocalDateTime.now())
                    .set(PaymentOrder::getCallbackTime, LocalDateTime.now());

            boolean result = update(updateWrapper);
            if (result) {
                log.info("订单支付成功: orderNo={}, transactionId={}", orderNo, thirdPartyTransactionId);
                // TODO: 执行业务逻辑 (发货、增加积分等)
                executeBusinessLogic(orderNo);
            }
            return result;
        } catch (Exception e) {
            log.error("更新订单支付状态失败: orderNo={}", orderNo, e);
            throw new RuntimeException("更新订单状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "payment_order", key = "#orderNo")
    public Boolean updateToCancelled(String orderNo, String reason) {
        try {
            LambdaUpdateWrapper<PaymentOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PaymentOrder::getOrderNo, orderNo)
                    .eq(PaymentOrder::getStatus, 0) // 只能从待支付状态取消
                    .set(PaymentOrder::getStatus, 2) // 已取消
                    .set(PaymentOrder::getFailureReason, reason);

            boolean result = update(updateWrapper);
            if (result) {
                log.info("订单取消成功: orderNo={}, reason={}", orderNo, reason);
            }
            return result;
        } catch (Exception e) {
            log.error("取消订单失败: orderNo={}", orderNo, e);
            throw new RuntimeException("取消订单失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "payment_order", key = "#orderNo")
    public Boolean updateToRefunded(String orderNo, BigDecimal refundAmount, String reason) {
        try {
            LambdaUpdateWrapper<PaymentOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PaymentOrder::getOrderNo, orderNo)
                    .eq(PaymentOrder::getStatus, 1) // 只能从已支付状态退款
                    .set(PaymentOrder::getStatus, 3) // 已退款
                    .set(PaymentOrder::getPaidAmount, refundAmount)
                    .set(PaymentOrder::getFailureReason, reason);

            boolean result = update(updateWrapper);
            if (result) {
                log.info("订单退款成功: orderNo={}, refundAmount={}, reason={}", orderNo, refundAmount, reason);
                // TODO: 执行退款业务逻辑
                executeRefundBusinessLogic(orderNo, refundAmount);
            }
            return result;
        } catch (Exception e) {
            log.error("订单退款失败: orderNo={}", orderNo, e);
            throw new RuntimeException("订单退款失败: " + e.getMessage());
        }
    }

    /**
     * 执行支付成功后的业务逻辑
     */
    private void executeBusinessLogic(String orderNo) {
        try {
            PaymentOrder order = getByOrderNo(orderNo);
            if (order == null) {
                return;
            }

            // 根据业务场景执行不同的业务逻辑
            switch (order.getBusinessScene()) {
                case "tarot":
                    // 塔罗牌业务逻辑
                    executeTarotBusinessLogic(order);
                    break;
                case "zns":
                    // 紫微斗数业务逻辑
                    executeZnsBusinessLogic(order);
                    break;
                case "chatoi":
                    // AI对话业务逻辑
                    executeChatOiBusinessLogic(order);
                    break;
                default:
                    log.warn("未知业务场景: {}", order.getBusinessScene());
            }
        } catch (Exception e) {
            log.error("执行业务逻辑失败: orderNo={}", orderNo, e);
        }
    }

    /**
     * 执行退款后的业务逻辑
     */
    private void executeRefundBusinessLogic(String orderNo, BigDecimal refundAmount) {
        try {
            PaymentOrder order = getByOrderNo(orderNo);
            if (order == null) {
                return;
            }

            // TODO: 根据业务场景执行退款逻辑
            log.info("执行退款业务逻辑: orderNo={}, scene={}, amount={}", 
                    orderNo, order.getBusinessScene(), refundAmount);
        } catch (Exception e) {
            log.error("执行退款业务逻辑失败: orderNo={}", orderNo, e);
        }
    }

    private void executeTarotBusinessLogic(PaymentOrder order) {
        // TODO: 塔罗牌支付成功业务逻辑
        log.info("执行塔罗牌业务逻辑: orderNo={}", order.getOrderNo());
    }

    private void executeZnsBusinessLogic(PaymentOrder order) {
        // TODO: 紫微斗数支付成功业务逻辑
        log.info("执行紫微斗数业务逻辑: orderNo={}", order.getOrderNo());
    }

    private void executeChatOiBusinessLogic(PaymentOrder order) {
        // TODO: AI对话支付成功业务逻辑
        log.info("执行AI对话业务逻辑: orderNo={}", order.getOrderNo());
    }

    @Override
    public PaymentOrder getByPaymentParams(String paymentParams) {
        LambdaQueryWrapper<PaymentOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PaymentOrder::getPaymentParams, paymentParams)
                .eq(PaymentOrder::getDeleted, 0);
        return getOne(queryWrapper);
    }
}
