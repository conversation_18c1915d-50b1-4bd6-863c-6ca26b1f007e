package com.hncboy.chatgpt.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.api.domain.entity.UserJointLogin;

import java.util.List;

/**
 * 用户联合登录服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface UserJointLoginService extends IService<UserJointLogin> {

    /**
     * 根据用户ID查询所有登录方式
     *
     * @param userId 用户ID
     * @return 登录方式列表
     */
    List<UserJointLogin> getByUserId(Integer userId);

    /**
     * 根据登录类型和第三方ID查询
     *
     * @param loginType 登录类型
     * @param thirdPartyId 第三方ID
     * @return 用户联合登录
     */
    UserJointLogin getByTypeAndThirdPartyId(String loginType, String thirdPartyId);

    /**
     * 根据微信OpenID查询
     *
     * @param openId 微信OpenID
     * @return 用户联合登录
     */
    UserJointLogin getByWechatOpenId(String openId);

    /**
     * 绑定第三方账号
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @param thirdPartyId 第三方ID
     * @param extraInfo 额外信息
     * @return 是否成功
     */
    Boolean bindThirdPartyAccount(Integer userId, String loginType, String thirdPartyId, String extraInfo);

    /**
     * 解绑第三方账号
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 是否成功
     */
    Boolean unbindThirdPartyAccount(Integer userId, String loginType);

    /**
     * 设置主要登录方式
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 是否成功
     */
    Boolean setPrimaryLogin(Integer userId, String loginType);
}
