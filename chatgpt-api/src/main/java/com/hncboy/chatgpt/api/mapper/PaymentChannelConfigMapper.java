package com.hncboy.chatgpt.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.api.domain.entity.PaymentChannelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 支付渠道配置Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface PaymentChannelConfigMapper extends BaseMapper<PaymentChannelConfig> {

    /**
     * 根据渠道代码和业务场景查询
     *
     * @param channelCode 渠道代码
     * @param businessScene 业务场景
     * @return 支付渠道配置
     */
    PaymentChannelConfig selectByChannelAndScene(@Param("channelCode") String channelCode, 
                                                @Param("businessScene") String businessScene);

    /**
     * 根据业务场景查询所有启用的渠道
     *
     * @param businessScene 业务场景
     * @return 支付渠道配置列表
     */
    List<PaymentChannelConfig> selectEnabledByScene(@Param("businessScene") String businessScene);

    /**
     * 根据渠道代码查询所有场景的配置
     *
     * @param channelCode 渠道代码
     * @return 支付渠道配置列表
     */
    List<PaymentChannelConfig> selectByChannelCode(@Param("channelCode") String channelCode);
}
