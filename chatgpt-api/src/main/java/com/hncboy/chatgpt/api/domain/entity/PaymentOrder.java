package com.hncboy.chatgpt.api.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 统一支付订单实体
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@TableName("payment_order")
@Schema(title = "统一支付订单实体")
public class PaymentOrder {

    @Schema(title = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "订单号")
    private String orderNo;

    @Schema(title = "用户ID")
    private Integer userId;

    @Schema(title = "产品ID")
    private Long productId;

    @Schema(title = "产品名称")
    private String productName;

    @Schema(title = "产品类型")
    private String productType;

    @Schema(title = "业务场景")
    private String businessScene;

    @Schema(title = "支付渠道")
    private String paymentChannel;

    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "订单金额")
    private BigDecimal amount;

    @Schema(title = "实付金额")
    private BigDecimal paidAmount;

    @Schema(title = "币种")
    private String currency;

    @Schema(title = "汇率")
    private BigDecimal exchangeRate;

    @Schema(title = "订单状态")
    private Integer status;

    @Schema(title = "第三方订单号")
    private String thirdPartyOrderNo;

    @Schema(title = "第三方交易号")
    private String thirdPartyTransactionId;

    @Schema(title = "支付时间")
    private LocalDateTime payTime;

    @Schema(title = "过期时间")
    private LocalDateTime expireTime;

    @Schema(title = "回调时间")
    private LocalDateTime callbackTime;

    @Schema(title = "支付IP")
    private String paymentIp;

    @Schema(title = "支付参数")
    private String paymentParams;

    @Schema(title = "回调参数")
    private String callbackParams;

    @Schema(title = "失败原因")
    private String failureReason;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "是否删除")
    @TableLogic
    private Integer deleted;

    @Schema(title = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(title = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
