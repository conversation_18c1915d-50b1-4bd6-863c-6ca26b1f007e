package com.hncboy.chatgpt.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.api.domain.entity.UserJointLogin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户联合登录Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface UserJointLoginMapper extends BaseMapper<UserJointLogin> {

    /**
     * 根据用户ID查询所有登录方式
     *
     * @param userId 用户ID
     * @return 登录方式列表
     */
    List<UserJointLogin> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据登录类型和第三方ID查询
     *
     * @param loginType 登录类型
     * @param thirdPartyId 第三方ID
     * @return 用户联合登录
     */
    UserJointLogin selectByTypeAndThirdPartyId(@Param("loginType") String loginType, @Param("thirdPartyId") String thirdPartyId);

    /**
     * 根据微信OpenID查询
     *
     * @param openId 微信OpenID
     * @return 用户联合登录
     */
    UserJointLogin selectByWechatOpenId(@Param("openId") String openId);

    /**
     * 根据用户ID和登录类型查询
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 用户联合登录
     */
    UserJointLogin selectByUserIdAndType(@Param("userId") Integer userId, @Param("loginType") String loginType);
}
