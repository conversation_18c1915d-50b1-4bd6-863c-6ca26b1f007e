package com.hncboy.chatgpt.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.api.domain.entity.PaymentChannelConfig;

import java.util.List;

/**
 * 支付渠道配置服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface PaymentChannelConfigService extends IService<PaymentChannelConfig> {

    /**
     * 根据渠道代码和业务场景查询
     *
     * @param channelCode 渠道代码
     * @param businessScene 业务场景
     * @return 支付渠道配置
     */
    PaymentChannelConfig getByChannelAndScene(String channelCode, String businessScene);

    /**
     * 根据业务场景查询所有启用的渠道
     *
     * @param businessScene 业务场景
     * @return 支付渠道配置列表
     */
    List<PaymentChannelConfig> getEnabledByScene(String businessScene);

    /**
     * 根据渠道代码查询所有场景的配置
     *
     * @param channelCode 渠道代码
     * @return 支付渠道配置列表
     */
    List<PaymentChannelConfig> getByChannelCode(String channelCode);

    /**
     * 刷新支付配置缓存
     *
     * @param channelCode 渠道代码
     * @param businessScene 业务场景
     */
    void refreshCache(String channelCode, String businessScene);
}
