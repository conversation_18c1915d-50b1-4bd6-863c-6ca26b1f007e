package com.hncboy.chatgpt.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.api.domain.entity.PaymentChannelConfig;
import com.hncboy.chatgpt.api.mapper.PaymentChannelConfigMapper;
import com.hncboy.chatgpt.api.service.PaymentChannelConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 支付渠道配置服务实现
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Service
@Slf4j
public class PaymentChannelConfigServiceImpl extends ServiceImpl<PaymentChannelConfigMapper, PaymentChannelConfig> implements PaymentChannelConfigService {

    @Override
    @Cacheable(value = "payment_channels", key = "#channelCode + '_' + #businessScene")
    public PaymentChannelConfig getByChannelAndScene(String channelCode, String businessScene) {
        return baseMapper.selectByChannelAndScene(channelCode, businessScene);
    }

    @Override
    @Cacheable(value = "payment_channels", key = "'scene_' + #businessScene")
    public List<PaymentChannelConfig> getEnabledByScene(String businessScene) {
        return baseMapper.selectEnabledByScene(businessScene);
    }

    @Override
    @Cacheable(value = "payment_channels", key = "'channel_' + #channelCode")
    public List<PaymentChannelConfig> getByChannelCode(String channelCode) {
        return baseMapper.selectByChannelCode(channelCode);
    }

    @Override
    @CacheEvict(value = "payment_channels", key = "#channelCode + '_' + #businessScene")
    public void refreshCache(String channelCode, String businessScene) {
        log.info("刷新支付配置缓存: channelCode={}, businessScene={}", channelCode, businessScene);
    }
}
