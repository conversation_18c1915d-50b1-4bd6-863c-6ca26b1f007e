package com.hncboy.chatgpt.api.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付渠道配置实体
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@TableName("payment_channel_config")
@Schema(title = "支付渠道配置实体")
public class PaymentChannelConfig {

    @Schema(title = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "渠道代码")
    private String channelCode;

    @Schema(title = "渠道名称")
    private String channelName;

    @Schema(title = "支付方式")
    private String paymentMethod;

    @Schema(title = "业务场景")
    private String businessScene;

    @Schema(title = "场景名称")
    private String sceneName;

    @Schema(title = "应用ID")
    private String appId;

    @Schema(title = "商户号")
    private String merchantId;

    @Schema(title = "应用密钥")
    private String appSecret;

    @Schema(title = "应用私钥")
    private String privateKey;

    @Schema(title = "平台公钥")
    private String publicKey;

    @Schema(title = "证书路径")
    private String certPath;

    @Schema(title = "API地址")
    private String apiUrl;

    @Schema(title = "异步通知地址")
    private String notifyUrl;

    @Schema(title = "同步返回地址")
    private String returnUrl;

    @Schema(title = "签名类型")
    private String signType;

    @Schema(title = "支持币种")
    private String supportedCurrencies;

    @Schema(title = "是否启用")
    private Integer enabled;

    @Schema(title = "排序权重")
    private Integer sortOrder;

    @Schema(title = "环境")
    private String environment;

    @Schema(title = "费率")
    private BigDecimal feeRate;

    @Schema(title = "最小金额")
    private BigDecimal minAmount;

    @Schema(title = "最大金额")
    private BigDecimal maxAmount;

    @Schema(title = "扩展配置")
    private String extraConfig;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "是否删除")
    @TableLogic
    private Integer deleted;

    @Schema(title = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(title = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
