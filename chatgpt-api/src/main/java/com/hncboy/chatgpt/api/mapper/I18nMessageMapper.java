package com.hncboy.chatgpt.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.api.domain.entity.I18nMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 国际化消息Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface I18nMessageMapper extends BaseMapper<I18nMessage> {

    /**
     * 根据消息键和语言区域查询
     *
     * @param messageKey 消息键
     * @param locale 语言区域
     * @return 国际化消息
     */
    I18nMessage selectByKeyAndLocale(@Param("messageKey") String messageKey, @Param("locale") String locale);

    /**
     * 根据消息键列表和语言区域查询
     *
     * @param messageKeys 消息键列表
     * @param locale 语言区域
     * @return 国际化消息列表
     */
    List<I18nMessage> selectByKeysAndLocale(@Param("messageKeys") List<String> messageKeys, @Param("locale") String locale);

    /**
     * 根据语言区域查询所有消息
     *
     * @param locale 语言区域
     * @return 国际化消息列表
     */
    List<I18nMessage> selectByLocale(@Param("locale") String locale);
}
