package com.hncboy.chatgpt.api.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.api.domain.vo.UserInfoVO;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.front.framework.annotation.ApiAdminRestController;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 用户信息控制器 - 展示国际化翻译效果
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@AllArgsConstructor
@Tag(name = "用户信息相关接口")
@ApiAdminRestController("/user")
public class UserInfoController {

    private final UserBaseInfoService userBaseInfoService;

    @Operation(summary = "获取用户信息 - 支持国际化翻译")
    @GetMapping("/info")
    public R<UserInfoVO> getUserInfo() {
        Integer userId = StpUtil.getLoginIdAsInt();
        
        // 获取用户基础信息
        UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
        if (userInfo == null) {
            return R.fail("用户不存在");
        }
        
        // 构建VO对象
        UserInfoVO vo = new UserInfoVO();
        BeanUtils.copyProperties(userInfo, vo);
        
        // 设置状态文本 (将被国际化翻译)
        vo.setStatusText(userInfo.getStatus() == 0 ? "正常" : "禁用");
        vo.setStatus(userInfo.getStatus());
        
        // 设置积分信息
        vo.setPoints(userInfo.getUseNum() + userInfo.getFreeNum());
        
        // 计算VIP剩余天数
        if (userInfo.getVipEndTime() != null) {
            long days = ChronoUnit.DAYS.between(LocalDateTime.now(), userInfo.getVipEndTime());
            vo.setDays((int) Math.max(0, days));
        } else {
            vo.setDays(0);
        }
        
        // 设置使用统计 (示例数据)
        vo.setChatCount(getUserChatCount(userId));
        vo.setDrawCount(getUserDrawCount(userId));
        vo.setTotalPoints(getUserTotalPoints(userId));

        // 设置VIP到期时间
        vo.setVipEndTime(userInfo.getVipEndTime());
        
        // 返回时会自动进行国际化翻译
        return R.data(vo);
    }

    @Operation(summary = "更新用户语言偏好")
    @PostMapping("/language")
    public R<Boolean> updateLanguage(@RequestParam String language) {
        Integer userId = StpUtil.getLoginIdAsInt();
        try {
            UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
            if (userInfo != null) {
                userInfo.setLanguage(language);
                userBaseInfoService.updateById(userInfo);
                return R.data(true);
            }
            return R.data(false);
        } catch (Exception e) {
            return R.data(false);
        }
    }

    @Operation(summary = "更新用户时区")
    @PostMapping("/timezone")
    public R<Boolean> updateTimezone(@RequestParam String timezone) {
        Integer userId = StpUtil.getLoginIdAsInt();
        try {
            UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
            if (userInfo != null) {
                userInfo.setTimezone(timezone);
                userBaseInfoService.updateById(userInfo);
                return R.data(true);
            }
            return R.data(false);
        } catch (Exception e) {
            return R.data(false);
        }
    }

    @Operation(summary = "更新用户币种偏好")
    @PostMapping("/currency")
    public R<Boolean> updateCurrency(@RequestParam String currency) {
        Integer userId = StpUtil.getLoginIdAsInt();
        try {
            UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
            if (userInfo != null) {
                userInfo.setCurrency(currency);
                userBaseInfoService.updateById(userInfo);
                return R.data(true);
            }
            return R.data(false);
        } catch (Exception e) {
            return R.data(false);
        }
    }

    /**
     * 获取用户对话次数 (示例)
     */
    private Integer getUserChatCount(Integer userId) {
        // TODO: 实现真实的统计逻辑
        return 50;
    }

    /**
     * 获取用户绘画次数 (示例)
     */
    private Integer getUserDrawCount(Integer userId) {
        // TODO: 实现真实的统计逻辑
        return 10;
    }

    /**
     * 获取用户总积分消耗 (示例)
     */
    private Integer getUserTotalPoints(Integer userId) {
        // TODO: 实现真实的统计逻辑
        return 800;
    }
}
