package com.hncboy.chatgpt.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.api.domain.entity.PaymentOrder;

import java.util.List;

/**
 * 统一支付订单服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface PaymentOrderService extends IService<PaymentOrder> {

    /**
     * 根据订单号查询
     *
     * @param orderNo 订单号
     * @return 支付订单
     */
    PaymentOrder getByOrderNo(String orderNo);

    /**
     * 根据用户ID查询订单列表
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    List<PaymentOrder> getByUserId(Integer userId);

    /**
     * 根据业务场景查询订单列表
     *
     * @param businessScene 业务场景
     * @return 订单列表
     */
    List<PaymentOrder> getByBusinessScene(String businessScene);

    /**
     * 更新订单状态为已支付
     *
     * @param orderNo 订单号
     * @param thirdPartyTransactionId 第三方交易号
     * @return 是否成功
     */
    Boolean updateToPaid(String orderNo, String thirdPartyTransactionId);

    /**
     * 更新订单状态为已取消
     *
     * @param orderNo 订单号
     * @param reason 取消原因
     * @return 是否成功
     */
    Boolean updateToCancelled(String orderNo, String reason);

    /**
     * 更新订单状态为已退款
     *
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @param reason 退款原因
     * @return 是否成功
     */
    Boolean updateToRefunded(String orderNo, java.math.BigDecimal refundAmount, String reason);

    /**
     * 根据支付参数查询订单
     *
     * @param paymentParams 支付参数
     * @return 支付订单
     */
    PaymentOrder getByPaymentParams(String paymentParams);
}
