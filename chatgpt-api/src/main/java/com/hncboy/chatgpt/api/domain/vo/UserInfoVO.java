package com.hncboy.chatgpt.api.domain.vo;

import com.hncboy.chatgpt.common.i18n.I18nTranslation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息VO - 支持国际化翻译
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Schema(title = "用户信息VO")
public class UserInfoVO {

    @Schema(title = "用户ID")
    private Integer id;

    @Schema(title = "用户名")
    private String name;

    @Schema(title = "头像")
    private String avatar;

    @Schema(title = "邮箱")
    private String email;

    /**
     * 用户状态翻译 - 按值翻译
     * 数据库中存储: 
     * - "正常" -> "正常" / "Normal" / "Bình thường"
     * - "禁用" -> "禁用" / "Disabled" / "Bị vô hiệu hóa"
     */
    @Schema(title = "状态文本")
    @I18nTranslation(byValue = true)
    private String statusText;

    @Schema(title = "状态码")
    private Integer status;

    /**
     * 积分消息翻译 (支持参数)
     * 数据库中存储:
     * - user.points.message -> "您有{{points}}个积分" / "You have {{points}} points" / "Bạn có {{points}} điểm"
     */
    @Schema(title = "积分消息")
    @I18nTranslation(key = "user.points.message", paramFields = {"points"})
    private String pointsMessage;

    @Schema(title = "积分数量")
    private Integer points;

    /**
     * VIP到期消息翻译 (支持参数)
     * 数据库中存储:
     * - user.vip.expire -> "VIP还有{{days}}天到期" / "VIP expires in {{days}} days" / "VIP hết hạn sau {{days}} ngày"
     */
    @Schema(title = "VIP到期消息")
    @I18nTranslation(key = "user.vip.expire", paramFields = {"days"})
    private String vipExpireMessage;

    @Schema(title = "VIP剩余天数")
    private Integer days;

    /**
     * 复杂参数翻译 (支持多个参数)
     * 数据库中存储:
     * - user.usage.summary -> "本月使用{{chatCount}}次对话，{{drawCount}}次绘画，消耗{{totalPoints}}积分"
     */
    @Schema(title = "使用统计")
    @I18nTranslation(key = "user.usage.summary", paramFields = {"chatCount", "drawCount", "totalPoints"})
    private String usageSummary;

    @Schema(title = "对话次数")
    private Integer chatCount;

    @Schema(title = "绘画次数")
    private Integer drawCount;

    @Schema(title = "总积分消耗")
    private Integer totalPoints;

    @Schema(title = "语言偏好")
    private String language;

    @Schema(title = "时区")
    private String timezone;

    @Schema(title = "币种偏好")
    private String currency;

    @Schema(title = "VIP到期时间")
    private LocalDateTime vipEndTime;

    @Schema(title = "创建时间")
    private LocalDateTime createTime;
}
