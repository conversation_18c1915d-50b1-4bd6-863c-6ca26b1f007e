package com.hncboy.chatgpt.api.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 国际化消息实体
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@TableName("i18n_message")
@Schema(title = "国际化消息实体")
public class I18nMessage {

    @Schema(title = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "消息键")
    private String messageKey;

    @Schema(title = "语言区域")
    private String locale;

    @Schema(title = "消息值")
    private String messageValue;

    @Schema(title = "所属模块")
    private String module;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(title = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
