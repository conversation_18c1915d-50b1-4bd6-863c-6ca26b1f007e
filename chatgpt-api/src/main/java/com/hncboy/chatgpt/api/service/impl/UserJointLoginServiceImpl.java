package com.hncboy.chatgpt.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.api.domain.entity.UserJointLogin;
import com.hncboy.chatgpt.api.mapper.UserJointLoginMapper;
import com.hncboy.chatgpt.api.service.UserJointLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户联合登录服务实现
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Service
@Slf4j
public class UserJointLoginServiceImpl extends ServiceImpl<UserJointLoginMapper, UserJointLogin> implements UserJointLoginService {

    @Override
    @Cacheable(value = "user_joint_login", key = "'user_' + #userId")
    public List<UserJointLogin> getByUserId(Integer userId) {
        return baseMapper.selectByUserId(userId);
    }

    @Override
    @Cacheable(value = "user_joint_login", key = "#loginType + '_' + #thirdPartyId")
    public UserJointLogin getByTypeAndThirdPartyId(String loginType, String thirdPartyId) {
        return baseMapper.selectByTypeAndThirdPartyId(loginType, thirdPartyId);
    }

    @Override
    @Cacheable(value = "user_joint_login", key = "'wechat_' + #openId")
    public UserJointLogin getByWechatOpenId(String openId) {
        return baseMapper.selectByWechatOpenId(openId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "user_joint_login", key = "'user_' + #userId")
    public Boolean bindThirdPartyAccount(Integer userId, String loginType, String thirdPartyId, String extraInfo) {
        try {
            // 检查是否已绑定
            UserJointLogin existing = getByTypeAndThirdPartyId(loginType, thirdPartyId);
            if (existing != null) {
                throw new RuntimeException("该第三方账号已被其他用户绑定");
            }

            // 检查用户是否已绑定该类型
            UserJointLogin userExisting = baseMapper.selectByUserIdAndType(userId, loginType);
            if (userExisting != null) {
                throw new RuntimeException("您已绑定该类型的账号");
            }

            // 创建绑定记录
            UserJointLogin jointLogin = new UserJointLogin();
            jointLogin.setUserId(userId);
            jointLogin.setLoginType(loginType);
            jointLogin.setThirdPartyId(thirdPartyId);
            jointLogin.setExtraInfo(extraInfo);
            jointLogin.setStatus(1);
            jointLogin.setIsPrimary(0);
            jointLogin.setBindTime(LocalDateTime.now());

            return save(jointLogin);
        } catch (Exception e) {
            log.error("绑定第三方账号失败: userId={}, loginType={}", userId, loginType, e);
            throw new RuntimeException("绑定失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "user_joint_login", key = "'user_' + #userId")
    public Boolean unbindThirdPartyAccount(Integer userId, String loginType) {
        try {
            // 检查是否为主要登录方式
            UserJointLogin jointLogin = baseMapper.selectByUserIdAndType(userId, loginType);
            if (jointLogin == null) {
                throw new RuntimeException("未找到绑定记录");
            }

            if (jointLogin.getIsPrimary() == 1) {
                // 检查是否还有其他登录方式
                List<UserJointLogin> allLogins = getByUserId(userId);
                if (allLogins.size() <= 1) {
                    throw new RuntimeException("不能解绑唯一的登录方式");
                }
            }

            // 软删除
            LambdaUpdateWrapper<UserJointLogin> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(UserJointLogin::getUserId, userId)
                    .eq(UserJointLogin::getLoginType, loginType)
                    .set(UserJointLogin::getDeleted, 1);

            return update(updateWrapper);
        } catch (Exception e) {
            log.error("解绑第三方账号失败: userId={}, loginType={}", userId, loginType, e);
            throw new RuntimeException("解绑失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "user_joint_login", key = "'user_' + #userId")
    public Boolean setPrimaryLogin(Integer userId, String loginType) {
        try {
            // 1. 取消所有主要登录方式
            LambdaUpdateWrapper<UserJointLogin> clearWrapper = new LambdaUpdateWrapper<>();
            clearWrapper.eq(UserJointLogin::getUserId, userId)
                    .set(UserJointLogin::getIsPrimary, 0);
            update(clearWrapper);

            // 2. 设置新的主要登录方式
            LambdaUpdateWrapper<UserJointLogin> setWrapper = new LambdaUpdateWrapper<>();
            setWrapper.eq(UserJointLogin::getUserId, userId)
                    .eq(UserJointLogin::getLoginType, loginType)
                    .set(UserJointLogin::getIsPrimary, 1);

            return update(setWrapper);
        } catch (Exception e) {
            log.error("设置主要登录方式失败: userId={}, loginType={}", userId, loginType, e);
            throw new RuntimeException("设置失败: " + e.getMessage());
        }
    }
}
