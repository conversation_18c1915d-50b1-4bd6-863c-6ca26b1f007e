package com.hncboy.chatgpt.api.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户联合登录实体
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@TableName("user_joint_login")
@Schema(title = "用户联合登录实体")
public class UserJointLogin {

    @Schema(title = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(title = "用户ID")
    private Integer userId;

    @Schema(title = "登录类型")
    private String loginType;

    @Schema(title = "第三方唯一标识")
    private String thirdPartyId;

    @Schema(title = "第三方用户名")
    private String thirdPartyUsername;

    @Schema(title = "第三方邮箱")
    private String thirdPartyEmail;

    @Schema(title = "第三方头像")
    private String thirdPartyAvatar;

    @Schema(title = "微信OpenID")
    private String wechatOpenId;

    @Schema(title = "微信UnionID")
    private String wechatUnionId;

    @Schema(title = "微信会话密钥")
    private String wechatSessionKey;

    @Schema(title = "性别")
    private Integer gender;

    @Schema(title = "国家")
    private String country;

    @Schema(title = "省份")
    private String province;

    @Schema(title = "城市")
    private String city;

    @Schema(title = "语言")
    private String language;

    @Schema(title = "Facebook ID")
    private String fbId;

    @Schema(title = "Google ID")
    private String googleId;

    @Schema(title = "浏览器指纹值")
    private String finbId;

    @Schema(title = "推荐人ID")
    private Long referrerId;

    @Schema(title = "幸运币")
    private Long luckyCoins;

    @Schema(title = "访问令牌")
    private String accessToken;

    @Schema(title = "刷新令牌")
    private String refreshToken;

    @Schema(title = "令牌过期时间")
    private LocalDateTime tokenExpireTime;

    @Schema(title = "扩展信息")
    private String extraInfo;

    @Schema(title = "是否主要登录方式")
    private Integer isPrimary;

    @Schema(title = "状态")
    private Integer status;

    @Schema(title = "绑定时间")
    private LocalDateTime bindTime;

    @Schema(title = "最后使用时间")
    private LocalDateTime lastUseTime;

    @Schema(title = "是否删除")
    @TableLogic
    private Integer deleted;

    @Schema(title = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(title = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
