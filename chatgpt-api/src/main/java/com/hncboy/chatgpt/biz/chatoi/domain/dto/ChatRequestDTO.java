package com.hncboy.chatgpt.biz.chatoi.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * AI对话请求DTO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Schema(title = "AI对话请求DTO")
public class ChatRequestDTO {

    @Schema(title = "聊天室ID")
    @NotNull(message = "聊天室ID不能为空")
    private Long chatRoomId;

    @Schema(title = "消息内容")
    @NotBlank(message = "消息内容不能为空")
    private String content;

    @Schema(title = "模型ID")
    private Integer modelId;

    @Schema(title = "系统提示词")
    private String systemPrompt;

    @Schema(title = "是否流式响应")
    private Boolean stream = false;
}
