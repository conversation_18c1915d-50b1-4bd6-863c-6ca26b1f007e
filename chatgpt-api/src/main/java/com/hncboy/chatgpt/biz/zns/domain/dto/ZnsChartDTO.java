package com.hncboy.chatgpt.biz.zns.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 紫微斗数命盘请求DTO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Schema(title = "紫微斗数命盘请求DTO")
public class ZnsChartDTO {

    @Schema(title = "姓名")
    @NotBlank(message = "姓名不能为空")
    private String name;

    @Schema(title = "性别")
    @NotNull(message = "性别不能为空")
    private Integer gender;

    @Schema(title = "出生时间")
    @NotNull(message = "出生时间不能为空")
    private LocalDateTime birthTime;

    @Schema(title = "出生地点")
    private String birthPlace;

    @Schema(title = "农历/阳历")
    private Integer calendarType;
}
