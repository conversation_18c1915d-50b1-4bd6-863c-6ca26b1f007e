package com.hncboy.chatgpt.biz.zns.service;

import com.hncboy.chatgpt.biz.zns.domain.dto.ZnsChartDTO;
import com.hncboy.chatgpt.biz.zns.domain.vo.ZnsChartVO;

/**
 * 紫微斗数业务服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface ZnsService {

    /**
     * 生成紫微斗数命盘
     *
     * @param chartDTO 命盘请求
     * @return 命盘结果
     */
    ZnsChartVO generateChart(ZnsChartDTO chartDTO);

    /**
     * 获取紫微斗数历史记录
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 历史记录
     */
    Object getZnsHistory(Integer pageNum, Integer pageSize);
}
