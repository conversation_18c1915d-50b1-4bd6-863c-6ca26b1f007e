package com.hncboy.chatgpt.biz.zns.controller;

import com.hncboy.chatgpt.front.framework.annotation.ApiAdminRestController;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.biz.zns.service.ZnsService;
import com.hncboy.chatgpt.biz.zns.domain.vo.ZnsChartVO;
import com.hncboy.chatgpt.biz.zns.domain.dto.ZnsChartDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 紫微斗数业务控制器
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@AllArgsConstructor
@Tag(name = "紫微斗数业务相关接口")
@ApiAdminRestController("/zns")
public class ZnsController {

    private final ZnsService znsService;

    @Operation(summary = "生成紫微斗数命盘")
    @PostMapping("/chart")
    public R<ZnsChartVO> generateChart(@Validated @RequestBody ZnsChartDTO chartDTO) {
        return R.data(znsService.generateChart(chartDTO));
    }

    @Operation(summary = "获取紫微斗数历史记录")
    @GetMapping("/history")
    public R<Object> getZnsHistory(@RequestParam(defaultValue = "1") Integer pageNum,
                                 @RequestParam(defaultValue = "10") Integer pageSize) {
        return R.data(znsService.getZnsHistory(pageNum, pageSize));
    }
}
