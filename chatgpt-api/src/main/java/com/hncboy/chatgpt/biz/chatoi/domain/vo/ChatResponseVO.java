package com.hncboy.chatgpt.biz.chatoi.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AI对话响应VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Schema(title = "AI对话响应VO")
public class ChatResponseVO {

    @Schema(title = "消息ID")
    private Long messageId;

    @Schema(title = "聊天室ID")
    private Long chatRoomId;

    @Schema(title = "响应内容")
    private String content;

    @Schema(title = "模型名称")
    private String modelName;

    @Schema(title = "消耗Token数")
    private Integer tokenCount;

    @Schema(title = "响应时间")
    private LocalDateTime responseTime;
}
