package com.hncboy.chatgpt.biz.tarot.service;

import com.hncboy.chatgpt.biz.tarot.domain.vo.TarotReadingVO;
import com.hncboy.chatgpt.biz.tarot.domain.dto.TarotReadingDTO;

/**
 * 塔罗牌业务服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface TarotService {

    /**
     * 获取塔罗牌解读
     *
     * @param readingDTO 解读请求
     * @return 解读结果
     */
    TarotReadingVO getTarotReading(TarotReadingDTO readingDTO);

    /**
     * 获取塔罗牌历史记录
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 历史记录
     */
    Object getTarotHistory(Integer pageNum, Integer pageSize);
}
