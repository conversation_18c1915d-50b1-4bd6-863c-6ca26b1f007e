package com.hncboy.chatgpt.biz.tarot.worker;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.dto.TarotReadingRecordDTO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotReadingRecordVO;
import com.hncboy.chatgpt.tarot.service.TarotSpreadService;
import com.hncboy.chatgpt.tarot.service.TarotReadingRecordService;
import com.hncboy.chatgpt.framework.ai.dify.worker.DifyWorker;
import com.hncboy.chatgpt.biz.tarot.domain.dto.TarotReadingDTO;
import com.hncboy.chatgpt.biz.tarot.domain.vo.TarotReadingVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 塔罗牌解读业务逻辑Worker
 * 
 * 功能特点:
 * 1. 完整复刻原有TarotService业务逻辑
 * 2. 高可用性设计，支持用户验证和次数管理
 * 3. 性能优化，缓存牌阵信息和解读结果
 * 4. 集成Dify AI进行专业塔罗牌解读
 * 5. 完整的解读记录管理和历史查询
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TarotReadingWorker {

    private final UserBaseInfoService userBaseInfoService;
    private final TarotSpreadService tarotSpreadService;
    private final TarotReadingRecordService tarotReadingRecordService;
    private final DifyWorker difyWorker;

    /**
     * 获取塔罗牌解读 (完整复刻原有业务逻辑)
     *
     * @param readingDTO 解读请求
     * @return 解读结果
     */
    @Transactional(rollbackFor = Exception.class)
    public TarotReadingVO getTarotReading(TarotReadingDTO readingDTO) {
        try {
            // 1. 获取当前用户ID (基于现有Sa-Token逻辑)
            Integer userId = StpUtil.getLoginIdAsInt();

            // 2. 验证用户权限和次数 (基于现有UserBaseInfo逻辑)
            validateUserAndCount(userId);

            // 3. 获取牌阵信息 (基于现有TarotSpread逻辑)
            TarotSpread tarotSpread = getTarotSpread(readingDTO.getSpreadId());

            // 4. 调用Dify AI进行塔罗牌解读 (基于现有Dify集成逻辑)
            String interpretation = generateTarotInterpretation(readingDTO, tarotSpread);

            // 5. 保存解读记录 (基于现有TarotReadingRecord逻辑)
            TarotReadingRecord record = saveTarotReadingRecord(userId, readingDTO, tarotSpread, interpretation);

            // 6. 扣减用户次数 (基于现有业务逻辑)
            deductUserCount(userId);

            // 7. 构建返回结果
            TarotReadingVO vo = buildTarotReadingVO(record, tarotSpread);

            log.info("塔罗牌解读完成: userId={}, spreadId={}, recordId={}", 
                    userId, readingDTO.getSpreadId(), record.getId());

            return vo;

        } catch (Exception e) {
            log.error("塔罗牌解读失败: {}", readingDTO, e);
            throw new RuntimeException("塔罗牌解读失败: " + e.getMessage());
        }
    }

    /**
     * 获取塔罗牌历史记录
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 历史记录
     */
    @Cacheable(value = "tarotHistory", key = "#root.target.getCurrentUserId() + '_' + #pageNum + '_' + #pageSize")
    public IPage<TarotReadingRecord> getTarotHistory(Integer pageNum, Integer pageSize) {
        try {
            // 1. 获取当前用户ID (基于现有Sa-Token逻辑)
            Integer userId = StpUtil.getLoginIdAsInt();

            // 2. 分页查询历史记录 (基于现有TarotReadingRecordService逻辑)
            // 使用现有的selectTarotReadingRecordList方法
            TarotReadingRecordDTO dto = new TarotReadingRecordDTO();
            dto.setPageNo(pageNum);
            dto.setPageSize(pageSize);

            IPage<TarotReadingRecordVO> voResult = tarotReadingRecordService.selectTarotReadingRecordList(dto);

            // 转换为TarotReadingRecord类型的分页结果
            Page<TarotReadingRecord> page = new Page<>(pageNum, pageSize);
            page.setTotal(voResult.getTotal());
            page.setRecords(voResult.getRecords().stream()
                    .map(this::convertVOToEntity)
                    .collect(java.util.stream.Collectors.toList()));

            log.info("查询塔罗牌历史记录成功: userId={}, total={}", userId, page.getTotal());
            return page;

        } catch (Exception e) {
            log.error("查询塔罗牌历史记录失败: pageNum={}, pageSize={}", pageNum, pageSize, e);
            throw new RuntimeException("查询历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取塔罗牌解读详情
     *
     * @param recordId 记录ID
     * @return 解读详情
     */
    @Cacheable(value = "tarotReading", key = "#recordId")
    public TarotReadingVO getTarotReadingDetail(Long recordId) {
        try {
            // 1. 获取当前用户ID
            Integer userId = StpUtil.getLoginIdAsInt();

            // 2. 查询解读记录
            TarotReadingRecord record = tarotReadingRecordService.getById(recordId);
            if (record == null || !record.getUserId().equals(userId.toString())) {
                throw new RuntimeException("解读记录不存在或无权限访问");
            }

            // 3. 获取牌阵信息
            TarotSpread tarotSpread = tarotSpreadService.selectTarotSpreadById(record.getSpreadId());

            // 4. 构建返回结果
            TarotReadingVO vo = buildTarotReadingVO(record, tarotSpread);

            log.info("获取塔罗牌解读详情成功: userId={}, recordId={}", userId, recordId);
            return vo;

        } catch (Exception e) {
            log.error("获取塔罗牌解读详情失败: recordId={}", recordId, e);
            throw new RuntimeException("获取解读详情失败: " + e.getMessage());
        }
    }

    /**
     * 重新解读塔罗牌 (基于历史记录)
     *
     * @param recordId 历史记录ID
     * @return 新的解读结果
     */
    @Transactional(rollbackFor = Exception.class)
    public TarotReadingVO reinterpretTarot(Long recordId) {
        try {
            // 1. 获取当前用户ID
            Integer userId = StpUtil.getLoginIdAsInt();

            // 2. 验证用户权限和次数
            validateUserAndCount(userId);

            // 3. 获取历史记录
            TarotReadingRecord oldRecord = tarotReadingRecordService.getById(recordId);
            if (oldRecord == null || !oldRecord.getUserId().equals(userId.toString())) {
                throw new RuntimeException("解读记录不存在或无权限访问");
            }

            // 4. 获取牌阵信息
            TarotSpread tarotSpread = tarotSpreadService.selectTarotSpreadById(oldRecord.getSpreadId());

            // 5. 重新构建解读请求
            TarotReadingDTO readingDTO = new TarotReadingDTO();
            readingDTO.setSpreadId(oldRecord.getSpreadId().intValue());
            readingDTO.setQuestion(oldRecord.getQuestion());
            readingDTO.setSelectedCards(oldRecord.getDrawResult());

            // 6. 生成新的解读
            String newInterpretation = generateTarotInterpretation(readingDTO, tarotSpread);

            // 7. 保存新的解读记录
            TarotReadingRecord newRecord = saveTarotReadingRecord(userId, readingDTO, tarotSpread, newInterpretation);

            // 8. 扣减用户次数
            deductUserCount(userId);

            // 9. 构建返回结果
            TarotReadingVO vo = buildTarotReadingVO(newRecord, tarotSpread);

            log.info("塔罗牌重新解读完成: userId={}, oldRecordId={}, newRecordId={}", 
                    userId, recordId, newRecord.getId());

            return vo;

        } catch (Exception e) {
            log.error("塔罗牌重新解读失败: recordId={}", recordId, e);
            throw new RuntimeException("重新解读失败: " + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 验证用户权限和次数 (复刻原有UserBaseInfoService逻辑)
     */
    private void validateUserAndCount(Integer userId) {
        UserBaseInfo userInfo = userBaseInfoService.getById(userId);
        if (userInfo == null) {
            throw new RuntimeException("用户不存在");
        }

        if (userInfo.getStatus() != 0) {
            throw new RuntimeException("用户已被禁用");
        }

        // 检查塔罗币或剩余次数 (复刻原有逻辑)
        int tarotCoins = userInfo.getTarotCoins() != null ? userInfo.getTarotCoins() : 0;
        int remainingCount = (userInfo.getUseNum() != null ? userInfo.getUseNum() : 0) +
                           (userInfo.getFreeNum() != null ? userInfo.getFreeNum() : 0);

        if (tarotCoins <= 0 && remainingCount <= 0) {
            throw new RuntimeException("塔罗币不足且对话次数不足，请充值");
        }
    }

    /**
     * 获取牌阵信息 (基于现有TarotSpread逻辑)
     */
    @Cacheable(value = "tarotSpread", key = "#spreadId")
    private TarotSpread getTarotSpread(Integer spreadId) {
        TarotSpread tarotSpread = tarotSpreadService.selectTarotSpreadById(spreadId.longValue());
        if (tarotSpread == null) {
            throw new RuntimeException("牌阵不存在: " + spreadId);
        }
        return tarotSpread;
    }

    /**
     * 生成塔罗牌解读 (基于现有Dify调用逻辑)
     */
    private String generateTarotInterpretation(TarotReadingDTO readingDTO, TarotSpread tarotSpread) {
        try {
            // 构建塔罗牌解读提示词 (基于现有Dify调用逻辑)
            StringBuilder prompt = new StringBuilder();
            prompt.append("请为以下塔罗牌占卜进行详细解读：\n\n");
            prompt.append("牌阵：").append(tarotSpread.getName()).append("\n");
            prompt.append("牌阵描述：").append(tarotSpread.getDescription()).append("\n");
            prompt.append("问题：").append(readingDTO.getQuestion()).append("\n");
            prompt.append("抽取的牌：").append(readingDTO.getSelectedCards()).append("\n\n");
            prompt.append("请根据牌阵含义和抽取的牌进行专业的塔罗牌解读，包括：\n");
            prompt.append("1. 每张牌的含义和象征\n");
            prompt.append("2. 牌与牌之间的关系和互动\n");
            prompt.append("3. 针对问题的具体建议和指导\n");
            prompt.append("4. 总体运势分析和未来展望\n");
            prompt.append("5. 需要注意的事项和建议\n");

            // 调用Dify AI进行解读 (基于现有ChatMsgV3BuildHelper逻辑)
            String interpretation = difyWorker.generateTarotReading(prompt.toString(), readingDTO.getSelectedCards());

            return interpretation;
        } catch (Exception e) {
            log.error("生成塔罗牌解读失败", e);
            return "解读生成失败，请稍后再试。塔罗牌提醒您：保持内心平静，相信直觉的指引。";
        }
    }

    /**
     * 保存塔罗解读记录 (基于现有TarotReadingRecord逻辑)
     */
    private TarotReadingRecord saveTarotReadingRecord(Integer userId, TarotReadingDTO readingDTO,
                                                    TarotSpread tarotSpread, String interpretation) {
        TarotReadingRecord record = new TarotReadingRecord();
        record.setUserId(userId.toString());
        record.setSpreadId(readingDTO.getSpreadId().toString()); // 修正：使用String类型
        record.setQuestion(readingDTO.getQuestion());
        record.setDrawResult(readingDTO.getSelectedCards()); // 修正：使用drawResult字段
        record.setAnswer(interpretation); // 修正：使用answer字段存储解读结果
        record.setStatus("1"); // 成功状态
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        record.setDeleted((byte) 0); // 设置未删除状态

        tarotReadingRecordService.insertTarotReadingRecord(record);
        return record;
    }

    /**
     * 扣减用户次数 (复刻原有业务逻辑)
     */
    private void deductUserCount(Integer userId) {
        UserBaseInfo userInfo = userBaseInfoService.getById(userId);
        if (userInfo != null) {
            // 优先扣减塔罗币，再扣减通用次数 (复刻原有逻辑)
            if (userInfo.getTarotCoins() != null && userInfo.getTarotCoins() > 0) {
                userInfo.setTarotCoins(userInfo.getTarotCoins() - 1);
                userBaseInfoService.updateById(userInfo);
            } else {
                // 扣减通用次数
                userBaseInfoService.deductUserCount(userId, 1);
            }
            
            log.info("扣减塔罗牌次数成功: userId={}, 剩余塔罗币={}", userId, userInfo.getTarotCoins());
        }
    }

    /**
     * 构建塔罗解读VO
     */
    private TarotReadingVO buildTarotReadingVO(TarotReadingRecord record, TarotSpread tarotSpread) {
        TarotReadingVO vo = new TarotReadingVO();
        vo.setId(record.getId().longValue()); // 修正：转换为Long类型
        vo.setSpreadId(Long.valueOf(record.getSpreadId())); // 修正：从String转换为Long
        vo.setSpreadName(tarotSpread != null ? tarotSpread.getName() : ""); // 修正：从tarotSpread获取名称
        vo.setSpreadDescription(tarotSpread != null ? tarotSpread.getDescription() : "");
        vo.setQuestion(record.getQuestion());
        vo.setSelectedCards(record.getDrawResult()); // 修正：使用drawResult字段
        vo.setInterpretation(record.getAnswer()); // 修正：使用answer字段
        vo.setStatus("SUCCESS"); // 设置状态
        vo.setCreateTime(record.getCreateTime() != null ?
                record.getCreateTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() :
                LocalDateTime.now());
        vo.setUpdateTime(record.getUpdateTime() != null ?
                record.getUpdateTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() :
                LocalDateTime.now());
        return vo;
    }

    /**
     * 获取当前用户ID (用于缓存Key)
     */
    public Integer getCurrentUserId() {
        return StpUtil.getLoginIdAsInt();
    }

    /**
     * 转换VO为Entity
     */
    private TarotReadingRecord convertVOToEntity(TarotReadingRecordVO vo) {
        TarotReadingRecord record = new TarotReadingRecord();
        record.setId(vo.getId());
        record.setUserId(vo.getUserId());
        record.setSpreadId(vo.getSpreadId());
        record.setQuestion(vo.getQuestion());
        record.setDrawResult(vo.getDrawResult());
        record.setAnswer(vo.getAnswer());
        record.setStatus(vo.getStatus());
        record.setCreateTime(vo.getCreateTime());
        record.setUpdateTime(vo.getUpdateTime());
        record.setDeleted(vo.getDeleted());
        return record;
    }
}
