package com.hncboy.chatgpt.biz.chatoi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.biz.chatoi.domain.dto.ChatRequestDTO;
import com.hncboy.chatgpt.biz.chatoi.domain.vo.ChatResponseVO;
import com.hncboy.chatgpt.biz.chatoi.service.ChatOiService;
import com.hncboy.chatgpt.framework.ai.service.SpringAIChatService;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;
import com.hncboy.chatgpt.front.service.ChatMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

/**
 * AI对话业务服务实现 - 基于现有ChatService逻辑
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Service
@Slf4j
public class ChatOiServiceImpl implements ChatOiService {

    @Autowired
    private SpringAIChatService springAIChatService;
    
    @Autowired
    private ChatMessageService chatMessageService;

    @Override
    public ChatResponseVO chat(ChatRequestDTO chatRequestDTO) {
        try {
            // 直接调用Spring AI服务 (已包含完整的业务逻辑)
            return springAIChatService.chat(chatRequestDTO);
        } catch (Exception e) {
            log.error("AI对话失败: {}", chatRequestDTO, e);
            throw new RuntimeException("AI对话失败: " + e.getMessage());
        }
    }

    @Override
    public Flux<String> streamChat(ChatRequestDTO chatRequestDTO) {
        try {
            // 直接调用Spring AI流式服务 (已包含完整的业务逻辑)
            return springAIChatService.streamChat(chatRequestDTO);
        } catch (Exception e) {
            log.error("流式AI对话失败: {}", chatRequestDTO, e);
            return Flux.error(new RuntimeException("流式AI对话失败: " + e.getMessage()));
        }
    }

    @Override
    public Object getChatHistory(Long chatRoomId, Integer pageNum, Integer pageSize) {
        try {
            // 基于现有ChatMessageService实现对话历史查询
            Page<ChatMessageDO> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<ChatMessageDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ChatMessageDO::getChatRoomId, chatRoomId)
                    .orderByDesc(ChatMessageDO::getCreateTime);
            
            IPage<ChatMessageDO> result = chatMessageService.page(page, queryWrapper);
            
            log.info("查询对话历史成功: chatRoomId={}, total={}", chatRoomId, result.getTotal());
            return result;
            
        } catch (Exception e) {
            log.error("查询对话历史失败: chatRoomId={}, pageNum={}, pageSize={}", chatRoomId, pageNum, pageSize, e);
            throw new RuntimeException("查询对话历史失败: " + e.getMessage());
        }
    }
}
