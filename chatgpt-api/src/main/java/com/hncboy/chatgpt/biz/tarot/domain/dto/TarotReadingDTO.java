package com.hncboy.chatgpt.biz.tarot.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 塔罗牌解读请求DTO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Schema(title = "塔罗牌解读请求DTO")
public class TarotReadingDTO {

    @Schema(title = "牌阵ID")
    @NotNull(message = "牌阵ID不能为空")
    private Integer spreadId;

    @Schema(title = "问题")
    @NotBlank(message = "问题不能为空")
    private String question;

    @Schema(title = "抽取的牌")
    private String selectedCards;
}
