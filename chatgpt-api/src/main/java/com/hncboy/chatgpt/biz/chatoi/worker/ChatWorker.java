package com.hncboy.chatgpt.biz.chatoi.worker;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.db.entity.chat.ChatMessage;
import com.hncboy.chatgpt.db.entity.chat.ChatRoom;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.db.service.chat.ChatMessageService;
import com.hncboy.chatgpt.db.service.chat.ChatRoomService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.framework.ai.chat.worker.SpringAIChatWorker;
import com.hncboy.chatgpt.biz.chatoi.domain.dto.ChatRequestDTO;
import com.hncboy.chatgpt.biz.chatoi.domain.vo.ChatResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AI对话业务逻辑Worker
 * 
 * 功能特点:
 * 1. 完整复刻原有ChatService业务逻辑
 * 2. 高可用性设计，支持用户验证和次数管理
 * 3. 性能优化，缓存聊天室和用户信息
 * 4. 支持流式和非流式对话
 * 5. 完整的消息管理和历史记录
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChatWorker {

    private final UserBaseInfoService userBaseInfoService;
    private final ChatRoomService chatRoomService;
    private final ChatMessageService chatMessageService;
    private final SpringAIChatWorker springAIChatWorker;

    /**
     * AI对话 (非流式)
     *
     * @param request 对话请求
     * @return 对话响应
     */
    @Transactional(rollbackFor = Exception.class)
    public ChatResponseVO chat(ChatRequestDTO request) {
        try {
            // 1. 获取当前用户ID (基于Sa-Token)
            Integer userId = StpUtil.getLoginIdAsInt();

            // 2. 验证用户权限和次数 (复刻原有逻辑)
            validateUserAndCount(userId);

            // 3. 验证或创建聊天室
            ChatRoom chatRoom = validateOrCreateChatRoom(request.getChatRoomId(), userId);

            // 4. 保存用户消息 (复刻原有ChatMessageService逻辑)
            ChatMessage userMessage = saveUserMessage(chatRoom.getId(), userId, request.getContent());

            // 5. 获取对话历史 (复刻原有逻辑)
            List<ChatMessage> messageHistory = getMessageHistory(chatRoom.getId(), 10);

            // 6. 调用AI模型生成回复 (集成Spring AI)
            String aiResponse = springAIChatWorker.generateResponse(
                    request.getContent(), 
                    messageHistory, 
                    chatRoom.getSystemPrompt(),
                    chatRoom.getModelName()
            );

            // 7. 保存AI回复消息
            ChatMessage aiMessage = saveAiMessage(chatRoom.getId(), userId, aiResponse, userMessage.getId());

            // 8. 扣减用户次数 (复刻原有扣减逻辑)
            deductUserCount(userId);

            // 9. 更新聊天室信息
            updateChatRoomInfo(chatRoom, aiMessage);

            // 10. 构建响应结果
            ChatResponseVO response = buildChatResponse(userMessage, aiMessage, chatRoom);

            log.info("AI对话完成: userId={}, chatRoomId={}, messageId={}", 
                    userId, chatRoom.getId(), aiMessage.getId());

            return response;

        } catch (Exception e) {
            log.error("AI对话失败: {}", request, e);
            throw new RuntimeException("AI对话失败: " + e.getMessage());
        }
    }

    /**
     * AI流式对话
     *
     * @param request 对话请求
     * @return 流式响应
     */
    public Flux<String> streamChat(ChatRequestDTO request) {
        try {
            // 1. 获取当前用户ID
            Integer userId = StpUtil.getLoginIdAsInt();

            // 2. 验证用户权限和次数
            validateUserAndCount(userId);

            // 3. 验证或创建聊天室
            ChatRoom chatRoom = validateOrCreateChatRoom(request.getChatRoomId(), userId);

            // 4. 保存用户消息
            ChatMessage userMessage = saveUserMessage(chatRoom.getId(), userId, request.getContent());

            // 5. 获取对话历史
            List<ChatMessage> messageHistory = getMessageHistory(chatRoom.getId(), 10);

            // 6. 调用AI模型生成流式回复
            return springAIChatWorker.generateStreamResponse(
                    request.getContent(), 
                    messageHistory, 
                    chatRoom.getSystemPrompt(),
                    chatRoom.getModelName()
            ).doOnComplete(() -> {
                // 流式响应完成后的处理
                try {
                    // 扣减用户次数
                    deductUserCount(userId);
                    
                    // 更新聊天室信息
                    updateChatRoomInfo(chatRoom, null);
                    
                    log.info("AI流式对话完成: userId={}, chatRoomId={}", userId, chatRoom.getId());
                } catch (Exception e) {
                    log.error("AI流式对话后处理失败: userId={}, chatRoomId={}", userId, chatRoom.getId(), e);
                }
            });

        } catch (Exception e) {
            log.error("AI流式对话失败: {}", request, e);
            return Flux.error(new RuntimeException("AI流式对话失败: " + e.getMessage()));
        }
    }

    /**
     * 获取对话历史
     *
     * @param chatRoomId 聊天室ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 对话历史
     */
    @Cacheable(value = "chatHistory", key = "#chatRoomId + '_' + #pageNum + '_' + #pageSize")
    public IPage<ChatMessage> getChatHistory(Long chatRoomId, Integer pageNum, Integer pageSize) {
        try {
            // 1. 获取当前用户ID
            Integer userId = StpUtil.getLoginIdAsInt();

            // 2. 验证聊天室权限
            ChatRoom chatRoom = chatRoomService.getById(chatRoomId);
            if (chatRoom == null || !chatRoom.getUserId().equals(userId)) {
                throw new RuntimeException("聊天室不存在或无权限访问");
            }

            // 3. 分页查询消息历史 (复刻原有分页逻辑)
            Page<ChatMessage> page = new Page<>(pageNum, pageSize);
            IPage<ChatMessage> result = chatMessageService.getHistoryByChatRoom(chatRoomId, page);

            log.info("获取对话历史成功: userId={}, chatRoomId={}, total={}", 
                    userId, chatRoomId, result.getTotal());

            return result;

        } catch (Exception e) {
            log.error("获取对话历史失败: chatRoomId={}, pageNum={}, pageSize={}", 
                    chatRoomId, pageNum, pageSize, e);
            throw new RuntimeException("获取对话历史失败: " + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 验证用户权限和次数 (复刻原有UserBaseInfoService逻辑)
     */
    private void validateUserAndCount(Integer userId) {
        UserBaseInfo userInfo = userBaseInfoService.getById(userId);
        if (userInfo == null) {
            throw new RuntimeException("用户不存在");
        }

        if (userInfo.getStatus() != 0) {
            throw new RuntimeException("用户已被禁用");
        }

        // 检查剩余次数 (复刻原有逻辑)
        int remainingCount = (userInfo.getUseNum() != null ? userInfo.getUseNum() : 0) + 
                           (userInfo.getFreeNum() != null ? userInfo.getFreeNum() : 0);
        if (remainingCount <= 0) {
            throw new RuntimeException("对话次数不足，请充值或等待免费次数恢复");
        }
    }

    /**
     * 验证或创建聊天室
     */
    private ChatRoom validateOrCreateChatRoom(Long chatRoomId, Integer userId) {
        if (chatRoomId != null) {
            ChatRoom chatRoom = chatRoomService.getById(chatRoomId);
            if (chatRoom != null && chatRoom.getUserId().equals(userId)) {
                return chatRoom;
            }
        }

        // 创建新的聊天室 (复刻原有创建逻辑)
        ChatRoom newChatRoom = new ChatRoom();
        newChatRoom.setUserId(userId);
        newChatRoom.setName("新对话 " + LocalDateTime.now().toString().substring(0, 16));
        newChatRoom.setRoomType("CHAT");
        newChatRoom.setModelName("gpt-3.5-turbo");
        newChatRoom.setSystemPrompt("你是一个有用的AI助手。");
        newChatRoom.setTemperature(0.7);
        newChatRoom.setMaxTokens(2000);
        newChatRoom.setContextLength(10);
        newChatRoom.setStatus(1);
        newChatRoom.setMessageCount(0);
        
        chatRoomService.save(newChatRoom);
        return newChatRoom;
    }

    /**
     * 保存用户消息 (复刻原有ChatMessageService逻辑)
     */
    private ChatMessage saveUserMessage(Long chatRoomId, Integer userId, String content) {
        ChatMessage message = new ChatMessage();
        message.setChatRoomId(chatRoomId);
        message.setUserId(userId);
        message.setMessageType(1); // 用户消息
        message.setContent(content);
        message.setContentFormat("TEXT");
        message.setStatus(1); // 成功
        
        chatMessageService.save(message);
        return message;
    }

    /**
     * 保存AI回复消息
     */
    private ChatMessage saveAiMessage(Long chatRoomId, Integer userId, String content, Long parentMessageId) {
        ChatMessage message = new ChatMessage();
        message.setChatRoomId(chatRoomId);
        message.setUserId(userId);
        message.setMessageType(2); // AI回复
        message.setContent(content);
        message.setContentFormat("MARKDOWN");
        message.setParentMessageId(parentMessageId);
        message.setStatus(1); // 成功
        
        chatMessageService.save(message);
        return message;
    }

    /**
     * 获取消息历史 (复刻原有逻辑)
     */
    private List<ChatMessage> getMessageHistory(Long chatRoomId, Integer limit) {
        return chatMessageService.getRecentMessages(chatRoomId, limit);
    }

    /**
     * 扣减用户次数 (复刻原有扣减逻辑)
     */
    private void deductUserCount(Integer userId) {
        userBaseInfoService.deductUserCount(userId, 1);
    }

    /**
     * 更新聊天室信息
     */
    private void updateChatRoomInfo(ChatRoom chatRoom, ChatMessage lastMessage) {
        chatRoom.setLastMessageTime(LocalDateTime.now());
        chatRoom.setMessageCount(chatRoom.getMessageCount() + 2); // 用户消息 + AI回复
        chatRoomService.updateById(chatRoom);
    }

    /**
     * 构建对话响应
     */
    private ChatResponseVO buildChatResponse(ChatMessage userMessage, ChatMessage aiMessage, ChatRoom chatRoom) {
        ChatResponseVO response = new ChatResponseVO();
        response.setChatRoomId(chatRoom.getId());
        response.setUserMessageId(userMessage.getId());
        response.setAiMessageId(aiMessage.getId());
        response.setContent(aiMessage.getContent());
        response.setModelName(chatRoom.getModelName());
        response.setCreateTime(aiMessage.getCreateTime());
        return response;
    }
}
