package com.hncboy.chatgpt.biz.chatoi.service;

import com.hncboy.chatgpt.biz.chatoi.domain.dto.ChatRequestDTO;
import com.hncboy.chatgpt.biz.chatoi.domain.vo.ChatResponseVO;
import reactor.core.publisher.Flux;

/**
 * AI对话业务服务接口
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface ChatOiService {

    /**
     * AI对话
     *
     * @param chatRequestDTO 对话请求
     * @return 对话响应
     */
    ChatResponseVO chat(ChatRequestDTO chatRequestDTO);

    /**
     * AI流式对话
     *
     * @param chatRequestDTO 对话请求
     * @return 流式响应
     */
    Flux<String> streamChat(ChatRequestDTO chatRequestDTO);

    /**
     * 获取对话历史
     *
     * @param chatRoomId 聊天室ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 对话历史
     */
    Object getChatHistory(Long chatRoomId, Integer pageNum, Integer pageSize);
}
