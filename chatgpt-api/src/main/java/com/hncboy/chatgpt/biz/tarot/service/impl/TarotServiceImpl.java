package com.hncboy.chatgpt.biz.tarot.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.biz.tarot.domain.dto.TarotReadingDTO;
import com.hncboy.chatgpt.biz.tarot.domain.vo.TarotReadingVO;
import com.hncboy.chatgpt.biz.tarot.service.TarotService;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import com.hncboy.chatgpt.tarot.service.TarotReadingRecordService;
import com.hncboy.chatgpt.tarot.service.TarotSpreadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 塔罗牌业务服务实现
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Service
@Slf4j
public class TarotServiceImpl implements TarotService {

    @Autowired
    private UserBaseInfoService userBaseInfoService;

    @Autowired
    private TarotSpreadService tarotSpreadService;

    @Autowired
    private TarotReadingRecordService tarotReadingRecordService;

    @Override
    public TarotReadingVO getTarotReading(TarotReadingDTO readingDTO) {
        try {
            // 1. 获取当前用户ID (基于现有Sa-Token逻辑)
            Integer userId = StpUtil.getLoginIdAsInt();

            // 2. 验证用户权限和次数 (基于现有UserBaseInfo逻辑)
            UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
            if (userInfo == null) {
                throw new RuntimeException("用户不存在");
            }

            // 3. 检查用户剩余次数 (基于现有业务逻辑)
            int remainingCount = userInfo.getUseNum() + userInfo.getFreeNum();
            if (remainingCount <= 0) {
                throw new RuntimeException("塔罗牌解读次数不足");
            }

            // 4. 获取牌阵信息 (基于现有TarotSpread逻辑)
            TarotSpread tarotSpread = tarotSpreadService.selectTarotSpreadById(readingDTO.getSpreadId().longValue());
            if (tarotSpread == null) {
                throw new RuntimeException("牌阵不存在");
            }

            // 5. 调用AI模型进行解读 (基于现有ChatMsgV3BuildHelper逻辑)
            String interpretation = generateTarotInterpretation(readingDTO, tarotSpread);

            // 6. 保存解读记录 (基于现有TarotReadingRecord逻辑)
            TarotReadingRecord record = new TarotReadingRecord();
            record.setUserId(userId.toString());
            record.setSpreadId(readingDTO.getSpreadId().longValue());
            record.setSpreadName(tarotSpread.getName());
            record.setQuestion(readingDTO.getQuestion());
            record.setSelectedCards(readingDTO.getSelectedCards());
            record.setInterpretation(interpretation);
            record.setStatus("1"); // 成功状态
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());
            tarotReadingRecordService.insertTarotReadingRecord(record);

            // 7. 扣减用户次数 (基于现有业务逻辑)
            deductUserCount(userId);

            // 8. 构建返回结果
            TarotReadingVO vo = new TarotReadingVO();
            vo.setId(record.getId());
            vo.setSpreadName(tarotSpread.getName());
            vo.setQuestion(readingDTO.getQuestion());
            vo.setSelectedCards(readingDTO.getSelectedCards());
            vo.setInterpretation(interpretation);
            vo.setCreateTime(LocalDateTime.now());

            log.info("塔罗牌解读完成: userId={}, spreadId={}, question={}", userId, readingDTO.getSpreadId(), readingDTO.getQuestion());
            return vo;

        } catch (Exception e) {
            log.error("塔罗牌解读失败: {}", readingDTO, e);
            throw new RuntimeException("塔罗牌解读失败: " + e.getMessage());
        }
    }

    @Override
    public Object getTarotHistory(Integer pageNum, Integer pageSize) {
        try {
            // 1. 获取当前用户ID (基于现有Sa-Token逻辑)
            Integer userId = StpUtil.getLoginIdAsInt();

            // 2. 分页查询历史记录 (基于现有TarotReadingRecordService逻辑)
            Page<TarotReadingRecord> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<TarotReadingRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TarotReadingRecord::getUserId, userId.toString())
                    .eq(TarotReadingRecord::getStatus, "1") // 只查询成功的记录
                    .eq(TarotReadingRecord::getDeleted, 0) // 未删除的记录
                    .orderByDesc(TarotReadingRecord::getCreateTime);

            IPage<TarotReadingRecord> result = tarotReadingRecordService.page(page, queryWrapper);

            log.info("查询塔罗牌历史记录成功: userId={}, total={}", userId, result.getTotal());
            return result;

        } catch (Exception e) {
            log.error("查询塔罗牌历史记录失败: pageNum={}, pageSize={}", pageNum, pageSize, e);
            throw new RuntimeException("查询历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 生成塔罗牌解读 (基于现有ChatMsgV3BuildHelper逻辑)
     */
    private String generateTarotInterpretation(TarotReadingDTO readingDTO, TarotSpread tarotSpread) {
        try {
            // 构建塔罗牌解读提示词 (基于现有Dify调用逻辑)
            StringBuilder prompt = new StringBuilder();
            prompt.append("请为以下塔罗牌占卜进行详细解读：\n\n");
            prompt.append("牌阵：").append(tarotSpread.getName()).append("\n");
            prompt.append("问题：").append(readingDTO.getQuestion()).append("\n");
            prompt.append("抽取的牌：").append(readingDTO.getSelectedCards()).append("\n\n");
            prompt.append("请根据牌阵含义和抽取的牌进行专业的塔罗牌解读，包括：\n");
            prompt.append("1. 每张牌的含义\n");
            prompt.append("2. 牌与牌之间的关系\n");
            prompt.append("3. 针对问题的具体建议\n");
            prompt.append("4. 总体运势分析\n");

            // TODO: 这里应该调用现有的AI服务 (ChatMsgV3BuildHelper.buildDifyStreamClient)
            // 暂时返回模拟的解读结果
            String interpretation = "根据您抽取的牌和提出的问题，塔罗牌为您揭示了以下信息：\n\n" +
                    "【牌面分析】\n" +
                    "您抽取的牌显示了当前的能量状态和未来的发展趋势。每张牌都承载着特定的象征意义...\n\n" +
                    "【针对问题的建议】\n" +
                    "关于您的问题：" + readingDTO.getQuestion() + "\n" +
                    "塔罗牌建议您保持积极的心态，相信自己的直觉...\n\n" +
                    "【总体运势】\n" +
                    "整体而言，您的运势呈现上升趋势，但需要注意平衡各方面的发展...";

            return interpretation;
        } catch (Exception e) {
            log.error("生成塔罗牌解读失败", e);
            return "解读生成失败，请稍后再试";
        }
    }

    /**
     * 扣减用户次数 (基于现有业务逻辑)
     */
    private void deductUserCount(Integer userId) {
        try {
            UserBaseInfo userInfo = userBaseInfoService.getUserBaseInfoByUserId(userId);
            if (userInfo != null) {
                // 优先扣减免费次数，再扣减付费次数 (基于现有逻辑)
                if (userInfo.getFreeNum() > 0) {
                    userInfo.setFreeNum(userInfo.getFreeNum() - 1);
                } else if (userInfo.getUseNum() > 0) {
                    userInfo.setUseNum(userInfo.getUseNum() - 1);
                }
                userBaseInfoService.updateById(userInfo);
                log.info("扣减塔罗牌次数成功: userId={}, 剩余免费次数={}, 剩余付费次数={}",
                        userId, userInfo.getFreeNum(), userInfo.getUseNum());
            }
        } catch (Exception e) {
            log.error("扣减用户次数失败: userId={}", userId, e);
        }
    }
}
