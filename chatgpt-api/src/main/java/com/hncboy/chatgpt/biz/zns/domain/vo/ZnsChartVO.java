package com.hncboy.chatgpt.biz.zns.domain.vo;

import com.hncboy.chatgpt.common.i18n.I18nTranslation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 紫微斗数命盘结果VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Schema(title = "紫微斗数命盘结果VO")
public class ZnsChartVO {

    @Schema(title = "命盘ID")
    private Long id;

    @Schema(title = "姓名")
    private String name;

    @Schema(title = "性别")
    @I18nTranslation(byValue = true)
    private String genderText;

    @Schema(title = "命盘数据")
    private String chartData;

    @Schema(title = "解读结果")
    private String interpretation;

    @Schema(title = "创建时间")
    private LocalDateTime createTime;
}
