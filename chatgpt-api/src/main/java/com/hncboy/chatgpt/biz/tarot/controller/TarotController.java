package com.hncboy.chatgpt.biz.tarot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.annotation.ApiAdminRestController;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.biz.tarot.worker.TarotReadingWorker;
import com.hncboy.chatgpt.biz.tarot.domain.vo.TarotReadingVO;
import com.hncboy.chatgpt.biz.tarot.domain.dto.TarotReadingDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 塔罗牌业务控制器 (重构后)
 *
 * 功能特点:
 * 1. 完整复刻原有TarotController功能
 * 2. 使用Worker模式替代Service+Impl
 * 3. 支持专业的塔罗牌解读和历史记录管理
 * 4. 集成Dify AI进行智能解读
 * 5. 统一的异常处理和日志记录
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@RequiredArgsConstructor
@Tag(name = "塔罗牌业务相关接口")
@ApiAdminRestController("/tarot")
public class TarotController {

    private final TarotReadingWorker tarotReadingWorker;

    @Operation(summary = "获取塔罗牌解读", description = "基于选择的牌阵和问题进行专业的塔罗牌解读")
    @PostMapping("/reading")
    public R<TarotReadingVO> getTarotReading(@Validated @RequestBody TarotReadingDTO readingDTO) {
        try {
            log.info("塔罗牌解读请求: {}", readingDTO);

            TarotReadingVO result = tarotReadingWorker.getTarotReading(readingDTO);

            log.info("塔罗牌解读成功: spreadId={}, recordId={}",
                    readingDTO.getSpreadId(), result.getId());

            return R.data(result);

        } catch (Exception e) {
            log.error("塔罗牌解读失败: {}", readingDTO, e);
            return R.fail("塔罗牌解读失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取塔罗牌历史记录", description = "分页获取用户的塔罗牌解读历史记录")
    @GetMapping("/history")
    public R<IPage<TarotReadingRecord>> getTarotHistory(@RequestParam(defaultValue = "1") Integer pageNum,
                                                       @RequestParam(defaultValue = "20") Integer pageSize) {
        try {
            log.info("获取塔罗牌历史记录请求: pageNum={}, pageSize={}", pageNum, pageSize);

            IPage<TarotReadingRecord> result = tarotReadingWorker.getTarotHistory(pageNum, pageSize);

            log.info("获取塔罗牌历史记录成功: total={}", result.getTotal());

            return R.data(result);

        } catch (Exception e) {
            log.error("获取塔罗牌历史记录失败: pageNum={}, pageSize={}", pageNum, pageSize, e);
            return R.fail("获取历史记录失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取塔罗牌解读详情", description = "根据记录ID获取详细的解读内容")
    @GetMapping("/reading/{recordId}")
    public R<TarotReadingVO> getTarotReadingDetail(@PathVariable Long recordId) {
        try {
            log.info("获取塔罗牌解读详情请求: recordId={}", recordId);

            TarotReadingVO result = tarotReadingWorker.getTarotReadingDetail(recordId);

            log.info("获取塔罗牌解读详情成功: recordId={}", recordId);

            return R.data(result);

        } catch (Exception e) {
            log.error("获取塔罗牌解读详情失败: recordId={}", recordId, e);
            return R.fail("获取解读详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "重新解读塔罗牌", description = "基于历史记录重新进行塔罗牌解读")
    @PostMapping("/reinterpret/{recordId}")
    public R<TarotReadingVO> reinterpretTarot(@PathVariable Long recordId) {
        try {
            log.info("重新解读塔罗牌请求: recordId={}", recordId);

            TarotReadingVO result = tarotReadingWorker.reinterpretTarot(recordId);

            log.info("重新解读塔罗牌成功: oldRecordId={}, newRecordId={}", recordId, result.getId());

            return R.data(result);

        } catch (Exception e) {
            log.error("重新解读塔罗牌失败: recordId={}", recordId, e);
            return R.fail("重新解读失败: " + e.getMessage());
        }
    }
}
