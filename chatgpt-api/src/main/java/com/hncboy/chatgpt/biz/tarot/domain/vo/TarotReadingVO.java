package com.hncboy.chatgpt.biz.tarot.domain.vo;

import com.hncboy.chatgpt.common.i18n.I18nTranslation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 塔罗牌解读结果VO
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@Schema(title = "塔罗牌解读结果VO")
public class TarotReadingVO {

    @Schema(title = "解读ID")
    private Long id;

    @Schema(title = "牌阵名称")
    @I18nTranslation(byValue = true)
    private String spreadName;

    @Schema(title = "问题")
    private String question;

    @Schema(title = "解读结果")
    private String interpretation;

    @Schema(title = "抽取的牌")
    private String selectedCards;

    @Schema(title = "创建时间")
    private LocalDateTime createTime;
}
