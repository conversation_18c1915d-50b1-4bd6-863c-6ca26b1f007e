package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.UserJointLogin;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户联合登录Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface UserJointLoginMapper extends BaseMapper<UserJointLogin> {

    /**
     * 根据第三方ID查询联合登录记录
     *
     * @param loginType 登录类型
     * @param thirdPartyId 第三方ID
     * @return 联合登录记录
     */
    @Select("SELECT * FROM user_joint_login WHERE login_type = #{loginType} AND third_party_id = #{thirdPartyId} " +
            "AND status = 1 AND deleted = 0 LIMIT 1")
    UserJointLogin selectByThirdPartyId(@Param("loginType") String loginType, @Param("thirdPartyId") String thirdPartyId);

    /**
     * 根据用户ID查询联合登录记录列表
     *
     * @param userId 用户ID
     * @return 联合登录记录列表
     */
    @Select("SELECT * FROM user_joint_login WHERE user_id = #{userId} AND status = 1 AND deleted = 0 " +
            "ORDER BY is_primary DESC, last_use_time DESC")
    List<UserJointLogin> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID和登录类型查询联合登录记录
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 联合登录记录
     */
    @Select("SELECT * FROM user_joint_login WHERE user_id = #{userId} AND login_type = #{loginType} " +
            "AND status = 1 AND deleted = 0 LIMIT 1")
    UserJointLogin selectByUserIdAndLoginType(@Param("userId") Integer userId, @Param("loginType") String loginType);

    /**
     * 更新最后使用时间
     *
     * @param id 记录ID
     * @return 影响行数
     */
    @Update("UPDATE user_joint_login SET last_use_time = NOW(), update_time = NOW() " +
            "WHERE id = #{id} AND deleted = 0")
    int updateLastUseTime(@Param("id") Long id);

    /**
     * 更新状态
     *
     * @param id 记录ID
     * @param status 状态
     * @return 影响行数
     */
    @Update("UPDATE user_joint_login SET status = #{status}, update_time = NOW() " +
            "WHERE id = #{id} AND deleted = 0")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 批量更新用户的主登录方式
     *
     * @param userId 用户ID
     * @param isPrimary 是否主要
     * @return 影响行数
     */
    @Update("UPDATE user_joint_login SET is_primary = #{isPrimary}, update_time = NOW() " +
            "WHERE user_id = #{userId} AND deleted = 0")
    int updatePrimaryByUserId(@Param("userId") Integer userId, @Param("isPrimary") Integer isPrimary);

    /**
     * 设置指定登录方式为主要
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 影响行数
     */
    @Update("UPDATE user_joint_login SET is_primary = 1, update_time = NOW() " +
            "WHERE user_id = #{userId} AND login_type = #{loginType} AND deleted = 0")
    int setPrimaryLogin(@Param("userId") Integer userId, @Param("loginType") String loginType);

    /**
     * 统计用户的登录方式数量
     *
     * @param userId 用户ID
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM user_joint_login WHERE user_id = #{userId} AND status = 1 AND deleted = 0")
    Long countByUserId(@Param("userId") Integer userId);

    /**
     * 查询活跃的联合登录记录
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 记录列表
     */
    @Select("SELECT * FROM user_joint_login WHERE last_use_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "AND status = 1 AND deleted = 0 ORDER BY last_use_time DESC LIMIT #{limit}")
    List<UserJointLogin> selectActiveLogins(@Param("days") Integer days, @Param("limit") Integer limit);

    /**
     * 统计登录类型使用情况
     *
     * @param loginType 登录类型
     * @param days 天数
     * @return 使用次数
     */
    @Select("SELECT COUNT(*) FROM user_joint_login WHERE login_type = #{loginType} " +
            "AND last_use_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "AND status = 1 AND deleted = 0")
    Long countByLoginTypeAndDays(@Param("loginType") String loginType, @Param("days") Integer days);
}
