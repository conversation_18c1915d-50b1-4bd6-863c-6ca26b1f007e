package com.hncboy.chatgpt.db.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.user.UserJointLogin;
import com.hncboy.chatgpt.db.mapper.user.UserJointLoginMapper;
import com.hncboy.chatgpt.db.service.user.UserJointLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户联合登录Service实现 (MyBatis-Plus标准实现)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Service
public class UserJointLoginServiceImpl extends ServiceImpl<UserJointLoginMapper, UserJointLogin> 
        implements UserJointLoginService {

    @Override
    public UserJointLogin getByThirdPartyId(String loginType, String thirdPartyId) {
        return getOne(new LambdaQueryWrapper<UserJointLogin>()
                .eq(UserJointLogin::getLoginType, loginType)
                .eq(UserJointLogin::getThirdPartyId, thirdPartyId)
                .eq(UserJointLogin::getStatus, 1)
                .last("LIMIT 1"));
    }

    @Override
    public List<UserJointLogin> listByUserId(Integer userId) {
        return list(new LambdaQueryWrapper<UserJointLogin>()
                .eq(UserJointLogin::getUserId, userId)
                .eq(UserJointLogin::getStatus, 1)
                .orderByDesc(UserJointLogin::getIsPrimary)
                .orderByDesc(UserJointLogin::getLastUseTime));
    }

    @Override
    public UserJointLogin getByUserIdAndLoginType(Integer userId, String loginType) {
        return getOne(new LambdaQueryWrapper<UserJointLogin>()
                .eq(UserJointLogin::getUserId, userId)
                .eq(UserJointLogin::getLoginType, loginType)
                .eq(UserJointLogin::getStatus, 1)
                .last("LIMIT 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLastUseTime(Long id) {
        try {
            return update(new LambdaUpdateWrapper<UserJointLogin>()
                    .eq(UserJointLogin::getId, id)
                    .set(UserJointLogin::getLastUseTime, LocalDateTime.now()));
        } catch (Exception e) {
            log.error("更新最后使用时间失败: id={}", id, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, Integer status) {
        try {
            return update(new LambdaUpdateWrapper<UserJointLogin>()
                    .eq(UserJointLogin::getId, id)
                    .set(UserJointLogin::getStatus, status));
        } catch (Exception e) {
            log.error("更新联合登录状态失败: id={}, status={}", id, status, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setPrimaryLogin(Integer userId, String loginType) {
        try {
            // 先将该用户的所有登录方式设为非主要
            update(new LambdaUpdateWrapper<UserJointLogin>()
                    .eq(UserJointLogin::getUserId, userId)
                    .set(UserJointLogin::getIsPrimary, 0));

            // 再将指定的登录方式设为主要
            return update(new LambdaUpdateWrapper<UserJointLogin>()
                    .eq(UserJointLogin::getUserId, userId)
                    .eq(UserJointLogin::getLoginType, loginType)
                    .set(UserJointLogin::getIsPrimary, 1));
        } catch (Exception e) {
            log.error("设置主登录方式失败: userId={}, loginType={}", userId, loginType, e);
            return false;
        }
    }

    @Override
    public Long countByUserId(Integer userId) {
        return count(new LambdaQueryWrapper<UserJointLogin>()
                .eq(UserJointLogin::getUserId, userId)
                .eq(UserJointLogin::getStatus, 1));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByUserIdAndLoginType(Integer userId, String loginType) {
        try {
            return remove(new LambdaQueryWrapper<UserJointLogin>()
                    .eq(UserJointLogin::getUserId, userId)
                    .eq(UserJointLogin::getLoginType, loginType));
        } catch (Exception e) {
            log.error("删除联合登录记录失败: userId={}, loginType={}", userId, loginType, e);
            return false;
        }
    }
}
