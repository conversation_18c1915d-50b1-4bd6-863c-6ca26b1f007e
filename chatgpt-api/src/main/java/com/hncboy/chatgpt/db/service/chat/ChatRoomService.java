package com.hncboy.chatgpt.db.service.chat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.chat.ChatRoom;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天室Service (MyBatis-Plus标准CRUD，无业务逻辑)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface ChatRoomService extends IService<ChatRoom> {

    /**
     * 根据用户ID查询聊天室列表
     *
     * @param userId 用户ID
     * @param page 分页参数
     * @return 聊天室列表
     */
    IPage<ChatRoom> pageByUserId(Integer userId, Page<ChatRoom> page);

    /**
     * 根据用户ID和聊天室类型查询聊天室列表
     *
     * @param userId 用户ID
     * @param roomType 聊天室类型
     * @return 聊天室列表
     */
    List<ChatRoom> listByUserIdAndType(Integer userId, String roomType);

    /**
     * 更新聊天室最后消息时间
     *
     * @param chatRoomId 聊天室ID
     * @param lastMessageTime 最后消息时间
     * @return 是否成功
     */
    boolean updateLastMessageTime(Long chatRoomId, LocalDateTime lastMessageTime);

    /**
     * 更新聊天室消息数量
     *
     * @param chatRoomId 聊天室ID
     * @param increment 增量
     * @return 是否成功
     */
    boolean updateMessageCount(Long chatRoomId, Integer increment);

    /**
     * 设置聊天室置顶状态
     *
     * @param chatRoomId 聊天室ID
     * @param isTop 是否置顶
     * @return 是否成功
     */
    boolean updateTopStatus(Long chatRoomId, Integer isTop);

    /**
     * 根据用户ID查询置顶的聊天室
     *
     * @param userId 用户ID
     * @return 置顶聊天室列表
     */
    List<ChatRoom> listTopRoomsByUserId(Integer userId);

    /**
     * 统计用户的聊天室数量
     *
     * @param userId 用户ID
     * @param roomType 聊天室类型 (可选)
     * @return 聊天室数量
     */
    Long countByUserId(Integer userId, String roomType);

    /**
     * 查询用户最近活跃的聊天室
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 聊天室列表
     */
    List<ChatRoom> listRecentActiveRooms(Integer userId, Integer limit);

    /**
     * 批量删除聊天室
     *
     * @param chatRoomIds 聊天室ID列表
     * @return 是否成功
     */
    boolean batchDelete(List<Long> chatRoomIds);

    /**
     * 清理长时间未使用的聊天室
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 清理数量
     */
    Integer cleanInactiveRooms(Integer days, Integer limit);

    /**
     * 根据模型名称查询聊天室
     *
     * @param modelName 模型名称
     * @param limit 限制数量
     * @return 聊天室列表
     */
    List<ChatRoom> listByModelName(String modelName, Integer limit);
}
