package com.hncboy.chatgpt.db.service.pay;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.pay.PayOrder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一支付订单Service (MyBatis-Plus标准CRUD，无业务逻辑)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface PayOrderService extends IService<PayOrder> {

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @return 支付订单
     */
    PayOrder getByOrderNo(String orderNo);

    /**
     * 根据第三方订单号查询订单
     *
     * @param thirdPartyOrderNo 第三方订单号
     * @return 支付订单
     */
    PayOrder getByThirdPartyOrderNo(String thirdPartyOrderNo);

    /**
     * 根据用户ID和业务场景查询订单列表
     *
     * @param userId 用户ID
     * @param businessScene 业务场景
     * @param status 订单状态
     * @param page 分页参数
     * @return 订单列表
     */
    IPage<PayOrder> pageByUserAndScene(Integer userId, String businessScene, String status, Page<PayOrder> page);

    /**
     * 更新订单状态
     *
     * @param orderNo 订单号
     * @param status 新状态
     * @param payResult 支付结果
     * @param payTime 支付时间
     * @return 是否成功
     */
    boolean updateOrderStatus(String orderNo, String status, String payResult, LocalDateTime payTime);

    /**
     * 更新订单支付信息
     *
     * @param orderNo 订单号
     * @param thirdPartyOrderNo 第三方订单号
     * @param paidAmount 实际支付金额
     * @param payParams 支付参数
     * @return 是否成功
     */
    boolean updatePayInfo(String orderNo, String thirdPartyOrderNo, BigDecimal paidAmount, String payParams);

    /**
     * 查询过期未支付订单
     *
     * @param expireTime 过期时间
     * @param limit 限制数量
     * @return 订单列表
     */
    List<PayOrder> listExpiredOrders(LocalDateTime expireTime, Integer limit);

    /**
     * 统计用户支付金额
     *
     * @param userId 用户ID
     * @param businessScene 业务场景
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 支付总金额
     */
    BigDecimal sumPaidAmount(Integer userId, String businessScene, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计支付渠道使用情况
     *
     * @param payChannel 支付渠道
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单数量
     */
    Long countByChannel(String payChannel, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询待处理的回调订单
     *
     * @param payChannel 支付渠道
     * @param limit 限制数量
     * @return 订单列表
     */
    List<PayOrder> listPendingCallbackOrders(String payChannel, Integer limit);

    /**
     * 批量更新订单状态为过期
     *
     * @param orderIds 订单ID列表
     * @return 是否成功
     */
    boolean batchUpdateToExpired(List<Long> orderIds);

    /**
     * 查询用户最近的订单
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 订单列表
     */
    List<PayOrder> listRecentOrders(Integer userId, Integer limit);

    /**
     * 统计业务场景的订单数量
     *
     * @param businessScene 业务场景
     * @param status 订单状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单数量
     */
    Long countByBusinessScene(String businessScene, String status, LocalDateTime startTime, LocalDateTime endTime);
}
