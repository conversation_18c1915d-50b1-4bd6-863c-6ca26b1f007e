package com.hncboy.chatgpt.db.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.user.UserJointLogin;

import java.util.List;

/**
 * 用户联合登录Service (MyBatis-Plus标准CRUD，无业务逻辑)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface UserJointLoginService extends IService<UserJointLogin> {

    /**
     * 根据第三方ID查询联合登录记录
     *
     * @param loginType 登录类型
     * @param thirdPartyId 第三方ID
     * @return 联合登录记录
     */
    UserJointLogin getByThirdPartyId(String loginType, String thirdPartyId);

    /**
     * 根据用户ID查询联合登录记录列表
     *
     * @param userId 用户ID
     * @return 联合登录记录列表
     */
    List<UserJointLogin> listByUserId(Integer userId);

    /**
     * 根据用户ID和登录类型查询联合登录记录
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 联合登录记录
     */
    UserJointLogin getByUserIdAndLoginType(Integer userId, String loginType);

    /**
     * 更新最后使用时间
     *
     * @param id 记录ID
     * @return 是否成功
     */
    boolean updateLastUseTime(Long id);

    /**
     * 启用或禁用联合登录记录
     *
     * @param id 记录ID
     * @param status 状态 (0:禁用 1:启用)
     * @return 是否成功
     */
    boolean updateStatus(Long id, Integer status);

    /**
     * 设置主登录方式
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 是否成功
     */
    boolean setPrimaryLogin(Integer userId, String loginType);

    /**
     * 统计用户的登录方式数量
     *
     * @param userId 用户ID
     * @return 登录方式数量
     */
    Long countByUserId(Integer userId);

    /**
     * 删除用户的指定登录方式
     *
     * @param userId 用户ID
     * @param loginType 登录类型
     * @return 是否成功
     */
    boolean removeByUserIdAndLoginType(Integer userId, String loginType);
}
