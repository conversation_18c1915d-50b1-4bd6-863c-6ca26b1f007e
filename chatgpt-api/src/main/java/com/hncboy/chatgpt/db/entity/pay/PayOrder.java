package com.hncboy.chatgpt.db.entity.pay;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 统一支付订单实体 (合并al_orders+wx_pay_order+se_pay_order+qr_payment)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("pay_order")
public class PayOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号 (系统生成)
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 第三方订单号
     */
    @TableField("third_party_order_no")
    private String thirdPartyOrderNo;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 业务场景 (tarot/chatoi/zns/common)
     */
    @TableField("business_scene")
    private String businessScene;

    /**
     * 支付渠道 (ALIPAY/WECHAT/SEPAY/MOMO)
     */
    @TableField("pay_channel")
    private String payChannel;

    /**
     * 支付方式 (APP/H5/NATIVE/JSAPI/BANK_TRANSFER/WALLET)
     */
    @TableField("pay_method")
    private String payMethod;

    /**
     * 订单金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 实际支付金额
     */
    @TableField("paid_amount")
    private BigDecimal paidAmount;

    /**
     * 币种 (CNY/USD/VND)
     */
    @TableField("currency")
    private String currency;

    /**
     * 订单状态 (PENDING/PAID/FAILED/CANCELLED/REFUNDED)
     */
    @TableField("status")
    private String status;

    /**
     * 支付参数 (JSON格式存储各渠道特有参数)
     */
    @TableField("pay_params")
    private String payParams;

    /**
     * 支付结果 (JSON格式存储支付回调信息)
     */
    @TableField("pay_result")
    private String payResult;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;

    /**
     * 通知URL
     */
    @TableField("notify_url")
    private String notifyUrl;

    /**
     * 返回URL
     */
    @TableField("return_url")
    private String returnUrl;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 (0:否 1:是)
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    // === SE支付特有字段 ===

    /**
     * 银行账号 (SE支付)
     */
    @TableField("bank_account")
    private String bankAccount;

    /**
     * 银行名称 (SE支付)
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 二维码URL (SE支付)
     */
    @TableField("qr_code_url")
    private String qrCodeUrl;

    // === Momo支付特有字段 ===

    /**
     * 合作伙伴代码 (Momo支付)
     */
    @TableField("partner_code")
    private String partnerCode;

    /**
     * 请求ID (Momo支付)
     */
    @TableField("request_id")
    private String requestId;

    /**
     * 签名 (Momo支付)
     */
    @TableField("signature")
    private String signature;

    // === 微信支付特有字段 ===

    /**
     * 微信AppID
     */
    @TableField("wx_app_id")
    private String wxAppId;

    /**
     * 微信商户号
     */
    @TableField("wx_mch_id")
    private String wxMchId;

    /**
     * 微信预支付交易会话标识
     */
    @TableField("wx_prepay_id")
    private String wxPrepayId;

    // === 支付宝特有字段 ===

    /**
     * 支付宝AppID
     */
    @TableField("ali_app_id")
    private String aliAppId;

    /**
     * 支付宝卖家ID
     */
    @TableField("ali_seller_id")
    private String aliSellerId;

    /**
     * 支付宝交易号
     */
    @TableField("ali_trade_no")
    private String aliTradeNo;
}
