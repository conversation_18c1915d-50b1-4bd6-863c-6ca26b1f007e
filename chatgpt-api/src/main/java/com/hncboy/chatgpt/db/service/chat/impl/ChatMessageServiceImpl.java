package com.hncboy.chatgpt.db.service.chat.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.chat.ChatMessage;
import com.hncboy.chatgpt.db.mapper.chat.ChatMessageMapper;
import com.hncboy.chatgpt.db.service.chat.ChatMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天消息Service实现 (MyBatis-Plus标准实现)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Service
public class ChatMessageServiceImpl extends ServiceImpl<ChatMessageMapper, ChatMessage> implements ChatMessageService {

    @Override
    public IPage<ChatMessage> getHistoryByChatRoom(Long chatRoomId, Page<ChatMessage> page) {
        return page(page, new LambdaQueryWrapper<ChatMessage>()
                .eq(ChatMessage::getChatRoomId, chatRoomId)
                .orderByDesc(ChatMessage::getCreateTime));
    }

    @Override
    public List<ChatMessage> getRecentMessages(Long chatRoomId, Integer limit) {
        return list(new LambdaQueryWrapper<ChatMessage>()
                .eq(ChatMessage::getChatRoomId, chatRoomId)
                .orderByDesc(ChatMessage::getCreateTime)
                .last("LIMIT " + limit));
    }

    @Override
    public IPage<ChatMessage> pageByUserId(Integer userId, Page<ChatMessage> page) {
        return page(page, new LambdaQueryWrapper<ChatMessage>()
                .eq(ChatMessage::getUserId, userId)
                .orderByDesc(ChatMessage::getCreateTime));
    }

    @Override
    public Long countByChatRoom(Long chatRoomId, Integer messageType) {
        LambdaQueryWrapper<ChatMessage> wrapper = new LambdaQueryWrapper<ChatMessage>()
                .eq(ChatMessage::getChatRoomId, chatRoomId);
        
        if (messageType != null) {
            wrapper.eq(ChatMessage::getMessageType, messageType);
        }
        
        return count(wrapper);
    }

    @Override
    public Long countByUser(Integer userId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<ChatMessage> wrapper = new LambdaQueryWrapper<ChatMessage>()
                .eq(ChatMessage::getUserId, userId);
        
        if (startTime != null) {
            wrapper.ge(ChatMessage::getCreateTime, startTime);
        }
        
        if (endTime != null) {
            wrapper.le(ChatMessage::getCreateTime, endTime);
        }
        
        return count(wrapper);
    }

    @Override
    public List<ChatMessage> listFailedMessages(Integer limit) {
        return list(new LambdaQueryWrapper<ChatMessage>()
                .ne(ChatMessage::getStatus, 1) // 状态不为成功
                .orderByAsc(ChatMessage::getCreateTime)
                .last("LIMIT " + limit));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMessageStatus(Long messageId, Integer status, String errorMessage) {
        try {
            LambdaUpdateWrapper<ChatMessage> wrapper = new LambdaUpdateWrapper<ChatMessage>()
                    .eq(ChatMessage::getId, messageId)
                    .set(ChatMessage::getStatus, status);
            
            if (errorMessage != null) {
                wrapper.set(ChatMessage::getErrorMessage, errorMessage);
            }
            
            return update(wrapper);
        } catch (Exception e) {
            log.error("更新消息状态失败: messageId={}, status={}", messageId, status, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> messageIds) {
        try {
            if (messageIds == null || messageIds.isEmpty()) {
                return true;
            }
            return removeByIds(messageIds);
        } catch (Exception e) {
            log.error("批量删除消息失败: messageIds={}", messageIds, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByChatRoom(Long chatRoomId) {
        try {
            return remove(new LambdaQueryWrapper<ChatMessage>()
                    .eq(ChatMessage::getChatRoomId, chatRoomId));
        } catch (Exception e) {
            log.error("删除聊天室消息失败: chatRoomId={}", chatRoomId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cleanExpiredMessages(Integer days, Integer limit) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
            List<ChatMessage> expiredMessages = list(new LambdaQueryWrapper<ChatMessage>()
                    .lt(ChatMessage::getCreateTime, cutoffTime)
                    .last("LIMIT " + limit));
            
            if (!expiredMessages.isEmpty()) {
                List<Long> messageIds = expiredMessages.stream()
                        .map(ChatMessage::getId)
                        .collect(java.util.stream.Collectors.toList());
                removeByIds(messageIds);
                return messageIds.size();
            }
            
            return 0;
        } catch (Exception e) {
            log.error("清理过期消息失败: days={}, limit={}", days, limit, e);
            return 0;
        }
    }

    @Override
    public TokenStats getTokenStats(Integer userId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 这里需要使用原生SQL查询或者聚合查询
            // 暂时返回模拟数据，实际实现需要在Mapper中添加对应方法
            TokenStats stats = new TokenStats();
            stats.setTotalPromptTokens(1000L);
            stats.setTotalCompletionTokens(2000L);
            stats.setTotalTokens(3000L);
            stats.setMessageCount(100L);
            return stats;
        } catch (Exception e) {
            log.error("获取令牌统计失败: userId={}", userId, e);
            TokenStats stats = new TokenStats();
            stats.setTotalPromptTokens(0L);
            stats.setTotalCompletionTokens(0L);
            stats.setTotalTokens(0L);
            stats.setMessageCount(0L);
            return stats;
        }
    }

    @Override
    public List<ChatMessage> searchByContent(String content, Integer limit) {
        return list(new LambdaQueryWrapper<ChatMessage>()
                .like(ChatMessage::getContent, content)
                .orderByDesc(ChatMessage::getCreateTime)
                .last("LIMIT " + limit));
    }
}
