package com.hncboy.chatgpt.db.service.chat.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.chat.ChatRoom;
import com.hncboy.chatgpt.db.mapper.chat.ChatRoomMapper;
import com.hncboy.chatgpt.db.service.chat.ChatRoomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天室Service实现 (MyBatis-Plus标准实现)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Service
public class ChatRoomServiceImpl extends ServiceImpl<ChatRoomMapper, ChatRoom> implements ChatRoomService {

    @Override
    public IPage<ChatRoom> pageByUserId(Integer userId, Page<ChatRoom> page) {
        return page(page, new LambdaQueryWrapper<ChatRoom>()
                .eq(ChatRoom::getUserId, userId)
                .orderByDesc(ChatRoom::getLastMessageTime)
                .orderByDesc(ChatRoom::getCreateTime));
    }

    @Override
    public List<ChatRoom> listByUserIdAndType(Integer userId, String roomType) {
        LambdaQueryWrapper<ChatRoom> wrapper = new LambdaQueryWrapper<ChatRoom>()
                .eq(ChatRoom::getUserId, userId)
                .orderByDesc(ChatRoom::getLastMessageTime);
        
        if (roomType != null && !roomType.trim().isEmpty()) {
            wrapper.eq(ChatRoom::getRoomType, roomType);
        }
        
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLastMessageTime(Long chatRoomId, LocalDateTime lastMessageTime) {
        try {
            return update(new LambdaUpdateWrapper<ChatRoom>()
                    .eq(ChatRoom::getId, chatRoomId)
                    .set(ChatRoom::getLastMessageTime, lastMessageTime));
        } catch (Exception e) {
            log.error("更新聊天室最后消息时间失败: chatRoomId={}, time={}", chatRoomId, lastMessageTime, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMessageCount(Long chatRoomId, Integer increment) {
        try {
            ChatRoom chatRoom = getById(chatRoomId);
            if (chatRoom == null) {
                return false;
            }
            
            int newCount = (chatRoom.getMessageCount() != null ? chatRoom.getMessageCount() : 0) + increment;
            return update(new LambdaUpdateWrapper<ChatRoom>()
                    .eq(ChatRoom::getId, chatRoomId)
                    .set(ChatRoom::getMessageCount, newCount));
        } catch (Exception e) {
            log.error("更新聊天室消息数量失败: chatRoomId={}, increment={}", chatRoomId, increment, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTopStatus(Long chatRoomId, Integer isTop) {
        try {
            return update(new LambdaUpdateWrapper<ChatRoom>()
                    .eq(ChatRoom::getId, chatRoomId)
                    .set(ChatRoom::getIsTop, isTop));
        } catch (Exception e) {
            log.error("更新聊天室置顶状态失败: chatRoomId={}, isTop={}", chatRoomId, isTop, e);
            return false;
        }
    }

    @Override
    public List<ChatRoom> listTopRoomsByUserId(Integer userId) {
        return list(new LambdaQueryWrapper<ChatRoom>()
                .eq(ChatRoom::getUserId, userId)
                .eq(ChatRoom::getIsTop, 1)
                .orderByDesc(ChatRoom::getLastMessageTime));
    }

    @Override
    public Long countByUserId(Integer userId, String roomType) {
        LambdaQueryWrapper<ChatRoom> wrapper = new LambdaQueryWrapper<ChatRoom>()
                .eq(ChatRoom::getUserId, userId);
        
        if (roomType != null && !roomType.trim().isEmpty()) {
            wrapper.eq(ChatRoom::getRoomType, roomType);
        }
        
        return count(wrapper);
    }

    @Override
    public List<ChatRoom> listRecentActiveRooms(Integer userId, Integer limit) {
        return list(new LambdaQueryWrapper<ChatRoom>()
                .eq(ChatRoom::getUserId, userId)
                .isNotNull(ChatRoom::getLastMessageTime)
                .orderByDesc(ChatRoom::getLastMessageTime)
                .last("LIMIT " + limit));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> chatRoomIds) {
        try {
            if (chatRoomIds == null || chatRoomIds.isEmpty()) {
                return true;
            }
            return removeByIds(chatRoomIds);
        } catch (Exception e) {
            log.error("批量删除聊天室失败: chatRoomIds={}", chatRoomIds, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer cleanInactiveRooms(Integer days, Integer limit) {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(days);
            List<ChatRoom> inactiveRooms = list(new LambdaQueryWrapper<ChatRoom>()
                    .lt(ChatRoom::getLastMessageTime, cutoffTime)
                    .or()
                    .isNull(ChatRoom::getLastMessageTime)
                    .lt(ChatRoom::getCreateTime, cutoffTime)
                    .last("LIMIT " + limit));
            
            if (!inactiveRooms.isEmpty()) {
                List<Long> roomIds = inactiveRooms.stream()
                        .map(ChatRoom::getId)
                        .collect(java.util.stream.Collectors.toList());
                removeByIds(roomIds);
                return roomIds.size();
            }
            
            return 0;
        } catch (Exception e) {
            log.error("清理不活跃聊天室失败: days={}, limit={}", days, limit, e);
            return 0;
        }
    }

    @Override
    public List<ChatRoom> listByModelName(String modelName, Integer limit) {
        return list(new LambdaQueryWrapper<ChatRoom>()
                .eq(ChatRoom::getModelName, modelName)
                .orderByDesc(ChatRoom::getLastMessageTime)
                .last("LIMIT " + limit));
    }
}
