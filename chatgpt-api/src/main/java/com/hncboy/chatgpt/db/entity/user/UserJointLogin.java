package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户联合登录实体 (合并users+wx_user_info)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_joint_login")
public class UserJointLogin implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID (关联user_base_info.id)
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 登录类型 (WECHAT/GOOGLE/FACEBOOK/PHONE/EMAIL/FINGERPRINT)
     */
    @TableField("login_type")
    private String loginType;

    /**
     * 第三方用户ID
     */
    @TableField("third_party_id")
    private String thirdPartyId;

    /**
     * 第三方用户名
     */
    @TableField("third_party_username")
    private String thirdPartyUsername;

    /**
     * 第三方邮箱
     */
    @TableField("third_party_email")
    private String thirdPartyEmail;

    /**
     * 第三方头像
     */
    @TableField("third_party_avatar")
    private String thirdPartyAvatar;

    /**
     * 访问令牌
     */
    @TableField("access_token")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @TableField("refresh_token")
    private String refreshToken;

    /**
     * 令牌过期时间
     */
    @TableField("token_expire_time")
    private LocalDateTime tokenExpireTime;

    /**
     * 状态 (0:禁用 1:启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否主登录方式 (0:否 1:是)
     */
    @TableField("is_primary")
    private Integer isPrimary;

    /**
     * 最后使用时间
     */
    @TableField("last_use_time")
    private LocalDateTime lastUseTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 (0:否 1:是)
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    // === 原wx_user_info表字段 ===

    /**
     * 微信昵称
     */
    @TableField("wx_nickname")
    private String wxNickname;

    /**
     * 微信性别 (1:男 2:女 0:未知)
     */
    @TableField("wx_sex")
    private Integer wxSex;

    /**
     * 微信省份
     */
    @TableField("wx_province")
    private String wxProvince;

    /**
     * 微信城市
     */
    @TableField("wx_city")
    private String wxCity;

    /**
     * 微信国家
     */
    @TableField("wx_country")
    private String wxCountry;

    /**
     * 微信头像URL
     */
    @TableField("wx_head_img_url")
    private String wxHeadImgUrl;

    /**
     * 微信特权信息
     */
    @TableField("wx_privilege")
    private String wxPrivilege;

    /**
     * 微信UnionID
     */
    @TableField("wx_union_id")
    private String wxUnionId;
}
