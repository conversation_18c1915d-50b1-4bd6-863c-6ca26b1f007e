package com.hncboy.chatgpt.db.entity.chat;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 聊天室实体 (原chat_room表)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("chat_room")
public class ChatRoom implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 聊天室名称
     */
    @TableField("name")
    private String name;

    /**
     * 聊天室描述
     */
    @TableField("description")
    private String description;

    /**
     * 聊天室类型 (CHAT/DRAW/MUSIC/WRITE)
     */
    @TableField("room_type")
    private String roomType;

    /**
     * 模型ID
     */
    @TableField("model_id")
    private Integer modelId;

    /**
     * 模型名称
     */
    @TableField("model_name")
    private String modelName;

    /**
     * 智能体ID
     */
    @TableField("agent_id")
    private Integer agentId;

    /**
     * 系统提示词
     */
    @TableField("system_prompt")
    private String systemPrompt;

    /**
     * 温度参数
     */
    @TableField("temperature")
    private Double temperature;

    /**
     * 最大令牌数
     */
    @TableField("max_tokens")
    private Integer maxTokens;

    /**
     * 上下文长度
     */
    @TableField("context_length")
    private Integer contextLength;

    /**
     * 状态 (0:禁用 1:启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否置顶 (0:否 1:是)
     */
    @TableField("is_top")
    private Integer isTop;

    /**
     * 最后消息时间
     */
    @TableField("last_message_time")
    private LocalDateTime lastMessageTime;

    /**
     * 消息数量
     */
    @TableField("message_count")
    private Integer messageCount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除 (0:否 1:是)
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;
}
