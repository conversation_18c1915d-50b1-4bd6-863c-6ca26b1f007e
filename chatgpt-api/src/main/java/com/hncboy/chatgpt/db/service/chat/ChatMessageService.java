package com.hncboy.chatgpt.db.service.chat;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.db.entity.chat.ChatMessage;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天消息Service (MyBatis-Plus标准CRUD，无业务逻辑)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
public interface ChatMessageService extends IService<ChatMessage> {

    /**
     * 根据聊天室ID分页查询消息历史
     *
     * @param chatRoomId 聊天室ID
     * @param page 分页参数
     * @return 消息历史
     */
    IPage<ChatMessage> getHistoryByChatRoom(Long chatRoomId, Page<ChatMessage> page);

    /**
     * 根据聊天室ID查询最近的消息
     *
     * @param chatRoomId 聊天室ID
     * @param limit 限制数量
     * @return 最近消息列表
     */
    List<ChatMessage> getRecentMessages(Long chatRoomId, Integer limit);

    /**
     * 根据用户ID查询消息列表
     *
     * @param userId 用户ID
     * @param page 分页参数
     * @return 消息列表
     */
    IPage<ChatMessage> pageByUserId(Integer userId, Page<ChatMessage> page);

    /**
     * 统计聊天室的消息数量
     *
     * @param chatRoomId 聊天室ID
     * @param messageType 消息类型 (可选)
     * @return 消息数量
     */
    Long countByChatRoom(Long chatRoomId, Integer messageType);

    /**
     * 统计用户的消息数量
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 消息数量
     */
    Long countByUser(Integer userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询失败的消息
     *
     * @param limit 限制数量
     * @return 失败消息列表
     */
    List<ChatMessage> listFailedMessages(Integer limit);

    /**
     * 更新消息状态
     *
     * @param messageId 消息ID
     * @param status 状态
     * @param errorMessage 错误信息
     * @return 是否成功
     */
    boolean updateMessageStatus(Long messageId, Integer status, String errorMessage);

    /**
     * 批量删除消息
     *
     * @param messageIds 消息ID列表
     * @return 是否成功
     */
    boolean batchDelete(List<Long> messageIds);

    /**
     * 根据聊天室ID删除所有消息
     *
     * @param chatRoomId 聊天室ID
     * @return 是否成功
     */
    boolean deleteByChatRoom(Long chatRoomId);

    /**
     * 清理过期的消息
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 清理数量
     */
    Integer cleanExpiredMessages(Integer days, Integer limit);

    /**
     * 统计令牌使用情况
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 令牌统计信息
     */
    TokenStats getTokenStats(Integer userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询包含特定内容的消息
     *
     * @param content 内容关键词
     * @param limit 限制数量
     * @return 消息列表
     */
    List<ChatMessage> searchByContent(String content, Integer limit);

    /**
     * 令牌统计信息
     */
    class TokenStats {
        private Long totalPromptTokens;
        private Long totalCompletionTokens;
        private Long totalTokens;
        private Long messageCount;

        // Getters and Setters
        public Long getTotalPromptTokens() { return totalPromptTokens; }
        public void setTotalPromptTokens(Long totalPromptTokens) { this.totalPromptTokens = totalPromptTokens; }
        public Long getTotalCompletionTokens() { return totalCompletionTokens; }
        public void setTotalCompletionTokens(Long totalCompletionTokens) { this.totalCompletionTokens = totalCompletionTokens; }
        public Long getTotalTokens() { return totalTokens; }
        public void setTotalTokens(Long totalTokens) { this.totalTokens = totalTokens; }
        public Long getMessageCount() { return messageCount; }
        public void setMessageCount(Long messageCount) { this.messageCount = messageCount; }
    }
}
