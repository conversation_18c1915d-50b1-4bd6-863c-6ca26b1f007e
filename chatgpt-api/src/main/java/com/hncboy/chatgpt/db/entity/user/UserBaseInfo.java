package com.hncboy.chatgpt.db.entity.user;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户基础信息实体 (原user_base_info表)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_base_info")
public class UserBaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 本表ID
     */
    @TableField("commission_id")
    private Integer commissionId;

    /**
     * 账号(手机号或其他账号)
     */
    @TableField("account")
    private String account;

    /**
     * 用户类型 zns:智能社 tarot:塔罗
     */
    @TableField("user_type")
    private String userType;

    /**
     * 用户名
     */
    @TableField("name")
    private String name;

    /**
     * 昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 上级用户id（邀请人id）
     */
    @TableField("parent_id")
    private Integer parentId;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * vip到期时间
     */
    @TableField("vip_end_time")
    private LocalDateTime vipEndTime;

    /**
     * 积分
     */
    @TableField("points")
    private Integer points;

    /**
     * 绘画次数
     */
    @TableField("draw_num")
    private Integer drawNum;

    /**
     * 音乐次数
     */
    @TableField("music_num")
    private Integer musicNum;

    /**
     * 写作次数
     */
    @TableField("write_num")
    private Integer writeNum;

    /**
     * 塔罗币
     */
    @TableField("tarot_coins")
    private Integer tarotCoins;

    /**
     * 最后登录时间
     */
    @TableField("login_time")
    private LocalDateTime loginTime;

    /**
     * 头像
     */
    @TableField("head_sculpture")
    private String headSculpture;

    /**
     * 是否首次登录
     */
    @TableField("first_status")
    private String firstStatus;

    /**
     * IP地址
     */
    @TableField("ip")
    private String ip;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 状态0正常1禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 剩余使用次数(充值)
     */
    @TableField("use_num")
    private Integer useNum;

    /**
     * 微信openid
     */
    @TableField("open_id")
    private String openId;

    /**
     * 免费使用次数(赠送)
     */
    @TableField("free_num")
    private Integer freeNum;

    /**
     * 每日免费次数
     */
    @TableField("daily_free_time")
    private Integer dailyFreeTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 用户语言偏好
     */
    @TableField("language")
    private String language;

    /**
     * 用户币种偏好
     */
    @TableField("currency")
    private String currency;

    /**
     * 用户时区
     */
    @TableField("timezone")
    private String timezone;

    /**
     * users表id
     */
    @TableField("users_id")
    private Long usersId;

    /**
     * 是否注销（0否1是）
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;
}
