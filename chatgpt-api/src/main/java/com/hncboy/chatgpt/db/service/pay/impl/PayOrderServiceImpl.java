package com.hncboy.chatgpt.db.service.pay.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.pay.PayOrder;
import com.hncboy.chatgpt.db.mapper.pay.PayOrderMapper;
import com.hncboy.chatgpt.db.service.pay.PayOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一支付订单Service实现 (MyBatis-Plus标准实现)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Service
public class PayOrderServiceImpl extends ServiceImpl<PayOrderMapper, PayOrder> implements PayOrderService {

    @Override
    public PayOrder getByOrderNo(String orderNo) {
        return getOne(new LambdaQueryWrapper<PayOrder>()
                .eq(PayOrder::getOrderNo, orderNo)
                .last("LIMIT 1"));
    }

    @Override
    public PayOrder getByThirdPartyOrderNo(String thirdPartyOrderNo) {
        return getOne(new LambdaQueryWrapper<PayOrder>()
                .eq(PayOrder::getThirdPartyOrderNo, thirdPartyOrderNo)
                .last("LIMIT 1"));
    }

    @Override
    public IPage<PayOrder> pageByUserAndScene(Integer userId, String businessScene, String status, Page<PayOrder> page) {
        LambdaQueryWrapper<PayOrder> wrapper = new LambdaQueryWrapper<PayOrder>()
                .eq(PayOrder::getUserId, userId)
                .orderByDesc(PayOrder::getCreateTime);

        if (businessScene != null && !businessScene.trim().isEmpty()) {
            wrapper.eq(PayOrder::getBusinessScene, businessScene);
        }

        if (status != null && !status.trim().isEmpty()) {
            wrapper.eq(PayOrder::getStatus, status);
        }

        return page(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderStatus(String orderNo, String status, String payResult, LocalDateTime payTime) {
        try {
            LambdaUpdateWrapper<PayOrder> wrapper = new LambdaUpdateWrapper<PayOrder>()
                    .eq(PayOrder::getOrderNo, orderNo)
                    .set(PayOrder::getStatus, status);

            if (payResult != null) {
                wrapper.set(PayOrder::getPayResult, payResult);
            }

            if (payTime != null) {
                wrapper.set(PayOrder::getPayTime, payTime);
            }

            return update(wrapper);
        } catch (Exception e) {
            log.error("更新订单状态失败: orderNo={}, status={}", orderNo, status, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePayInfo(String orderNo, String thirdPartyOrderNo, BigDecimal paidAmount, String payParams) {
        try {
            LambdaUpdateWrapper<PayOrder> wrapper = new LambdaUpdateWrapper<PayOrder>()
                    .eq(PayOrder::getOrderNo, orderNo);

            if (thirdPartyOrderNo != null) {
                wrapper.set(PayOrder::getThirdPartyOrderNo, thirdPartyOrderNo);
            }

            if (paidAmount != null) {
                wrapper.set(PayOrder::getPaidAmount, paidAmount);
            }

            if (payParams != null) {
                wrapper.set(PayOrder::getPayParams, payParams);
            }

            return update(wrapper);
        } catch (Exception e) {
            log.error("更新订单支付信息失败: orderNo={}", orderNo, e);
            return false;
        }
    }

    @Override
    public List<PayOrder> listExpiredOrders(LocalDateTime expireTime, Integer limit) {
        return list(new LambdaQueryWrapper<PayOrder>()
                .eq(PayOrder::getStatus, "PENDING")
                .lt(PayOrder::getExpireTime, expireTime)
                .orderByAsc(PayOrder::getCreateTime)
                .last("LIMIT " + limit));
    }

    @Override
    public BigDecimal sumPaidAmount(Integer userId, String businessScene, LocalDateTime startTime, LocalDateTime endTime) {
        // 这里需要使用原生SQL或者聚合查询
        // 暂时返回0，实际实现需要在Mapper中添加对应方法
        return baseMapper.sumPaidAmount(userId, businessScene, startTime, endTime);
    }

    @Override
    public Long countByChannel(String payChannel, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.countByChannel(payChannel, startTime, endTime);
    }

    @Override
    public List<PayOrder> listPendingCallbackOrders(String payChannel, Integer limit) {
        return baseMapper.selectPendingCallbackOrders(payChannel, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateToExpired(List<Long> orderIds) {
        try {
            if (orderIds == null || orderIds.isEmpty()) {
                return true;
            }

            return update(new LambdaUpdateWrapper<PayOrder>()
                    .in(PayOrder::getId, orderIds)
                    .set(PayOrder::getStatus, "EXPIRED"));
        } catch (Exception e) {
            log.error("批量更新订单为过期状态失败: orderIds={}", orderIds, e);
            return false;
        }
    }

    @Override
    public List<PayOrder> listRecentOrders(Integer userId, Integer limit) {
        return list(new LambdaQueryWrapper<PayOrder>()
                .eq(PayOrder::getUserId, userId)
                .orderByDesc(PayOrder::getCreateTime)
                .last("LIMIT " + limit));
    }

    @Override
    public Long countByBusinessScene(String businessScene, String status, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<PayOrder> wrapper = new LambdaQueryWrapper<PayOrder>()
                .eq(PayOrder::getBusinessScene, businessScene);

        if (status != null && !status.trim().isEmpty()) {
            wrapper.eq(PayOrder::getStatus, status);
        }

        if (startTime != null) {
            wrapper.ge(PayOrder::getCreateTime, startTime);
        }

        if (endTime != null) {
            wrapper.le(PayOrder::getCreateTime, endTime);
        }

        return count(wrapper);
    }
}
