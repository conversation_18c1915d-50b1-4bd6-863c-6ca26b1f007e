package com.hncboy.chatgpt.db.mapper.user;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 用户基础信息Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface UserBaseInfoMapper extends BaseMapper<UserBaseInfo> {

    /**
     * 根据账号查询用户信息
     *
     * @param account 账号
     * @return 用户信息
     */
    @Select("SELECT * FROM user_base_info WHERE account = #{account} AND deleted = 0 AND status = 0")
    UserBaseInfo selectByAccount(@Param("account") String account);

    /**
     * 根据OpenID查询用户信息
     *
     * @param openId OpenID
     * @return 用户信息
     */
    @Select("SELECT * FROM user_base_info WHERE open_id = #{openId} AND deleted = 0")
    UserBaseInfo selectByOpenId(@Param("openId") String openId);

    /**
     * 根据UsersID查询用户信息
     *
     * @param usersId UsersID
     * @return 用户信息
     */
    @Select("SELECT * FROM user_base_info WHERE users_id = #{usersId} AND deleted = 0")
    UserBaseInfo selectByUsersId(@Param("usersId") Long usersId);

    /**
     * 更新用户使用次数
     *
     * @param userId 用户ID
     * @param useNum 使用次数变化量
     * @param freeNum 免费次数变化量
     * @return 影响行数
     */
    @Update("UPDATE user_base_info SET use_num = use_num + #{useNum}, free_num = free_num + #{freeNum}, " +
            "update_time = NOW() WHERE id = #{userId} AND deleted = 0")
    int updateUserCount(@Param("userId") Integer userId, @Param("useNum") Integer useNum, @Param("freeNum") Integer freeNum);

    /**
     * 更新用户积分
     *
     * @param userId 用户ID
     * @param points 积分变化量
     * @return 影响行数
     */
    @Update("UPDATE user_base_info SET points = points + #{points}, update_time = NOW() " +
            "WHERE id = #{userId} AND deleted = 0")
    int updateUserPoints(@Param("userId") Integer userId, @Param("points") Integer points);

    /**
     * 更新用户VIP状态
     *
     * @param userId 用户ID
     * @param vipEndTime VIP到期时间
     * @return 影响行数
     */
    @Update("UPDATE user_base_info SET vip_end_time = #{vipEndTime}, update_time = NOW() " +
            "WHERE id = #{userId} AND deleted = 0")
    int updateUserVip(@Param("userId") Integer userId, @Param("vipEndTime") String vipEndTime);

    /**
     * 批量查询用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户信息列表
     */
    @Select("<script>" +
            "SELECT * FROM user_base_info WHERE id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            " AND deleted = 0" +
            "</script>")
    List<UserBaseInfo> selectByUserIds(@Param("userIds") List<Integer> userIds);

    /**
     * 统计用户数量
     *
     * @param userType 用户类型
     * @param status 状态
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM user_base_info WHERE user_type = #{userType} AND status = #{status} AND deleted = 0")
    Long countByTypeAndStatus(@Param("userType") String userType, @Param("status") Integer status);

    /**
     * 查询活跃用户列表
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 用户列表
     */
    @Select("SELECT * FROM user_base_info WHERE login_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "AND deleted = 0 ORDER BY login_time DESC LIMIT #{limit}")
    List<UserBaseInfo> selectActiveUsers(@Param("days") Integer days, @Param("limit") Integer limit);

    /**
     * 更新用户登录时间
     *
     * @param userId 用户ID
     * @param ip IP地址
     * @param address 地址
     * @return 影响行数
     */
    @Update("UPDATE user_base_info SET login_time = NOW(), ip = #{ip}, address = #{address}, " +
            "update_time = NOW() WHERE id = #{userId} AND deleted = 0")
    int updateLoginInfo(@Param("userId") Integer userId, @Param("ip") String ip, @Param("address") String address);
}
