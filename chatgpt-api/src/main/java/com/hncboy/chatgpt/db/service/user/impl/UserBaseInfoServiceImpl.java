package com.hncboy.chatgpt.db.service.user.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.db.entity.user.UserBaseInfo;
import com.hncboy.chatgpt.db.mapper.user.UserBaseInfoMapper;
import com.hncboy.chatgpt.db.service.user.UserBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户基础信息Service实现 (MyBatis-Plus标准实现)
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
@Service
public class UserBaseInfoServiceImpl extends ServiceImpl<UserBaseInfoMapper, UserBaseInfo> 
        implements UserBaseInfoService {

    @Override
    public UserBaseInfo getByAccount(String account) {
        return baseMapper.selectByAccount(account);
    }

    @Override
    public UserBaseInfo getByOpenId(String openId) {
        return baseMapper.selectByOpenId(openId);
    }

    @Override
    public UserBaseInfo getByUsersId(Long usersId) {
        return baseMapper.selectByUsersId(usersId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserCount(Integer userId, Integer useNum, Integer freeNum) {
        try {
            int result = baseMapper.updateUserCount(userId, useNum, freeNum);
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户次数失败: userId={}, useNum={}, freeNum={}", userId, useNum, freeNum, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserPoints(Integer userId, Integer points) {
        try {
            int result = baseMapper.updateUserPoints(userId, points);
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户积分失败: userId={}, points={}", userId, points, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserVip(Integer userId, String vipEndTime) {
        try {
            int result = baseMapper.updateUserVip(userId, vipEndTime);
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户VIP状态失败: userId={}, vipEndTime={}", userId, vipEndTime, e);
            return false;
        }
    }

    @Override
    public List<UserBaseInfo> listByUserIds(List<Integer> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return List.of();
        }
        return baseMapper.selectByUserIds(userIds);
    }

    @Override
    public Long countByTypeAndStatus(String userType, Integer status) {
        return baseMapper.countByTypeAndStatus(userType, status);
    }

    @Override
    public List<UserBaseInfo> listActiveUsers(Integer days, Integer limit) {
        return baseMapper.selectActiveUsers(days, limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLoginInfo(Integer userId, String ip, String address) {
        try {
            int result = baseMapper.updateLoginInfo(userId, ip, address);
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户登录信息失败: userId={}, ip={}, address={}", userId, ip, address, e);
            return false;
        }
    }

    @Override
    public boolean hasEnoughCount(Integer userId, Integer requiredCount) {
        UserBaseInfo userInfo = getById(userId);
        if (userInfo == null) {
            return false;
        }
        int totalCount = (userInfo.getUseNum() != null ? userInfo.getUseNum() : 0) + 
                        (userInfo.getFreeNum() != null ? userInfo.getFreeNum() : 0);
        return totalCount >= requiredCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deductUserCount(Integer userId, Integer count) {
        UserBaseInfo userInfo = getById(userId);
        if (userInfo == null) {
            log.warn("用户不存在: userId={}", userId);
            return false;
        }

        int freeNum = userInfo.getFreeNum() != null ? userInfo.getFreeNum() : 0;
        int useNum = userInfo.getUseNum() != null ? userInfo.getUseNum() : 0;
        int totalCount = freeNum + useNum;

        if (totalCount < count) {
            log.warn("用户次数不足: userId={}, totalCount={}, requiredCount={}", userId, totalCount, count);
            return false;
        }

        // 优先扣减免费次数
        int deductFree = Math.min(freeNum, count);
        int deductUse = count - deductFree;

        return updateUserCount(userId, -deductUse, -deductFree);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addUserCount(Integer userId, Integer useNum, Integer freeNum) {
        return updateUserCount(userId, useNum, freeNum);
    }
}
