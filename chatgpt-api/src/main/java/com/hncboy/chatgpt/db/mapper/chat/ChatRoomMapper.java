package com.hncboy.chatgpt.db.mapper.chat;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.db.entity.chat.ChatRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 聊天室Mapper
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Mapper
public interface ChatRoomMapper extends BaseMapper<ChatRoom> {

    /**
     * 根据用户ID查询聊天室列表
     *
     * @param userId 用户ID
     * @return 聊天室列表
     */
    @Select("SELECT * FROM chat_room WHERE user_id = #{userId} AND deleted = 0 " +
            "ORDER BY last_message_time DESC, create_time DESC")
    List<ChatRoom> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID和类型查询聊天室列表
     *
     * @param userId 用户ID
     * @param roomType 聊天室类型
     * @return 聊天室列表
     */
    @Select("SELECT * FROM chat_room WHERE user_id = #{userId} " +
            "AND room_type = #{roomType} AND deleted = 0 " +
            "ORDER BY last_message_time DESC")
    List<ChatRoom> selectByUserIdAndType(@Param("userId") Integer userId, @Param("roomType") String roomType);

    /**
     * 更新聊天室最后消息时间
     *
     * @param chatRoomId 聊天室ID
     * @param lastMessageTime 最后消息时间
     * @return 影响行数
     */
    @Update("UPDATE chat_room SET last_message_time = #{lastMessageTime}, update_time = NOW() " +
            "WHERE id = #{chatRoomId} AND deleted = 0")
    int updateLastMessageTime(@Param("chatRoomId") Long chatRoomId, @Param("lastMessageTime") LocalDateTime lastMessageTime);

    /**
     * 更新聊天室消息数量
     *
     * @param chatRoomId 聊天室ID
     * @param messageCount 消息数量
     * @return 影响行数
     */
    @Update("UPDATE chat_room SET message_count = #{messageCount}, update_time = NOW() " +
            "WHERE id = #{chatRoomId} AND deleted = 0")
    int updateMessageCount(@Param("chatRoomId") Long chatRoomId, @Param("messageCount") Integer messageCount);

    /**
     * 更新聊天室置顶状态
     *
     * @param chatRoomId 聊天室ID
     * @param isTop 是否置顶
     * @return 影响行数
     */
    @Update("UPDATE chat_room SET is_top = #{isTop}, update_time = NOW() " +
            "WHERE id = #{chatRoomId} AND deleted = 0")
    int updateTopStatus(@Param("chatRoomId") Long chatRoomId, @Param("isTop") Integer isTop);

    /**
     * 查询用户置顶的聊天室
     *
     * @param userId 用户ID
     * @return 置顶聊天室列表
     */
    @Select("SELECT * FROM chat_room WHERE user_id = #{userId} AND is_top = 1 AND deleted = 0 " +
            "ORDER BY last_message_time DESC")
    List<ChatRoom> selectTopRoomsByUserId(@Param("userId") Integer userId);

    /**
     * 统计用户的聊天室数量
     *
     * @param userId 用户ID
     * @param roomType 聊天室类型 (可选)
     * @return 聊天室数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM chat_room WHERE user_id = #{userId} AND deleted = 0 " +
            "<if test='roomType != null and roomType != \"\"'>" +
            "AND room_type = #{roomType}" +
            "</if>" +
            "</script>")
    Long countByUserId(@Param("userId") Integer userId, @Param("roomType") String roomType);

    /**
     * 查询用户最近活跃的聊天室
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 聊天室列表
     */
    @Select("SELECT * FROM chat_room WHERE user_id = #{userId} AND last_message_time IS NOT NULL " +
            "AND deleted = 0 ORDER BY last_message_time DESC LIMIT #{limit}")
    List<ChatRoom> selectRecentActiveRooms(@Param("userId") Integer userId, @Param("limit") Integer limit);

    /**
     * 查询不活跃的聊天室
     *
     * @param cutoffTime 截止时间
     * @param limit 限制数量
     * @return 聊天室列表
     */
    @Select("SELECT * FROM chat_room WHERE " +
            "(last_message_time < #{cutoffTime} OR (last_message_time IS NULL AND create_time < #{cutoffTime})) " +
            "AND deleted = 0 LIMIT #{limit}")
    List<ChatRoom> selectInactiveRooms(@Param("cutoffTime") LocalDateTime cutoffTime, @Param("limit") Integer limit);

    /**
     * 根据模型名称查询聊天室
     *
     * @param modelName 模型名称
     * @param limit 限制数量
     * @return 聊天室列表
     */
    @Select("SELECT * FROM chat_room WHERE model_name = #{modelName} AND deleted = 0 " +
            "ORDER BY last_message_time DESC LIMIT #{limit}")
    List<ChatRoom> selectByModelName(@Param("modelName") String modelName, @Param("limit") Integer limit);
}
