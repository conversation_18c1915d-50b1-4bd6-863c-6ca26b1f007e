package com.hncboy.chatgpt;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * <AUTHOR>
 * @date 2023/3/22 16:50
 * ChatGptApplication
 */
@MapperScan(value = {"com.hncboy.**.mapper"})
@SpringBootApplication
@EnableScheduling
public class ChatGptApplication {

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(ChatGptApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("server.servlet.context-path");
        String path = property == null ? "" : property;
        System.out.println(
                "\n\t" +
                        "----------------------------------------------------------\n\t" +
                        "超级智能社启动成功! Access URLs:\n\t" +
                        "后台地址: \thttp://" + ip + ":" + port + path + "/\n\t" +
                        "文档地址: \thttp://" + ip + ":" + port + path + "/swagger-ui/index.html#/ \n\t" +
                        "------------------------------------------------------------");
    }
}
