package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付状态枚举
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Getter
@AllArgsConstructor
public enum PayStatusEnum {

    /**
     * 待支付
     */
    PENDING("PENDING", "待支付", 0),

    /**
     * 支付中
     */
    PROCESSING("PROCESSING", "支付中", 1),

    /**
     * 支付成功
     */
    PAID("PAID", "支付成功", 2),

    /**
     * 支付失败
     */
    FAILED("FAILED", "支付失败", 3),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消", 4),

    /**
     * 已退款
     */
    REFUNDED("REFUNDED", "已退款", 5),

    /**
     * 部分退款
     */
    PARTIAL_REFUNDED("PARTIAL_REFUNDED", "部分退款", 6),

    /**
     * 已过期
     */
    EXPIRED("EXPIRED", "已过期", 7);

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 状态值
     */
    private final Integer value;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static PayStatusEnum getByCode(String code) {
        for (PayStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static PayStatusEnum getByValue(Integer value) {
        for (PayStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查是否为终态状态
     *
     * @return 是否为终态
     */
    public boolean isFinalStatus() {
        return this == PAID || this == FAILED || this == CANCELLED || 
               this == REFUNDED || this == EXPIRED;
    }

    /**
     * 检查是否为成功状态
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return this == PAID;
    }

    /**
     * 检查是否可以取消
     *
     * @return 是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == PROCESSING;
    }

    /**
     * 检查是否可以退款
     *
     * @return 是否可以退款
     */
    public boolean canRefund() {
        return this == PAID || this == PARTIAL_REFUNDED;
    }
}
