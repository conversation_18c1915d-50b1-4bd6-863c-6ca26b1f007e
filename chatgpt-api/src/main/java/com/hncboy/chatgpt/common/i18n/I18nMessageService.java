package com.hncboy.chatgpt.common.i18n;

import com.hncboy.chatgpt.api.domain.entity.I18nMessage;
import com.hncboy.chatgpt.api.mapper.I18nMessageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 国际化消息服务
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Service
@Slf4j
public class I18nMessageService {
    
    @Autowired
    private I18nMessageMapper i18nMessageMapper;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    /**
     * 获取翻译消息 - 支持缓存
     */
    @Cacheable(value = "i18n_messages", key = "#messageKey + '_' + #locale")
    public String getMessage(String messageKey, String locale) {
        // 1. 先查询指定语言的翻译
        I18nMessage message = i18nMessageMapper.selectByKeyAndLocale(messageKey, locale);
        
        if (message != null) {
            return message.getMessageValue();
        }
        
        // 2. 如果没有找到，尝试查询默认语言(中文)
        if (!"zh_CN".equals(locale)) {
            message = i18nMessageMapper.selectByKeyAndLocale(messageKey, "zh_CN");
            if (message != null) {
                return message.getMessageValue();
            }
        }
        
        // 3. 都没有找到，返回null
        return null;
    }
    
    /**
     * 批量获取翻译消息
     */
    public Map<String, String> getMessages(List<String> messageKeys, String locale) {
        // 1. 先从缓存批量获取
        List<String> cacheKeys = messageKeys.stream()
            .map(key -> "i18n_messages::" + key + "_" + locale)
            .collect(Collectors.toList());
        
        List<String> cachedValues = redisTemplate.opsForValue().multiGet(cacheKeys);
        
        Map<String, String> result = new HashMap<>();
        List<String> missedKeys = new ArrayList<>();
        
        for (int i = 0; i < messageKeys.size(); i++) {
            String key = messageKeys.get(i);
            String cachedValue = cachedValues != null && i < cachedValues.size() ? cachedValues.get(i) : null;
            
            if (cachedValue != null) {
                result.put(key, cachedValue);
            } else {
                missedKeys.add(key);
            }
        }
        
        // 2. 从数据库查询缓存未命中的
        if (!missedKeys.isEmpty()) {
            List<I18nMessage> messages = i18nMessageMapper.selectByKeysAndLocale(missedKeys, locale);
            
            for (I18nMessage message : messages) {
                result.put(message.getMessageKey(), message.getMessageValue());
                
                // 更新缓存
                String cacheKey = "i18n_messages::" + message.getMessageKey() + "_" + locale;
                redisTemplate.opsForValue().set(cacheKey, message.getMessageValue(), 1, TimeUnit.HOURS);
            }
        }
        
        return result;
    }
    
    /**
     * 刷新缓存
     */
    @CacheEvict(value = "i18n_messages", allEntries = true)
    public void refreshCache() {
        log.info("刷新国际化消息缓存");
    }
    
    /**
     * 预热缓存
     */
    @PostConstruct
    public void warmUpCache() {
        try {
            List<String> locales = Arrays.asList("zh_CN", "en_US", "vi_VN");
            
            for (String locale : locales) {
                List<I18nMessage> messages = i18nMessageMapper.selectByLocale(locale);
                
                for (I18nMessage message : messages) {
                    String cacheKey = "i18n_messages::" + message.getMessageKey() + "_" + locale;
                    redisTemplate.opsForValue().set(cacheKey, message.getMessageValue(), 2, TimeUnit.HOURS);
                }
                
                log.info("预热国际化缓存完成: locale={}, count={}", locale, messages.size());
            }
        } catch (Exception e) {
            log.error("预热国际化缓存失败", e);
        }
    }
}
