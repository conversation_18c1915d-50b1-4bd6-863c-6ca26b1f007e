package com.hncboy.chatgpt.common.i18n;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.hncboy.chatgpt.api.service.I18nMessageService;
import com.hncboy.chatgpt.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 国际化翻译处理器 - 参考RuoYi-Vue-Plus TranslationHandler
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Slf4j
public class I18nTranslationHandler extends JsonSerializer<Object> implements ContextualSerializer {
    
    /**
     * 全局翻译实现类映射器
     */
    public static final Map<String, I18nTranslationInterface> TRANSLATION_MAPPER = new ConcurrentHashMap<>();
    
    private I18nTranslation i18nTranslation;
    
    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        try {
            // 1. 获取当前用户语言环境
            String locale = getCurrentLocale();
            
            // 2. 确定翻译键
            String messageKey;
            if (i18nTranslation.byValue() || StringUtils.isBlank(i18nTranslation.key())) {
                // 按值翻译：直接用字段值作为翻译键
                messageKey = String.valueOf(value);
            } else {
                // 按键翻译：使用配置的key
                messageKey = i18nTranslation.key();
            }
            
            // 3. 构建参数Map
            Map<String, Object> params = buildParams(gen.getCurrentValue(), i18nTranslation.paramFields());
            
            // 4. 根据翻译类型选择处理策略
            String translatedMessage = translateByType(messageKey, locale, params, i18nTranslation.type());
            
            // 5. 智能回退：有翻译就翻译，没有就返回原文
            if (StringUtils.isBlank(translatedMessage)) {
                if (StringUtils.isNotBlank(i18nTranslation.defaultValue())) {
                    translatedMessage = i18nTranslation.defaultValue();
                } else {
                    // 没有翻译就返回原文
                    translatedMessage = String.valueOf(value);
                }
            }
            
            // 6. 输出翻译结果
            gen.writeString(translatedMessage);
            
        } catch (Exception e) {
            log.error("国际化翻译失败: value={}, byValue={}", value, i18nTranslation.byValue(), e);
            // 异常时返回原文
            gen.writeString(String.valueOf(value));
        }
    }
    
    /**
     * 根据翻译类型进行翻译
     */
    private String translateByType(String messageKey, String locale, Map<String, Object> params, TranslationType type) {
        switch (type) {
            case MESSAGE:
                return translateMessage(messageKey, locale, params);
            case DICT:
                return translateDict(messageKey, locale, params);
            case USER_DEFINED:
                return translateUserDefined(messageKey, locale, params);
            default:
                return translateMessage(messageKey, locale, params);
        }
    }
    
    /**
     * 普通消息翻译，支持{{param}}格式的参数替换
     */
    private String translateMessage(String messageKey, String locale, Map<String, Object> params) {
        try {
            // 1. 从缓存或数据库获取翻译模板
            I18nMessageService i18nMessageService = SpringUtil.getBean(I18nMessageService.class);
            String template = i18nMessageService.getMessage(messageKey, locale);
            
            if (StringUtils.isBlank(template)) {
                return null;
            }
            
            // 2. 替换参数 {{count}} -> 实际值
            String result = template;
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String placeholder = "{{" + entry.getKey() + "}}";
                String value = formatParamValue(entry.getValue(), locale);
                result = result.replace(placeholder, value);
            }
            
            return result;
        } catch (Exception e) {
            log.error("翻译消息失败: key={}, locale={}", messageKey, locale, e);
            return null;
        }
    }
    
    /**
     * 字典翻译 (用于状态码、类型码等)
     */
    private String translateDict(String dictType, String locale, Map<String, Object> params) {
        // 从字典表获取翻译
        String dictValue = params.get("value") != null ? params.get("value").toString() : "";
        // TODO: 实现字典翻译逻辑
        return null;
    }
    
    /**
     * 用户自定义翻译
     */
    private String translateUserDefined(String key, String locale, Map<String, Object> params) {
        I18nTranslationInterface translator = TRANSLATION_MAPPER.get(key);
        if (translator != null) {
            return translator.translate(key, locale, params);
        }
        return null;
    }
    
    /**
     * 构建参数Map
     */
    private Map<String, Object> buildParams(Object currentObject, String[] paramFields) {
        Map<String, Object> params = new HashMap<>();
        
        if (currentObject == null || paramFields.length == 0) {
            return params;
        }
        
        for (String field : paramFields) {
            try {
                Object value = getFieldValue(currentObject, field);
                params.put(field, value);
            } catch (Exception e) {
                log.warn("获取参数字段失败: field={}", field, e);
                params.put(field, "");
            }
        }
        
        return params;
    }
    
    /**
     * 获取字段值 (支持嵌套字段)
     */
    private Object getFieldValue(Object obj, String fieldPath) throws Exception {
        if (obj == null) {
            return null;
        }
        
        String[] fields = fieldPath.split("\\.");
        Object current = obj;
        
        for (String field : fields) {
            if (current == null) {
                return null;
            }
            
            // 使用反射获取字段值
            Field declaredField = current.getClass().getDeclaredField(field);
            declaredField.setAccessible(true);
            current = declaredField.get(current);
        }
        
        return current;
    }
    
    /**
     * 格式化参数值 (根据语言环境格式化数字、日期等)
     */
    private String formatParamValue(Object value, String locale) {
        if (value == null) {
            return "";
        }
        
        // 数字格式化
        if (value instanceof Number) {
            // TODO: 根据locale格式化数字
            return String.valueOf(value);
        }
        
        // 日期格式化
        if (value instanceof Date || value instanceof LocalDateTime) {
            // TODO: 根据locale格式化日期
            return String.valueOf(value);
        }
        
        return String.valueOf(value);
    }
    
    /**
     * 获取当前用户语言环境
     */
    private String getCurrentLocale() {
        try {
            // 1. 从请求头获取
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String acceptLanguage = request.getHeader("Accept-Language");
                if (StringUtils.isNotBlank(acceptLanguage)) {
                    return parseLocale(acceptLanguage);
                }
            }
            
            // 2. 从用户配置获取
            // TODO: 从当前用户获取语言偏好
            
            // 3. 默认中文
            return "zh_CN";
        } catch (Exception e) {
            log.warn("获取当前语言环境失败", e);
            return "zh_CN";
        }
    }
    
    /**
     * 解析Accept-Language头
     */
    private String parseLocale(String acceptLanguage) {
        if (acceptLanguage.contains("en")) {
            return "en_US";
        } else if (acceptLanguage.contains("vi")) {
            return "vi_VN";
        } else {
            return "zh_CN";
        }
    }
    
    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        I18nTranslation translation = property.getAnnotation(I18nTranslation.class);
        if (Objects.nonNull(translation)) {
            this.i18nTranslation = translation;
            return this;
        }
        return prov.findValueSerializer(property.getType(), property);
    }
}
