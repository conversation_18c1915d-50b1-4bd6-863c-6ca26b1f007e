package com.hncboy.chatgpt.common.i18n;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.*;

/**
 * 国际化翻译注解 - 参考RuoYi-Vue-Plus Translation注解
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Inherited
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
@Documented
@JacksonAnnotationsInside
@JsonSerialize(using = I18nTranslationHandler.class)
public @interface I18nTranslation {
    
    /**
     * 翻译键 (对应i18n_message表的message_key)
     * 如果为空，则使用字段值作为翻译键
     */
    String key() default "";
    
    /**
     * 是否按值翻译 (直接用字段值查找翻译)
     */
    boolean byValue() default false;
    
    /**
     * 参数映射字段 (支持{{count}}等动态参数)
     * 例如: {"count", "days", "amount"}
     */
    String[] paramFields() default {};
    
    /**
     * 默认值 (当翻译不存在时使用)
     */
    String defaultValue() default "";
    
    /**
     * 是否缓存翻译结果
     */
    boolean cached() default true;
    
    /**
     * 翻译类型 (用于扩展不同的翻译策略)
     */
    TranslationType type() default TranslationType.MESSAGE;
}

/**
 * 翻译类型枚举
 */
enum TranslationType {
    MESSAGE,        // 普通消息翻译
    DICT,          // 字典翻译
    USER_DEFINED   // 用户自定义翻译
}
