package com.hncboy.chatgpt.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付渠道枚举
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Getter
@AllArgsConstructor
public enum PayChannelEnum {

    /**
     * 支付宝
     */
    ALIPAY("ALIPAY", "支付宝", "alipay"),

    /**
     * 微信支付
     */
    WECHAT("WECHAT", "微信支付", "wechat"),

    /**
     * SE支付 (越南)
     */
    SEPAY("SEPAY", "SE支付", "sepay"),

    /**
     * Momo支付 (越南)
     */
    MOMO("MOMO", "Momo支付", "momo"),

    /**
     * 银行转账
     */
    BANK_TRANSFER("BANK_TRANSFER", "银行转账", "bank"),

    /**
     * 数字钱包
     */
    DIGITAL_WALLET("DIGITAL_WALLET", "数字钱包", "wallet");

    /**
     * 渠道代码
     */
    private final String code;

    /**
     * 渠道名称
     */
    private final String name;

    /**
     * 渠道标识
     */
    private final String identifier;

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举
     */
    public static PayChannelEnum getByCode(String code) {
        for (PayChannelEnum channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }

    /**
     * 根据标识获取枚举
     *
     * @param identifier 标识
     * @return 枚举
     */
    public static PayChannelEnum getByIdentifier(String identifier) {
        for (PayChannelEnum channel : values()) {
            if (channel.getIdentifier().equals(identifier)) {
                return channel;
            }
        }
        return null;
    }

    /**
     * 检查是否为有效的支付渠道
     *
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
}
