package com.hncboy.chatgpt.tarot.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.tarot.domain.converter.TarotCardMeaningConvert;
import com.hncboy.chatgpt.tarot.domain.converter.TarotSpreadConvert;
import com.hncboy.chatgpt.tarot.domain.dto.TarotSpreadDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotSpreadVO;
import com.hncboy.chatgpt.tarot.mapper.TarotSpreadMapper;
import com.hncboy.chatgpt.tarot.service.TarotCardMeaningService;
import com.hncboy.chatgpt.tarot.service.TarotSpreadService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * 塔罗牌牌阵Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Service
@RequiredArgsConstructor
public class TarotSpreadServiceImpl extends ServiceImpl<TarotSpreadMapper, TarotSpread> implements TarotSpreadService {

    private final TarotCardMeaningService tarotCardMeaningService;

    private final UserBaseInfoService userBaseInfoService;

    /**
     * 查询塔罗牌牌阵
     *
     * @param id 塔罗牌牌阵主键
     * @return 塔罗牌牌阵
     */
    @Override
    public TarotSpread selectTarotSpreadById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询塔罗牌牌阵列表
     *
     * @param dto 塔罗牌牌阵
     * @return 塔罗牌牌阵
     */
    @Override
    public IPage<TarotSpreadVO> selectTarotSpreadList(TarotSpreadDTO dto) {
        //查询用户信息 提起用户创建信息作为条件
        UserBaseInfo info = userBaseInfoService.getUserBaseInfoByUserId(CurrentUserUtil.getV2UserId());
        Date createTime= info.getCreateTime();

        LambdaQueryWrapper<TarotSpread> wrapper = new LambdaQueryWrapper<>();
        //wrapper.eq(TarotCardMeaning::getUserId, CurrentUserUtil.getV2UserId());
        //.eq(TarotCardMeaning::getIsDelete,0);
        wrapper.like(StringUtils.isNotEmpty(dto.getName()), TarotSpread::getName, dto.getName());
        wrapper.eq(ObjectUtil.isNotEmpty(dto.getId()), TarotSpread::getId, dto.getId());
        wrapper.eq(TarotSpread::getStatus, 0);
        //wrapper.eq(StringUtils.isNotEmpty(dto.getTag()), TarotSpread::getTag, dto.getTag());;
        wrapper.orderByAsc(TarotSpread::getSort);

        //用户创建时间在其范围
        wrapper.le(TarotSpread::getUserRegTimeS, createTime);
        wrapper.ge(TarotSpread::getUserRegTimeE, createTime);

        IPage<TarotSpread> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<TarotSpread> iPage = this.page(userPage, wrapper);

        // 这个带分页，这么写可能有问题  只能处理一页的数据
        List<TarotSpread> records = iPage.getRecords();
        //防止时间段重合的相同产品名重复显示
        LinkedHashMap <String,TarotSpread> map=new LinkedHashMap <>();
        for (TarotSpread tarotSpread : records) {
            if(!map.containsKey(tarotSpread.getName())){
                map.put(tarotSpread.getName(),tarotSpread);
            }else{
                TarotSpread mapSpreat = map.get(tarotSpread.getName());
                //比较时间
                if(mapSpreat.getCreateTime().compareTo(tarotSpread.getCreateTime())<0){
                    map.put(tarotSpread.getName(),tarotSpread);
                }
            }
        }
        List<TarotSpread> newRecords=new ArrayList<>();
        map.forEach((k,v)->{
            newRecords.add(v);
        });
        iPage.setRecords(newRecords);

        IPage<TarotSpreadVO> convert = iPage.convert(TarotSpreadConvert.INSTANCE::entityToVO);
        return convert;
    }

    @Override
    public List<TarotCardMeaningVO> randomTarot() {
        List<TarotCardMeaningVO> tarotCardMeaningVOS = null;
        for (int i = 0; i < 2; i++) {
            tarotCardMeaningVOS = tarotCardMeaningService.list()
                    .stream()
                    .map(TarotCardMeaningConvert.INSTANCE::entityToVO)
                    .collect(Collectors.toList());
            boolean nameUnique = tarotCardMeaningVOS.stream()
                    .map(TarotCardMeaningVO::getName)
                    .distinct()
                    .count() == tarotCardMeaningVOS.size();
            boolean urlUnique = tarotCardMeaningVOS.stream()
                    .map(TarotCardMeaningVO::getCardFrontUrl)
                    .distinct()
                    .count() == tarotCardMeaningVOS.size();
            if (nameUnique && urlUnique) {
                break;
            }
            if (i == 1) {
                log.error("重复查询两次最终还是有重复");
                throw new ServiceException("失败，可联系客服");
            }
        }
        shuffle(tarotCardMeaningVOS);
        return tarotCardMeaningVOS;
    }
    /**
     * 新增塔罗牌牌阵
     *
     * @param tarotSpread 塔罗牌牌阵
     * @return 结果
     */
    @Override
    public int insertTarotSpread(TarotSpread tarotSpread) {
            return baseMapper.insert(tarotSpread);
    }

    /**
     * 修改塔罗牌牌阵
     *
     * @param tarotSpread 塔罗牌牌阵
     * @return 结果
     */
    @Override
    public int updateTarotSpread(TarotSpread tarotSpread) {
        return baseMapper.updateById(tarotSpread);
    }


    /**
     * 删除塔罗牌牌阵信息
     *
     * @param id 塔罗牌牌阵主键
     * @return 结果
     */
    @Override
    public int deleteTarotSpreadById(Long id) {
       
        return baseMapper.deleteById(id);
    }

    public static void shuffle(List<TarotCardMeaningVO> array) {
        Random rand = new Random();  // 随机数生成器

        // 从数组的最后一个元素开始，逐个与之前的元素交换
        for (int i = array.size()-1 ; i > 0; i--) {
            // 生成一个 0 到 i 之间的随机数
            int j = rand.nextInt(i + 1);

            // 交换 array[i] 和 array[j]
            TarotCardMeaningVO temp = array.get(i);
            array.set(i, array.get(j));
            array.set(j, temp);
        }

        // 随机设置每张牌的正位或逆位
        for (TarotCardMeaningVO card : array) {
            if(rand.nextBoolean()){
                //设置正位
                card.setPosition("upright");
            }else{
                //设置逆位
                card.setPosition("reversed");
            }

        }
    }

}
