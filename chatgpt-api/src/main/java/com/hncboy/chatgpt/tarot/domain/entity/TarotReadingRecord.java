package com.hncboy.chatgpt.tarot.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 塔罗牌占卜记录对象 tarot_reading_record
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@TableName("tarot_reading_record")
@Data
public class TarotReadingRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /** 牌阵id */
    private String spreadId;

    /** 问题 */
    private String question;

    /** 答案 */
    private String answer;

    /** 抽牌结果 */
    private String drawResult;

    /** 消耗 */
    private Integer consume;
    /**
     * 状态
     */
    private String status;

    private String userId;

    private String conversationId;
    /**
     * 解读模式 0:抽牌模式1:自选牌模式
     */
    private String interpretationMode;
    /**
     * 异常信息
     */
    private String errorMsg;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    private Date updateTime;

    /** 新增：是否删除*/
    private byte deleted;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("spreadId", getSpreadId())
            .append("question", getQuestion())
            .append("answer", getAnswer())
            .append("drawResult", getDrawResult())
            .append("consume", getConsume())
            .append("interpretationMode", getInterpretationMode())
            .append("errorMsg", getErrorMsg())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("deleted", getDeleted())
                .toString();
    }
}