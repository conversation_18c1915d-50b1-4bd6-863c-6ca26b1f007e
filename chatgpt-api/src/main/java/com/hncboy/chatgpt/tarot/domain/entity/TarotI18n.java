package com.hncboy.chatgpt.tarot.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 塔罗国际化 tarot_i18n
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@TableName("tarot_i18n")
@Data
@EqualsAndHashCode(callSuper = false)
public class TarotI18n implements Serializable {
    private static final long serialVersionUID = 1L;

        /** 主键 */
        @TableId(value = "id",type = IdType.AUTO)
        private Integer id;

        /** 编码，同一段文字的不同语言翻译的code必须相同，code可以为中文 */
        private String code;

        /*翻译值*/
        private String lang;

        /** 含义 */
        private String value;

        /** 创建时间 */
        private Date createTime;

        /** 更新时间 */
        private Date updateTime;
}