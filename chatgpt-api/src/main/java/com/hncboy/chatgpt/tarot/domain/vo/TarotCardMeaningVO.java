package com.hncboy.chatgpt.tarot.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hncboy.chatgpt.tarot.i18n.I18nStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * 塔罗牌牌义对象 tarot_card_meaning
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Data
public class TarotCardMeaningVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String name;

    /**
     * 含义
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String meaning;

    /**
     * 分类
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String tag;

    /**
     * 指引语
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String guidanceText;

    /**
     * 建议
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String advice;

    /**
     * 不建议
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String discouraged;

    /**
     * 正面图
     */
    private String cardFrontUrl;
    /**
     * 正逆位
     */
    private String position;
}
