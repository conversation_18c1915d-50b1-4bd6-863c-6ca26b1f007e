package com.hncboy.chatgpt.tarot.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.entity.WxUserInfo;
import com.hncboy.chatgpt.front.framework.domain.query.LoginInfoParam;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.enums.WxAppIdEnum;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * zcWu
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Tag(name = "登录相关接口")
@RestController
@RequestMapping("/login")
@Slf4j
public class Login2Controller {

    private final WxMpService wxService;
    private final WxUserInfoService wxUserInfoService;
    private final UserBaseInfoService userBaseInfoService;


    @Operation(summary = "塔罗微信登录")
    @GetMapping("/taroLogin")
    public R<UserBaseInfoVO> greetUser(@RequestParam String code, @RequestParam String parentId) {
        try {
            wxService.switchover(WxAppIdEnum.zns.getCode());
            WxOAuth2AccessToken accessToken = wxService.getOAuth2Service().getAccessToken(code);
            WxOAuth2UserInfo user = wxService.getOAuth2Service().getUserInfo(accessToken, null);
            UserBaseInfoVO userBaseInfoByOpenId = null;
            if (Objects.nonNull(user)) {
                //查询用户是否存在
                userBaseInfoByOpenId= userBaseInfoService.getUserBaseInfoByOpenId(user.getOpenid());
                if (userBaseInfoByOpenId == null) {
                    //查询保存微信用户
                    WxUserInfoVO wxUserInfoVO = wxUserInfoService.saveWxCodeUser(user,WxAppIdEnum.zns.getCode());
                    LoginInfoParam loginInfoParam = new LoginInfoParam();
                    loginInfoParam.setOpenId(wxUserInfoVO.getOpenId());
                    loginInfoParam.setNickName(wxUserInfoVO.getNickName());
                    loginInfoParam.setHeadImgUrl(wxUserInfoVO.getAvatarUrl());
                    if (StringUtils.isNotEmpty(parentId)) {
                        loginInfoParam.setParentId(parentId);
                    }
                    String uuId = RandomUtil.randomString(8);
                    loginInfoParam.setAccount("wx_"+uuId);
                    userBaseInfoByOpenId = userBaseInfoService.initUserInfo2(loginInfoParam);
                    if (ObjectUtil.isNotEmpty(userBaseInfoByOpenId.getParentId())){
                        if(userBaseInfoService.isOnlyOneUserBaseInfoRecord(user.getOpenid())) {
                            userBaseInfoService.addUserPoints(userBaseInfoByOpenId.getParentId(), loginInfoParam.getAccount(), "tarot_invite_points");
                        }
                    }
                }else{
                    WxUserInfo wxUserInfo = new WxUserInfo();
                    wxUserInfo.setNickName(user.getNickname());
                    wxUserInfo.setOpenId(user.getOpenid());
                    wxUserInfo.setUnionId(user.getUnionId());
                    wxUserInfoService.updateById(wxUserInfo);
                }
                StpUtil.login(userBaseInfoByOpenId.getId());
                //会话相关
                StpUtil.getSession().set(ApplicationConstant.USER, userBaseInfoByOpenId);
                userBaseInfoByOpenId.setToken(StpUtil.getTokenValue());
                return R.data(userBaseInfoByOpenId);
            }
        } catch (WxErrorException e) {
            //e.printStackTrace();
            log.error("登录失败", e);
        }
        return R.fail("用户获取失败");
    }


}
