package com.hncboy.chatgpt.tarot.helper;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;
import com.hncboy.chatgpt.front.framework.domain.entity.SiteInfo;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessV2Request;
import com.hncboy.chatgpt.front.framework.domain.request.DifyProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.vo.*;
import com.hncboy.chatgpt.front.framework.exception.BalanceException;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.handler.response.ResultCode;
import com.hncboy.chatgpt.front.framework.util.*;
import com.hncboy.chatgpt.front.handler.config.SensitiveWordHandler;
import com.hncboy.chatgpt.front.helper.ChannelConfigHelper;
import com.hncboy.chatgpt.front.helper.RedisLockHelper;
import com.hncboy.chatgpt.front.helper.SensitiveWordEmitterChain;
import com.hncboy.chatgpt.front.mapper.SysConfigMapper;
import com.hncboy.chatgpt.front.service.*;
import com.hncboy.chatgpt.front.util.JsonRepair;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import com.hncboy.chatgpt.tarot.mapper.TableConfigMapper;
import com.hncboy.chatgpt.tarot.service.TarotReadingRecordService;
import com.hncboy.chatgpt.tarot.service.TarotSpreadService;
import com.unfbx.chatgpt.entity.chat.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.UnknownHostException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.appError;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ChatMsgV3BuildHelper {

    private final UserBaseInfoService userBaseInfoService;
    private final TarotSpreadService tarotSpreadService;
    private final TarotReadingRecordService tarotReadingRecordService;
    private final ChannelConfigHelper channelConfigHelper;
    private final DifyMsgBuildHelper2 difyMsgBuildHelper;
    private final ExceptionLogService exceptionLogService;
    private final ISysDictDataService sysDictDataService;
    private ReentrantLock[] locks;
    private final RedisLockHelper lockHelper;
    private final RedisTemplate<String, String> redisTemplate;
    private final TableConfigMapper tableConfigMapper;
    private final SysConfigService sysConfigService;
    private final StringRedisTemplate stringRedisTemplate;
    private static final String TIME_SET_KEY = "tarot:time:set";
    @Value("${spring.profiles.active}")
    private String activeProfile;
    {
        locks = new ReentrantLock[100];
        for (int i = 0; i < 100; i++) {
            locks[i] = new ReentrantLock();
        }
    }

    @NotNull
    @Transactional(rollbackFor = Exception.class)
    public R buildCompletionMessageBody(ChatProcessV2Request chatProcessRequest) {
        long totalStart = System.currentTimeMillis();
        // 初始化 ResponseBodyEmitter
        if (!StpUtil.isLogin()) {
            return R.data( ResultCode.UN_AUTHORIZED.getCode(),null,"您还未登录，请登录后操作");
        }
        // 获取用户信息
        UserBaseInfoVO userBaseInfoVO = userBaseInfoService.queryUserInfoById(CurrentUserUtil.getV2UserId());
        //生成key
        String CACHE_KEY = "TAROT_READING_" + CurrentUserUtil.getV2UserId();
        // 检查缓存是否存在
        Boolean exists = redisTemplate.hasKey(CACHE_KEY);
        if (exists != null && exists) {
            return R.data(ResultCode.THE_REQUEST_IS_DUPLICATED.getCode(),null, "您上次的问题还在解读中，请稍后在提问历程里查看结果");
        }
        //存入缓存，防止重复提交占卜请求
        redisTemplate.opsForValue().set(CACHE_KEY,CurrentUserUtil.getV2UserId().toString(), 100, TimeUnit.SECONDS);

        try {
            List<Message> msgList = chatProcessRequest.getMessageList();
            if (CollUtil.isNotEmpty(msgList)) {
                chatProcessRequest.setPrompt(msgList.get(msgList.size() - 1).getContent());
            }
            // 检查敏感词
            if (!SensitiveWordEmitterChain.doChain(chatProcessRequest)) {
                List<String> prompts = SensitiveWordHandler.checkWord(chatProcessRequest.getPrompt());
                //return ChatReplyMessageVO.buildFailureMessage("您的输入包含敏感词【" + CollUtil.join(prompts, ",") + "】，请重新输入", ResultCode.FAILURE.getCode());
                return R.data(ResultCode.FAILURE.getCode(),null,"您的输入包含敏感词【" + CollUtil.join(prompts, ",") + "】，请重新输入");
            }

            //获取牌阵信息
            TarotSpread tarotSpread = tarotSpreadService.getById(chatProcessRequest.getIntelligentAgentId());

            SiteInfo siteInfo = channelConfigHelper.queryChannelInfoByGid(tarotSpread.getGid());
            if(siteInfo.getApiKey()==null){
                siteInfo.setApiKey(tarotSpread.getModelKey());
            }
            DifyProcessRequest baseDifyChatCompletion=null ;
            try {
                baseDifyChatCompletion=difyMsgBuildHelper.buildDifyChatCompletion(chatProcessRequest);
            }catch (Exception e){
                log.error("DifyChatCompletion 配置失败:{}", e.getMessage());
                return R.data(ResultCode.FAILURE.getCode(),null,appError);
            }

            //保存占卜记录
            TarotReadingRecord tarotReadingRecord = new TarotReadingRecord();
            tarotReadingRecord.setUserId(CurrentUserUtil.getV2UserId().toString());
            tarotReadingRecord.setQuestion(baseDifyChatCompletion.getQuery());
            tarotReadingRecord.setSpreadId(tarotSpread.getId().toString());
            //JSONUtil.toJsonStr()
            tarotReadingRecord.setDrawResult( JSONUtil.toJsonStr(baseDifyChatCompletion.getInputs()));
            tarotReadingRecord.setConsume(tarotSpread.getConsume());
            tarotReadingRecord.setStatus("0");
            tarotReadingRecord.setInterpretationMode(chatProcessRequest.getModel());
            tarotReadingRecord.setCreateBy(CurrentUserUtil.getV2UserId().toString());
            tarotReadingRecord.setUpdateBy(CurrentUserUtil.getV2UserId().toString());
            ChatMessageDO chatMessageDO = new ChatMessageDO();
            // 消息
            chatMessageDO.setMessageType(9);

            chatMessageDO.setModelGid(tarotSpread.getGid());
            chatMessageDO.setAgentId(tarotSpread.getId());
            chatMessageDO.setAgentName(tarotSpread.getName());
            chatMessageDO.setSiteUrl(siteInfo.getUrl());
            chatMessageDO.setSiteName(siteInfo.getName());
            // 配置聊天处理请求
            try {
                configureChatProcessRequest(userBaseInfoVO, tarotSpread);
            } catch (ServiceException e) {
                return R.data(  ResultCode.INTERNAL_SERVER_ERROR.getCode(),null,e.getMessage());
            } catch (BalanceException e) {
                return R.data(  ResultCode.BALANCE.getCode(),null,e.getMessage());
            }catch (Exception e) {
                return R.data(  ResultCode.FAILURE.getCode(),null,e.getMessage());
            }

            try {

                tarotReadingRecordService.insertTarotReadingRecord(tarotReadingRecord);
                chatMessageDO.setId(tarotReadingRecord.getId().longValue());
            }catch (Exception e){
                handleErrorResponse(tarotReadingRecord, chatProcessRequest, chatMessageDO, userBaseInfoVO, siteInfo,
                        tarotSpread, "存储信息异常", null, e);
                return R.data(ResultCode.FAILURE.getCode(),null,appError);
            }

            long difyStart;
            long difyEnd;
            String response = "";
            try {
                // 尝试获取塔罗占卜结果
                difyStart = System.currentTimeMillis();
                response = this.buildDifyStreamClient(siteInfo).difyBatchCompletion(baseDifyChatCompletion);
                difyEnd = System.currentTimeMillis();
                log.info("塔罗解读结果：{}", response);
                JSONObject entries = JsonRepair.repairAnswer(response);
                // 检查响应是否包含错误码
                if (entries.containsKey("answer")) {
                    // 处理成功的占卜结果
                    handleSuccessResponse(tarotReadingRecord, entries);
                    sendToDingIfExpire(difyStart, difyEnd, totalStart, tarotReadingRecord, siteInfo);
                    return R.data(ResultCode.SUCCESS.getCode(), entries.getStr("answer"), null);
                } else {
                    // 处理错误情况
                    handleErrorResponse(tarotReadingRecord, chatProcessRequest, chatMessageDO, userBaseInfoVO, siteInfo,
                            tarotSpread, response, entries, null);
                    return R.data(ResultCode.FAILURE.getCode(), null, appError);
                }
            } catch (Exception e) {
                // 处理其他异常
                log.error("塔罗解读请求异常: ", e);
                handleErrorResponse(tarotReadingRecord, chatProcessRequest, chatMessageDO, userBaseInfoVO, siteInfo,
                        tarotSpread, response, null, e);
                return R.data(ResultCode.FAILURE.getCode(), null, appError);
            }
        }finally {
            redisTemplate.delete(CACHE_KEY);
        }

    }
    @Resource
    private  DingTalkHookUtil dingTalkHookUtil;
    public void sendToDingIfExpire(long difyStart,long difyEnd,long totalStart,TarotReadingRecord tarotReadingRecord,SiteInfo siteInfo) {
        TarotSpread spread = tarotSpreadService
                .selectTarotSpreadById(Long.valueOf(tarotReadingRecord.getSpreadId()));
        Integer timeSet = spread.getTimeSet();
        if(timeSet==null){
            log.error("timeSet为null");
            return;
        }
        //查询系统超时时间
        String timeStr = stringRedisTemplate.opsForValue().get(TIME_SET_KEY);
        Integer tarotTimeSet = 0;
        if(timeStr==null){
            timeStr = sysConfigService.querySysConfig("tarot_time_set").getConfigValue();
            if(timeStr==null|| timeStr.isEmpty()){
                log.error("未设置塔罗定时任务时间间隔！！！");
                return;
            }else{
                tarotTimeSet = Integer.parseInt(timeStr);
                stringRedisTemplate.opsForValue().set(TIME_SET_KEY,timeStr,6, TimeUnit.HOURS);
            }
        }else{
            tarotTimeSet = Integer.parseInt(timeStr);
        }
        long totalEnd = System.currentTimeMillis();
        if((difyEnd-difyStart)/1000.0>timeSet||(totalEnd-totalStart-(difyEnd-difyStart))/1000.0>tarotTimeSet){
            String now = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                    .withZone(ZoneId.of("Asia/Shanghai")).format(Instant.ofEpochMilli(totalEnd));
            UserBaseInfo user = userBaseInfoService.lambdaQuery()
                    .eq(UserBaseInfo::getId, tarotReadingRecord.getUserId()).one();

            double di = (difyEnd - difyStart) / 1000.0;
            String dify = String.format("%.3fS", di);
            double tc = (totalEnd - totalStart) / 1000.0;
            String timeCost = String.format("%.3fS",tc);
            String sys = String.format("%.3fS", tc-di);
            String host = "";
            try {
                host = String.valueOf(InetAddress.getLocalHost());
            } catch (UnknownHostException e) {
                log.error("获取本机信息失败", e);
            }
            String message ="=== 塔罗通道用时告警 ===\n" +
                    "time:       " + now + "\n" +
                    "env:        " + activeProfile + "\n" +
                    "host:       " + (host.equals("null") ? "" : host) + "\n" +
                    "userid:     " + tarotReadingRecord.getUserId() + "\n" +
                    "account:  " + (user.getAccount() != null ? user.getAccount() : "") + "\n" +
                    "nickname:   " + (user.getNickName() != null ? user.getNickName() : "") + "\n" +
                    "spreadname: " + (spread.getName() != null ? spread.getName() : "") + "\n" +
                    "timeset:    " + timeSet + "S\n" +"systimeset:    " + tarotTimeSet + "S\n" +
                    "readingid:  " + tarotReadingRecord.getId() + "\n" +
                    "conversationid: " + (tarotReadingRecord.getConversationId() != null ? tarotReadingRecord.getConversationId() : "") + "\n" +
                    "starttime:  " + convert(totalStart) + "\n" +
                    "reqtime:    " + convert(difyStart) + "\n" +
                    "restime:    " + convert(difyEnd) + "\n" +
                    "endtime:   " + convert(totalEnd) + "\n" +
                    "timecost:     " +timeCost+"(dify("+dify+")"+"sys("+sys+"))";
            dingTalkHookUtil.sendDingTalkForTarotException(message);
        }
    }
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss SSS");
    private static String convert(long milliseconds) {
        // 一步完成：转换为带时区时间并直接格式化（包含毫秒）
        return ZonedDateTime.ofInstant(
                Instant.ofEpochMilli(milliseconds),
                ZoneId.systemDefault()
        ).format(DATE_TIME_FORMATTER);
    }

    // 错误处理辅助方法
    private static final int TEXT_LENGTH = 65535;
    // 错误处理辅助方法
    public void handleErrorResponse(TarotReadingRecord tarotReadingRecord, ChatProcessV2Request chatProcessRequest,
                                    ChatMessageDO chatMessageDO, UserBaseInfoVO userBaseInfoVO, SiteInfo siteInfo,
                                    TarotSpread tarotSpread, String response, JSONObject entries, Exception e) {
        String errorMsg = "";
        //防止response超出字段
        String tRREMtype = tableConfigMapper.getTypeByTableAndField("tarot_reading_record", "error_msg");
        Integer tRRMmaxLength = Integer.valueOf(tRREMtype.substring(tRREMtype.indexOf("(") + 1, tRREMtype.indexOf(")")));
        String resp = response;
        if (response != null) {
            if(response.length()>tRRMmaxLength){
                resp = response.substring(0, tRRMmaxLength);
            }
        }else {
            resp = "未知错误";
        }
        tarotReadingRecord.setErrorMsg(resp);
        if (entries != null && entries.containsKey("message")) {
            errorMsg = entries.getStr("message");
        } else {
            errorMsg = e.getMessage();
        }
        //防止errorMsg超出字段
        String errMs = errorMsg;
        if(errorMsg!=null&&errorMsg.length()> TEXT_LENGTH ){//text字段长度
            errMs = errorMsg.substring(0, TEXT_LENGTH);
        }
        tarotReadingRecordService.updateTarotReadingRecord(tarotReadingRecord);
        exceptionLogService.saveTarotExceptionLog(chatProcessRequest, e, chatMessageDO, userBaseInfoVO,
                siteInfo, resp, errMs);
        //退回塔罗币
        userBaseInfoService.addUserTarotNumMp(userBaseInfoVO.getId(), tarotSpread.getConsume());
        //发送异常消息
        dingTalkHookUtil.sendTarotExceptionMessage(chatProcessRequest, tarotSpread, userBaseInfoVO, siteInfo, response, errorMsg);
    }

    // 成功响应处理辅助方法
    private void handleSuccessResponse(TarotReadingRecord tarotReadingRecord, JSONObject entries) {
        tarotReadingRecord.setAnswer(entries.getStr("answer"));
        tarotReadingRecord.setStatus("1");
        tarotReadingRecord.setConversationId(entries.getStr("conversation_id"));
        tarotReadingRecordService.updateTarotReadingRecord(tarotReadingRecord);
    }

    /**
     * 处理塔罗请求
     *
     * @param
     * @param tarotSpread
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/7/3
     */
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void configureChatProcessRequest(
            UserBaseInfoVO userBaseInfoVO,TarotSpread tarotSpread) {

        if (tarotSpread == null) {
            throw new ServiceException("请选择塔罗牌阵");
        }
        //检查应用状态
        checkAgentStatus(Integer.parseInt(tarotSpread.getStatus()));
        Integer Consume = tarotSpread.getConsume();//getConsume(inputs,writeAgentVO);


        //boolean bol=true;
        //获取收费测试名单
        List<String> sysDictData = sysDictDataService.selectDictLabelString("tarot_toll_testing_list");
        int hash = Math.abs(userBaseInfoVO.getId() %  locks.length);
        ReentrantLock lock = locks[hash];
        try {
            if (lock.tryLock(30, TimeUnit.SECONDS)) { // 尝试在 30 秒内获取锁
                try {
                    log.info("收费测试名单:{}", sysDictData);
                    UserBaseInfoVO userBaseInfoVO2 = userBaseInfoService.queryUserInfoById(CurrentUserUtil.getV2UserId());
                    //判断当前用户是否在收费测试名单中
                    if(sysDictData.isEmpty()){
                        log.info("当前测试名单为空,正常进行扣费");
                    }else if(sysDictData.contains(userBaseInfoVO2.getId().toString())){
                        log.info("当前用户位于收费测试名单中,则对当前用户进行扣费");
                        //如果在收费测试名单中，则扣除塔罗币
                    }else{
                        //反之，则不扣除塔罗币
                        log.info("当前用户不在收费测试名单中,不进行扣费");
                        Consume=0;
                    }

                    if (userBaseInfoVO2.getTarotCoins() < Consume) {
                        throw new BalanceException("您的塔罗币不足，请充值");
                    }
                    // 扣除塔罗币
                    userBaseInfoService.updateUserTarotNumMp(userBaseInfoVO2.getId(), Consume);
                } finally {
                    lock.unlock(); // 释放锁
                    log.info("锁已释放");
                }
            } else {
                log.error("获取锁超时");
                throw new RuntimeException("上次占卜还未完成，请稍后重试");

            }

        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }


    }

    //0: 可见也可用
    //1：页面提示：该应用正在优化，现临时停用，优化后重新启用。
    //2: 隐藏应用，可用，但不可见
    //3: 写作应用，不可用，不可见，提示：该对话助手已迁移到写作模块，请前往写作模块里使用
    //9：页面提示：该应用可能已下架。。。。。。。
    //其他: 该应用目前已不可用，可能已下架，如需使用，请联系管理员。
    private void checkAgentStatus(Integer status) {
        if (status != 0 && status != 2) {
            if (status == 1) {
                throw new ServiceException("该应用正在优化，现临时停用，优化后重新启用。");
            }if (status == 3) {
                throw new ServiceException("该对话助手已迁移到写作模块，请前往写作模块里使用");
            } else if (status == 9) {
                throw new ServiceException("该应用可能已下架。");
            } else {
                throw new ServiceException("该应用目前已不可用，可能已下架，如需使用，请联系管理员。");
            }
        }
    }


    /**
     * 获取写作消耗点数
     *
     * @Author: 赵雨晨
     * @Date: 2023/7/16
     * @return
     */
    private Integer getConsume(String inputs, TarotSpread tarotSpread) {
        //获取应用返回json中是否包含wordsCount字段
        //获取wirte_agent表中的consume字段，该字段为字符串类型的Json，若该应用无额外收费要求则Json为{'Default':Integer}
        //该应用采用分段计费Json为{'字数':Integer}
        //强制要求有分段计费的应用中，涉及到计费的字段名为wordsCount，否则报错：请选择正确的字数！
        //没有分段计费要求的应用默认wordsCount的值为Default
        if (!JSONUtil.isTypeJSON(inputs)) {
            throw new ServiceException("输入内容必须是json格式");
        }
        JSONObject jsonObject = JSONUtil.parseObj(inputs);
        String wordsCount = jsonObject.getStr("wordsCount");
        if (StringUtils.isEmpty(wordsCount)) {
            wordsCount = "Default";
        }
        //JSON字符串转为Map<String, Integer>
        ObjectMapper objectMapper = new ObjectMapper();
        Integer consumeJson = tarotSpread.getConsume();
        Map<String, Integer> consumeMap = null;
        try {
            consumeMap = objectMapper.readValue(consumeJson.toString(), new TypeReference<Map<String, Integer>>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        Integer Consume = consumeMap.getOrDefault(wordsCount,consumeMap.getOrDefault("Default",-1));
        if (Consume == -1) {
            throw new BalanceException("请选择正确的字数！");
        }
        return Consume;
    }


    /**
     * 查询渠道配置
     *
     * @param siteInfo
     * @return OpenAiStreamClient
     * @Author: zd.zhong
     * @Date: 2024/7/2
     */
    public DifyBatchClient buildDifyStreamClient(SiteInfo siteInfo) {
        long startTime = System.currentTimeMillis();
        Proxy proxy = Proxy.NO_PROXY;
        if (Objects.nonNull(siteInfo) && StrUtil.isNotEmpty(siteInfo.getHttpProxyHost())) {
            //加入代理
            proxy = getProxy(siteInfo.getHttpProxyHost(), siteInfo.getHttpProxyPort());
        }
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS).proxy(proxy)
                .build();
        log.info("=== current siteName:  {}", siteInfo.getName());
        log.info("=== current hostUrl:   {}", siteInfo.getUrl());
        DifyBatchClient difyStreamClient = DifyBatchClient.builder().okHttpClient(okHttpClient)
                .apiKey(Arrays.asList(siteInfo.getApiKey()))
                .apiHost(siteInfo.getUrl()).build();
        long endTime = System.currentTimeMillis();
        log.info("=== final time [{} - {} = {}]", endTime, startTime, endTime - startTime);
        return difyStreamClient;
    }


    private Proxy getProxy(String host, Integer prot) {
        //ChatConfig chatConfig = SpringUtil.getBean(ChatConfig.class);
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, prot));
        return proxy;
    }

}

