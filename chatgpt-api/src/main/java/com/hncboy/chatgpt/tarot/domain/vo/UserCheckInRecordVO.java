package com.hncboy.chatgpt.tarot.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户签到记录对象 user_check_in_record
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Data
public class UserCheckInRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
        @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /** 用户id */
    private Integer userId;

    /** 签到日期;yyyyMMDD */
    private String checkInDate;

    /** 微信openid */
    private String openId;

    /** 类型;TAROT-塔罗牌签到 */
    private String type;

    /** 是否补签;0-否; 1-是，默认0 */
    private String isMakeUp;

    /** 奖励 */
    private Integer awarded;

    private String week;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("checkInDate", getCheckInDate())
            .append("openId", getOpenId())
            .append("type", getType())
            .append("isMakeUp", getIsMakeUp())
            .append("awarded", getAwarded())
                .toString();
    }
}