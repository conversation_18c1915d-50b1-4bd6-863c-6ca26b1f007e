package com.hncboy.chatgpt.tarot.i18n;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 国际化序列化器
 */
@Slf4j
public class I18nStringSerializer extends JsonSerializer<String> {


    @Override
    public void serialize(String value, JsonGenerator gen, com.fasterxml.jackson.databind.SerializerProvider serializers)
            throws IOException {

        String data = I18nCache.getValue(value);
        gen.writeString(data);
    }


}