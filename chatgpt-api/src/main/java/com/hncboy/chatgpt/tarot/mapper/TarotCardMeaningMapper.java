package com.hncboy.chatgpt.tarot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;

import java.util.List;

/**
 * 塔罗牌牌义Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
public interface TarotCardMeaningMapper extends BaseMapper<TarotCardMeaning> {
    /**
     * 查询塔罗牌牌义列表
     *
     * @param tarotCardMeaning 塔罗牌牌义
     * @return 塔罗牌牌义集合
     */
     List<TarotCardMeaning> selectTarotCardMeaningList(TarotCardMeaning tarotCardMeaning);

     TarotCardMeaningVO  getById(String id);

}