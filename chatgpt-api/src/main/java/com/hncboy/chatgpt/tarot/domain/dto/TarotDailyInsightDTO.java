package com.hncboy.chatgpt.tarot.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 塔罗今日指引对象 tarot_daily_insight
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Data
public class TarotDailyInsightDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 用户id */
    private Integer userId;

    /** 微信openid */
    private String openId;


    private String startDate;
    private String endDate;

}