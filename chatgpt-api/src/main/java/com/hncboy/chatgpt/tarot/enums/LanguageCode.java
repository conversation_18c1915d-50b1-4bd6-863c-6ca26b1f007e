package com.hncboy.chatgpt.tarot.enums;

/**
 * 国际化枚举
 */
public enum LanguageCode {
    CHINESE("zh_CN", "中文"),
    ENGLISH("en_US", "英文"),
    VIETNAMESE("vi_VN", "越南"),
    ENGLISH_WOED("en_US_word", "分词"),
    VIETNAMESE_WOED("vi_VN_word", "分词")

    ;

    private final String code; // 语言代码
    private final String name;  // 语言名称

    // 构造函数
    LanguageCode(String code, String name) {
        this.code = code;
        this.name = name;
    }

    // 获取语言代码
    public String getCode() {
        return code;
    }

    // 获取语言名称
    public String getName() {
        return name;
    }
    // 根据代码查找对应的枚举
    public static LanguageCode fromCode(String code) {
        for (LanguageCode language : LanguageCode.values()) {
            if (language.getCode().equals(code)) {
                return language;
            }
        }
        // 如果找不到，返回null或者可以抛出异常
        return null; // 可以根据需求更改为抛出一个合适的异常
    }
}