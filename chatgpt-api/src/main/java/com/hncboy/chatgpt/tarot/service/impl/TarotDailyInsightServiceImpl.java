package com.hncboy.chatgpt.tarot.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.SysDictData;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.service.ISysDictDataService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.tarot.domain.converter.TarotDailyInsightConvert;
import com.hncboy.chatgpt.tarot.domain.dto.TarotDailyInsightDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning;
import com.hncboy.chatgpt.tarot.domain.entity.TarotDailyInsight;
import com.hncboy.chatgpt.tarot.domain.vo.TarotDailyInsightVO;
import com.hncboy.chatgpt.tarot.mapper.TarotDailyInsightMapper;
import com.hncboy.chatgpt.tarot.service.TarotCardMeaningService;
import com.hncboy.chatgpt.tarot.service.TarotDailyInsightService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 塔罗今日指引Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TarotDailyInsightServiceImpl extends ServiceImpl<TarotDailyInsightMapper, TarotDailyInsight> implements TarotDailyInsightService {


    private final TarotCardMeaningService tarotCardMeaningService;
    private final ISysDictDataService iSysDictDataService;
    private static List<SysDictData> sysDictData;


    /**
     * 查询塔罗今日指引
     *
     * @param id 塔罗今日指引主键
     * @return 塔罗今日指引
     */
    @Override
    public TarotDailyInsightVO selectTarotDailyInsightById(Long id) {
        TarotDailyInsightVO tarotDailyInsightVO = baseMapper.selectTarotDailyInsightById(id);
        if(sysDictData == null){
            sysDictData = iSysDictDataService.selectDictLabel("tarot_lucky_color");
        }

        for (SysDictData sysDictDatum : sysDictData) {
            if(sysDictDatum.getDictLabel().equals(tarotDailyInsightVO.getLuckyColor())){
                tarotDailyInsightVO.setLuckyColorUrl(sysDictDatum.getDictValue());
            }
        }


        return tarotDailyInsightVO;
    }

    /**
     * 新增塔罗今日指引
     *
     * @param dto 塔罗今日指引
     * @return 结果
     */
    @Override
    public TarotDailyInsightVO insertTarotDailyInsight(TarotDailyInsightDTO dto) {
        Integer v2UserId =CurrentUserUtil.getV2UserId();
        UserBaseInfoVO currentUser = CurrentUserUtil.getCurrentUser();
        dto.setUserId(v2UserId);
        //UserBaseInfo userBaseInfoByUserId = userBaseInfoService.getUserBaseInfoByUserId(v2UserId);
        if(dto.getOpenId() == null&& currentUser.getOpenId()!=null){
                dto.setOpenId(currentUser.getOpenId());
        }

        //判断用户是否已经生成过今日指引
        //TarotDailyInsight tarotDailyInsight1 = new TarotDailyInsight();
        String day = DateUtil.format(new Date(), "yyyy-MM-dd");
        dto.setEndDate(day+" 23:59:59");
        dto.setStartDate(day+" 00:00:00");


        TarotDailyInsightVO tarotDailyInsight1 = baseMapper.selectDayTarotDailyInsight(dto);
        if(tarotDailyInsight1 != null){
            log.info("用户已经生成过今日指引");
            if(sysDictData == null){
                sysDictData = iSysDictDataService.selectDictLabel("tarot_lucky_color");
            }
            for (SysDictData sysDictDatum : sysDictData) {
                if(sysDictDatum.getDictLabel().equals(tarotDailyInsight1.getLuckyColor())){
                    tarotDailyInsight1.setLuckyColorUrl(sysDictDatum.getDictValue());
                }
            }
            return tarotDailyInsight1;
            //throw new ServiceException("今日指引已生成，请勿重复操作");
            // return null;
        }
        try {
            TarotDailyInsight tarotDailyInsight = TarotDailyInsightConvert.INSTANCE.dtoToEntity(dto);
            //生成1-9的随机数
            Random rand = new Random();
            // 生成1到9之间的随机数（包含1和9）
            int randomNumber = rand.nextInt(9) + 1;

            tarotDailyInsight.setLuckyNumber(randomNumber+"");

            //随机获取彩虹颜色中的一个

            if(sysDictData == null){
                sysDictData = iSysDictDataService.selectDictLabel("tarot_lucky_color");
            }

            SysDictData sysDictData1 = sysDictData.get(rand.nextInt(sysDictData.size() - 1));
            //String randomColor = colors[rand.nextInt(colors.length)];
            tarotDailyInsight.setLuckyColor(sysDictData1.getDictLabel());
            //tarotDailyInsight.setLuckyColor(sysDictData1.getDictLabel());

            //ToDO 临时获取一张牌 逻辑需要改动
            List<TarotCardMeaning> list = tarotCardMeaningService.list();
            ArrayList<TarotCardMeaning> tarotCardMeanings = new ArrayList<>();
            for (TarotCardMeaning tarotCardMeaning : list) {
                if(ObjectUtil.isNotEmpty(tarotCardMeaning.getGuidanceText())){
                    tarotCardMeanings.add(tarotCardMeaning);
                }
            }

            TarotCardMeaning tarotCardMeaning = tarotCardMeanings.get(rand.nextInt(tarotCardMeanings.size()));
            //TarotCardMeaning tarotCardMeaning = list.get(0);
            tarotDailyInsight.setCardId(tarotCardMeaning.getId());
            tarotDailyInsight.setInsightDate(day);

            //upright-正位， reversed-逆位
            //随机获取正位或者逆位
            if (rand.nextBoolean()) {
                tarotDailyInsight.setPosition("upright");
            }else{
                tarotDailyInsight.setPosition("reversed");
            }

            try {
                baseMapper.insert(tarotDailyInsight);
            }catch (Exception e){
                if(e.getMessage().contains("Duplicate entry") || e.getMessage().contains("违反唯一约束条件")){
                    TarotDailyInsightVO tarotDailyInsightVO = baseMapper.selectDayTarotDailyInsight(dto);
                    if(sysDictData == null){
                        sysDictData = iSysDictDataService.selectDictLabel("tarot_lucky_color");
                    }
                    for (SysDictData sysDictDatum : sysDictData) {
                        if(sysDictDatum.getDictLabel().equals(tarotDailyInsightVO.getLuckyColor())){
                            tarotDailyInsightVO.setLuckyColorUrl(sysDictDatum.getDictValue());
                        }
                    }
                    return tarotDailyInsightVO;
                }else{
                    throw e;
                }

                //System.out.println(e.getMessage());


            }

            TarotDailyInsightVO tarotDailyInsightVO = TarotDailyInsightConvert.INSTANCE.entityToVO(tarotDailyInsight);
            tarotDailyInsightVO.setName(tarotCardMeaning.getName());
            tarotDailyInsightVO.setLuckyColorUrl(sysDictData1.getDictValue());
            tarotDailyInsightVO.setMeaning(tarotCardMeaning.getMeaning());
            tarotDailyInsightVO.setAdvice(tarotCardMeaning.getAdvice());
            tarotDailyInsightVO.setDiscouraged(tarotCardMeaning.getDiscouraged());
            tarotDailyInsightVO.setGuidanceText(tarotCardMeaning.getGuidanceText());
            tarotDailyInsightVO.setCardFrontUrl(tarotCardMeaning.getCardFrontUrl());
            return tarotDailyInsightVO;
        }catch (Exception e){
            log.error("生成塔罗今日指引失败", e);
            throw new ServiceException("生成塔罗今日指引失败");
        }
        //return null;

    }

    /**
     * 修改塔罗今日指引
     *
     * @param tarotDailyInsight 塔罗今日指引
     * @return 结果
     */
    @Override
    public int updateTarotDailyInsight(TarotDailyInsight tarotDailyInsight) {
        return baseMapper.updateById(tarotDailyInsight);
    }


    /**
     * 删除塔罗今日指引信息
     *
     * @param id 塔罗今日指引主键
     * @return 结果
     */
    @Override
    public int deleteTarotDailyInsightById(Long id) {
        return baseMapper.deleteById(id);
    }


    /**
     * 批量删除塔罗今日指引
     *
     * @param ids 需要删除的塔罗今日指引主键
     * @return 结果
     */
    @Override
    public int deleteTarotDailyInsightByIds(Long[] ids)
    {

        return  baseMapper.deleteBatchIds(Arrays.stream(ids).collect(Collectors.toList()));
    }


}