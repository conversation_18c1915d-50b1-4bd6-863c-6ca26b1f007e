package com.hncboy.chatgpt.tarot.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hncboy.chatgpt.tarot.i18n.I18nStringSerializer;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 塔罗牌牌阵对象 tarot_spread
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Data
public class TarotSpreadVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String name;

    /**
     * 布局;枚举
     */
    private String spreadLayout;

    /**
     * 示意图
     */
    private String spreadDiagramUrl;

    /**
     * 简述
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String summary;

    /**
     * 描述
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String description;

    /**
     * 输入示例
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String inputExample;

    /**
     * 消耗
     */
    private Integer consume;

    /**
     * 状态
     */
    private String status;
    /**
     * 是否上新 0:上新 1:非上新
     */
    private String isNew;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("spreadLayout", getSpreadLayout())
                .append("spreadDiagramUrl", getSpreadDiagramUrl())
                .append("summary", getSummary())
                .append("description", getDescription())
                .append("inputExample", getInputExample())
                .append("consume", getConsume())
                .append("isNew", getIsNew())
                .toString();
    }
}
