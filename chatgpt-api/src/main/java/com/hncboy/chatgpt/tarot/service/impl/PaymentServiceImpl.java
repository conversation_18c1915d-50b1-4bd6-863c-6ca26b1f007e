package com.hncboy.chatgpt.tarot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.tarot.domain.entity.QrPayment;
import com.hncboy.chatgpt.tarot.mapper.QrPaymentMapper;
import com.hncboy.chatgpt.tarot.mapper.TransactionMapper;
import com.hncboy.chatgpt.tarot.service.PaymentService;
import lombok.RequiredArgsConstructor;
import com.hncboy.chatgpt.tarot.domain.entity.Transaction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class PaymentServiceImpl extends ServiceImpl<QrPaymentMapper, QrPayment> implements PaymentService {

    private final TransactionMapper transactionMapper;
    private final QrPaymentMapper qrPaymentMapper;

    @Override
    public QrPayment createPayment(QrPayment payment) {
        QrPayment paymentStatus = getPaymentStatus(payment.getUniqueId());
        if (paymentStatus != null) {
            return paymentStatus;
        }
        save(payment);
        return payment;
    }

    @Override
    @Transactional
    public void handlePaymentCallback(Transaction transaction) {
        // 保存交易记录
        transactionMapper.insert(transaction);
        // 查找对应的QR付款
        QueryWrapper<QrPayment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_number", transaction.getAccountNumber())
                   .eq("status", "PENDING")
                   .orderByDesc("created_at")
                   .last("LIMIT 1");

        QrPayment payment = qrPaymentMapper.selectOne(queryWrapper);

        if (payment != null) {
            // 更新付款状态
            payment.setStatus("PAID");
            payment.setUpdatedAt(LocalDateTime.now());
            transaction.setUniqueId(payment.getUniqueId());
            transaction.setProductId(payment.getUserId());
            transactionMapper.updateById(transaction);
            qrPaymentMapper.updateById(payment);
        }
    }

    @Override
    public QrPayment getPaymentStatus(String uniqueId) {
        QueryWrapper<QrPayment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("unique_id", uniqueId);
        return qrPaymentMapper.selectOne(queryWrapper);
    }
}
