package com.hncboy.chatgpt.tarot.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.hncboy.chatgpt.front.framework.domain.entity.SysConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.mapper.UserPointsLogMapper;
import com.hncboy.chatgpt.front.service.SysConfigService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.tarot.domain.dto.UserCheckInRecordDTO;
import com.hncboy.chatgpt.tarot.domain.entity.UserCheckInRecord;
import com.hncboy.chatgpt.tarot.domain.vo.UserCheckInRecordVO;
import com.hncboy.chatgpt.tarot.mapper.UserCheckInRecordMapper;
import com.hncboy.chatgpt.tarot.service.UserCheckInRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户签到记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserCheckInRecordServiceImpl extends ServiceImpl<UserCheckInRecordMapper, UserCheckInRecord> implements UserCheckInRecordService {

    private final UserBaseInfoService userBaseInfoService;
    private final SysConfigService sysConfigService;
    private final UserPointsLogMapper userPointsLogMapper;
    private final RedissonClient redissonClient;
    private final RedisTemplate<String, Object> redisTemplate;
    /**
     * 查询用户签到记录
     *
     * @param id 用户签到记录主键
     * @return 用户签到记录
     */
    @Override
    public UserCheckInRecord selectUserCheckInRecordById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询用户签到记录列表
     *
     * @param userCheckInRecord 用户签到记录
     * @return 用户签到记录
     */
    @Override
    public List<UserCheckInRecordVO> selectUserCheckInRecordList(UserCheckInRecordDTO userCheckInRecord) {
        Integer v2UserId = CurrentUserUtil.getV2UserId();
        userCheckInRecord.setUserId(v2UserId);

        //查询本周签到记录
        LocalDate today = LocalDate.now();
        // 计算本周周一的日期
        LocalDate monday = today.with(java.time.temporal.TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 计算本周周日的日期
        LocalDate sunday = today.with(java.time.temporal.TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));

        String sundayTime = sunday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String mondayTime = monday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        userCheckInRecord.setStartDate(mondayTime);
        userCheckInRecord.setEndDate(sundayTime);

        return baseMapper.selectUserCheckInRecordList(userCheckInRecord);
    }



    /**
     * 新增用户签到记录
     *
     * @param userCheckInRecord 用户签到记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUserCheckInRecord(UserCheckInRecord userCheckInRecord) {
        Integer v2UserId = CurrentUserUtil.getV2UserId();
        UserBaseInfoVO currentUser = CurrentUserUtil.getCurrentUser();
        userCheckInRecord.setUserId(v2UserId);
        if(userCheckInRecord.getOpenId()==null&&currentUser.getOpenId() != null){
            userCheckInRecord.setOpenId(currentUser.getOpenId());
        }

        String formatDate ;
        if("1".equals(userCheckInRecord.getIsMakeUp())){
            formatDate=userCheckInRecord.getCheckInDate();
        }else{
            formatDate = DateUtil.format(new Date(), "yyyyMMdd");
        }

        // 构建缓存key和锁key
        String cacheKey = "user_check_in:" + v2UserId + ":" + formatDate;
        String lockKey = "check_in_lock:" + v2UserId + ":" + formatDate;

        // 第一重校验：检查Redis缓存
        if (Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey))) {
            log.info("用户{}在{}已签到(Redis缓存校验),请勿重复签到", v2UserId, formatDate);
            return 0;
        }

        // 获取分布式锁
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，等待时间3秒，锁持有时间10秒
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                try {
                    return doCheckIn(userCheckInRecord, v2UserId, formatDate, cacheKey);
                } catch (Exception e) {
                    // 方法报错时清除缓存
                    clearCheckInCache(cacheKey);
                    throw e;
                } finally {
                    // 释放锁
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            } else {
                log.warn("用户{}获取签到锁失败，操作过于频繁", v2UserId);
                throw new ServiceException("签到操作过于频繁，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("用户{}签到操作被中断", v2UserId, e);
            throw new ServiceException("签到操作被中断，请重试");
        }
    }

    /**
     * 执行签到业务逻辑
     *
     * @param userCheckInRecord 签到记录
     * @param v2UserId 用户ID
     * @param formatDate 格式化日期
     * @param cacheKey 缓存key
     * @return 结果
     */
    private int doCheckIn(UserCheckInRecord userCheckInRecord, Integer v2UserId, String formatDate, String cacheKey) {
        // 第二重校验：再次检查Redis缓存（双重校验）
        if (Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey))) {
            log.info("用户{}在{}已签到(分布式锁内二次校验),请勿重复签到", v2UserId, formatDate);
            return 0;
        }

        // 第三重校验：检查数据库
        List<UserCheckInRecord> userCheckInRecords = baseMapper.selectList(new QueryWrapper<UserCheckInRecord>()
                .eq("check_in_date", formatDate)
                .eq("user_id", v2UserId)
        );

        if (!userCheckInRecords.isEmpty()) {
            log.info("用户{}在{}已签到(数据库校验),设置缓存", v2UserId, formatDate);
            // 设置缓存，过期时间1天
            redisTemplate.opsForValue().set(cacheKey, "1", 1, TimeUnit.DAYS);
            return 0;
        }

        // 执行签到业务逻辑
        SysConfig tarotReward = sysConfigService.querySysConfig("tarot_reward");
        UserBaseInfo userBaseInfoByUserId = userBaseInfoService.getUserBaseInfoByUserId(v2UserId);
        Integer tarotCurrency = userBaseInfoByUserId.getTarotCoins();
        tarotCurrency += Integer.parseInt(tarotReward.getConfigValue());

        userBaseInfoService.update(new UpdateWrapper<UserBaseInfo>().eq("id", v2UserId)
                .set("tarot_coins", tarotCurrency));

        userCheckInRecord.setCheckInDate(formatDate);
        int dayOfWeek = DateUtil.dayOfWeek(new Date());

        // 设置星期
        switch (dayOfWeek) {
            case 1:
                userCheckInRecord.setWeek("星期日");
                break;
            case 2:
                userCheckInRecord.setWeek("星期一");
                break;
            case 3:
                userCheckInRecord.setWeek("星期二");
                break;
            case 4:
                userCheckInRecord.setWeek("星期三");
                break;
            case 5:
                userCheckInRecord.setWeek("星期四");
                break;
            case 6:
                userCheckInRecord.setWeek("星期五");
                break;
            case 7:
                userCheckInRecord.setWeek("星期六");
                break;
            default:
                log.warn("输入的日期有误");
        }

        userCheckInRecord.setAwarded(Integer.parseInt(tarotReward.getConfigValue()));
        userCheckInRecord.setType("TAROT");

        // 存入 user_points_log 表
        final UserPointsLog userPointsLog = new UserPointsLog()
                .setUserId(userBaseInfoByUserId.getId())
                .setRelOrder(userBaseInfoByUserId.getAccount())
                .setPoints(Integer.valueOf(tarotReward.getConfigValue()))
                .setPointsType(tarotReward.getConfigKey())
                .setRemark(tarotReward.getRemark())
                .setCreateTime(new Date())
                .setCreateBy(CurrentUserUtil.getUserId());
        userPointsLogMapper.insert(userPointsLog);

        // 插入签到记录
        int result = baseMapper.insert(userCheckInRecord);

        if (result > 0) {
            // 签到成功，设置缓存，过期时间1天
            redisTemplate.opsForValue().set(cacheKey, "1", 1, TimeUnit.DAYS);
            log.info("用户{}在{}签到成功，已设置缓存", v2UserId, formatDate);
        }

        return result;
    }

    /**
     * 清除签到缓存
     *
     * @param cacheKey 缓存key
     */
    private void clearCheckInCache(String cacheKey) {
        try {
            redisTemplate.delete(cacheKey);
            log.info("已清除签到缓存: {}", cacheKey);
        } catch (Exception e) {
            log.error("清除签到缓存失败: {}", cacheKey, e);
        }
    }

    @Override
    public int getCheckAwarded() {
        SysConfig tarotReward = sysConfigService.querySysConfig("tarot_reward");
        return Integer.parseInt(tarotReward.getConfigValue());
    }

    /**
     * 修改用户签到记录
     *
     * @param userCheckInRecord 用户签到记录
     * @return 结果
     */
    @Override
    public int updateUserCheckInRecord(UserCheckInRecord userCheckInRecord) {
        return baseMapper.updateById(userCheckInRecord);
    }


    /**
     * 删除用户签到记录信息
     *
     * @param id 用户签到记录主键
     * @return 结果
     */
    @Override
    public int deleteUserCheckInRecordById(Long id) {
        return baseMapper.deleteById(id);
    }



}