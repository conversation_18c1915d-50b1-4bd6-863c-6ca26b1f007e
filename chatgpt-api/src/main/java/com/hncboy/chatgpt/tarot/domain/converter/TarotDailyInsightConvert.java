package com.hncboy.chatgpt.tarot.domain.converter;

import com.hncboy.chatgpt.tarot.domain.dto.TarotCardMeaningDTO;
import com.hncboy.chatgpt.tarot.domain.dto.TarotDailyInsightDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning;
import com.hncboy.chatgpt.tarot.domain.entity.TarotDailyInsight;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotDailyInsightVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface TarotDailyInsightConvert {

    TarotDailyInsightConvert INSTANCE = Mappers.getMapper(TarotDailyInsightConvert.class);

    TarotDailyInsight dtoToEntity(TarotDailyInsightDTO dto);

    TarotDailyInsightVO entityToVO(TarotDailyInsight entity);

    List<TarotCardMeaningVO> entityListToVOList(List<TarotDailyInsight> entityList);

    TarotDailyInsight queryDtoToEntity(TarotCardMeaningDTO dto);


}
