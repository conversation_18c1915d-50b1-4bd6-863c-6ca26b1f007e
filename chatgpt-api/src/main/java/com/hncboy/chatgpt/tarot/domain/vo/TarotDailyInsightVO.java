package com.hncboy.chatgpt.tarot.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hncboy.chatgpt.tarot.i18n.I18nStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * 塔罗今日指引对象 tarot_daily_insight
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@Data
public class TarotDailyInsightVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 微信openid
     */
    private String openId;

    /**
     * 今日牌面
     */
    private Integer cardId;

    /**
     * 正逆位;upright-正位， reversed-逆位
     */
    private String position;

    /**
     * 幸运色
     */
    private String luckyColor;
    private String luckyColorUrl;

    /**
     * 幸运数
     */
    private String luckyNumber;


    @JsonSerialize(using = I18nStringSerializer.class)
    private String guidanceText;
    @JsonSerialize(using = I18nStringSerializer.class)
    private String advice;
    @JsonSerialize(using = I18nStringSerializer.class)
    private String discouraged;
    @JsonSerialize(using = I18nStringSerializer.class)
    private String meaning;
    @JsonSerialize(using = I18nStringSerializer.class)
    private String name;
    private String cardFrontUrl;


}