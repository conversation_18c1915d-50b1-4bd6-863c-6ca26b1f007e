package com.hncboy.chatgpt.tarot.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.tarot.domain.entity.QrPayment;
import com.hncboy.chatgpt.tarot.domain.entity.Transaction;
import com.hncboy.chatgpt.tarot.service.PaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

@RestController
@RequestMapping("/api/payment")
@RequiredArgsConstructor
@Slf4j
public class PaymentController {

    private final PaymentService paymentService;

    @PostMapping("/generate-qr")
    public R<?> generateQrCode(@RequestBody QrPayment request, HttpServletRequest httpRequest) {
        request.setBankName("MB");
        request.setAccountNumber("*************");
        request.setAmount(10000.0);
        request.setStatus("PENDING");
        request.setCreatedAt(LocalDateTime.now());
        request.setIpAddress(httpRequest.getRemoteAddr());
        request.setUniqueId(request.getUniqueId());
        request.setUserId(StpUtil.getLoginIdAsString());
        String qrCodeUrl = String.format("https://qr.sepay.vn/img?acc=%s&bank=%s&amount=%s&des=%s",
                request.getAccountNumber(),
                request.getBankName(),
                request.getAmount() != null ? request.getAmount() : "",
                request.getUniqueId() != null ? request.getUniqueId() : "");
        request.setQrCodeUrl(qrCodeUrl);
        QrPayment savedPayment = paymentService.createPayment(request);
        return R.data(savedPayment, "二维码生成成功");
    }

    @GetMapping("/check/{uniqueId}")
    public R<?> checkPaymentStatus(@PathVariable String uniqueId) {
        QrPayment payment = paymentService.getPaymentStatus(uniqueId);
        if (payment == null) {
            return R.data(null, "找不到付款");
        }
        return R.data(payment, "已成功检索付款状态");
    }

    @PostMapping("/callback")
    public R<?> handleCallback(@RequestBody Transaction transaction) {
        paymentService.handlePaymentCallback(transaction);
        return R.data(null, "已成功处理回调");
    }
}
