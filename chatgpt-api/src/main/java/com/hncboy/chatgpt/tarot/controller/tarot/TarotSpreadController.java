package com.hncboy.chatgpt.tarot.controller.tarot;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.tarot.domain.dto.TarotSpreadDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotSpreadVO;
import com.hncboy.chatgpt.tarot.service.TarotSpreadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 塔罗牌牌阵Controller
 * 
 * <AUTHOR>
 * @date 2024-09-12
 */
@Slf4j
@FrontPreAuth
@AllArgsConstructor
@RestController
@Tag(name = "塔罗牌牌阵")
@RequestMapping("/tarot/spread")
public class TarotSpreadController {

    private final TarotSpreadService tarotSpreadService;

    /**
     * 查询塔罗牌牌阵列表
     */
    @Operation(summary = "查询塔罗牌牌阵列表")
    @PostMapping("/list")
    public R<IPage<TarotSpreadVO>> list(@RequestBody TarotSpreadDTO tarotSpread)
    {
        //startPage();
        IPage<TarotSpreadVO> list = tarotSpreadService.selectTarotSpreadList(tarotSpread);
        return R.data(list);
    }

    /**
     * 随机洗乱塔罗牌
     */
    @Operation(summary = "随机洗乱塔罗牌")
    @GetMapping("/randomTarot")
    public R<List<TarotCardMeaningVO>> random(){
        List<TarotCardMeaningVO> tarotCardMeaningVOS = tarotSpreadService.randomTarot();
        return R.data(tarotCardMeaningVOS);
    }


}
