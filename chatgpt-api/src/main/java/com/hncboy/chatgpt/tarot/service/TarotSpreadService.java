package com.hncboy.chatgpt.tarot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.tarot.domain.dto.TarotSpreadDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotSpreadVO;

import java.util.List;

/**
 * 塔罗牌牌阵Service接口
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
public interface TarotSpreadService extends IService<TarotSpread> {
    /**
     * 查询塔罗牌牌阵
     *
     * @param id 塔罗牌牌阵主键
     * @return 塔罗牌牌阵
     */
    public TarotSpread selectTarotSpreadById(Long id);

    /**
     * 查询塔罗牌牌阵列表
     *
     * @param tarotSpread 塔罗牌牌阵
     * @return 塔罗牌牌阵集合
     */
    public IPage<TarotSpreadVO> selectTarotSpreadList(TarotSpreadDTO tarotSpread);

    List<TarotCardMeaningVO> randomTarot();

    /**
     * 新增塔罗牌牌阵
     *
     * @param tarotSpread 塔罗牌牌阵
     * @return 结果
     */
    public int insertTarotSpread(TarotSpread tarotSpread);

    /**
     * 修改塔罗牌牌阵
     *
     * @param tarotSpread 塔罗牌牌阵
     * @return 结果
     */
    public int updateTarotSpread(TarotSpread tarotSpread);


    /**
     * 删除塔罗牌牌阵信息
     *
     * @param id 塔罗牌牌阵主键
     * @return 结果
     */
    public int deleteTarotSpreadById(Long id);
}