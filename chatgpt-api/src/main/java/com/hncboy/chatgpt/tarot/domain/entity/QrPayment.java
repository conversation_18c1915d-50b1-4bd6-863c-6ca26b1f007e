package com.hncboy.chatgpt.tarot.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("qr_payment")
public class QrPayment {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String userId;
    private String accountNumber; // 银行账号
    private String bankName; // 银行名称
    private Double amount; // 转账金额
    private String content; // 传输内容
    private String qrCodeUrl; // 生成的QR码URL
    private String status; // PENDING, PAID, FAILED   待定、已支付、失败
    private String ipAddress;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String uniqueId; // 付款的唯一标识符
}
