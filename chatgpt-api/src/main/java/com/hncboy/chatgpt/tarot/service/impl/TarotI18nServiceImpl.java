package com.hncboy.chatgpt.tarot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.tarot.domain.entity.TarotI18n;
import com.hncboy.chatgpt.tarot.mapper.TarotI18nMapper;
import com.hncboy.chatgpt.tarot.service.TarotI18nService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 塔塔罗国际化Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Service
@RequiredArgsConstructor
public class TarotI18nServiceImpl extends ServiceImpl<TarotI18nMapper, TarotI18n> implements TarotI18nService {

}