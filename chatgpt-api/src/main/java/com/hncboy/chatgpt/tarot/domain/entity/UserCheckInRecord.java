package com.hncboy.chatgpt.tarot.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户签到记录对象 user_check_in_record
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@TableName("user_check_in_record")
@Data
public class UserCheckInRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
        @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 用户id */
    private Integer userId;

    /** 签到日期;yyyyMMDD */
    private String checkInDate;

    /** 微信openid */
    private String openId;

    /** 类型;TAROT-塔罗牌签到 */
    private String type;

    /** 是否补签;0-否; 1-是，默认0 */
    private String isMakeUp;

    /** 奖励 */
    private Integer awarded;

    private String week;


    /** 创建者 */
    private String createBy;
    /** 创建时间 */
    private Date createTime;
    /** 更新者 */
    private String updateBy;
    /** 更新时间 */
    private Date updateTime;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("checkInDate", getCheckInDate())
            .append("openId", getOpenId())
            .append("type", getType())
            .append("isMakeUp", getIsMakeUp())
            .append("awarded", getAwarded())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
                .toString();
    }
}