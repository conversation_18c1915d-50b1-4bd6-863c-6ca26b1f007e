package com.hncboy.chatgpt.tarot.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.query.LoginInfoParam;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.tarot.domain.entity.User;
import com.hncboy.chatgpt.tarot.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class FingerprintByBrowserAuthService {

    private final UserMapper userMapper;
    private final UserBaseInfoService userBaseInfoService;

    public UserBaseInfoVO login(String parentId, Map<String, String> paramsMap) {
        String fingerprint = MapUtil.getStr(paramsMap, "fingerprint");
        String components = MapUtil.getStr(paramsMap, "components");
        if(StrUtil.isBlank(fingerprint)){
            throw new RuntimeException("游客身份异常");
        }
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("finb_id", fingerprint));
        if (user == null) {
            user = new User();
            user.setCreatedAt(LocalDateTime.now());
            user.setFinbId(fingerprint);
            user.setExtraData(components);
            user.setName("finb-"+RandomUtil.randomString(6));
        }
        user.setLastLoginTime(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        UserBaseInfoVO baseUser;
        boolean isNew = false;
        if (user.getId() == null) {
            userMapper.insert(user);
            baseUser = null;
            isNew = true;
        } else {
            baseUser = userBaseInfoService.getUserBaseInfoByUsersId(user.getId());
            if (baseUser != null) {
                Integer existedId = null;
                try{
                    existedId = StpUtil.getLoginIdAsInt();
                } catch (Exception ignored){
                }
                if(baseUser.getId().equals(existedId)){
                    //已经登录了
                    return baseUser;
                }
            }
            userMapper.updateById(user);
        }
        return associatedUser(parentId, user, baseUser, isNew);
    }

    /**
     * 关联创建用户
     * @param user
     * @param parentId
     * @return
     */
    private UserBaseInfoVO associatedUser(String parentId, User user, UserBaseInfoVO baseUser, boolean isNew) {
        if (null == baseUser) {
            LoginInfoParam loginInfoParam = new LoginInfoParam();
            loginInfoParam.setNickName(user.getName());
            loginInfoParam.setHeadImgUrl(user.getPicture());
            String uuId = RandomUtil.randomString(8);
            loginInfoParam.setAccount("finb_" + uuId);
            loginInfoParam.setUsersId(user.getId());
            loginInfoParam.setParentId(parentId);
            baseUser = userBaseInfoService.initUserInfo3(loginInfoParam, isNew);
            if (isNew && ObjectUtil.isNotEmpty(baseUser.getParentId())){
                userBaseInfoService.addUserPoints(baseUser.getParentId(), loginInfoParam.getAccount(),"tarot_invite_points");
            }
        }
        //登录
        StpUtil.login(baseUser.getId());
        //会话相关
        StpUtil.getSession().set(ApplicationConstant.USER, baseUser);
        baseUser.setToken(StpUtil.getTokenValue());
        return baseUser;
    }

}