package com.hncboy.chatgpt.tarot.domain.dto;

import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class WxMiniQrCodeDTO implements Serializable {
    private static final long serialVersionUID = 130569783040564371L;

    @Schema(description = "二维码场景")
    private String scene;
    @Schema(description = "二维码路径")
    private String page;
    @Schema(description = "是否需要检查页面路径")
    private boolean checkPath;
    @Schema(description = "环境版本")
    private String envVersion;
    @Schema(description = "二维码宽度")
    private int width;
    @Schema(description = "是否自动配置线条颜色")
    private boolean autoColor;
    @Schema(description = "线条颜色")
    private WxMaCodeLineColor lineColor;
    @Schema(description = "是否需要透明底色")
    private boolean hyaline;

    private String G;
    private String B;
    private String R;

}
