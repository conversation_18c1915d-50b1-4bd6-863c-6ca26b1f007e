package com.hncboy.chatgpt.tarot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.tarot.domain.dto.TarotCardMeaningDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning;
import com.hncboy.chatgpt.tarot.domain.vo.TagSelectVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 塔罗牌牌义Service接口
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
public interface TarotCardMeaningService extends IService<TarotCardMeaning> {
    /**
     * 查询塔罗牌牌义
     *
     * @param id 塔罗牌牌义主键
     * @return 塔罗牌牌义
     */
    public TarotCardMeaningVO selectTarotCardMeaningById(Long id);

    /**
     * 查询塔罗牌牌义列表
     *
     * @param tarotCardMeaning 塔罗牌牌义
     * @return 塔罗牌牌义集合
     */
//    public List<TarotCardMeaning> selectTarotCardMeaningList(TarotCardMeaning tarotCardMeaning);
      IPage<TarotCardMeaningVO> selectTarotCardMeaningList(TarotCardMeaningDTO tarotCardMeaning);


      List<TagSelectVO> getSelectTagList();

    /**
     * 新增塔罗牌牌义
     *
     * @param tarotCardMeaning 塔罗牌牌义
     * @return 结果
     */
    public int insertTarotCardMeaning(TarotCardMeaning tarotCardMeaning);

    /**
     * 修改塔罗牌牌义
     *
     * @param tarotCardMeaning 塔罗牌牌义
     * @return 结果
     */
    public int updateTarotCardMeaning(TarotCardMeaning tarotCardMeaning);



    /**
     * 删除塔罗牌牌义信息
     *
     * @param id 塔罗牌牌义主键
     * @return 结果
     */
    public int deleteTarotCardMeaningById(Long id);
}