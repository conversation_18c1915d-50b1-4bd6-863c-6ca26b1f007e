package com.hncboy.chatgpt.tarot.controller.tarot;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.tarot.domain.dto.TarotCardMeaningDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning;
import com.hncboy.chatgpt.tarot.domain.vo.TagSelectVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;
import com.hncboy.chatgpt.tarot.service.TarotCardMeaningService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 塔罗牌牌义Controller
 * 
 * <AUTHOR>
 * @date 2024-09-12
 */
@Slf4j
@FrontPreAuth
@AllArgsConstructor
@RestController
@Tag(name = "塔罗牌牌义")
@RequestMapping("/tarot/meaning")
public class TarotCardMeaningController {

    private final TarotCardMeaningService tarotCardMeaningService;

    /**
     * 查询塔罗牌牌义列表
     */
    @Operation(summary = "查询塔罗牌牌义列表")
    @PostMapping("/list")
    public R<IPage<TarotCardMeaningVO>> list(@RequestBody TarotCardMeaningDTO tarotCardMeaning)
    {
        IPage<TarotCardMeaningVO> list = tarotCardMeaningService.selectTarotCardMeaningList(tarotCardMeaning);
        return R.data(list);
    }

    /**
     * 查询塔罗牌牌义列表
     */
    @Operation(summary = "查询塔罗牌义详情")
    @GetMapping("/info")
    public R<TarotCardMeaningVO> info(@RequestParam Long id)
    {
        TarotCardMeaningVO tarotDailyInsight = tarotCardMeaningService.selectTarotCardMeaningById(id);
        return R.data(tarotDailyInsight);
    }

    /**
     * 查询塔罗牌牌义列表
     */
    @Operation(summary = "获取牌义下拉框")
    @PostMapping("/getSelect")
    public R all()
    {
        List<TagSelectVO> selectTagList = tarotCardMeaningService.getSelectTagList();
        return R.data(selectTagList);
    }


}
