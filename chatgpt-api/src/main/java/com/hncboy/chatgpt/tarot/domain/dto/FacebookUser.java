package com.hncboy.chatgpt.tarot.domain.dto;


import lombok.Data;

import java.io.Serializable;

@Data
public class FacebookUser  implements Serializable {
    private String id;
    private String name;
    private String email;
    private String picture;

    // ✅ 1. 全参构造
    public FacebookUser(String id, String name, String email, String picture) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.picture = picture;
    }

    // ✅ 2. 无参构造（Spring 需要）
    public FacebookUser() {}

}

