package com.hncboy.chatgpt.tarot.controller.tarot;


import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.tarot.domain.dto.TarotDailyInsightDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotDailyInsight;
import com.hncboy.chatgpt.tarot.domain.vo.TarotDailyInsightVO;
import com.hncboy.chatgpt.tarot.service.TarotDailyInsightService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 塔罗今日指引Controller
 * 
 * <AUTHOR>
 * @date 2024-09-12
 */
@Slf4j
@FrontPreAuth
@AllArgsConstructor
@RestController
@Tag(name = "塔罗今日指引")
@RequestMapping("/tarot/insight")
public class TarotDailyInsightController {
    private final TarotDailyInsightService tarotDailyInsightService;



    /**
     * 查询塔罗今日指引详情
     */
    @Operation(summary = "查询塔罗今日指引详情")
    @GetMapping("/info")
    public R<TarotDailyInsightVO> info(@RequestParam Long id)
    {
        TarotDailyInsightVO tarotDailyInsight = tarotDailyInsightService.selectTarotDailyInsightById(id);
        return R.data(tarotDailyInsight);
    }


    /**
     * 生成塔罗今日指引
     */
    @Operation(summary = "生成塔罗今日指引")
    @PostMapping("/generateInsight")
    public R add(@RequestBody TarotDailyInsightDTO tarotDailyInsight)
    {
        TarotDailyInsightVO tarotDailyInsightVO = tarotDailyInsightService.insertTarotDailyInsight(tarotDailyInsight);
        return R.data(tarotDailyInsightVO);
    }

}
