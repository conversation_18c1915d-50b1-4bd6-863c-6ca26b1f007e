package com.hncboy.chatgpt.tarot.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 塔罗今日指引对象 tarot_daily_insight
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@TableName("tarot_daily_insight")
@Data
public class TarotDailyInsight implements Serializable {
    private static final long serialVersionUID = 1L;

        /** 主键 */
        @TableId(value = "id",type = IdType.AUTO)
        private Integer id;

        /** 用户id */
        private Long userId;

        /** 微信openid */
        private String openId;

        /** 今日牌面 */
        private Integer cardId;

        /** 正逆位;upright-正位， reversed-逆位 */
        private String position;

        /** 幸运色 */
        private String luckyColor;

        /** 幸运数 */
        private String luckyNumber;

        /**
        * 创建时间
        */
        private String insightDate;

        /** 创建人 */
        private String createBy;

        /** 创建时间 */
        private Date createTime;

        /** 更新人 */
        private String updateBy;

        /** 更新时间 */
        private Date updateTime;


}