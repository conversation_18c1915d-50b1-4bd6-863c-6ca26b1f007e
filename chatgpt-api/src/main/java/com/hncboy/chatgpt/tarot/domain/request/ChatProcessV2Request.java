package com.hncboy.chatgpt.tarot.domain.request;

import cn.hutool.json.JSONObject;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatRoomDO;
import com.unfbx.chatgpt.entity.chat.Message;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/23 13:17 消息处理请求
 */
@Data
@Schema(title = "消息处理请求")
public class ChatProcessV2Request {

    @Schema(title = "问题")
    private String prompt;

    @Schema(title = "参数")
    private String remark;

    @Schema(title = "model")
    private String model;

    @Schema(title = "maxTokens")
    private Integer maxTokens = 2048;

    @Schema(title = "temperature")
    private double temperature = 0.2;

    @Schema(title = "ip")
    private String ip;

    @Schema(title = "临时切换的智能体ID")
    private Integer intelligentAgentId;

    @Schema(title = "消息列表")
    private List<Message> messageList;

    @Schema(title = "房间信息ID")
    private String chatRoomId;

    @Schema(title = "构建图片请求参数 前端区分---")
    private List<JSONObject> imgMessages;

    @Schema(title = "房间信息")
    private ChatRoomDO chatRoomDO;

    @Schema(title = "未购买继续")
    private Integer notContinue;

}
