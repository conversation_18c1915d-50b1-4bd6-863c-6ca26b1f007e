package com.hncboy.chatgpt.tarot.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.tarot.domain.entity.User;
import com.hncboy.chatgpt.tarot.service.FacebookAuthService;
import com.hncboy.chatgpt.tarot.service.FingerprintByBrowserAuthService;
import com.hncboy.chatgpt.tarot.service.GoogleAuthService;
import com.hncboy.chatgpt.tarot.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class ThirdAuthController {

    @Value("${google.local-domain}")
    private String localDomain;

    private final FacebookAuthService facebookAuthService;
    private final GoogleAuthService googleAuthService;
    private final UserService userService;
    private final FingerprintByBrowserAuthService fingerprintByBrowserAuthService;

    @GetMapping("/facebook/callback")
    public void facebookCallback(@RequestParam(required = false) String code,
                               @RequestParam String state,
                               @RequestParam(required = false) String error,
                               @RequestParam(required = false) String error_description,
                               HttpServletResponse response) throws Exception {
        try {
            // 检查是否有错误
            if (error != null) {
                String errorMsg = error_description != null ? error_description : error;
                log.error("Facebook auth error: {}", errorMsg);
                response.sendRedirect(localDomain+ "/#/pages/login/login?error="+ URLEncoder.encode("No authorization code received", String.valueOf(StandardCharsets.UTF_8)));
                return;
            }

            // 检查code是否存在
            if (code == null || code.trim().isEmpty()) {
                log.error("No authorization code received from Facebook");
                response.sendRedirect(localDomain + "/#/pages/login/login?error="+ URLEncoder.encode("No authorization code received", String.valueOf(StandardCharsets.UTF_8)));
                return;
            }

            // 解析state参数获取重定向地址和邀请码
            JSONObject stateJson = JSONUtil.parseObj(state);
            String redirectUrl = stateJson.getStr("redirect");
            String referralCode = stateJson.getStr("referralCode");

            // 1. 用code换取access token
            String accessToken = facebookAuthService.getAccessTokenFromCode(code);

            // 2. 认证用户并获取用户信息
            Map<String, Object> userInfo = facebookAuthService.authenticateUser(accessToken,referralCode);

            // 3. 使用sa-token登录
            Long userId = (Long) userInfo.get("userId");
            StpUtil.login(userId);
            String saToken = StpUtil.getTokenValue();
            // 4. 直接在URL中返回token，而不是设置cookie
            response.sendRedirect(redirectUrl + "?token=" + URLEncoder.encode(saToken, StandardCharsets.UTF_8.toString()));

        } catch (Exception e) {
            log.error("Error during Facebook authentication", e);
            String errorMsg = e.getMessage() != null ? e.getMessage() : "Authentication failed";
            response.sendRedirect(state + "?error=" + URLEncoder.encode(errorMsg, String.valueOf(StandardCharsets.UTF_8)));
        }
    }

    @GetMapping("/google/callback")
    public void googleCallback(@RequestParam(required = false) String code,
                               @RequestParam String state,
                               @RequestParam(required = false) String error,
                               @RequestParam(required = false) String error_description,
                               HttpServletResponse response) throws Exception {

        log.info("\n接收到来自Google服务器的回调消息：[{}, {}, {}, {}]", code, state, error, error_description);
        try {
            // 检查是否有错误
            if (error != null) {
                String errorMsg = error_description != null ? error_description : error;
                log.error("Google auth错误: {}", errorMsg);
                response.sendRedirect(localDomain + "/#/pages/login/login?error=" + URLEncoder.encode(errorMsg, String.valueOf(StandardCharsets.UTF_8)));
                return;
            }

            // 检查code是否存在
            if (code == null || code.trim().isEmpty()) {
                log.error("未收到来自Google的授权码");
                response.sendRedirect(localDomain + "/#/pages/login/login?error="+ URLEncoder.encode("No authorization code received", String.valueOf(StandardCharsets.UTF_8)));
                return;
            }

            // 解析state参数获取重定向地址和邀请码
            JSONObject stateJson = JSONUtil.parseObj(state);
            String redirectUrl = stateJson.getStr("redirect");
            String referralCode = stateJson.getStr("referralCode");

            // 1. 用code换取access token
            String accessToken = googleAuthService.getAccessTokenFromCode(code);

            // 2. 认证用户并获取用户信息
            Map<String, Object> userInfo = googleAuthService.authenticateUser(accessToken, referralCode);

            //3、获取sa-token token 登录步骤在 authenticateUser
            String saToken = StpUtil.getTokenValue();

            // 4. 重定向到前端，带上token
            log.info("重定向到前端，带上token: {}", redirectUrl + "?token=" + URLEncoder.encode(saToken, StandardCharsets.UTF_8.toString()));
            response.sendRedirect(redirectUrl + "?token=" + URLEncoder.encode(saToken, StandardCharsets.UTF_8.toString()));

        } catch (Exception e) {
            log.error("Google身份验证期间出错", e);
            String errorMsg = e.getMessage() != null ? e.getMessage() : "Authentication failed";
            response.sendRedirect(localDomain + "/#/pages/login/login?error=" + URLEncoder.encode(errorMsg, String.valueOf(StandardCharsets.UTF_8)));
        }
    }

    @GetMapping("/verify/{authHeader}")
    public R<?> verifyToken(@PathVariable String authHeader) {
        try {
            // 验证sa-token
            Object loginId = StpUtil.getLoginIdByToken(authHeader);
            if (loginId != null) {
                return R.data(loginId, "Token is valid");
            } else {
                return R.fail("Invalid token");
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 模拟登录接口
    @PostMapping("/mock")
    public R<?> mockLogin(@RequestBody Map<String, String> request) {
        try {
            String referralCode = request.get("referralCode");

            // 创建新用户
            User user = new User();
            user.setCreatedAt(LocalDateTime.now());
            //新用户送50福币
            user.setLuckyCoins(50);

            // 处理邀请码
            if (StrUtil.isNotEmpty(referralCode)) {
                User referrer = userService.getOne(new QueryWrapper<User>().eq("referral_code", referralCode));
                if (referrer != null) {
                    //给邀请人也加福币
                    referrer.setLuckyCoins(referrer.getLuckyCoins() + 3);
                    user.setReferrerId(referrer.getId());
                    userService.updateById(referrer);
                }
            }

            long userId = DateUtil.current();
            // 设置用户基本信息
            user.setName(""+userId);
            user.setEmail("mock" + userId + "@example.com");
            user.setLastLoginTime(LocalDateTime.now());
            user.setUpdatedAt(LocalDateTime.now());
            user.setFbId("fb_" + userId);
            // 保存用户
            userService.save(user);

            // 使用sa-token登录
            StpUtil.login(user.getId());
            String saToken = StpUtil.getTokenValue();

            JSONObject result = JSONUtil.parseObj(user);
            result.set("token", saToken);
            return R.data(result, "Mock login successful");

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @Operation(summary = "浏览器指纹登录")
    @PostMapping("/fingerprint")
    public R<UserBaseInfoVO> fingerprintByBrowser(@RequestParam("parentId") String parentId
            , @RequestBody Map<String, String> paramsMap) {
        return R.data(fingerprintByBrowserAuthService.login(parentId, paramsMap));
    }


}
