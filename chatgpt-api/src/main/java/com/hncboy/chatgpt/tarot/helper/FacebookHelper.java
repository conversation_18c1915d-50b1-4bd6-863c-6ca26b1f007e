package com.hncboy.chatgpt.tarot.helper;

import cn.hutool.json.JSONObject;
import com.hncboy.chatgpt.tarot.domain.dto.FacebookUser;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class FacebookHelper {

    private static final String FACEBOOK_GRAPH_API_URL = "https://graph.facebook.com/me";
    private static final String FACEBOOK_APP_SECRET = "0cc0b221ca4e86e1979eebd8a692bd58";

    public  static  FacebookUser verifyAccessToken(String accessToken) {
        String url = String.format("%s?fields=id,name,email,picture&access_token=%s", FACEBOOK_GRAPH_API_URL, accessToken);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
        JSONObject json = new JSONObject(response.getBody());
        FacebookUser facebookUser = new FacebookUser(
                json.getStr("id"),
                json.getStr("name"),
                json.getStr("email", ""),
                json.getJSONObject("picture").getJSONObject("data").getStr("url"));
        return facebookUser;
    }


}

