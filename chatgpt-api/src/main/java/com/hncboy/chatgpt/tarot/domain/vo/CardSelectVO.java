package com.hncboy.chatgpt.tarot.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hncboy.chatgpt.tarot.i18n.I18nStringSerializer;
import lombok.Data;

import java.util.List;

@Data
public class CardSelectVO {

    @JsonSerialize(using = I18nStringSerializer.class)
    private String value;
    @JsonSerialize(using = I18nStringSerializer.class)
    private String key;
    private List<PositionSelectVo> children;
}
