package com.hncboy.chatgpt.tarot.interceptor;

import com.hncboy.chatgpt.tarot.enums.LanguageCode;
import com.hncboy.chatgpt.tarot.i18n.I18nCache;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 国际化 拦截器
 */
@Component
public class LanguageInterceptor implements HandlerInterceptor {

//    private static final String LANG_HEADER = "Tarot-Language"; // 语言头
    private static final String LANG_HEADER = "Accept-Language"; // 语言头
    private static final String DEFAULT_LANG = LanguageCode.CHINESE.getCode(); // 语言头

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 获取请求头中的 Accept-Language
        String lang = request.getHeader(LANG_HEADER);

        // 如果 lang 不存在或为空，则使用默认值
        if (lang == null || lang.isEmpty()) {
            lang = DEFAULT_LANG;
        } else {
            // 通常 Accept-Language 可能有多个值，取第一个值
            lang = lang.split(",")[0].trim();
        }
        //如果lang  不是我们系统维护的 国际化 也默认中文
        if(LanguageCode.fromCode(lang) == null) {
            lang = DEFAULT_LANG;
        }

        // 将 lang 值存入 ThreadLocal
        I18nCache.setLang(lang);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 请求完成后清除 ThreadLocal，防止内存泄漏
        I18nCache.clear();
    }
}