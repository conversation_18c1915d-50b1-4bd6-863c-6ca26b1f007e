package com.hncboy.chatgpt.tarot.test;

import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.annotation.IgnoreAuth;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessV2Request;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.tarot.helper.ChatMsgV3BuildHelper;
import com.hncboy.chatgpt.tarot.helper.InviteTreeHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@FrontPreAuth
@AllArgsConstructor
@Tag(name = "聊天转接接口")
@RestController
@RequestMapping("/chat/v3")
@Slf4j
public class  ChatTransfer2Controller {

    private final ChatMsgV3BuildHelper chatMsgV2BuildHelper;

    private final InviteTreeHelper inviteTreeHelper;

    @IgnoreAuth
    @Operation(summary = "塔罗解读")
    @PostMapping("/buildBatchCompletion")
    public R buildBatchCompletion(@RequestBody ChatProcessV2Request chatProcessRequest,
                                  HttpServletResponse response) {
        //response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        return chatMsgV2BuildHelper.buildCompletionMessageBody(chatProcessRequest) ;
    }

    @IgnoreAuth
    @GetMapping("/tree")
    public R<InviteTreeHelper.TreeNode> getInviteTree(@RequestParam Integer userInfoId) {
        return R.data(inviteTreeHelper.getInviteTree(userInfoId));
    }

}
