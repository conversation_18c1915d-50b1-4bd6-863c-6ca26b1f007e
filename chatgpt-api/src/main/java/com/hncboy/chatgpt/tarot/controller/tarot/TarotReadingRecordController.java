package com.hncboy.chatgpt.tarot.controller.tarot;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.tarot.domain.dto.TarotReadingRecordDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.vo.TarotReadingRecordVO;
import com.hncboy.chatgpt.tarot.service.TarotReadingRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 塔罗牌占卜记录Controller
 * 
 * <AUTHOR>
 * @date 2024-09-12
 */
@Slf4j
@AllArgsConstructor
@RestController
@Tag(name = "塔罗牌占卜记录")
@RequestMapping("/tarot/readingRecord")
public class TarotReadingRecordController {

    private final TarotReadingRecordService tarotReadingRecordService;

    /**
     * 获取塔罗牌占卜记录列表
     */
    @Operation(summary = "获取塔罗牌占卜记录列表")
    @PostMapping("/list")
    public R<IPage<TarotReadingRecordVO>> list(@RequestBody TarotReadingRecordDTO tarotReadingRecord)
    {
        IPage<TarotReadingRecordVO> tarotReadingRecords = tarotReadingRecordService.selectTarotReadingRecordList(tarotReadingRecord);
        return R.data(tarotReadingRecords);
    }

    /**
     * 获取塔罗牌占卜记录列表
     */
    @Operation(summary = "获取塔罗牌占卜记录列表")
    @GetMapping("/info")
    public R<TarotReadingRecordVO> info(@RequestParam Long id) {
        TarotReadingRecordVO tarotReadingRecord = tarotReadingRecordService.selectTarotReadingRecordById(id);
        return R.data(tarotReadingRecord);
    }


    @Operation(summary = "软删除记录")
    @DeleteMapping
    public R softDelete(@RequestParam Long id) {
        return R.data(tarotReadingRecordService.deleteTarotReadingRecordById(id));
    }

}
