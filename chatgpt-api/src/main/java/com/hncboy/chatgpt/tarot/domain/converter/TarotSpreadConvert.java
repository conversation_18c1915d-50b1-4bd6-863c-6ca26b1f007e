package com.hncboy.chatgpt.tarot.domain.converter;


import com.hncboy.chatgpt.tarot.domain.dto.TarotReadingRecordDTO;
import com.hncboy.chatgpt.tarot.domain.dto.TarotSpreadDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import com.hncboy.chatgpt.tarot.domain.vo.TarotReadingRecordVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotSpreadVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface TarotSpreadConvert {

    TarotSpreadConvert INSTANCE = Mappers.getMapper(TarotSpreadConvert.class);

    TarotSpread dtoToEntity(TarotSpreadDTO dto);

    TarotSpreadVO entityToVO(TarotSpread entity);

    List<TarotSpreadVO> entityListToVOList(List<TarotSpread> entityList);

    TarotSpread queryDtoToEntity(TarotSpreadDTO dto);


}
