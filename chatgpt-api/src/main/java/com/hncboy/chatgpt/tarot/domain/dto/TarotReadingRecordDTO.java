package com.hncboy.chatgpt.tarot.domain.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hncboy.chatgpt.front.framework.domain.query.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 塔罗牌占卜记录对象 tarot_reading_record
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TarotReadingRecordDTO extends PageParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /** 牌阵id */
    private String spreadId;

    /** 问题 */
    private String question;

    /** 答案 */
    private String answer;

    /** 抽牌结果 */
    private String drawResult;

    /** 消耗 */
    private Integer consume;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("spreadId", getSpreadId())
            .append("question", getQuestion())
            .append("answer", getAnswer())
            .append("drawResult", getDrawResult())
            .append("consume", getConsume())
                .toString();
    }
}