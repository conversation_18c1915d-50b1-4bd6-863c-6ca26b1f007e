package com.hncboy.chatgpt.tarot.helper;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hncboy.chatgpt.front.framework.domain.entity.CommissionIdentity;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.entity.WxPayOrder;
import com.hncboy.chatgpt.front.mapper.CommissionIdentityMapper;
import com.hncboy.chatgpt.front.mapper.UserBaseInfoMapper;
import com.hncboy.chatgpt.front.mapper.WxPayOrderMapper;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class InviteTreeHelper {

    private final CommissionIdentityMapper commissionIdentityMapper;

    private final UserBaseInfoMapper userBaseInfoMapper;

    private final WxPayOrderMapper wxPayOrderMapper;

    /**
     * 表示邀请树中的单个节点，存储用户信息及相关统计数据。
     */
    @Data
    public static class Node {
        /**
         * 参与身份类型（如“代理商” 普通用户）。
         */
        private String commType;
        /**
         * 节点用户ID。 (user_base_info.id)
         */
        private Integer nodeId;
        /**
         * 用户账号。 (user_base_info.account)
         */
        private String nodeName;
        /**
         * 用户昵称。 (user_base_info.nick_name)
         */
        private String nodeDesc;
        /**
         * 节点层级（从0开始，0表示顶级节点）。
         */
        private Integer nodeLevel;
        /**
         * 佣金百分比。只有顶级节点（代理商）有值，其他用户为0。
         */
        private Integer percentage;
        /**
         * 当前节点用户.自己的累计充值订单总额。
         */
        private BigDecimal crtOrderSum;
        /**
         * 当前节点的今日充值订单总额。
         */
        private BigDecimal crtTdOrderSum;
        /**
         * 当前节点用户.所有直接和间接下级用户的今日充值订单总额（不含当前节点）。
         */
        private BigDecimal todayOrderSum;
        /**
         * 当前节点用户.所有直接和间接下级用户的累计充值订单总额（不含当前节点）。
         */
        private BigDecimal totalOrderSum;
        /**
         * 当前节点用户.直接下级用户总数。
         */
        private Long subInviteSum;
        /**
         * 当前节点用户.所有直接和间接下级用户的今日新邀请的用户总数。
         */
        private Long todayInviteSum;
        /**
         * 当前节点用户.所有直接和间接下级的用户累计邀请的用户总数。
         */
        private Long totalInviteSum;
    }

    /**
     * 表示邀请树的节点结构，包含当前节点信息及其子节点列表。
     */
    @Data
    public static class TreeNode {
        /**
         * 当前节点的信息。
         */
        private Node crtNode;
        /**
         * 当前节点的子节点列表。
         */
        private List<TreeNode> subNodeList;
    }

    public TreeNode getInviteTree(Integer userInfoId) {
        TimeInterval timer = DateUtil.timer();
        // 获取当前时间
        Date now = new Date();
        Date today = DateUtil.beginOfDay(new Date());

        // 1. 查询当前登录人的参与身份
        CommissionIdentity identity = commissionIdentityMapper.selectOne(
                Wrappers.<CommissionIdentity>lambdaQuery()
                        .eq(CommissionIdentity::getUserInfoId, userInfoId)
                        .lt(CommissionIdentity::getStartTime, now)
                        .gt(CommissionIdentity::getEndTime, now)
                        .eq(CommissionIdentity::getStatus, "0")
                        .last("limit 1")
        );

        if (identity == null) {
            log.warn("没有找到当前人的参与身份. userInfoId {}", userInfoId);
            return new TreeNode();
        }

        // 2. 查询所有相关的邀请人数据（包括顶级用户）
        List<UserBaseInfo> inviteUsers = userBaseInfoMapper.selectList(
                Wrappers.<UserBaseInfo>lambdaQuery()
                        .eq(UserBaseInfo::getCommissionId, identity.getUserInfoId())
                        .eq(UserBaseInfo::getStatus, "0")
        );

        if (inviteUsers.isEmpty()) {
            log.warn("没有找到任何代理商下的用户. commissionId {}", identity.getUserInfoId());
            return new TreeNode();
        }

        // 3. 提取用户ID列表
        List<Integer> userIds = inviteUsers.stream()
                .filter(user -> user.getCreateTime() != null &&
                        (user.getCreateTime().after(identity.getStartTime()) || user.getCreateTime().equals(identity.getStartTime())) &&
                        (user.getCreateTime().before(identity.getEndTime()) || user.getCreateTime().equals(identity.getEndTime())))
                .map(UserBaseInfo::getId)
                .collect(Collectors.toList());

        // 4. 查询充值订单数据
        Map<Integer, BigDecimal> totalOrderSumMap = new HashMap<>();
        Map<Integer, BigDecimal> todayOrderSumMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            QueryWrapper<WxPayOrder> todayOrderQuery = Wrappers.query();
            QueryWrapper<WxPayOrder> totalOrderQuery;
            todayOrderQuery
                    .select("user_id", "SUM(total_fee) as total_fee")
                    .eq("product_type", "TAROT")
                    .eq("status", "1")
                    .in("user_id", userIds)
                    .groupBy("user_id");
            totalOrderQuery = todayOrderQuery.clone();
            todayOrderQuery.ge("create_time", today);
            totalOrderQuery
                    .ge("create_time", identity.getStartTime())
                    .le("create_time", identity.getEndTime());

            // 累计充值总额
            List<Map<String, Object>> totalOrderSums = wxPayOrderMapper.selectMaps(totalOrderQuery);
            totalOrderSums.forEach(map ->
                    totalOrderSumMap.put(Convert.toInt(map.get("user_id")), Convert.toBigDecimal(map.get("total_fee")))
            );
            // 今日充值总额
            List<Map<String, Object>> todayOrderSums = wxPayOrderMapper.selectMaps(todayOrderQuery);
            todayOrderSums.forEach(map ->
                    todayOrderSumMap.put(Convert.toInt(map.get("user_id")), Convert.toBigDecimal(map.get("total_fee")))
            );
        }

        // 5. 构建树状结构，处理 NULL parent_id
        Map<Integer, List<UserBaseInfo>> parentIdMap = new HashMap<>();
        for (UserBaseInfo user : inviteUsers) {
            Integer parentId = user.getParentId();
            // 如果 user.id == 根节点id 并且 parentId == null，则跳过
            if (user.getId().equals(userInfoId) && parentId == null) {
                continue;
            }
            Integer key = parentId != null ? parentId : 0;
            parentIdMap.computeIfAbsent(key, k -> new ArrayList<>()).add(user);
        }

        // 6. 计算邀请人数统计
        Map<Integer, Long> subInviteSumMap = new HashMap<>();
        Map<Integer, Long> todayInviteSumMap = new HashMap<>();
        Map<Integer, Long> totalInviteSumMap = new HashMap<>();
        for (UserBaseInfo user : inviteUsers) {
            int userId = user.getId();
            // 直接下级人数
            long subInviteSum = parentIdMap.getOrDefault(userId, Collections.emptyList()).size();
            subInviteSumMap.put(userId, subInviteSum);

            // 今日邀请人数（所有下级）
            long todayInviteSum = calculateTodayInviteSum(userId, parentIdMap, today);
            todayInviteSumMap.put(userId, todayInviteSum);

            // 累计邀请人数（递归）
            long totalInviteSum = calculateTotalInviteSum(userId, parentIdMap);
            totalInviteSumMap.put(userId, totalInviteSum);
        }

        // 7. 构建树节点
        TreeNode treeNode = buildTreeNode(userInfoId, identity, inviteUsers, parentIdMap,
                totalOrderSumMap, todayOrderSumMap, subInviteSumMap, todayInviteSumMap, totalInviteSumMap, 0, today);

        log.info("构建邀请树耗时: {} 毫秒", timer.intervalMs());
        return treeNode;
    }

    private long calculateTotalInviteSum(Integer userId, Map<Integer, List<UserBaseInfo>> parentIdMap) {
        List<UserBaseInfo> children = parentIdMap.getOrDefault(userId, Collections.emptyList());
        long total = children.size();
        for (UserBaseInfo child : children) {
            total += calculateTotalInviteSum(child.getId(), parentIdMap);
        }
        return total;
    }

    private long calculateTodayInviteSum(Integer userId, Map<Integer, List<UserBaseInfo>> parentIdMap, Date today) {
        List<UserBaseInfo> children = parentIdMap.getOrDefault(userId, Collections.emptyList());
        long total = children.stream()
                .filter(u -> u.getCreateTime() != null && (u.getCreateTime().after(today) || u.getCreateTime().equals(today)))
                .count();
        for (UserBaseInfo child : children) {
            total += calculateTodayInviteSum(child.getId(), parentIdMap, today);
        }
        return total;
    }

    private BigDecimal calculateTotalOrderSum(Integer userId, Map<Integer, List<UserBaseInfo>> parentIdMap,
                                             Map<Integer, BigDecimal> totalOrderSumMap) {
        BigDecimal total = BigDecimal.ZERO;
        List<UserBaseInfo> children = parentIdMap.getOrDefault(userId, Collections.emptyList());
        for (UserBaseInfo child : children) {
            // 累加子节点的充值总额（包括子节点的直接和间接下级）
            total = total.add(totalOrderSumMap.getOrDefault(child.getId(), BigDecimal.ZERO));
            // 递归累加子节点的子树
            total = total.add(calculateTotalOrderSum(child.getId(), parentIdMap, totalOrderSumMap));
        }
        return total;
    }

    private BigDecimal calculateTodayOrderSum(Integer userId, Map<Integer, List<UserBaseInfo>> parentIdMap,
                                             Map<Integer, BigDecimal> todayOrderSumMap) {
        BigDecimal total = BigDecimal.ZERO;
        List<UserBaseInfo> children = parentIdMap.getOrDefault(userId, Collections.emptyList());
        for (UserBaseInfo child : children) {
            total = total.add(todayOrderSumMap.getOrDefault(child.getId(), BigDecimal.ZERO));
            total = total.add(calculateTodayOrderSum(child.getId(), parentIdMap, todayOrderSumMap));
        }
        return total;
    }

    private TreeNode buildTreeNode(Integer userId, CommissionIdentity identity, List<UserBaseInfo> inviteUsers,
                                   Map<Integer, List<UserBaseInfo>> parentIdMap,
                                   Map<Integer, BigDecimal> totalOrderSumMap,
                                   Map<Integer, BigDecimal> todayOrderSumMap,
                                   Map<Integer, Long> subInviteSumMap,
                                   Map<Integer, Long> todayInviteSumMap,
                                   Map<Integer, Long> totalInviteSumMap,
                                   int level, Date today) {
        TreeNode treeNode = new TreeNode();
        Node node = new Node();

        // 获取当前用户信息
        UserBaseInfo currentUser = inviteUsers.stream()
                .filter(u -> u.getId().equals(userId))
                .findFirst()
                .orElseGet(() -> userBaseInfoMapper.selectById(userId));

        if (currentUser == null) {
            log.warn("User with ID {} not found", userId);
            return treeNode;
        }

        node.setCommType(Objects.equals(currentUser.getId(), identity.getUserInfoId()) ? identity.getType() : "用户");
        node.setNodeId(userId);
        node.setNodeName(currentUser.getAccount() != null ? currentUser.getAccount() : "");
        node.setNodeDesc(currentUser.getNickName() != null ? currentUser.getNickName() : "");
        node.setNodeLevel(level);
        node.setPercentage(Objects.equals(currentUser.getId(), identity.getUserInfoId()) ? identity.getPercentage() : 0);
        node.setCrtOrderSum(totalOrderSumMap.getOrDefault(userId, BigDecimal.ZERO));
        node.setCrtTdOrderSum(todayOrderSumMap.getOrDefault(userId, BigDecimal.ZERO));
        node.setTodayOrderSum(calculateTodayOrderSum(userId, parentIdMap, todayOrderSumMap));
        node.setTotalOrderSum(calculateTotalOrderSum(userId, parentIdMap, totalOrderSumMap));
        node.setSubInviteSum(subInviteSumMap.getOrDefault(userId, 0L));
        node.setTodayInviteSum(todayInviteSumMap.getOrDefault(userId, 0L));
        node.setTotalInviteSum(totalInviteSumMap.getOrDefault(userId, 0L));

        treeNode.setCrtNode(node);
        treeNode.setSubNodeList(new ArrayList<>());

        // 递归构建子节点
        List<UserBaseInfo> children = parentIdMap.getOrDefault(userId, Collections.emptyList());
        for (UserBaseInfo child : children) {
            TreeNode subNode = buildTreeNode(child.getId(), identity, inviteUsers, parentIdMap,
                    totalOrderSumMap, todayOrderSumMap, subInviteSumMap, todayInviteSumMap, totalInviteSumMap, level + 1, today);
            treeNode.getSubNodeList().add(subNode);
        }

        return treeNode;
    }
}