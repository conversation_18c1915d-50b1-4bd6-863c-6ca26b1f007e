package com.hncboy.chatgpt.tarot.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 塔罗牌牌义对象 tarot_card_meaning
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
@TableName("tarot_card_meaning")
@Data
@EqualsAndHashCode(callSuper = false)
public class TarotCardMeaning implements Serializable {
    private static final long serialVersionUID = 1L;

        /** 主键 */
            @TableId(value = "id",type = IdType.AUTO)
        private Integer id;

        /** 名称 */
        private String name;

        /** 含义 */
        private String meaning;

        /** 分类 */
        private String tag;

        /** 指引语 */
        private String guidanceText;

        /** 建议 */
        private String advice;

        /** 不建议 */
        private String discouraged;

        /** 正面图 */
        private String cardFrontUrl;

        private Integer sort;

/*        *//** 背面图 *//*
        private String cardBackUrl;*/

        /** 创建人 */
        private String createBy;

        /** 创建时间 */
        private Date createTime;

        /** 更新人 */
        private String updateBy;

        /** 更新时间 */
        private Date updateTime;
}