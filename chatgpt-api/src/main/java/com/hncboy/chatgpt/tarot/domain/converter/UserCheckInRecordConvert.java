package com.hncboy.chatgpt.tarot.domain.converter;


import com.hncboy.chatgpt.tarot.domain.dto.TarotReadingRecordDTO;
import com.hncboy.chatgpt.tarot.domain.dto.TarotSpreadDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import com.hncboy.chatgpt.tarot.domain.vo.TarotReadingRecordVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotSpreadVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface UserCheckInRecordConvert {

    UserCheckInRecordConvert INSTANCE = Mappers.getMapper(UserCheckInRecordConvert.class);

    TarotReadingRecord dtoToEntity(TarotReadingRecordDTO dto);

    TarotReadingRecordVO entityToVO(TarotReadingRecord entity);

    List<TarotReadingRecordVO> entityListToVOList(List<TarotReadingRecord> entityList);

    TarotReadingRecord queryDtoToEntity(TarotReadingRecordDTO dto);


}
