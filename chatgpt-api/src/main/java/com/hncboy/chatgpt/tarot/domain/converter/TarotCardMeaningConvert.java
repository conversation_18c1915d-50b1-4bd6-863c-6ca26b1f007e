package com.hncboy.chatgpt.tarot.domain.converter;

import com.hncboy.chatgpt.tarot.domain.dto.TarotCardMeaningDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper
public interface TarotCardMeaningConvert {

    TarotCardMeaningConvert INSTANCE = Mappers.getMapper(TarotCardMeaningConvert.class);

    TarotCardMeaning dtoToEntity(TarotCardMeaningDTO dto);

    TarotCardMeaningVO entityToVO(TarotCardMeaning entity);

    List<TarotCardMeaningVO> entityListToVOList(List<TarotCardMeaning> entityList);

    TarotCardMeaning queryDtoToEntity(TarotCardMeaningDTO dto);


}
