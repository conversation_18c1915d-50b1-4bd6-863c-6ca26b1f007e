package com.hncboy.chatgpt.tarot.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface TableConfigMapper {

    @Select("SELECT COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS " +
            "WHERE TABLE_SCHEMA = DATABASE() " +
            "AND TABLE_NAME = #{tableName} " +
            "AND COLUMN_NAME = #{fieldName}")
    String getTypeByTableAndField(String tableName, String fieldName);
}
