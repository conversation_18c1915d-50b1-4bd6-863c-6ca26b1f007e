package com.hncboy.chatgpt.tarot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.tarot.domain.dto.UserCheckInRecordDTO;
import com.hncboy.chatgpt.tarot.domain.entity.UserCheckInRecord;
import com.hncboy.chatgpt.tarot.domain.vo.UserCheckInRecordVO;

import java.util.List;

/**
 * 用户签到记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
public interface UserCheckInRecordMapper extends BaseMapper<UserCheckInRecord> {
    /**
     * 查询用户签到记录列表
     *
     * @param userCheckInRecord 用户签到记录
     * @return 用户签到记录集合
     */
     List<UserCheckInRecordVO> selectUserCheckInRecordList(UserCheckInRecordDTO userCheckInRecord);
     UserCheckInRecord selectDayUserCheck(UserCheckInRecord userCheckInRecord);

}