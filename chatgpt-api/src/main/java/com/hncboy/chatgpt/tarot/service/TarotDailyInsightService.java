package com.hncboy.chatgpt.tarot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.tarot.domain.dto.TarotDailyInsightDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotDailyInsight;
import com.hncboy.chatgpt.tarot.domain.vo.TarotDailyInsightVO;

/**
 * 塔罗今日指引Service接口
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
public interface TarotDailyInsightService extends IService<TarotDailyInsight> {
    /**
     * 查询塔罗今日指引
     *
     * @param id 塔罗今日指引主键
     * @return 塔罗今日指引
     */
    public TarotDailyInsightVO selectTarotDailyInsightById(Long id);

    /**
     * 查询塔罗今日指引列表
     *
     * @param tarotDailyInsight 塔罗今日指引
     * @return 塔罗今日指引集合
     */
//    public List<TarotDailyInsight> selectTarotDailyInsightList(TarotDailyInsight tarotDailyInsight);

    /**
     * 新增塔罗今日指引
     *
     * @param tarotDailyInsight 塔罗今日指引
     * @return 结果
     */
    public TarotDailyInsightVO insertTarotDailyInsight(TarotDailyInsightDTO tarotDailyInsight);

    /**
     * 修改塔罗今日指引
     *
     * @param tarotDailyInsight 塔罗今日指引
     * @return 结果
     */
    public int updateTarotDailyInsight(TarotDailyInsight tarotDailyInsight);

    /**
     * 批量删除塔罗今日指引
     *
     * @param ids 需要删除的塔罗今日指引主键集合
     * @return 结果
     */
    public int deleteTarotDailyInsightByIds(Long[] ids);

    /**
     * 删除塔罗今日指引信息
     *
     * @param id 塔罗今日指引主键
     * @return 结果
     */
    public int deleteTarotDailyInsightById(Long id);
}