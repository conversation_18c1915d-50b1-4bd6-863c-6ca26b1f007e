package com.hncboy.chatgpt.tarot.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hncboy.chatgpt.tarot.domain.entity.User;
import com.hncboy.chatgpt.tarot.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class FacebookAuthService {

    private final UserMapper userMapper;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${facebook.app-id}")
    private String appId;

    @Value("${facebook.app-secret}")
    private String appSecret;

    @Value("${facebook.redirect-uri}")
    private String redirectUri;


    public String getAccessTokenFromCode(String code) throws Exception {
        String tokenUrl = "https://graph.facebook.com/v17.0/oauth/access_token";
        String url = String.format("%s?client_id=%s&client_secret=%s&code=%s&redirect_uri=%s", tokenUrl, appId, appSecret, code, redirectUri);

        String response = restTemplate.getForObject(url, String.class);
        JsonNode node = objectMapper.readTree(response);

        if (node.has("error")) {
            throw new Exception("Failed to get access token: " + node.get("error").get("message").asText());
        }

        return node.get("access_token").asText();
    }

    public Map<String, Object> authenticateUser(String accessToken, String referralCode) throws Exception {
        String graphUrl = "https://graph.facebook.com/v17.0/me";
        String url = String.format("%s?fields=id,name,email,picture&access_token=%s", graphUrl, accessToken);

        String response = restTemplate.getForObject(url, String.class);
        JsonNode node = objectMapper.readTree(response);

        if (node.has("error")) {
            throw new Exception("Failed to get user info: " + node.get("error").get("message").asText());
        }

        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", node.get("id").asLong());
        userInfo.put("name", node.get("name").asText());
        if (node.has("email")) {
            userInfo.put("email", node.get("email").asText());
        }
        if (node.has("picture")) {
            userInfo.put("picture", node.get("picture").get("data").get("url").asText());
        }
        // 查找或创建用户
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("fb_id", userInfo.get("id")));
        if (user == null) {
            user = new User();
            user.setCreatedAt(LocalDateTime.now());
            //新用户送50
            user.setLuckyCoins(50);
            if (StrUtil.isNotEmpty(referralCode)) {
                User userRe = userMapper.selectOne(new QueryWrapper<User>().eq("referral_code", referralCode));
                if (null != userRe) {
                    //给要求也加福币
                    userRe.setLuckyCoins(userRe.getLuckyCoins() + 5);
                    user.setReferrerId(userRe.getId());
                }
            }
        }
        // 更新用户信息
        user.setFbId(String.valueOf(userInfo.get("id")));
        user.setName(String.valueOf(userInfo.get("name")));
        if (userInfo.containsKey("email")) {
            user.setEmail(String.valueOf(userInfo.get("email")));
        }
        if (userInfo.containsKey("picture")) {
            user.setPicture(String.valueOf(userInfo.get("picture")));
        }
        user.setAccessToken(accessToken);
        user.setLastLoginTime(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        if (user.getId() == null) {
            userMapper.insert(user);
        } else {
            userMapper.updateById(user);
        }
        userInfo.put("userId", user.getId());
        return userInfo;
    }
}