package com.hncboy.chatgpt.tarot.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * 塔罗牌占卜记录对象 tarot_reading_record
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Data
public class TarotReadingRecordVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /** 牌阵id */
    private String spreadId;

    /** 问题 */
    private String question;

    /** 答案 */
    private String answer;

    /** 抽牌结果 */
    private String drawResult;

    /** 消耗 */
    private Integer consume;

    private String conversationId;

    private String spreadLayout;

    /**
     * 解读模式 0:抽牌模式1:自选牌模式
     */
    private String interpretationMode;



    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("spreadId", getSpreadId())
            .append("question", getQuestion())
            .append("answer", getAnswer())
            .append("drawResult", getDrawResult())
            .append("consume", getConsume())
                .toString();
    }
}