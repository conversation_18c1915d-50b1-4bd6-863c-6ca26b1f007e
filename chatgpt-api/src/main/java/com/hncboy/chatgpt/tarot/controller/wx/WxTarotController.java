package com.hncboy.chatgpt.tarot.controller.wx;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.dto.WxMiniLoginDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.AlyOssUtils;
import com.hncboy.chatgpt.front.helper.QiNuOssHelper;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import com.hncboy.chatgpt.tarot.domain.dto.WxMiniQrCodeDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.service.WxService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @create 2023-06-03 13:51
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/wx/mini")
@Tag(name = "微信小程序接口")
public class WxTarotController {

    private final WxMaService wxMaService;


    @Operation(summary = "获取小程序码")
    @PostMapping("/getQrCode")
    public R mpLogin(@RequestBody @Validated WxMiniQrCodeDTO wxMiniQrCodeDTO) {

        byte[] wxaCodeUnlimitBytes = null;
        try {
            WxMaCodeLineColor wxMaCodeLineColor = new WxMaCodeLineColor();
            wxMaCodeLineColor.setB(wxMiniQrCodeDTO.getB());
            wxMaCodeLineColor.setG(wxMiniQrCodeDTO.getG());
            wxMaCodeLineColor.setR(wxMiniQrCodeDTO.getR());
            wxaCodeUnlimitBytes = this.wxMaService.getQrcodeService().createWxaCodeUnlimitBytes(
                    wxMiniQrCodeDTO.getScene(), wxMiniQrCodeDTO.getPage(), wxMiniQrCodeDTO.isCheckPath(),
                    wxMiniQrCodeDTO.getEnvVersion(), wxMiniQrCodeDTO.getWidth(),wxMiniQrCodeDTO.isAutoColor()
                    ,wxMaCodeLineColor, wxMiniQrCodeDTO.isHyaline());
        } catch (WxErrorException e) {
            log.error("获取小程序码失败", e);
            return R.fail("获取小程序码失败");
        }
        return R.data(wxaCodeUnlimitBytes);
    }

}
