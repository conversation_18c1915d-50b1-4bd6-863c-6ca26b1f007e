package com.hncboy.chatgpt.tarot.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.tarot.domain.dto.TarotReadingRecordDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.vo.TarotReadingRecordVO;

/**
 * 塔罗牌占卜记录Service接口
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
public interface TarotReadingRecordService extends IService<TarotReadingRecord> {
    /**
     * 查询塔罗牌占卜记录
     *
     * @param id 塔罗牌占卜记录主键
     * @return 塔罗牌占卜记录
     */
    public TarotReadingRecordVO selectTarotReadingRecordById(Long id);

    /**
     * 查询塔罗牌占卜记录列表
     *
     * @param tarotReadingRecord 塔罗牌占卜记录
     * @return 塔罗牌占卜记录集合
     */
    public IPage<TarotReadingRecordVO> selectTarotReadingRecordList(TarotReadingRecordDTO tarotReadingRecord);

    /**
     * 新增塔罗牌占卜记录
     *
     * @param tarotReadingRecord 塔罗牌占卜记录
     * @return 结果
     */
    public int insertTarotReadingRecord(TarotReadingRecord tarotReadingRecord);

    /**
     * 修改塔罗牌占卜记录
     *
     * @param tarotReadingRecord 塔罗牌占卜记录
     * @return 结果
     */
    public int updateTarotReadingRecord(TarotReadingRecord tarotReadingRecord);


    /**
     * 删除塔罗牌占卜记录信息
     *
     * @param id 塔罗牌占卜记录主键
     * @return 结果
     */
    public int deleteTarotReadingRecordById(Long id);
}