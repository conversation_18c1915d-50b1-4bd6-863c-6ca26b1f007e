package com.hncboy.chatgpt.tarot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.front.service.RedisService;
import com.hncboy.chatgpt.tarot.domain.converter.TarotCardMeaningConvert;
import com.hncboy.chatgpt.tarot.domain.converter.TarotReadingRecordConvert;
import com.hncboy.chatgpt.tarot.domain.dto.TarotReadingRecordDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO;
import com.hncboy.chatgpt.tarot.domain.vo.TarotReadingRecordVO;
import com.hncboy.chatgpt.tarot.mapper.TarotReadingRecordMapper;
import com.hncboy.chatgpt.tarot.service.TarotReadingRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 塔罗牌占卜记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Service
public class TarotReadingRecordServiceImpl extends ServiceImpl<TarotReadingRecordMapper, TarotReadingRecord> implements TarotReadingRecordService {

    private final static String KEY_PREFIX = "tarot-record:";
    private final RedisService redisService;

    public TarotReadingRecordServiceImpl(RedisService redisService) {
        this.redisService = redisService;
    }

    /**
     * 查询塔罗牌占卜记录
     *
     * @param id 塔罗牌占卜记录主键
     * @return 塔罗牌占卜记录
     */
    @Override
    public TarotReadingRecordVO selectTarotReadingRecordById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询塔罗牌占卜记录列表
     *
     * @param dto 塔罗牌占卜记录
     * @return 塔罗牌占卜记录
     */
    @Override
    public IPage<TarotReadingRecordVO> selectTarotReadingRecordList(TarotReadingRecordDTO dto) {
        String userId = CurrentUserUtil.getUserId();

        TarotReadingRecord tarotReadingRecord = TarotReadingRecordConvert.INSTANCE.dtoToEntity(dto);
        tarotReadingRecord.setUserId(userId);
        tarotReadingRecord.setStatus("1");
        Page<TarotReadingRecord> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<TarotReadingRecord> iPage = baseMapper.page(userPage, tarotReadingRecord);
        IPage<TarotReadingRecordVO> convert = iPage.convert(TarotReadingRecordConvert.INSTANCE::entityToVO);
        return  convert;
    }

    /**
     * 新增塔罗牌占卜记录
     *
     * @param tarotReadingRecord 塔罗牌占卜记录
     * @return 结果
     */
    @Override
    public int insertTarotReadingRecord(TarotReadingRecord tarotReadingRecord) {
            return baseMapper.insert(tarotReadingRecord);
    }

    /**
     * 修改塔罗牌占卜记录
     *
     * @param tarotReadingRecord 塔罗牌占卜记录
     * @return 结果
     */
    @Override
    public int updateTarotReadingRecord(TarotReadingRecord tarotReadingRecord) {
        return baseMapper.updateById(tarotReadingRecord);
    }


    /**
     * 删除塔罗牌占卜记录信息
     *
     * @param id 塔罗牌占卜记录主键
     * @return 结果
     */
    @Override
    public int deleteTarotReadingRecordById(Long id) {
        TarotReadingRecord tarotReadingRecord = getOne(new LambdaQueryWrapper<TarotReadingRecord>().eq(TarotReadingRecord::getId,id));
        if(tarotReadingRecord==null){
            return 0;
        }
        //倘若有人先删除了，那么接下来的操作就无意义了，
        String key = KEY_PREFIX+id;
        if(!redisService.tryLock(key)){
            return 0;
        }
        try{
            if(tarotReadingRecord.getDeleted()==1){
                return 1;
            }
            tarotReadingRecord.setDeleted((byte) 1);
            int res = update(new UpdateWrapper<TarotReadingRecord>().eq("id",id).set("deleted",1))?1:0;
            redisService.releaseLock(key);
            return res;
        } catch (Exception e) {
            redisService.releaseLock(key);
            throw new ServiceException(e.getMessage());
        }

    }



}