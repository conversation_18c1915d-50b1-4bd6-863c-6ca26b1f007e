package com.hncboy.chatgpt.tarot.helper;

import cn.hutool.core.collection.CollectionUtil;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessV2Request;
import com.hncboy.chatgpt.front.handler.config.SensitiveWordHandler;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/29 11:58
 * 敏感词检测
 */
public class SensitiveWordEmitterChain {

    public static boolean doChain(ChatProcessRequest request) {
        List<String> prompts = SensitiveWordHandler.checkWord(request.getPrompt());
        if (CollectionUtil.isNotEmpty(prompts)) {
            return false;
        }
        return true;
    }

    public static boolean doChain(ChatProcessV2Request request) {
        List<String> prompts = SensitiveWordHandler.checkWord(request.getPrompt());
        if (CollectionUtil.isNotEmpty(prompts)) {
            return false;
        }
        return true;
    }

    public static boolean doChain(String request) {
        List<String> prompts = SensitiveWordHandler.checkWord(request);
        return !CollectionUtil.isNotEmpty(prompts);
    }

}
