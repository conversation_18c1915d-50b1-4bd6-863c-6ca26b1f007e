package com.hncboy.chatgpt.tarot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.tarot.domain.dto.TarotDailyInsightDTO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotDailyInsight;
import com.hncboy.chatgpt.tarot.domain.vo.TarotDailyInsightVO;


/**
 * 塔罗今日指引Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-12
 */
public interface TarotDailyInsightMapper extends BaseMapper<TarotDailyInsight> {


    TarotDailyInsightVO selectTarotDailyInsightById(Long id);
    TarotDailyInsightVO selectDayTarotDailyInsight(TarotDailyInsightDTO insight);
}