package com.hncboy.chatgpt.tarot.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.query.LoginInfoParam;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.tarot.domain.entity.User;
import com.hncboy.chatgpt.tarot.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class GoogleAuthService {

    private final UserMapper userMapper;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    private final UserBaseInfoService userBaseInfoService;

    @Value("${google.client-id}")
    private String clientId;

    @Value("${google.client-secret}")
    private String clientSecret;

    @Value("${google.local-domain}")
    private String localDomain;

    public String getAccessTokenFromCode(String code) throws Exception {
        String tokenUrl = "https://oauth2.googleapis.com/token";

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("client_id", clientId);
        requestBody.put("client_secret", clientSecret);
        requestBody.put("code", code);
        requestBody.put("redirect_uri", localDomain + "/api/auth/google/callback");
        requestBody.put("grant_type", "authorization_code");
        log.info("Request body: " + requestBody);
        ResponseEntity<String> response = restTemplate.postForEntity(tokenUrl, requestBody, String.class);
        JsonNode node = objectMapper.readTree(response.getBody());

        if (node.has("error")) {
            throw new Exception("Failed to get access token: " + node.get("error_description").asText());
        }

        return node.get("access_token").asText();
    }

    public Map<String, Object> authenticateUser(String accessToken, String referralCode) throws Exception {
        String userInfoUrl = "https://www.googleapis.com/oauth2/v3/userinfo";
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + accessToken);

        ResponseEntity<String> response = restTemplate.getForEntity(userInfoUrl + "?access_token=" + accessToken, String.class);
        JsonNode node = objectMapper.readTree(response.getBody());

        if (node.has("error")) {
            throw new Exception("获取用户信息失败: " + node.get("error").get("message").asText());
        }

        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("id", node.get("sub").asText());
        userInfo.put("name", node.get("name").asText());
        if (node.has("email")) {
            userInfo.put("email", node.get("email").asText());
        }
        if (node.has("picture")) {
            userInfo.put("picture", node.get("picture").asText());
        }

        // 查找或创建用户
        User user = userMapper.selectOne(new QueryWrapper<User>().eq("google_id", userInfo.get("id")));
        if (user == null) {
            // 检查用户是否已使用其他方法注册（如Facebook）
            if (userInfo.containsKey("email")) {
                User existingUser = userMapper.selectOne(new QueryWrapper<User>().eq("email", userInfo.get("email")));
                if (existingUser != null) {
                    // 将Google帐户链接到现有帐户
                    user = existingUser;
                }
            }

            if (user == null) {
                //创建新用户
                user = new User();
                user.setCreatedAt(LocalDateTime.now());
            }
        }

        // 更新用户信息
        user.setGoogleId(String.valueOf(userInfo.get("id")));
        user.setName(String.valueOf(userInfo.get("name")));
        if (userInfo.containsKey("email")) {
            user.setEmail(String.valueOf(userInfo.get("email")));
        }
        if (userInfo.containsKey("picture")) {
            user.setPicture(String.valueOf(userInfo.get("picture")));
        }
        user.setAccessToken(accessToken);
        user.setLastLoginTime(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        UserBaseInfoVO baseUser;
        boolean isNew = false;
        if (user.getId() == null) {
            userMapper.insert(user);
            baseUser = null;
            isNew = true;
        } else {
            baseUser = userBaseInfoService.getUserBaseInfoByUsersId(user.getId());
            if (baseUser != null) {
                Integer existedId = null;
                try{
                    existedId = StpUtil.getLoginIdAsInt();
                } catch (Exception ignored){
                }
                if(baseUser.getId().equals(existedId)){
                    //已经登录了
                    userInfo.put("userId", baseUser.getId());
                    return userInfo;
                }
            }
            userMapper.updateById(user);
        }

        UserBaseInfoVO userBaseInfoVO = associatedUser(user, referralCode, baseUser, isNew);
        userInfo.put("userId", userBaseInfoVO.getId());
        return userInfo;
    }

    //关联创建用户
    private UserBaseInfoVO associatedUser(User user, String referralCode, UserBaseInfoVO userBaseInfoByUsersId, boolean isNew) {
        if (null == userBaseInfoByUsersId) {
            LoginInfoParam loginInfoParam = new LoginInfoParam();
            loginInfoParam.setNickName(user.getName());
            loginInfoParam.setHeadImgUrl(user.getPicture());
            String uuId = RandomUtil.randomString(8);
            loginInfoParam.setAccount("gg_" + uuId);
            loginInfoParam.setUsersId(user.getId());
            loginInfoParam.setParentId(referralCode);
            userBaseInfoByUsersId = userBaseInfoService.initUserInfo3(loginInfoParam, isNew);
        if (isNew && ObjectUtil.isNotEmpty(userBaseInfoByUsersId.getParentId())){
            userBaseInfoService.addUserPoints(userBaseInfoByUsersId.getParentId(), loginInfoParam.getAccount(),"tarot_invite_points");
        }
        }
        //登录
        StpUtil.login(userBaseInfoByUsersId.getId());
        //会话相关
        StpUtil.getSession().set(ApplicationConstant.USER, userBaseInfoByUsersId);
        userBaseInfoByUsersId.setToken(StpUtil.getTokenValue());
        return userBaseInfoByUsersId;
    }

} 