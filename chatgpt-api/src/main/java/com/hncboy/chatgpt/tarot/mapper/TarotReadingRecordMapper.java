package com.hncboy.chatgpt.tarot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.vo.TarotReadingRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * 塔罗牌占卜记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Mapper
public interface TarotReadingRecordMapper extends BaseMapper<TarotReadingRecord> {
    /**
     * 查询塔罗牌占卜记录列表
     *
     * @param tarotReadingRecord 塔罗牌占卜记录
     * @return 塔罗牌占卜记录集合
     */
    IPage<TarotReadingRecord> page(Page<TarotReadingRecord> page, @Param("e") TarotReadingRecord tarotReadingRecord);
    TarotReadingRecordVO selectById(Long id);

}