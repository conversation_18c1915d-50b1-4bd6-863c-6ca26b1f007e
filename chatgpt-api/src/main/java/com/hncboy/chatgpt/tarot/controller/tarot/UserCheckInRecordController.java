package com.hncboy.chatgpt.tarot.controller.tarot;


import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.tarot.domain.dto.UserCheckInRecordDTO;
import com.hncboy.chatgpt.tarot.domain.entity.UserCheckInRecord;
import com.hncboy.chatgpt.tarot.domain.vo.UserCheckInRecordVO;
import com.hncboy.chatgpt.tarot.service.UserCheckInRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户签到记录Controller
 * 
 * <AUTHOR>
 * @date 2024-09-12
 */
@Slf4j
@FrontPreAuth
@AllArgsConstructor
@RestController
@Tag(name = "用户签到记录")
@RequestMapping("/tarot/checkInRecord")
public class UserCheckInRecordController {

    private final UserCheckInRecordService userCheckInRecordService;

    /**
     * 获取近期签到记录
     */
    @Operation(summary = "获取近期签到记录")
    @PostMapping("/list")
    public R<List<UserCheckInRecordVO>> list(@RequestBody UserCheckInRecordDTO userCheckInRecord)
    {
        List<UserCheckInRecordVO> userCheckInRecords = userCheckInRecordService.selectUserCheckInRecordList(userCheckInRecord);
        return R.data(userCheckInRecords);
    }

    /**
     * 生成签到记录
     */
    @Operation(summary = "签到")
    @PostMapping("/add")
    public R add(@RequestBody UserCheckInRecord userCheckInRecord) {
        userCheckInRecordService.insertUserCheckInRecord(userCheckInRecord);
        return R.success();
    }

    /**
     * 生成签到记录
     */
    @Operation(summary = "获取签到奖励")
    @GetMapping("/getCheckAwarded")
    public R getCheckAwarded() {
        int checkAwarded = userCheckInRecordService.getCheckAwarded();
        return R.data(checkAwarded);
    }





}
