package com.hncboy.chatgpt.tarot.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("transaction") // 指定数据库表名
public class Transaction {

    @TableId // 设置主键
    private Integer id; // 交易ID

    private String gateway; // 银行网关名称
    private String transactionDate; // 交易时间
    private String accountNumber; // 银行账号
    private String code; // 付款代码
    private String content; // 转账内容
    private String transferType; // 交易类型：进或出
    private Double transferAmount; // 交易金额
    private Double accumulated; // 累计账户余额
    private String subAccount; // 子账户
    private String referenceCode; // 短信参考代码
    private String description; // 短信内容
    private String productId; // 产品ID
    private String uniqueId; // 唯一ID
}
