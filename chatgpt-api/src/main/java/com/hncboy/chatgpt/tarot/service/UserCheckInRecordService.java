package com.hncboy.chatgpt.tarot.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.tarot.domain.dto.UserCheckInRecordDTO;
import com.hncboy.chatgpt.tarot.domain.entity.UserCheckInRecord;
import com.hncboy.chatgpt.tarot.domain.vo.UserCheckInRecordVO;

/**
 * 用户签到记录Service接口
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
public interface UserCheckInRecordService extends IService<UserCheckInRecord> {
    /**
     * 查询用户签到记录
     *
     * @param id 用户签到记录主键
     * @return 用户签到记录
     */
    public UserCheckInRecord selectUserCheckInRecordById(Long id);

    /**
     * 查询用户签到记录列表
     *
     * @param userCheckInRecord 用户签到记录
     * @return 用户签到记录集合
     */
    public List<UserCheckInRecordVO> selectUserCheckInRecordList(UserCheckInRecordDTO userCheckInRecord);

    /**
     * 新增用户签到记录
     *
     * @param userCheckInRecord 用户签到记录
     * @return 结果
     */
    public int insertUserCheckInRecord(UserCheckInRecord userCheckInRecord);
    public int getCheckAwarded();

    /**
     * 修改用户签到记录
     *
     * @param userCheckInRecord 用户签到记录
     * @return 结果
     */
    public int updateUserCheckInRecord(UserCheckInRecord userCheckInRecord);


    /**
     * 删除用户签到记录信息
     *
     * @param id 用户签到记录主键
     * @return 结果
     */
    public int deleteUserCheckInRecordById(Long id);
}