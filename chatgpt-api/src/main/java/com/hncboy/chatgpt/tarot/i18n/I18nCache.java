package com.hncboy.chatgpt.tarot.i18n;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hncboy.chatgpt.tarot.domain.entity.TarotI18n;
import com.hncboy.chatgpt.tarot.enums.LanguageCode;
import com.hncboy.chatgpt.tarot.service.TarotI18nService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class I18nCache {

    private static final ThreadLocal<String> LANG_CONTEXT = new ThreadLocal<>();

    // 设置当前语言的默认值
    public static void setLang(String lang) {
        LANG_CONTEXT.set(lang);
    }

    // 获取当前语言的值
    public static String getLang() {
        return LANG_CONTEXT.get();
    }

    // 清除当前语言的值，防止内存泄漏
    public static void clear() {
        LANG_CONTEXT.remove();
    }

    /**
     * 获取 翻译值
     * @param key
     * @return
     */
    public static String getValue(String key) {
        String internationalizedValue=null;
        String lang = getLang();
        //中文直接返回
        if(LanguageCode.CHINESE.getCode().equals(lang)) {
            return key;
        }
        String redisKey = "i18n:" + getLang() + ":" + key; // Redis 键前缀
        try{
            StringRedisTemplate redisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
            internationalizedValue = redisTemplate.opsForValue().get(redisKey);

            if (internationalizedValue == null) {
                internationalizedValue = fetchFromDatabase(lang, key);
                if(!key.equals(internationalizedValue)){
                    redisTemplate.opsForValue().set(redisKey, internationalizedValue, 1, TimeUnit.DAYS);
                }
            }
        }catch (Exception e){
            log.error("I18nCache:redis缓存查询错误：直接查询数据库！{}",e);
            internationalizedValue = fetchFromDatabase(lang, key);
        }

        return internationalizedValue;
    }

    private static String fetchFromDatabase(String lang, String code) {
        try {
            TarotI18nService tarotI18nService = SpringUtil.getBean(TarotI18nService.class);
            List<TarotI18n> list = tarotI18nService.list(
                    new LambdaQueryWrapper<TarotI18n>()
                            .eq(TarotI18n::getLang, lang)
                            .eq(TarotI18n::getCode, code)
                            .orderByDesc(TarotI18n::getCreateTime));
            TarotI18n i18nRecord=null;
            if(!list.isEmpty()){
                i18nRecord=list.get(0);
            }
            return (i18nRecord != null) ? i18nRecord.getValue() : code; // 如果没找到，则返回原始代码
        } catch (Exception e) {
            log.error("从数据库获取国际化字符串时发生错误", e);
            return code; // 发生错误时返回原始代码
        }
    }
}
