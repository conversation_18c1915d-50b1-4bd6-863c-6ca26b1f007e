package com.hncboy.chatgpt.tarot.config;

import com.hncboy.chatgpt.tarot.interceptor.LanguageInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    private final LanguageInterceptor languageInterceptor;

    @Autowired
    public WebConfig(LanguageInterceptor languageInterceptor) {
        this.languageInterceptor = languageInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册拦截器，对所有请求生效
        registry.addInterceptor(languageInterceptor).addPathPatterns("/**");
    }
}