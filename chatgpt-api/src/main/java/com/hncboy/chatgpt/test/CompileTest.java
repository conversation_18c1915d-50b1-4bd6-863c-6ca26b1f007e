package com.hncboy.chatgpt.test;

import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.framework.auth.worker.AuthWorker;
import com.hncboy.chatgpt.framework.pay.worker.UnifiedPayWorker;
import com.hncboy.chatgpt.framework.cache.manager.MultiLevelCacheManager;
import com.hncboy.chatgpt.framework.i18n.worker.I18nWorker;
import com.hncboy.chatgpt.biz.tarot.worker.TarotReadingWorker;
import com.hncboy.chatgpt.biz.chatoi.worker.ChatWorker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 编译测试类 - 验证所有依赖是否正确
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Component
public class CompileTest {

    @Autowired(required = false)
    private AuthWorker authWorker;

    @Autowired(required = false)
    private UnifiedPayWorker unifiedPayWorker;

    @Autowired(required = false)
    private MultiLevelCacheManager multiLevelCacheManager;

    @Autowired(required = false)
    private I18nWorker i18nWorker;

    @Autowired(required = false)
    private TarotReadingWorker tarotReadingWorker;

    @Autowired(required = false)
    private ChatWorker chatWorker;

    public void testCompile() {
        System.out.println("编译测试通过！");
        
        // 测试Sa-Token
        try {
            boolean isLogin = StpUtil.isLogin();
            System.out.println("Sa-Token测试通过: " + isLogin);
        } catch (Exception e) {
            System.out.println("Sa-Token测试失败: " + e.getMessage());
        }
        
        // 测试各个Worker
        if (authWorker != null) {
            System.out.println("AuthWorker注入成功");
        }
        
        if (unifiedPayWorker != null) {
            System.out.println("UnifiedPayWorker注入成功");
        }
        
        if (multiLevelCacheManager != null) {
            System.out.println("MultiLevelCacheManager注入成功");
        }
        
        if (i18nWorker != null) {
            System.out.println("I18nWorker注入成功");
        }
        
        if (tarotReadingWorker != null) {
            System.out.println("TarotReadingWorker注入成功");
        }
        
        if (chatWorker != null) {
            System.out.println("ChatWorker注入成功");
        }
    }
}
