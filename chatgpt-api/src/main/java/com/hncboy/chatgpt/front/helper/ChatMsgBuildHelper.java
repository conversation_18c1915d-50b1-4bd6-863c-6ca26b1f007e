package com.hncboy.chatgpt.front.helper;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hncboy.chatgpt.front.api.apikey.ApiKeyChatClientBuilder;
import com.hncboy.chatgpt.front.framework.config.ChatConfig;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;
import com.hncboy.chatgpt.front.framework.domain.entity.HomeConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.RoleMsgTemplate;
import com.hncboy.chatgpt.front.framework.domain.request.BuildChatProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.vo.ChatReplyMessageVO;
import com.hncboy.chatgpt.front.framework.enums.ApiKeyModelEnum;
import com.hncboy.chatgpt.front.framework.enums.ChatMessageStatusEnum;
import com.hncboy.chatgpt.front.framework.enums.ChatMessageTypeEnum;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.service.*;
import com.unfbx.chatgpt.entity.chat.*;
import com.unfbx.chatgpt.entity.images.Image;
import com.unfbx.chatgpt.entity.images.ImageResponse;
import com.unfbx.chatgpt.utils.TikTokensUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import javax.websocket.Session;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.USER_IMAGE_COUNT;
import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.USER_NOW_COUNT;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ChatMsgBuildHelper {

    private final ChatConfig chatConfig;
    private final ChatMessageService chatMessageService;
    private final HomeConfigService homeConfigService;
    private final RoleMsgTemplateService roleMsgTemplateService;
    private final RedisService redisService;
    private final WxUserInfoService wxUserInfoService;


    @NotNull
    public BuildChatProcessRequest buildMessageBody(ChatProcessRequest chatProcessRequest) {
        // 所有消息
        LinkedList<Message> messages = new LinkedList<>();
        // 添加用户上下文消息
        addContextChatMessage(chatProcessRequest, messages);
        //最后添加本条消息
        messages.addLast(Message.builder().role(BaseMessage.Role.USER)
                .content(chatProcessRequest.getPrompt())
                .build());
        if (Objects.nonNull(chatProcessRequest.getChatRoomDO().getRoleId())) {
            HomeConfig byId = homeConfigService.getHomeConfigById(chatProcessRequest.getChatRoomDO().getRoleId());
            if (StringUtils.isNotEmpty(byId.getSysContent())) {
                messages.addFirst(Message.builder().role(BaseMessage.Role.SYSTEM)
                        .content(byId.getSysContent())
                        .build());
            }
        } else if (StrUtil.isNotBlank(chatProcessRequest.getChatRoomDO().getDescription())) {
            // 系统消息
            Message systemMessage = Message.builder().role(Message.Role.SYSTEM)
                    .content(chatProcessRequest.getChatRoomDO().getDescription()).build();
            messages.addFirst(systemMessage);
        }
        int totalTokenCount = TikTokensUtil.tokens(chatConfig.getOpenaiApiModel(), messages);
        //token监测
        totalTokenCount = getTotalTokenCount(messages, totalTokenCount, chatProcessRequest);
        // 插入此次聊天记录
        ChatMessageDO resDo = getChatMessageDO(chatProcessRequest, totalTokenCount, ChatMessageTypeEnum.QUESTION.getCode());
        //放入可用次数
        setRedisNum(USER_NOW_COUNT, chatProcessRequest.getOpenId());
        return BuildChatProcessRequest.builder()
                .messages(messages)
                .msgId(resDo.getId())
                .tokens(totalTokenCount)
                .build();
    }


    /**
     * 插入此次聊天记录
     *
     * @param chatProcessRequest
     * @param totalTokenCount
     * @return ChatMessageDO
     */
    private ChatMessageDO getChatMessageDO(ChatProcessRequest chatProcessRequest, Integer totalTokenCount, Integer msgType) {
        ChatMessageDO chatMessageDO = new ChatMessageDO();
        chatMessageDO.setMessageType(msgType);
        if (Objects.nonNull(chatProcessRequest.getChatRoomDO())) {
            chatMessageDO.setChatRoomId(chatProcessRequest.getChatRoomDO().getId());
        }
        chatMessageDO.setOpenId(StringUtils.isNotEmpty(chatProcessRequest.getOpenId()) ?
                chatProcessRequest.getOpenId() : CurrentUserUtil.getUserId());
        chatMessageDO.setContent(chatProcessRequest.getPrompt());
        chatMessageDO.setIp(chatProcessRequest.getIp());
        chatMessageDO.setTotalTokens(totalTokenCount);
        ChatMessageDO resDo = chatMessageService.initChatMessage(chatMessageDO);
        return resDo;
    }


    /**
     * 保存系统回答
     *
     * @param msg
     * @param chatProcessRequest
     * @return ChatMessageDO
     * @Author: zc.wu
     * @Date: 2024/1/12 0012 下午 01:17
     */
    @Async
    public ChatMessageDO saveSysChatMsg(String msg, ChatProcessRequest chatProcessRequest) {
        // 插入此次聊天记录
        ChatMessageDO chatMessageDO = new ChatMessageDO();
        chatMessageDO.setMessageType(ChatMessageTypeEnum.ANSWER.getCode());
        if (Objects.nonNull(chatProcessRequest.getChatRoomDO())) {
            chatMessageDO.setChatRoomId(chatProcessRequest.getChatRoomDO().getId());
        }
        chatMessageDO.setOpenId(chatProcessRequest.getOpenId());
        chatMessageDO.setContent(chatProcessRequest.getPrompt());
        chatMessageDO.setIp(chatProcessRequest.getIp());
        chatMessageDO.setParentMsgId(chatProcessRequest.getMsgId());
        chatMessageDO.setContent(msg);
        chatMessageDO.setStatus(ChatMessageStatusEnum.PART_SUCCESS.getCode());
        ChatMessageDO chatMessageParent = new ChatMessageDO();
        chatMessageParent.setId(chatProcessRequest.getMsgId());
        chatMessageParent.setStatus(ChatMessageStatusEnum.PART_SUCCESS.getCode());
        chatMessageService.updateById(chatMessageParent);
        return chatMessageService.initChatMessage(chatMessageDO);
    }


    /**
     * 检查 tokenCount 是否超出当前模型的 Token 数量限制
     *
     * @param messages
     * @param totalTokenCount
     * @return int
     * @Author: zc.wu
     * @Date: 2024/1/11 0011 下午 05:51
     */
    private int getTotalTokenCount(LinkedList<Message> messages, int totalTokenCount, ChatProcessRequest chatProcessRequest) {
        int maxTokens = ApiKeyModelEnum.maxTokens(chatConfig.getOpenaiApiModel());
        int currentPromptTokens = TikTokensUtil.tokens(chatConfig.getOpenaiApiModel(), chatProcessRequest.getPrompt());

        log.info("当前token数量{}，最大token:{}:currentPromptTokens:{}", totalTokenCount, maxTokens, currentPromptTokens);
        if (maxTokens <= totalTokenCount) {
            int remainingTokens = totalTokenCount - currentPromptTokens;
            if (maxTokens <= remainingTokens) {
                log.info("当前上下文字数已经达到上限，请减少上下文关联的条数", totalTokenCount, maxTokens);
            } else {
                log.info("当前上下文 Token 数量：{}，超过上限：{}，请减少字数发送或减少上下文关联的条数", totalTokenCount, maxTokens);
            }
            List<Message> messagesToRemove = new ArrayList<>();
            for (Message message : messages) {
                if (message.getRole().equals(Message.Role.USER.getName())) {
                    messagesToRemove.add(message);
                }
            }
            // 在循环之外移除识别到的消息
            messages.removeAll(messagesToRemove);
            // 删除消息后重新计算 totalTokenCount
            totalTokenCount = TikTokensUtil.tokens(ChatCompletion.Model.GPT_4.getName(), messages);
            // 递归调用以继续检查总令牌数量是否在限制范围内
            return this.getTotalTokenCount(messages, totalTokenCount, chatProcessRequest);
        }
        return totalTokenCount;
    }


    /**
     * 添加上下文问题消息
     *
     * @param chatProcessRequest 当前消息
     * @param messages           消息列表
     */
    private void addContextChatMessage(ChatProcessRequest chatProcessRequest, LinkedList<Message> messages) {
        // 根据消息类型去选择角色，需要添加问题和回答到上下文
        LambdaQueryWrapper<ChatMessageDO> wrapper = new LambdaQueryWrapper<ChatMessageDO>()
                .eq(ChatMessageDO::getOpenId, chatProcessRequest.getOpenId())
                .eq(ChatMessageDO::getChatRoomId, chatProcessRequest.getChatRoomDO().getId());
        wrapper.orderByDesc(ChatMessageDO::getUpdateTime);
        wrapper.last("limit 6");
        List<ChatMessageDO> parentMessage = chatMessageService.list(wrapper);
        parentMessage.stream().filter(item -> {
            //过滤不成功的消息
            if (item.getMessageType() == 1 && item.getStatus()
                    .equals(ChatMessageStatusEnum.PART_SUCCESS.getCode())) {
                return true;
            } else if (item.getMessageType() == 1 && item.getStatus()
                    .equals(ChatMessageStatusEnum.INIT.getCode())) {
                return false;
            }
            return true;
        }).forEach(item -> {
            Message.Role role = item.getMessageType() == 1 ? Message.Role.USER : Message.Role.ASSISTANT;
            // 从下往上找并添加，越上面的数据放越前面
            messages.addFirst(Message.builder().role(role)
                    .content(item.getContent())
                    .build());
        });
    }


    /**
     * 添加模版训练内容
     *
     * @param roleId
     * @param messages
     * @return void
     * @Author: zc.wu
     * @Date: 2023/8/2 0002 下午 06:02
     */
    private void addTemplateContent(Integer roleId, LinkedList<Message> messages) {
        List<RoleMsgTemplate> roleMsgTemplates = roleMsgTemplateService.queryRoleMsgTemplateList(roleId);
        roleMsgTemplates.forEach(item -> {
            Message.Role role = (item.getType().equals(ChatMessageTypeEnum.ANSWER.getCode())) ?
                    Message.Role.ASSISTANT : Message.Role.USER;
            // 从下往上找并添加，越上面的数据放越前面
            messages.addFirst(Message.builder().role(role)
                    .content(item.getContent())
                    .build());
        });
    }


    /**
     * 图片识别
     *
     * @return void
     * @Author: zc.wu
     * @Date: 2023/8/2 0002 下午 06:02
     */
    public BuildChatProcessRequest imageRecognition(ChatProcessRequest chatProcessRequest) {
        if (StrUtil.isEmpty(chatProcessRequest.getFilePath())) {
            return null;
        }

        Content textContent = Content.builder().text(chatProcessRequest.getPrompt()).type(Content.Type.TEXT.getName()).build();
        ImageUrl imageUrl = ImageUrl.builder().url(chatProcessRequest.getFilePath() + "?imageView2/1/w/500/h/500").build();
        Content imageContent = Content.builder().imageUrl(imageUrl).type(Content.Type.IMAGE_URL.getName()).build();

        List<Content> contentList = new ArrayList<>();
        contentList.add(textContent);
        contentList.add(imageContent);
        ChatMessageDO resDo = getChatMessageDO(chatProcessRequest, null, ChatMessageTypeEnum.IDENTIFY_IMAGES.getCode());
        return BuildChatProcessRequest.builder()
                .contentList(contentList)
                .msgId(resDo.getId())
                .build();
    }


    /**
     * 图片请求
     *
     * @param chatProcessRequest
     * @return String
     * @Author: zc.wu
     * @Date: 2024/1/15 0015 下午 02:16
     */
    @Transactional(rollbackFor = Exception.class)
    public String buildImageMsg(ChatProcessRequest chatProcessRequest) {
        String redisKey = "GENERATE_IMAGES" + chatProcessRequest.getPrompt();
        if (redisService.exists(redisKey)) {
            return (String) redisService.get(redisKey);
        }
        Image image = Image.builder()
                .responseFormat(com.unfbx.chatgpt.entity.images.ResponseFormat.B64_JSON.getName())
                .model(Image.Model.DALL_E_3.getName())
                .prompt(chatProcessRequest.getPrompt())
                .n(1)
                .quality(Image.Quality.STANDARD.getName())
                .size(chatProcessRequest.getImage().getSize())
                .style(chatProcessRequest.getImage().getStyle())
                .build();
        ImageResponse imageResponse = ApiKeyChatClientBuilder.buildOpenAiClient().genImages(image);
        wxUserInfoService.updateUserNum(CurrentUserUtil.getUserId(), 2);
        String base64Data = imageResponse.getData().get(0).getB64Json();
        byte[] bytes = Base64.decodeBase64(base64Data);
        String url = QiNuOssHelper.qnyUpload(UUID.randomUUID() + ".jpg", bytes);
        if (StrUtil.isNotEmpty(url)) {
            redisService.set(redisKey, url, 60L * 60L * 24L);
        }
        ChatMessageDO chatMessageDO = new ChatMessageDO();
        chatMessageDO.setMessageType(ChatMessageTypeEnum.GENERATE_IMAGES.getCode());
        if (Objects.nonNull(chatProcessRequest.getChatRoomDO())) {
            chatMessageDO.setChatRoomId(chatProcessRequest.getChatRoomDO().getId());
        }
        chatMessageDO.setOpenId(StringUtils.isNotEmpty(chatProcessRequest.getOpenId())
                ? chatProcessRequest.getOpenId() : CurrentUserUtil.getUserId());
        chatMessageDO.setContent(chatProcessRequest.getPrompt());
        chatMessageDO.setIp(chatProcessRequest.getIp());
        chatMessageDO.setRemark(url);
        chatMessageService.initChatMessage(chatMessageDO);
        setRedisNum(USER_IMAGE_COUNT, chatProcessRequest.getOpenId());
        return url;
    }


    /**
     * api调用构建消息回复
     *
     * @param chatProcessRequest
     * @return String
     * @Author: zc.wu
     * @Date: 2024/1/31 0031 下午 02:53
     */
    public String buildApiMsg(ChatProcessRequest chatProcessRequest) {
        LinkedList<Message> messages = new LinkedList<>();
        Message message = Message.builder().role(Message.Role.USER).content(chatProcessRequest.getPrompt()).build();
        messages.add(message);
        Message messageSys = Message.builder().role(Message.Role.SYSTEM).content(
                (StrUtil.isEmpty(chatProcessRequest.getSysContent()) ? ApplicationConstant.imagePrompts : chatProcessRequest.getSysContent())
        ).build();
        messages.addFirst(messageSys);
        ChatCompletion chatCompletion = ChatCompletion.builder().messages(messages).build();
        ChatCompletionResponse chatCompletionResponse = ApiKeyChatClientBuilder.buildOpenAiClient().chatCompletion(chatCompletion);
        AtomicReference<String> res = new AtomicReference<>("");
        chatCompletionResponse.getChoices().forEach(e -> {
            res.set(e.getMessage().getContent());
        });
        ChatMessageDO chatMessageDO = new ChatMessageDO();
        chatMessageDO.setMessageType(ChatMessageTypeEnum.GENERATE_IMAGES.getCode());
        if (Objects.nonNull(chatProcessRequest.getChatRoomDO())) {
            chatMessageDO.setChatRoomId(chatProcessRequest.getChatRoomDO().getId());
        }
        chatMessageDO.setOpenId(StringUtils.isNotEmpty(chatProcessRequest.getOpenId())
                ? chatProcessRequest.getOpenId() : CurrentUserUtil.getUserId());
        chatMessageDO.setContent(chatProcessRequest.getPrompt());
        chatMessageDO.setIp(chatProcessRequest.getIp());
        chatMessageService.initChatMessage(chatMessageDO);
        setRedisNum(USER_IMAGE_COUNT, chatProcessRequest.getOpenId());
        return res.get();
    }


    private void setRedisNum(String userImageCount, String openId) {
        //放入可用次数
        String key = userImageCount + openId;
        if (redisService.exists(key)) {
            Integer operateNum = (Integer) redisService.get(key);
            redisService.set(key, operateNum + 1);
        } else {
            redisService.set(key, 1);
        }
    }


    @Async
    public void convertTextToSpeech(String text, String voice, Session session) {
        String key = "tts:" + text;
        String res = "";
        if (redisService.exists(key)) {
            res = (String) redisService.get(key);
        }
        ChatTTSHelper chatTTSHelper = new ChatTTSHelper();
        CompletableFuture<String> stringCompletableFuture = chatTTSHelper.textToSpeed(text, voice);
        try {
            res = stringCompletableFuture.get();
            if (StrUtil.isNotEmpty(res)) {
                redisService.set(key, stringCompletableFuture.get());
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        if (StrUtil.isNotEmpty(res)) {
            try {
                session.getBasicRemote().sendText(ChatReplyMessageVO.buildSpeakMessage(res));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


}
