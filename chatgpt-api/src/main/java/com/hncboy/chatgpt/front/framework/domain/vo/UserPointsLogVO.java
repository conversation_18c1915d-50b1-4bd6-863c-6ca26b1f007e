package com.hncboy.chatgpt.front.framework.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 积分明细 VO
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/8/8
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserPointsLogVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "用户ID")
    private String userId;

    @Schema(title = "受邀人手机号")
    private String account;

    @Schema(title = "产品名称")
    private String productName;

    @Schema(title = "积分")
    private String points;

    @Schema(title = "积分类型")
    private String pointsType;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "昵称")
    private String nickName;

}
