package com.hncboy.chatgpt.front.framework.domain.vo;

import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Map;

/**
 * 支付订单信息 VO
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "WxPayOrder对象", description = "支付订单信息")
public class WxPayOrderVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "商品ID")
    private Integer goodsId;

    @Schema(title = "商品描述")
    private String body;

    @Schema(title = "商户订单号")
    private String outTradeNo;

    @Schema(title = "总金额")
    private Double totalFee;

    @Schema(title = "终端IP")
    private String spbillCreateIp;

    @Schema(title = "通知地址")
    private String notifyUrl;

    @Schema(title = "交易类型")
    private String tradeType;

    @Schema(title = "用户标识")
    private String openid;


    @Schema(title = "是否关注公众账号")
    private String isSubscribe;

    @Schema(title = "微信支付订单号")
    private String transactionId;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "状态")
    private Integer status;
    

    @Schema(title = "拉起支付所需参数")
    private WxPayMpOrderResult wxPayMpOrderResult;

    @Schema(title = "拉起支付所需参数(通用)")
    private Map<String, Object> payExtraMap;


}
