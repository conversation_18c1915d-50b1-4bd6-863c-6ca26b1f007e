//package com.hncboy.chatgpt.front.helper;
//
//import cn.hutool.core.util.RandomUtil;
//import cn.hutool.crypto.SecureUtil;
//import com.hncboy.chatgpt.base.domain.request.MqMsg;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.client.producer.MessageQueueSelector;
//import org.apache.rocketmq.client.producer.SendCallback;
//import org.apache.rocketmq.client.producer.SendResult;
//import org.apache.rocketmq.common.message.MessageQueue;
//import org.apache.rocketmq.spring.core.RocketMQTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.messaging.support.MessageBuilder;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.List;
//
///**
// * zcWu
// */
//@Slf4j
//@Component
//public class MqMsgUtils {
//
//
//
//    @Resource
//    public RocketMQTemplate rocketMQTemplate;
//
//
//    public void convertAndSend(MqMsg mqMsg) {
//        rocketMQTemplate.convertAndSend(mqMsg.getTopic() + ":" + mqMsg.getTags(),
//                mqMsg.getContent());
//        log.info("convertAndSend sendTime is {}",
//                DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss").format(LocalDateTime.now()));
//    }
//
//
//    /**
//     * 发送有序消息
//     *
//     * @param mqMsg
//     * @return void
//     * @Author: zc.wu
//     * @Date: 2023/5/11 0011 下午 06:43
//     */
//
//    public void sendOrderlyMsg(MqMsg mqMsg) {
//        //顺序消息
//        //选择器规则构建
//        rocketMQTemplate.setMessageQueueSelector(new MessageQueueSelector() {
//
//            public MessageQueue select(List<MessageQueue> list,
//                                       org.apache.rocketmq.common.message.Message message, Object o) {
//                return list.get(RandomUtil.randomInt(list.size()));
//            }
//        });
//        rocketMQTemplate.sendOneWayOrderly(mqMsg.getTopic(), mqMsg.getContent(),
//                SecureUtil.md5(mqMsg.getContent().toString()));
//    }
//
//
//    public void asyncSend(MqMsg mqMsg) {
//        rocketMQTemplate.asyncSend(mqMsg.getTopic(), mqMsg.getContent(),
//                new SendCallback() {
//
//                    public void onSuccess(SendResult sendResult) {
//                        log.info("mqMsg消息发送成功--{}", sendResult);
//                        // 成功不做日志记录或处理
//                    }
//
//
//                    public void onException(Throwable throwable) {
//                        log.error("mqMsg={}消息发送失败--{}", mqMsg, throwable);
//                    }
//                });
//        log.info("asyncSend sendTime is {}",
//                DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss").format(LocalDateTime.now()));
//    }
//
//
//    public void sendOneWay(MqMsg mqMsg) {
//        rocketMQTemplate.sendOneWay(mqMsg.getTopic() + ":" + mqMsg.getTags(), mqMsg.getContent());
//        log.info("sendOneWay sendTime is {}",
//                DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss").format(LocalDateTime.now()));
//    }
//
//
//    public void sendDelayed(MqMsg mqMsg, Integer delayLevel) {
//        rocketMQTemplate.syncSend(mqMsg.getTopic() + ":" + mqMsg.getTags(),
//                MessageBuilder.withPayload(mqMsg.getContent()).build(), 3000, delayLevel);
//        log.info("sendDelayed sendTime is {}",
//                DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss").format(LocalDateTime.now()));
//    }
//
//
//    public void sendMsgToTag(String topic, String tags, Object obj) {
//        MqMsg mqMsg = new MqMsg();
//        mqMsg.setTopic(topic);
//        mqMsg.setTags(tags);
//        mqMsg.setContent(obj);
//        this.convertAndSend(mqMsg);
//    }
//
//}
