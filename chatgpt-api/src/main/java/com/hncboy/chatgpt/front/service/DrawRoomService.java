package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.entity.DrawRoomDO;
import com.hncboy.chatgpt.front.framework.domain.vo.DrawRoomVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【draw_room(绘图室表)】的数据库操作Service
* @createDate 2024-03-20 16:46:36
*/
public interface DrawRoomService extends IService<DrawRoomDO> {

    DrawRoomVO createDrawRoom(DrawRoomDO drawRoomDO);

    List<DrawRoomVO> getDrawRoom(String idsString);

    boolean updateDrawRoom(DrawRoomDO DrawRoomDO);
}
