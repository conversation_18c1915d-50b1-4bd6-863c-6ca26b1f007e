package com.hncboy.chatgpt.front.controller.common;


import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.service.SePayOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 交易性接口
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/se-pay")
@RequiredArgsConstructor
public class SePayOrderController {

private final SePayOrderService sePayOrderService;


    /**
     * 生成支付二维码
     *
     * @return
     */
    @PostMapping(value = "/payQrCode/{productId}", name = "获取支付二维码")
    public R PayQrCode(@PathVariable("productId") Long productId, HttpServletRequest httpRequest) {
            return R.data(sePayOrderService.generateSePayQrCode(productId,httpRequest));
    }

    /**
     * 支付状态查询
     *
     * @param orderNo the order id
     * @return the R
     */
    @GetMapping(value = "/status/{orderNo}", name = "支付宝支付状态", produces = MediaType.APPLICATION_JSON_VALUE)
    public R alipayIsSucceed(@PathVariable("orderNo")String orderNo) {
        return R.data(sePayOrderService.getOrderByOrderNo(orderNo));
    }

}
