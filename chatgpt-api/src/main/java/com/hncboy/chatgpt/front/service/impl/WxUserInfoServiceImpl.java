package com.hncboy.chatgpt.front.service.impl;

import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.converter.WxUserConverter;
import com.hncboy.chatgpt.front.framework.domain.dto.SysConfigDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.WxUserInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.mapper.WxUserInfoMapper;
import com.hncboy.chatgpt.front.service.RedisService;
import com.hncboy.chatgpt.front.service.UserConfigService;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.USER_IMAGE_COUNT;
import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.USER_NOW_COUNT;


/**
 * 微信用户信息 Dao
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/7
 */
@Slf4j
@Service
public class WxUserInfoServiceImpl extends ServiceImpl<WxUserInfoMapper, WxUserInfo> implements
        WxUserInfoService {


    @Resource
    private UserConfigService userConfigService;

    @Autowired
    private RedisService redisService;


    /**
     * 根据openid查询用户
     *
     * @param openId
     * @return WxUserInfo
     * @Author: zc.wu
     * @Date: 2023/4/7 17:44
     */
    @Override
    public WxUserInfoVO queryUserInfoByOpenId(String openId) {
        LambdaQueryWrapper<WxUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxUserInfo::getOpenId, openId);
        WxUserInfo one = this.getOne(queryWrapper);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(one);
        if (Objects.isNull(wxUserInfoVO)) {
            return null;
        }
        wxUserInfoVO.setVip(isVip(wxUserInfoVO) ? 1 : 0);
        //放入可用次数
        String key = USER_NOW_COUNT + CurrentUserUtil.getUserId();
        if (redisService.exists(key)) {
            Integer operateNum = (Integer) redisService.get(key);
            if (Objects.nonNull(operateNum)) {
                wxUserInfoVO.setOperateNum(operateNum);
            }
        }
        key = USER_IMAGE_COUNT + CurrentUserUtil.getUserId();
        if (redisService.exists(key)) {
            Integer operateNum = (Integer) redisService.get(key);
            if (Objects.nonNull(operateNum)) {
                wxUserInfoVO.setImageNum(operateNum);
            }
        }
        String model = userConfigService.queryUserModel(openId);
        if (StrUtil.isNotEmpty(model)) {
            wxUserInfoVO.setModel(model);
        }


        return wxUserInfoVO;
    }


    /**
     * 根据用户生成推广二维码
     *
     * @param openId
     * @return String
     * @Author: zc.wu
     * @Date: 2023/6/26 0026 下午 02:24
     */
    @Override
    public Integer createMpQrCodeTicket(String openId) {
        LambdaQueryWrapper<WxUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxUserInfo::getOpenId, openId);
        WxUserInfo one = this.getOne(queryWrapper);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(one);
        if (Objects.isNull(wxUserInfoVO) && Objects.nonNull(wxUserInfoVO.getCreateBy())) {
            return null;
        }
        //生成一个1-10万的数字
        int i = RandomUtil.randomInt(1, 100000);
        LambdaQueryWrapper<WxUserInfo> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(WxUserInfo::getCreateBy, i);
        WxUserInfo one2 = this.getOne(queryWrapper2);
        if (one2 != null) {
            return createMpQrCodeTicket(openId);
        }
        one.setCreateBy(String.valueOf(i));
        this.updateById(one);
        return i;
    }


    /**
     * 更新vip到期时间
     *
     * @param openId
     * @return WxUserInfo
     * @Author: zc.wu
     * @Date: 2023/4/7 17:44
     */
    @Override
    public void updateVipTime(String openId, int month) {
        LambdaQueryWrapper<WxUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxUserInfo::getOpenId, openId);
        WxUserInfo one = this.getOne(queryWrapper);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(one);
        if (Objects.isNull(wxUserInfoVO)) {
            return;
        }
        Date date = new Date();
        if (Objects.nonNull(one.getVipEndTime())) {
            if (one.getVipEndTime().after(date)) {
                date = one.getVipEndTime();
            }
        }
        DateTime dateTime = DateUtil.offsetMonth(date, month);
        one.setVip(1);
        one.setVipEndTime(dateTime);
        this.updateById(one);
    }


    /**
     * 保存用户
     *
     * @param wxMpUser
     * @return boolean
     * @Author: zc.wu
     * @Date: 2023/4/7 17:46
     */
    @Override
    public WxUserInfoVO saveOrUpdateUser(WxMpUser wxMpUser) {
        WxUserInfoVO wxUserInfoS = queryUserInfoByOpenId(wxMpUser.getOpenId());
        if (Objects.nonNull(wxUserInfoS)) {
            return wxUserInfoS;
        }
        WxUserInfo wxUserInfo = new WxUserInfo();
        BeanUtils.copyProperties(wxMpUser, wxUserInfo);
        this.saveOrUpdate(wxUserInfo);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(wxUserInfo);
        return wxUserInfoVO;
    }
    @Override
    public WxUserInfoVO saveOrUpdateUser(WxMpUser wxMpUser,String appId) {
        WxUserInfoVO wxUserInfoS = queryUserInfoByOpenId(wxMpUser.getOpenId());
        if (Objects.nonNull(wxUserInfoS)) {
            return wxUserInfoS;
        }
        WxUserInfo wxUserInfo = new WxUserInfo();
        BeanUtils.copyProperties(wxMpUser, wxUserInfo);
        wxUserInfo.setAppId(appId);
        this.saveOrUpdate(wxUserInfo);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(wxUserInfo);
        return wxUserInfoVO;
    }


    /**
     * 保存用户
     *
     * @param wxMpUser
     * @return boolean
     * @Author: zc.wu
     * @Date: 2023/4/7 17:46
     */
    @Override
    public WxUserInfoVO saveWxCodeUser(WxOAuth2UserInfo wxMpUser,String appId) {
        WxUserInfoVO wxUserInfoS = queryUserInfoByOpenId(wxMpUser.getOpenid());
        if (wxUserInfoS != null) {
            return wxUserInfoS;
        }
        WxUserInfo wxUserInfo = new WxUserInfo();
        wxUserInfo.setOpenId(wxMpUser.getOpenid());
        wxUserInfo.setNickName(wxMpUser.getNickname());
        wxUserInfo.setAvatarUrl(wxMpUser.getHeadImgUrl());
        wxUserInfo.setUnionId(wxMpUser.getUnionId());
        wxUserInfo.setCity(wxMpUser.getCity());
        wxUserInfo.setProvince(wxMpUser.getProvince());
        wxUserInfo.setCountry(wxMpUser.getCountry());
        wxUserInfo.setProvince(wxMpUser.getProvince());
        wxUserInfo.setGender(wxMpUser.getSex().toString());
        wxUserInfo.setCreateTime(new Date());
        wxUserInfo.setAppId(appId);
        this.saveOrUpdate(wxUserInfo);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(wxUserInfo);
        return wxUserInfoVO;
    }


    @Override
    public Boolean checkUserByType(String openId, String key) {
        WxUserInfoVO wxUserInfoVO = queryUserInfoByOpenId(openId);
        if (Objects.nonNull(wxUserInfoVO.getVip()) && wxUserInfoVO.getVip().equals(1)) {
            return true;
        }
        if (Objects.nonNull(wxUserInfoVO.getApplyNum()) && wxUserInfoVO.getApplyNum() <= 0) {
            return false;
        }
        SysConfigDTO sysConfigDTO = userConfigService.querySysConfig();
        if (key.equals("gpt") && wxUserInfoVO.getApplyNum() < sysConfigDTO.getConversations()) {
            return false;
        }
        if (key.equals("drawing") && wxUserInfoVO.getApplyNum() < sysConfigDTO.getDrawingTimes()) {
            return false;
        }
        return true;
    }


    /**
     * 效验用户是否为VIp
     *
     * @param openId
     * @return boolean
     * @Author: zc.wu
     * @Date: 2023/7/18 0018 下午 05:21
     */
    @Override
    public boolean checkUserIsVip(String openId) {
        WxUserInfoVO wxUserInfoVO = queryUserInfoByOpenId(openId);
        return isVip(wxUserInfoVO);
    }


    private boolean isVip(WxUserInfoVO wxUserInfoVO) {
        if (Objects.nonNull(wxUserInfoVO.getVip()) && wxUserInfoVO.getVip().equals(1)) {
            if (Objects.isNull(wxUserInfoVO.getVipEndTime())) {
                WxUserInfo wxUserInfo = new WxUserInfo();
                wxUserInfo.setOpenId(wxUserInfoVO.getOpenId());
                wxUserInfo.setVip(0);
                this.updateById(wxUserInfo);
                return false;
            }
            Date date = wxUserInfoVO.getVipEndTime();
            boolean after = date.after(new Date());
            if (!after) {
                WxUserInfo wxUserInfo = new WxUserInfo();
                wxUserInfo.setOpenId(wxUserInfoVO.getOpenId());
                wxUserInfo.setVip(0);
                this.updateById(wxUserInfo);
            }
            return after;
        }
        return false;
    }


    /**
     * 更新可用次数
     *
     * @param openId
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:33
     */
    @Override
    public void updateUserNum(String openId, Integer species) {
        WxUserInfoVO wxUserInfoVO = queryUserInfoByOpenId(openId);
        if (Objects.nonNull(wxUserInfoVO) && wxUserInfoVO.getApplyNum() > 0) {
            this.baseMapper.updateUserNumMp(openId, species);
        }
    }


    /**
     * 增加可用次数
     *
     * @param openId
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:33
     */
    @Override
    public void addUserNumMp(String openId, Integer species) {
        this.baseMapper.addUserNumMp(openId, species);
    }


    /**
     * 保存用户
     *
     * @param wxMpUser
     * @return boolean
     * @Author: zc.wu
     * @Date: 2023/4/7 17:46
     */
    @Override
    public WxUserInfoVO saveOrUpdateUser(WxOAuth2UserInfo wxMpUser) {
        WxUserInfo wxUserInfo = new WxUserInfo();
        WxUserInfoVO wxUserInfoS = queryUserInfoByOpenId(wxMpUser.getOpenid());
        if (wxUserInfoS == null) {
            wxUserInfo.setApplyNum(10);
            wxUserInfo.setCreateTime(new Date());
        } else {
            wxUserInfo.setUpdateTime(new Date());
        }
        wxUserInfo.setOpenId(wxMpUser.getOpenid());
        wxUserInfo.setNickName(wxMpUser.getNickname());
        wxUserInfo.setGender(wxMpUser.getSex().toString());
        wxUserInfo.setAvatarUrl(wxMpUser.getHeadImgUrl());
        wxUserInfo.setUnionId(wxMpUser.getUnionId());
        this.saveOrUpdate(wxUserInfo);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(wxUserInfo);
        return wxUserInfoVO;
    }

    @Override
    public WxUserInfoVO saveOrUpdateUser(WxMaUserInfo userInfo, String openId) {
        WxUserInfo wxUserInfo = new WxUserInfo();
        BeanUtils.copyProperties(userInfo, wxUserInfo);
        WxUserInfoVO wxUserInfoS = queryUserInfoByOpenId(openId);
        wxUserInfo.setOpenId(openId);
        if (wxUserInfoS == null) {
            wxUserInfo.setApplyNum(100);
            wxUserInfo.setVip(0);
            wxUserInfo.setCreateTime(new Date());
        } else {
            wxUserInfo.setUpdateTime(new Date());
        }
        wxUserInfo.setSubscribeScene("minapp");
        this.saveOrUpdate(wxUserInfo);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(wxUserInfo);
        wxUserInfoVO.setVip(isVip(wxUserInfoVO) ? 1 : 0);
        return wxUserInfoVO;
    }

    public WxUserInfoVO saveOrUpdateUserAppId(WxOAuth2UserInfo wxMpUser, String appId) {
        WxUserInfo wxUserInfo = new WxUserInfo();
        WxUserInfoVO wxUserInfoS = queryUserInfoByOpenId(wxMpUser.getOpenid());
        if (wxUserInfoS == null) {
            wxUserInfo.setApplyNum(10);
            wxUserInfo.setCreateTime(new Date());
        } else {
            wxUserInfo.setUpdateTime(new Date());
        }
        wxUserInfo.setOpenId(wxMpUser.getOpenid());
        wxUserInfo.setNickName(wxMpUser.getNickname());
        wxUserInfo.setGender(wxMpUser.getSex().toString());
        wxUserInfo.setAvatarUrl(wxMpUser.getHeadImgUrl());
        wxUserInfo.setUnionId(wxMpUser.getUnionId());
        wxUserInfo.setAppId(appId);
        this.saveOrUpdate(wxUserInfo);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(wxUserInfo);
        return wxUserInfoVO;
    }


    /**
     * 保存抖音用户
     *
     * @return boolean
     * @Author: zc.wu
     * @Date: 2023/4/7 17:46
     */
    @Override
    public WxUserInfoVO saveOrUpdateDyUser(JSONObject jsonObject) {
        String openId = jsonObject.getStr("openid");
        if (StringUtils.isEmpty(openId)) {
            throw new ServiceException("【openId】系统繁忙");
        }
        WxUserInfo wxUserInfo = new WxUserInfo();
        WxUserInfoVO wxUserInfoS = queryUserInfoByOpenId(openId);
        if (wxUserInfoS == null) {
            wxUserInfo.setApplyNum(10);
            wxUserInfo.setCreateTime(new Date());
        } else {
            wxUserInfo.setUpdateTime(new Date());
        }
        wxUserInfo.setOpenId(openId);
        wxUserInfo.setNickName(jsonObject.getJSONObject("userInfo").getStr("nickName"));
        wxUserInfo.setGender(jsonObject.getJSONObject("userInfo").getStr("gender"));
        wxUserInfo.setAvatarUrl(jsonObject.getJSONObject("userInfo").getStr("avatarUrl"));
        wxUserInfo.setUnionId(jsonObject.getStr("unionid"));
        wxUserInfo.setSubscribeScene("douyin");
        this.saveOrUpdate(wxUserInfo);
        WxUserInfoVO wxUserInfoVO = WxUserConverter.INSTANCE.entityToVO(wxUserInfo);
        return wxUserInfoVO;
    }


    /**
     * 绑定父ID
     *
     * @param openId
     * @param parentId
     * @return void
     * @Author: zc.wu
     * @Date: 2023/7/10 0010 下午 06:03
     */
    @Override
    public void bandDingParent(String openId, String parentId) {
        WxUserInfo wxUserInfo = new WxUserInfo();
        WxUserInfoVO wxUserInfoS = queryUserInfoByOpenId(openId);
        if (wxUserInfoS == null) {
            wxUserInfo.setApplyNum(10);
            wxUserInfo.setCreateTime(new Date());
            wxUserInfo.setOpenId(openId);
            this.save(wxUserInfo);
        }
        LambdaQueryWrapper<WxUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxUserInfo::getParentId, openId);
        List<WxUserInfo> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            //查询当事人的下级是否包含父亲
            boolean b = list.stream().anyMatch(e -> e.getOpenId().equals(parentId));
            if (b) {
                log.error("绑定失败，他是你的父亲");
                return;
            }
        }
        if (Objects.nonNull(wxUserInfoS) && StrUtil.isEmpty(wxUserInfoS.getParentId())) {
            wxUserInfo.setUpdateTime(new Date());
            wxUserInfo.setOpenId(wxUserInfoS.getOpenId());
            wxUserInfo.setParentId(parentId);
            this.updateById(wxUserInfo);
        }
    }


    /**
     * 查询下级
     *
     * @return List<WxUserInfo>
     * @Author: zc.wu
     * @Date: 2023/7/11 0011 下午 03:35
     */
    @Override
    public List<WxUserInfoVO> querySonUserList() {
        LambdaQueryWrapper<WxUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxUserInfo::getParentId, CurrentUserUtil.getUserId());
        List<WxUserInfo> list = this.list(queryWrapper);
        List<WxUserInfoVO> wxUserInfoVOS = WxUserConverter.INSTANCE.entityListToVOList(list);
        wxUserInfoVOS.forEach(item -> {
            item.setMonery(0.2);
        });
        return wxUserInfoVOS;
    }
}
