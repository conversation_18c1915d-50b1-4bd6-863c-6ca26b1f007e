package com.hncboy.chatgpt.front.framework.domain.query;


import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页数据
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class PageParam  {

    private static final long serialVersionUID = 1L;

    /**
     * 当前记录起始索引
     */
    @Schema(title = "当前记录起始索引")
    private Integer pageNo = 0;

    /**
     * 每页显示记录数
     */
    @Schema(title = "每页显示记录数")
    private Integer pageSize = 20;

    /**
     * 排序列
     */
    @Schema(title = "排序列")
    private String orderByColumn;

    /**
     * 排序的方向 "desc" 或者 "asc".
     */
    @Schema(title = "排序的方向 \"desc\" 或者 \"asc\".")
    private String isAsc;

    public String getOrderBy() {
        if (StrUtil.isEmpty(orderByColumn)) {
            return "";
        }
        return orderByColumn + " " + isAsc;
    }

}


