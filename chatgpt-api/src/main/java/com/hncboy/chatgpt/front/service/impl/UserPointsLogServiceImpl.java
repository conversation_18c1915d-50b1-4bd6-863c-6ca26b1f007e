package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.dto.UserPointsLogDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog;
import com.hncboy.chatgpt.front.framework.domain.vo.UserPointsLogVO;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.mapper.AlOrdersMapper;
import com.hncboy.chatgpt.front.mapper.UserPointsLogMapper;
import com.hncboy.chatgpt.front.service.UserPointsLogService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_points_log】的数据库操作Service实现
* @createDate 2024-03-28 11:22:39
*/
@Service
public class UserPointsLogServiceImpl extends ServiceImpl<UserPointsLogMapper, UserPointsLog>
    implements UserPointsLogService{

    private final AlOrdersMapper alOrdersMapper;

    public UserPointsLogServiceImpl(AlOrdersMapper alOrdersMapper) {
        this.alOrdersMapper = alOrdersMapper;
    }

    /**
     * 根据userId, 订单统计积分记录
     *
     * @param userId 用户Id
     * @param relOrder 关联订单
     * @return 用户基础信息
     */
    @Override
    public Integer checkPointsLog(Integer userId, String relOrder) {
        LambdaQueryWrapper<UserPointsLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPointsLog::getUserId, userId);
        queryWrapper.eq(UserPointsLog::getRelOrder, relOrder);
        return this.count(queryWrapper);
    }
    /**
     * 根据userId, 订单统计积分记录
     *
     * @param userId 用户Id
     * @return 用户基础信息
     */
    @Override
    public Integer checkPointsLog(Integer userId) {
        LambdaQueryWrapper<UserPointsLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPointsLog::getUserId, userId);
        List<UserPointsLog> list = this.list(queryWrapper);
        Integer sum = 0;
        for (UserPointsLog userPointsLog : list) {
            sum+=userPointsLog.getPoints();
        }
        return sum;
    }

    /**
     * 分页查询用户积分记录
     * @param dto
     * @return
     */
    @Override
    public IPage<UserPointsLogVO> queryListEntityPage(UserPointsLogDTO dto) {
        IPage<UserPointsLogVO> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        dto.setUserId(CurrentUserUtil.getV2UserId());
        return this.baseMapper.pageQueryPointsByUserId(userPage, dto.getUserId(), dto.getPointsType());
    }

    @Override
    public IPage<UserPointsLogVO> pageQueryTarotByUserId(UserPointsLogDTO dto) {
        IPage<UserPointsLogVO> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        dto.setUserId(CurrentUserUtil.getV2UserId());
        return this.baseMapper.pageQueryTarotByUserId(userPage, dto.getUserId(), dto.getPointsType());
    }
}




