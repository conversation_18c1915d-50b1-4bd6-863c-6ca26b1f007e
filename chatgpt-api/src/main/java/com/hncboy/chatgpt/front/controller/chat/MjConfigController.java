package com.hncboy.chatgpt.front.controller.chat;

import cn.hutool.dfa.WordTree;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.hncboy.chatgpt.front.framework.domain.entity.SensitiveWordDO;
import com.hncboy.chatgpt.front.framework.enums.EnableDisableStatusEnum;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.handler.config.SensitiveWordHandler;
import com.hncboy.chatgpt.front.service.SensitiveWordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/22 19:48 鉴权相关接口
 */
@AllArgsConstructor
@Tag(name = "配置相关")
@RestController
@RequestMapping
@Slf4j
public class MjConfigController {


    @Operation(summary = "清除缓存")
    @GetMapping("/cleanUp")
    public R cleanUp() {
        try {
            SensitiveWordHandler.CACHE.get("wordTree").clear();
            SensitiveWordHandler.CACHE = CacheBuilder.newBuilder()
                    // 设置并发级别为 CPU 核心数
                    .concurrencyLevel(Runtime.getRuntime().availableProcessors())
                    // 容量为 1
                    .initialCapacity(1)
                    // 过期时间为 12 小时
                    .expireAfterWrite(12, TimeUnit.HOURS)
                    .build(new CacheLoader<String, WordTree>() {
                        @Override
                        public @NotNull WordTree load(@NotNull String s) {
                            log.warn("开始构建敏感词树");
                            WordTree wordTree = new WordTree();
                            SensitiveWordService sensitiveWordService = SpringUtil.getBean(SensitiveWordService.class);
                            List<SensitiveWordDO> sensitiveWords = sensitiveWordService.list(new LambdaQueryWrapper<SensitiveWordDO>()
                                    .select(SensitiveWordDO::getWord)
                                    .eq(SensitiveWordDO::getStatus, EnableDisableStatusEnum.ENABLE.getCode()));
                            log.warn("查询数据库，敏感词数量为：{} 个", sensitiveWords.size());
                            // 生成关键词树
                            wordTree.addWords(sensitiveWords.stream().map(SensitiveWordDO::getWord).collect(Collectors.toSet()));
                            return wordTree;
                        }
                    });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
        return R.data("成功");
    }


//    @GetMapping("/testSend")
//    public void testSend() {
//        MqMsg mqMsg=new MqMsg();
//        mqMsg.setTopic("super-ai-msg-topic");
//        mqMsg.setContent("wzc");
//        mqMsgUtils.asyncSend(mqMsg);
//
//    }

}
