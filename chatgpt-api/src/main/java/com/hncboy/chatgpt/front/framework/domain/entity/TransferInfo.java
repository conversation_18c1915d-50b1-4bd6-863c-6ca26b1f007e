package com.hncboy.chatgpt.front.framework.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import lombok.Data;

/**
 * 提现申请信息对象 transfer_info
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@TableName("transfer_info")
@Data
public class TransferInfo {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
        @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 提现订单号 */
    private String  outDetailNo;

    /** openId */
    private String openId;

    /**
     * 用户id
     */
    private Integer userId;

    /** 用户昵称 */
    private String nickName;

    /** 提现积分 */
    private Integer transferPoints;
    /** 提现金额 */
    private Integer transferAmount;

    /** 提现备注 */
    private String transferRemark;

    /** 用户姓名 */
    private String userName;

    /** 提现状态 0:未提现 1:已提现 */
    private String status;

    /** 提现申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date transferStartTime;

    /** 提现到账日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date transferEndTime;

    /**
     * 提现批次号
     */
    private String outBatchNo;
    /**
     * 提现批次时间
     */
    private Date outBatchTime;
    private String failReason;


    private String createBy;
    private Date createTime;
    private String updateBy;
    private Date updateTime;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("openId", getOpenId())
            .append("outDetailNo", getOutDetailNo())
            .append("nickName", getNickName())
            .append("transferAmount", getTransferAmount())
            .append("transferRemark", getTransferRemark())
            .append("userName", getUserName())
            .append("status", getStatus())
            .append("transferStartTime", getTransferStartTime())
            .append("transferEndTime", getTransferEndTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
                .toString();
    }
}