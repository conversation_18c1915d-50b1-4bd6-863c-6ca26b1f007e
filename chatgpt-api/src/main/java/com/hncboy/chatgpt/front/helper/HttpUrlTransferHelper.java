package com.hncboy.chatgpt.front.helper;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.hncboy.chatgpt.front.framework.config.ChatConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.DrawMessage;
import com.hncboy.chatgpt.front.framework.domain.entity.SiteInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.exception.BalanceException;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.service.DrawMessageService;
import com.hncboy.chatgpt.front.service.RedisService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * zcWu
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class HttpUrlTransferHelper {

    private final ChatConfig chatConfig;

    private final RedisService redisService;

    private final UserBaseInfoService userBaseInfoService;
    private final DrawMessageService drawMessageService;
    private final ChannelConfigHelper channelConfigHelper;


    /**
     * mj请求转发
     *
     * @param jsonObject
     * @return Object
     * @Author: zc.wu
     * @Date: 2024/3/4 0004 下午 02:43
     */
    public Object mjHttpTransFer(JSONObject jsonObject) {
        String url = jsonObject.getStr("url");
        if (url.startsWith("mj/insight-face/swap")) {
            throw new ServiceException("暂不支持换脸功能");
        }
        //提交会话请求、调整精度、重绘图片需要扣次数,需要效验次数,其他查询类的不需要,自定义和局部重绘只在第一次提交时检查和扣除
        boolean needCharge = url.startsWith("mj/submit/imagine") ||
                url.startsWith("mj/submit/change") ||
                url.startsWith("mj/submit/action");
        if (needCharge) {
            UserBaseInfoVO userBaseInfoVO = userBaseInfoService.queryUserInfoById(CurrentUserUtil.getV2UserId());
            if (userBaseInfoVO.getDrawNum() <= 0) {
                throw new BalanceException("绘画次数不足，请充值后使用");
            }
        }
        //查询后台配置
        SiteInfo siteInfo = channelConfigHelper.queryChannelInfoByGid("midjourny");
        if(Objects.nonNull(siteInfo)){
            chatConfig.setOpenaiApiBaseUrl(siteInfo.getUrl());
            chatConfig.setOpenaiApiKey(siteInfo.getApiKey());
        }
        String apiHost = chatConfig.getOpenaiApiBaseUrl();
        String requestUrl = apiHost + url;
        jsonObject.remove("url");
        String response;
        DrawMessage drawMessage;
        //保存绘图记录 自定义和局部重绘第二次提交时不再保存，是更新任务
        if (!url.startsWith("mj/submit/modal")) {
            drawMessage = saveDrawPromptMsg(jsonObject);
        } else {
            LambdaQueryWrapper<DrawMessage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DrawMessage::getTaskId, jsonObject.getStr("taskId"));
            drawMessage = drawMessageService.getOne(lambdaQueryWrapper);
            drawMessage.setPrompt(Optional.ofNullable(jsonObject.getStr("prompt")).orElse(""));
        }
        try {
            log.info("转发的目标url：{},请求参数:{}", requestUrl, jsonObject);
            response = HttpRequest.post(requestUrl).body(jsonObject.toString())
                    .header("Mj-Api-Secret", chatConfig.getOpenaiApiKey())
                    .timeout(5000).execute().body();
        } catch (Exception e) {
            log.error("异常信息--{}", e.getMessage());
            throw new ServiceException("绘图提交失败，请稍后再试！");
        }
        log.info("mjHttpTransFer param={}  response = [{}]", jsonObject, response);
        JSONObject parse = JSONUtil.parseObj(response);
        if (parse.containsKey("code") && (parse.getInt("code") == 1 || parse.getInt("code") == 21)) {
            //绘画成功 扣次数
            if (needCharge) {
                userBaseInfoService.updateUserDrawNumMp(CurrentUserUtil.getV2UserId(), 1);
            }
            //更新taskId
            drawMessage.setTaskId(parse.getStr("result"));
            drawMessage.setState(parse.getStr("code"));
            drawMessage.setDescription(parse.getStr("description"));
            drawMessageService.updateById(drawMessage);
            return response;
        } else if (parse.containsKey("code") &&
                parse.getInt("code") == 24 &&
                "May contains sensitive words".equals(parse.getStr("description"))) {
            drawMessage.setFailReason(response);
            drawMessageService.updateById(drawMessage);
            throw new ServiceException("您的输入包含敏感词【" +
                    parse.getJSONObject("properties").getStr("bannedWord") + "】，请重新输入");
        } else {
            drawMessage.setFailReason(response);
            drawMessageService.updateById(drawMessage);
            throw new ServiceException("当前绘图服务繁忙，请稍后再试！");
        }
    }

    /**
     * 查询绘图结果
     *
     * @param requestId
     * @return
     */
    public String queryMJRes(String requestId) {
        //查询后台配置
        SiteInfo siteInfo = channelConfigHelper.queryChannelInfoByGid("midjourny");
        if(Objects.nonNull(siteInfo)){
            chatConfig.setOpenaiApiBaseUrl(siteInfo.getUrl());
            chatConfig.setOpenaiApiKey(siteInfo.getApiKey());
        }
        String res = HttpRequest.get(chatConfig.getOpenaiApiBaseUrl() + "mj/task/" + requestId + "/fetch")
                .header("Mj-Api-Secret", chatConfig.getOpenaiApiKey()).timeout(5000).execute().body();
        log.debug("查询结果:[{}]", res);
        JSONObject parse = null;
        try {
            parse = JSONUtil.parseObj(res);
        } catch (Exception e) {
            throw new ServiceException("查询绘图结果异常，请联系管理员");
        }
        if (parse.containsKey("status") && parse.getStr("status").equals("SUCCESS")) {
            log.info("绘图成功:{}", parse);
            //TODO 反向代理  存储图片到数据库
            LambdaQueryWrapper<DrawMessage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(DrawMessage::getTaskId, requestId);
            DrawMessage drawMessage = drawMessageService.getOne(lambdaQueryWrapper);
            if (drawMessage != null) {
                drawMessage.setStatus(parse.getStr("status"));
                drawMessage.setAction(parse.getStr("action"));
                drawMessage.setProgress(parse.getStr("progress"));
                drawMessage.setPromptEn(parse.getStr("promptEn"));
                drawMessage.setImageUrl(parse.getStr("imageUrl"));
                drawMessage.setButtons(Optional.ofNullable(parse.getStr("buttons")).orElse(""));
                drawMessage.setSubmitTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(parse.getLong("submitTime")),
                        ZoneId.systemDefault()));
                drawMessage.setStartTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(parse.getLong("startTime")),
                        ZoneId.systemDefault()));
                drawMessage.setFinishTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(parse.getLong("finishTime")),
                        ZoneId.systemDefault()));
                drawMessageService.updateById(drawMessage);
            }
        }
        return res;
    }


    /**
     * gpt请求转发
     *
     * @param jsonObject
     * @return Object
     * @Author: zc.wu
     * @Date: 2024/3/4 0004 下午 02:43
     */
    public Object gptHttpTransFer(JSONObject jsonObject) {
        String requestUrl = jsonObject.getStr("url");
        String response = HttpRequest.post(requestUrl).form(jsonObject.toString()).header(Header.AUTHORIZATION,
                        chatConfig.getOpenaiApiKey())
                .timeout(5000).execute().body();
        log.info("gptHttpTransFer param={}  response = [{}]", jsonObject, response);
        return response;
    }


    /**
     * file请求转发
     *
     * @param file
     * @return Object
     * @Author: zc.wu
     * @Date: 2024/3/4 0004 下午 02:43
     */
    public String fileHttpTransFer(File file) {
        String requestUrl = "https://api.open-proxy.cn/" + "v1/upload";
        String response = HttpRequest.post(requestUrl).form("file", file).header(Header.AUTHORIZATION, "sk" +
                        "-pvyxrRcodQW74DtlC2D1079850354c67808d521d0540D073")
                .header("Content-Type", "multipart/form-data; boundary=----WebKitFormBoundarysUAYWR4VutmD6Jiv")
                .timeout(5000).execute().body();
        log.info("fileHttpTransFer requestUrl:{} ==response = [{}]", requestUrl, response);
        return response;
    }


    /**
     * 随机查询四条新闻展示在首页
     *
     * @param
     * @return List<String>
     * @Author: zc.wu
     * @Date: 2024/3/6 0006 上午 10:30
     */
    public List<String> queryNews() {
        List<String> stringList = Lists.newArrayList();
        String keys = "news_gpt";
        String s = "";
        if (redisService.exists(keys)) {
            s = (String) redisService.get(keys);
        } else {
            s = HttpUtil.get("https://apis.tianapi.com/toutiaohot/index?key=906c135e3f4de4a8127b90c19f2fbd1c");
            //一天过期
            redisService.set(keys, s, 86400L);
        }
        JSONObject object = JSONUtil.parseObj(s);
        if (object.containsKey("code") && object.getInt("code") == 200) {
            JSONObject result = object.getJSONObject("result");
            JSONArray list = result.getJSONArray("list");
            //随机取四条新闻
            stringList = getRandomFour(list);
            return stringList;
        }
        stringList.add("中国第四艘航母很快公布");
        stringList.add("黄金现货价格接近历史新高");
        stringList.add("2024中国发展主要预期目标");
        stringList.add("德国公布窃听门调查结果");
        return stringList;
    }


    public static List<String> getRandomFour(JSONArray jsonArray) {
        // 将JSONArray转换为List<String>
        List<String> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            list.add(jsonObject.getStr("word"));
        }
        // 打乱list的顺序
        Collections.shuffle(list);
        // 取前四个元素，确保JSONArray的长度至少为4，否则取其实际长度
        list = list.subList(0, Math.min(4, list.size()));

        return list;
    }

    /**
     * 保存绘图消息
     *
     * @param chatProcessRequest
     * @return DrawMessage
     * @Author: zd.z
     * @Date: 2024/3/20 0001 上午 11:31
     */
    private DrawMessage saveDrawPromptMsg(JSONObject chatProcessRequest) {
        DrawMessage drawMessage = new DrawMessage();
        drawMessage.setUserId(CurrentUserUtil.getV2UserId());
        drawMessage.setPrompt(Optional.ofNullable(chatProcessRequest.getStr("prompt")).orElse(""));
        drawMessage.setPromptEn(Optional.ofNullable(chatProcessRequest.getStr("promptEn")).orElse(""));
        drawMessage.setBotType(Optional.ofNullable(chatProcessRequest.getStr("botType")).orElse(""));
        drawMessage.setCustomId(Optional.ofNullable(chatProcessRequest.getStr("customId")).orElse(""));
        drawMessage.setDrawRoomId(Optional.ofNullable(chatProcessRequest.getStr("drawRoomId")).orElse("1"));
        drawMessageService.initDrawMessage(drawMessage);
        if (drawMessage.getPromptEn() != null) {
            chatProcessRequest.set("prompt", drawMessage.getPromptEn());
            chatProcessRequest.remove("promptEn");
        }
        return drawMessage;
    }

}
