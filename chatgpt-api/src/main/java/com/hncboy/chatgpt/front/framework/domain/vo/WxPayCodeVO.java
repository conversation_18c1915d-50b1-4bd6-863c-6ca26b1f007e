package com.hncboy.chatgpt.front.framework.domain.vo;

import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 支付订单信息 VO
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "WxPayCoder对象", description = "支付订单信息")
public class WxPayCodeVO implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String qrCode;

    private String ordersId;

    private String productType;

    private String productName;

    private Double productPrice;

    private LocalDateTime createdTime;
    
    private Object prepayId;
    
    private String h5Url;

}
