package com.hncboy.chatgpt.front.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.converter.UserBaseInfoConverter;
import com.hncboy.chatgpt.front.framework.domain.dto.UserBaseInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.CommissionIdentity;
import com.hncboy.chatgpt.front.framework.domain.entity.SysConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog;
import com.hncboy.chatgpt.front.framework.domain.query.LoginInfoParam;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.AESUtil;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.front.framework.util.WebUtil;
import com.hncboy.chatgpt.front.mapper.UserBaseInfoMapper;
import com.hncboy.chatgpt.front.mapper.UserPointsLogMapper;
import com.hncboy.chatgpt.front.service.*;
import com.hncboy.chatgpt.tarot.domain.dto.TarotDailyInsightDTO;
import com.hncboy.chatgpt.tarot.domain.entity.UserCheckInRecord;
import com.hncboy.chatgpt.tarot.domain.vo.TarotDailyInsightVO;
import com.hncboy.chatgpt.tarot.mapper.TarotDailyInsightMapper;
import com.hncboy.chatgpt.tarot.mapper.UserCheckInRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.crypto.spec.SecretKeySpec;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.getUserHeaderImage;

/**
 * 用户基础信息实现
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2024/2/26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserBaseInfoServiceImpl extends ServiceImpl<UserBaseInfoMapper, UserBaseInfo> implements UserBaseInfoService {

    private final SysConfigService sysConfigService;
    private final UserPointsLogService userPointsLogService;
    private final UserPointsLogMapper userPointsLogMapper;
    private final TarotDailyInsightMapper tarotDailyInsightMapper;
    private final UserCheckInRecordMapper userCheckInRecordService;
    private final CommissionIdentityService commissionIdentityService;
    private final String TYPE_YEAR = "YEAR";
    private final String TYPE_MONTH = "MONTH";
    private final String TYPE_DAY = "DAY";
    private final RedisService redisService;
    private final static String KEY_PREFIX_INIT = "user-init:";
    private final static String KEY_PREFIX_INFO = "user-info:";
//    private final UserCommTree userCommTree;

    /**
     * 根据账号获取用户基础信息
     *
     * @param account 账号
     * @return 用户基础信息
     */
    @Override
    public UserBaseInfoVO getUserBaseInfoByAccount(String account) {
        LambdaQueryWrapper<UserBaseInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserBaseInfo::getAccount, account).eq(UserBaseInfo::getDeleted,0);//过滤掉注销用户
        //queryWrapper.eq(UserBaseInfo::getUserType, "zns");
        queryWrapper.eq(UserBaseInfo::getStatus, UserBaseInfoDTO.UserStatusEnum.normal.getStatus());
        return UserBaseInfoConverter.INSTANCE.entityToVO(this.getOne(queryWrapper));
    }


    /**
     * 根据用户ID获取用户基础信息
     *
     * @param userId 用户ID
     * @return 用户基础信息
     */
    @Override
    public UserBaseInfoVO queryUserInfoById(Integer userId) {
        UserBaseInfo baseInfo = this.getOne(new QueryWrapper<UserBaseInfo>().eq("id", userId).eq("deleted",0));
        if(Objects.isNull(baseInfo)){
            //当前用户不存在 清除当前登录信息
            StpUtil.logout();
            //Todo 当前用户不存在 需要更改异常抛出
            throw new ServiceException("当前用户不存在");
        }
        UserBaseInfoVO userBaseInfoVO = UserBaseInfoConverter.INSTANCE.entityToVO(baseInfo);
        Integer id = userBaseInfoVO.getId();
        String key = ApplicationConstant.REDIS_USER_KEY + id.toString();

        if (redisService.exists(key)) {

            userBaseInfoVO.setUserValue(redisService.get(key).toString());
        }else{
            //用户信息加密
            try {
                String s = AESUtil.generateSecureRandomString(25);

                SecretKeySpec keyFromPassword = AESUtil.getKeyFromPassword(s);

                String encrypt = AESUtil.encrypt(id.toString(), keyFromPassword);
                userBaseInfoVO.setUserValue(encrypt);
                redisService.set(key, encrypt,2 * 60 * 60L);
                redisService.set(key+"_key", s,2 * 60 * 60L);

            } catch (Exception e) {
                log.error("用户信息加密失败", e);
            }
        }

        TarotDailyInsightDTO dto = new TarotDailyInsightDTO();
        Date date = new Date();
        String s = DateUtil.format(date, "yyyy-MM-dd");
        dto.setEndDate(s+" 23:59:59");
        dto.setStartDate(s+" 00:00:00");
        dto.setUserId(id);
        TarotDailyInsightVO insightVO = tarotDailyInsightMapper.selectDayTarotDailyInsight(dto);
        //判断是否产生今日塔罗指引
        if(insightVO != null){
            userBaseInfoVO.setInsightId(insightVO.getId().toString());
        }

        userBaseInfoVO.setCurrentTime(s);
        String s1 = DateUtil.format(date, "yyyyMMdd");

        UserCheckInRecord one =null;
        try {
             one = userCheckInRecordService.selectOne(new QueryWrapper<UserCheckInRecord>()
                    .eq("user_id", userBaseInfoVO.getId())
                    .eq("check_in_date", s1)
            );
        }catch (Exception e){
            log.error("查询今日签到记录失败",e);
        }

        if (one != null) {
            userBaseInfoVO.setCheckUser("1");
        }else{
            userBaseInfoVO.setCheckUser("0");
        }

        String wxMpUrl = "https://mp.weixin.qq.com/mp/profile_ext?action=home&__biz=Mzg5ODg1MjQ4NA==&scene=500#wechat_redirect";
        if(ObjectUtil.isNotEmpty(userBaseInfoVO.getCommissionId())){
            CommissionIdentity commissionIdentity = commissionIdentityService.getByUserInfoId(userBaseInfoVO.getCommissionId());
            if(ObjectUtil.isNotEmpty(commissionIdentity)){
                userBaseInfoVO.setCommissionType("0");
                wxMpUrl = commissionIdentity.getWxMpUrl();
            }else{
                userBaseInfoVO.setCommissionType("1");
            }
        }else{
            userBaseInfoVO.setCommissionType("1");
        }
        userBaseInfoVO.setWxMpUrl(wxMpUrl);

        return userBaseInfoVO;
    }

    @Override
    public UserBaseInfoVO queryUserInfoByPhone(String phoneNumber) {
        UserBaseInfo baseInfo = this.getOne(new QueryWrapper<UserBaseInfo>().eq("account", phoneNumber));
        UserBaseInfoVO userBaseInfoVO;
        if(Objects.isNull(baseInfo)){
            //当前用户不存在 自动生成用户
            LoginInfoParam loginInfoParam = new LoginInfoParam();
            loginInfoParam.setAccount(phoneNumber);
            loginInfoParam.setUserType("chatoi");
             userBaseInfoVO = this.initUserInfo(loginInfoParam);
        }else{
            userBaseInfoVO = UserBaseInfoConverter.INSTANCE.entityToVO(baseInfo);
        }

        Integer id = userBaseInfoVO.getId();
        String key = ApplicationConstant.REDIS_USER_KEY + id.toString();

        if (redisService.exists(key)) {

            userBaseInfoVO.setUserValue(redisService.get(key).toString());
        }else{
            //用户信息加密
            try {
                String s = AESUtil.generateSecureRandomString(25);

                SecretKeySpec keyFromPassword = AESUtil.getKeyFromPassword(s);

                String encrypt = AESUtil.encrypt(id.toString(), keyFromPassword);
                userBaseInfoVO.setUserValue(encrypt);
                redisService.set(key, encrypt,4 * 60 * 60L);
                redisService.set(key+"_key", s,4 *60 * 60L);

            } catch (Exception e) {
                log.error("用户信息加密失败", e);
            }
        }

        Date date = new Date();
        String s = DateUtil.format(date, "yyyy-MM-dd");

        userBaseInfoVO.setCurrentTime(s);


        return userBaseInfoVO;
    }


    /**
     * 修改用户登录时间
     *
     * @param userId
     * @return void
     * @Author: zc.wu
     * @Date: 2024/3/1 0001 下午 03:26
     */
    @Override
    public void updateUserLoginTime(Integer userId) {
        UserBaseInfo baseInfo = new UserBaseInfo();
        baseInfo.setId(userId);
        baseInfo.setLoginTime(LocalDateTime.now());
        this.updateById(baseInfo);
    }

    /**
     * 初始化用户信息
     *
     * @param loginInfoParam 登录信息
     */
    @Override
    public UserBaseInfoVO initUserInfo(LoginInfoParam loginInfoParam) {
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setAccount(loginInfoParam.getAccount());
        userBaseInfo.setIp(StringUtils.isNotEmpty(loginInfoParam.getIpAddress())?loginInfoParam.getIpAddress():WebUtil.getIp());
        String parentNum = null;
        if (StrUtil.isNotBlank(loginInfoParam.getParentId() )){
            parentNum = getParentNum(loginInfoParam.getParentId());
            if (parentNum != null) {
                userBaseInfo.setParentId(Integer.parseInt(parentNum));
            }
        }
        //微信登录账号默认是wx+随机数 以后绑定了手机号修改成手机号
        if(StrUtil.isNotBlank(loginInfoParam.getOpenId())){
            //查询用户是否存在 防止重复注册
            UserBaseInfoVO userBaseInfoByOpenId = this.getUserBaseInfoByOpenId(loginInfoParam.getOpenId());
            if (Objects.nonNull(userBaseInfoByOpenId)) {
                return userBaseInfoByOpenId;
            }
            String uuId = RandomUtil.randomString(8);
            userBaseInfo.setAccount("wx_"+uuId);
            userBaseInfo.setOpenId(loginInfoParam.getOpenId());
            // 微信首次扫码登录时默认登录时间
            userBaseInfo.setLoginTime(LocalDateTime.now());
        }
        if(StrUtil.isNotBlank(loginInfoParam.getNickName())){
            userBaseInfo.setNickName(loginInfoParam.getNickName());
            userBaseInfo.setHeadSculpture(loginInfoParam.getHeadImgUrl());
        }
        //默认送三天
        int day = 3;
        SysConfig newUserGift = sysConfigService.querySysConfig("new_user_gift");
        SysConfig newUserDraw = sysConfigService.querySysConfig("new_user_draw");
        SysConfig newUserMusic = sysConfigService.querySysConfig("new_user_music");
        SysConfig newUserWrite = sysConfigService.querySysConfig("new_user_write");

        if (Objects.nonNull(newUserGift)) {
            day = Integer.parseInt(newUserGift.getConfigValue());
        }
        userBaseInfo.setVipEndTime(LocalDateTime.now().plusDays(day));
        if (Objects.nonNull(newUserDraw)) {
            userBaseInfo.setDrawNum(Integer.parseInt(newUserDraw.getConfigValue()));
        } else {
            userBaseInfo.setDrawNum(20);
        }
        if (Objects.nonNull(newUserMusic)) {
            userBaseInfo.setMusicNum(Integer.parseInt(newUserMusic.getConfigValue()));
        } else {
            userBaseInfo.setMusicNum(3);
        }
        if (Objects.nonNull(newUserWrite)) {
            userBaseInfo.setWriteNum(Integer.parseInt(newUserWrite.getConfigValue()));
        } else {
            userBaseInfo.setWriteNum(3);
        }
       if(StrUtil.isBlank(userBaseInfo.getHeadSculpture())){
           userBaseInfo.setHeadSculpture(getUserHeaderImage());
       }
        userBaseInfo.setStatus(UserBaseInfoDTO.UserStatusEnum.normal.getStatus());
        if(StringUtils.isNotEmpty(loginInfoParam.getUserType())){
            userBaseInfo.setUserType(loginInfoParam.getUserType());
        }else{
            userBaseInfo.setUserType("zns");
        }
        //userBaseInfo.setUserType(loginInfoParam.getUserType()==null?"zns":loginInfoParam.getUserType());
        this.save(userBaseInfo);

        return UserBaseInfoConverter.INSTANCE.entityToVO(userBaseInfo);
    }
    @Override
    public UserBaseInfoVO initUserInfo2(LoginInfoParam loginInfoParam) {
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setAccount(loginInfoParam.getAccount());
        userBaseInfo.setIp(WebUtil.getIp());
        String parentNum = null;
        if (StrUtil.isNotBlank(loginInfoParam.getParentId())) {
            try {
                parentNum = getParentNum(loginInfoParam.getParentId());
            } catch (Exception e) {
                log.error("获取用户父级号失败", e);
            }

            if (parentNum != null) {
                userBaseInfo.setParentId(Integer.parseInt(parentNum));
            }
        }
        String key = KEY_PREFIX_INIT +loginInfoParam.getOpenId();
        if(redisService.tryLock(key)){
            if(StrUtil.isNotBlank(loginInfoParam.getOpenId())){
                //查询用户是否存在 防止重复注册
                UserBaseInfoVO userBaseInfoByOpenId = this.getUserBaseInfoByOpenId(loginInfoParam.getOpenId());
                if (Objects.nonNull(userBaseInfoByOpenId)) {
                    redisService.releaseLock(key);
                    return userBaseInfoByOpenId;
                }
                /* String uuId = RandomUtil.randomString(8);*/
                /*  userBaseInfo.setAccount("wx_"+uuId);*/
                userBaseInfo.setOpenId(loginInfoParam.getOpenId());
                // 微信首次扫码登录时默认登录时间
                userBaseInfo.setLoginTime(LocalDateTime.now());
            }
            if (StrUtil.isNotBlank(loginInfoParam.getNickName())) {
                userBaseInfo.setNickName(loginInfoParam.getNickName());
                userBaseInfo.setHeadSculpture(loginInfoParam.getHeadImgUrl());
            }
            /// 如果曾经注册过就不赠送福利
            //1 查询是否存在记录
            List<UserBaseInfo> lastUserInfos = query().eq("open_id", loginInfoParam.getOpenId()).list();
            UserPointsLog userPointsLog = null;
            if (ObjectUtil.isEmpty(lastUserInfos)) {
                //完全新用户(非注销再注册), 默认送三天
                int day = 3;
                SysConfig newUserGift = sysConfigService.querySysConfig("new_user_gift");
                SysConfig newUserDraw = sysConfigService.querySysConfig("new_user_draw");
                SysConfig newUserMusic = sysConfigService.querySysConfig("new_user_music");
                SysConfig newUserWrite = sysConfigService.querySysConfig("new_user_write");
                SysConfig newUserTarot = sysConfigService.querySysConfig("new_user_tarot");
                if (Objects.nonNull(newUserGift)) {
                    day = Integer.parseInt(newUserGift.getConfigValue());
                }

                userBaseInfo.setVipEndTime(LocalDateTime.now().plusDays(day));

                if (Objects.nonNull(newUserTarot)) {
                    day = Integer.parseInt(newUserTarot.getConfigValue());
                } else {
                    day = 100;
                }
                userBaseInfo.setTarotCoins(day);

                if (Objects.nonNull(newUserDraw)) {
                    userBaseInfo.setDrawNum(Integer.parseInt(newUserDraw.getConfigValue()));
                } else {
                    userBaseInfo.setDrawNum(20);
                }
                if (Objects.nonNull(newUserMusic)) {
                    userBaseInfo.setMusicNum(Integer.parseInt(newUserMusic.getConfigValue()));
                } else {
                    userBaseInfo.setMusicNum(3);
                }
                if (Objects.nonNull(newUserWrite)) {
                    userBaseInfo.setWriteNum(Integer.parseInt(newUserWrite.getConfigValue()));
                } else {
                    userBaseInfo.setWriteNum(3);
                }
                //在user_points_log 信增新用户赠币记录
                userPointsLog = new UserPointsLog()
                        .setPoints(Integer.valueOf(newUserTarot.getConfigValue()))
                        .setPointsType(newUserTarot.getConfigKey())
                        .setRemark(newUserTarot.getRemark())
                        .setCreateTime(new Date())
                        .setCreateBy(CurrentUserUtil.getUserId());
            }else{
                //有记录, 不赠送
                userBaseInfo.setVipEndTime(LocalDateTime.now());
                userBaseInfo.setTarotCoins(0);
                userBaseInfo.setDrawNum(0);
                userBaseInfo.setMusicNum(0);
                userBaseInfo.setWriteNum(0);
            }
            if(StrUtil.isBlank(userBaseInfo.getHeadSculpture())){
                userBaseInfo.setHeadSculpture(getUserHeaderImage());
            }
            userBaseInfo.setStatus(UserBaseInfoDTO.UserStatusEnum.normal.getStatus());
            userBaseInfo.setUserType("tarot");
            this.save(userBaseInfo);
            setUserCommissionId(userBaseInfo);
            if(userPointsLog !=null){
                userPointsLog.setUserId(userBaseInfo.getId());
                userPointsLog.setRelOrder(userBaseInfo.getAccount());
                userPointsLogMapper.insert(userPointsLog);
            }
            redisService.releaseLock(key);
        }else{
            throw new ServiceException("系统繁忙，请稍后再试");
        }
        return UserBaseInfoConverter.INSTANCE.entityToVO(userBaseInfo);
    }
    /**
     *  三方登录 保存用户
     * @param loginInfoParam
     * @return
     */
    @Override
    public UserBaseInfoVO initUserInfo3(LoginInfoParam loginInfoParam, boolean isNew) {
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setAccount(loginInfoParam.getAccount());
        userBaseInfo.setIp(WebUtil.getIp());
        String parentNum = null;
        if (StrUtil.isNotBlank(loginInfoParam.getParentId() )){
            try {
                parentNum = getParentNum(loginInfoParam.getParentId());
            }catch (Exception e){
                log.error("获取用户父级号失败",e);
            }

            if (parentNum != null) {
                userBaseInfo.setParentId(Integer.parseInt(parentNum));
            }
        }

        String key = KEY_PREFIX_INIT +loginInfoParam.getUsersId();
        if(redisService.tryLock(key)){
            if(ObjectUtil.isNotEmpty(loginInfoParam.getUsersId())){
                //查询用户是否存在 防止重复注册
                UserBaseInfoVO existedUser = this.getUserBaseInfoByUsersId(loginInfoParam.getUsersId());
                if (Objects.nonNull(existedUser)) {
                    redisService.releaseLock(key);
                    return existedUser;
                }
                /* String uuId = RandomUtil.randomString(8);*/
                /*  userBaseInfo.setAccount("wx_"+uuId);*/
                userBaseInfo.setUsersId(loginInfoParam.getUsersId());
                // 微信首次扫码登录时默认登录时间
                userBaseInfo.setLoginTime(LocalDateTime.now());
            }
            if (StrUtil.isNotBlank(loginInfoParam.getNickName())) {
                userBaseInfo.setNickName(loginInfoParam.getNickName());
                userBaseInfo.setHeadSculpture(loginInfoParam.getHeadImgUrl());
            }
            /// 如果曾经注册过就不赠送福利
            //1 查询是否存在记录
            List<UserBaseInfo> lastUserInfos = query().eq("users_id", loginInfoParam.getUsersId()).list();
            UserPointsLog userPointsLog = null;
            if (ObjectUtil.isEmpty(lastUserInfos)) {
                //完全新用户(非注销再注册), 默认送三天
                int day = 3;
                SysConfig newUserGift = sysConfigService.querySysConfig("new_user_gift");
                SysConfig newUserDraw = sysConfigService.querySysConfig("new_user_draw");
                SysConfig newUserMusic = sysConfigService.querySysConfig("new_user_music");
                SysConfig newUserWrite = sysConfigService.querySysConfig("new_user_write");
                SysConfig newUserTarot = sysConfigService.querySysConfig("new_user_tarot");
                if (Objects.nonNull(newUserGift)) {
                    day = Integer.parseInt(newUserGift.getConfigValue());
                }

                userBaseInfo.setVipEndTime(LocalDateTime.now().plusDays(day));

                if (Objects.nonNull(newUserTarot)) {
                    day = Integer.parseInt(newUserTarot.getConfigValue());
                } else {
                    day = 100;
                }
                userBaseInfo.setTarotCoins(day);

                if (Objects.nonNull(newUserDraw)) {
                    userBaseInfo.setDrawNum(Integer.parseInt(newUserDraw.getConfigValue()));
                } else {
                    userBaseInfo.setDrawNum(20);
                }
                if (Objects.nonNull(newUserMusic)) {
                    userBaseInfo.setMusicNum(Integer.parseInt(newUserMusic.getConfigValue()));
                } else {
                    userBaseInfo.setMusicNum(3);
                }
                if (Objects.nonNull(newUserWrite)) {
                    userBaseInfo.setWriteNum(Integer.parseInt(newUserWrite.getConfigValue()));
                } else {
                    userBaseInfo.setWriteNum(3);
                }
                //在user_points_log 信增新用户赠币记录
                userPointsLog = new UserPointsLog()
                        .setPoints(Integer.valueOf(newUserTarot.getConfigValue()))
                        .setPointsType(newUserTarot.getConfigKey())
                        .setRemark(newUserTarot.getRemark())
                        .setCreateTime(new Date())
                        .setCreateBy(CurrentUserUtil.getUserId());
            }else{
                //有记录, 不赠送
                userBaseInfo.setVipEndTime(LocalDateTime.now());
                userBaseInfo.setTarotCoins(0);
                userBaseInfo.setDrawNum(0);
                userBaseInfo.setMusicNum(0);
                userBaseInfo.setWriteNum(0);
            }
            if(StrUtil.isBlank(userBaseInfo.getHeadSculpture())){
                userBaseInfo.setHeadSculpture(getUserHeaderImage());
            }
            userBaseInfo.setStatus(UserBaseInfoDTO.UserStatusEnum.normal.getStatus());
            userBaseInfo.setUserType("tarot");
            this.save(userBaseInfo);
            setUserCommissionId(userBaseInfo);
            if(userPointsLog !=null){
                userPointsLog.setUserId(userBaseInfo.getId());
                userPointsLog.setRelOrder(userBaseInfo.getAccount());
                userPointsLogMapper.insert(userPointsLog);
            }
            redisService.releaseLock(key);
        }else{
            throw new ServiceException("系统繁忙，请稍后再试");
        }
        return UserBaseInfoConverter.INSTANCE.entityToVO(userBaseInfo);
    }
    /**
     * 增加用户积分
     *
     * @param parentNum
     * @param phone
     */
    @Override
    public void addUserPoints(Integer parentNum, String phone,String type) {
        // 检查是否已加过积分
        if (userPointsLogService.checkPointsLog(parentNum, phone) == 0) {
            UserBaseInfo parentUser = getUserBaseInfoByUserId(parentNum);
            if(parentUser==null){
                return;
            }
            // 给邀请人加积分，生成积分记录
            SysConfig sysConfig = sysConfigService.querySysConfig(type);
            // 邀请积分从系统配置中获取
            Integer points =Integer.parseInt(sysConfig.getConfigValue());

            if(type.equals("invite_points")){
                int addPoints;
                if (parentUser.getPoints() == null) {
                    addPoints = points;
                }
                else {
                    addPoints = parentUser.getPoints() + points;
                }
                //更新用户--邀请人的积分
                updateUserPoints(parentNum, addPoints);
            }else if("tarot_invite_points".equals(type)){
                //更新用户--邀请人的塔罗币
                addUserTarotNumMp(parentNum, points);
            }

            final UserPointsLog userPointsLog = new UserPointsLog().setUserId(parentNum)
                    .setRelOrder(phone).setPoints(points).setPointsType(sysConfig.getConfigKey())
                    .setRemark(sysConfig.getRemark()).setCreateTime(new Date())
                    .setCreateBy(CurrentUserUtil.getUserId());
            userPointsLogMapper.insert(userPointsLog);
        } else {
            log.warn("已产生过积分, 邀请人id[{}]-新用户手机号[{}]", parentNum, phone);
        }
    }

    /**
     * 扣除用户可用次数
     *
     * @param userId  用户ID
     * @param species 次数
     */
    @Override
    public synchronized void updateUserNum(Integer userId, Integer species, Integer type) {
        if (type == 1) {
            //更新充值可用次数
            this.baseMapper.updateUserNumMp(userId, species);
            return;
        }
        //扣免费次数
        this.baseMapper.updateUserFreeNumMp(userId, species);
    }


    /**
     * 增加绘画可用次数
     *
     * @param userId
     * @param drawNum
     * @return void
     * @Author: zc.wu
     * @Date: 2024/2/26 0026 下午 01:48
     */
    @Override
    public void updateUserDrawNumMp(Integer userId, Integer drawNum) {
        this.baseMapper.updateUserDrawNumMp(userId, drawNum);
    }

    /**
     * 更新音乐创作可用次数
     *
     * @param userId
     * @param musicNum
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/4/25 14:51
     */
    @Override
    public void updateUserMusicNumMp(Integer userId, Integer musicNum) {
        this.baseMapper.updateUserMusicNumMp(userId, musicNum);
    }

    /**
     * 更新写作可用次数
     *
     * @param userId
     * @param writeNum
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/4/25 14:51
     */
    @Override
    public void updateUserWriteNumMp(Integer userId, Integer writeNum) {
        this.baseMapper.updateUserWriteNumMp(userId, writeNum);
    }

    /**
     * 更新塔罗牌可用次数
     * @param userId
     * @param tarotNum
     */
    @Override
    public void updateUserTarotNumMp(Integer userId, Integer tarotNum) {
        this.baseMapper.updateUserTarotNumMp(userId, tarotNum);
    }

    /**
     * 修改用户的vip到期时间
     *
     * @param userId
     * @param num
     */
    @Override
    public void updateVipTime(Integer userId, String unit, Long num) {
        UserBaseInfo userBaseInfo = this.baseMapper.selectById(userId);
        LocalDateTime date = LocalDateTime.now();
        if (Objects.nonNull(userBaseInfo.getVipEndTime())) {
            if (userBaseInfo.getVipEndTime().isAfter(date)) {
                date = userBaseInfo.getVipEndTime();
            }
        }
        LocalDateTime dateTime;
        if (TYPE_MONTH.equals(unit)) {
            dateTime = date.plusMonths(num);
        } else if (TYPE_YEAR.equals(unit)) {
            dateTime = date.plusYears(num);
        } else if (TYPE_DAY.equals(unit)) {
            dateTime = date.plusDays(num);
        } else {
            throw new RuntimeException("Invalid unit");
        }
        userBaseInfo.setVipEndTime(dateTime);
        this.baseMapper.updateById(userBaseInfo);
    }


    /**
     * 效验用户是否为VIp
     *
     * @param
     * @return boolean
     * @Author: zc.wu
     * @Date: 2024/3/27 15:39
     */
    @Override
    public boolean checkUserIsVip() {
        UserBaseInfo userBaseInfo = this.baseMapper.selectById(CurrentUserUtil.getV2UserId());
        return isVip(userBaseInfo);
    }

    @Override
    public boolean checkUserIsVip(UserBaseInfoVO userBaseInfoVO) {
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        BeanUtils.copyProperties(userBaseInfoVO, userBaseInfo);
        return isVip(userBaseInfo);
    }

    /**
     * 通过id获取用户信息
     * @param userId
     * @return
     */
    @Override
    public UserBaseInfo getUserBaseInfoByUserId(Integer userId) {
        return lambdaQuery().eq(UserBaseInfo::getId, userId).eq(UserBaseInfo::getDeleted, 0).one();
    }

    /**
     * 根据id添加积分
     * @param userId
     * @param newPoints
     */
    @Override
    public void updateUserPoints(Integer userId, Integer newPoints) {
        this.baseMapper.addPoints(userId,newPoints);
    }
    /**
     * 根据id添加塔罗币
     * @param userId
     * @param newPoints
     */
    @Override
    public void updateUserTarotCoins(Integer userId, Integer newPoints) {
        this.baseMapper.addTarotCoins(userId,newPoints);
    }

    /**
     * 根据id添加绘画次数
     * @param userId
     * @param times
     */
    @Override
    public void addUserDrawNumMp(Integer userId, Integer times) {
        this.baseMapper.addUserDrawNum(userId,times);
    }

    /**
     * 根据id添加音乐创作次数
     * @param userId
     * @param times
     */
    @Override
    public void addUserMusicNumMp(Integer userId, Integer times) {
        this.baseMapper.addUserMusicNum(userId,times);
    }

    /**
     * 根据id添加用户写作点数
     * @param userId
     * @param times
     */
    @Override
    public void addUserWriteNumMp(Integer userId, Integer times) {
        this.baseMapper.addUserWriteNum(userId,times);
    }

    @Override
    public void addUserTarotNumMp(Integer userId, Integer times) {
        this.baseMapper.addUserTarotNumMp(userId,times);
    }


    /**
     * 根据id设置父级id
     * @param id
     * @param parentId
     */
    @Override
    public void setParentId(Integer id, Integer parentId) {
        this.baseMapper.setParentId(id,parentId);
    }


    private boolean isVip(UserBaseInfo userBaseInfo) {
        if (Objects.isNull(userBaseInfo.getVipEndTime())) {
            return false;
        }
        LocalDateTime date = userBaseInfo.getVipEndTime();
        return date.isAfter(LocalDateTime.now());
    }


    /**
     * 增加用户可用次数
     *
     * @param userId
     * @param species
     * @return void
     * @Author: zc.wu
     * @Date: 2024/2/26 0026 下午 01:48
     */
    @Override
    public void addUserNum(Integer userId, Integer species) {
        this.baseMapper.addUserNumMp(userId, species);
    }

    @Override
    public void updateUserInfoById(UserBaseInfoDTO userInfoDTO) {
        UserBaseInfo userBaseInfo = this.baseMapper.selectById(CurrentUserUtil.getV2UserId());
        if(userBaseInfo.getDeleted()==1){
            return;
        }
        if (StrUtil.isNotBlank(userInfoDTO.getNickName())) {
            userBaseInfo.setNickName(userInfoDTO.getNickName());
        }
        if (StrUtil.isNotBlank(userInfoDTO.getHeadSculpture())) {
            userBaseInfo.setHeadSculpture(userInfoDTO.getHeadSculpture());
        }
        this.baseMapper.updateUserInfoById(CurrentUserUtil.getV2UserId(),
                userBaseInfo.getNickName(), userBaseInfo.getHeadSculpture());
    }


    /**
     * 根据openId获取用户信息
     * @param openId
     * @return
     */
    @Override
    public UserBaseInfoVO getUserBaseInfoByOpenId(String openId) {
        UserBaseInfo userBaseInfo = this.baseMapper.selectOne(new LambdaQueryWrapper<UserBaseInfo>().eq(UserBaseInfo::getOpenId, openId).eq(UserBaseInfo::getDeleted,0));
        return UserBaseInfoConverter.INSTANCE.entityToVO(userBaseInfo);
    }
    /**
     * 根据UsersId获取用户信息
     * @param usersId
     * @return
     */
    @Override
    public UserBaseInfoVO getUserBaseInfoByUsersId(Long usersId) {
        UserBaseInfo userBaseInfo = this.baseMapper.selectOne(new LambdaQueryWrapper<UserBaseInfo>()
                .eq(UserBaseInfo::getUsersId, usersId)
                .eq(UserBaseInfo::getDeleted,0));
        return UserBaseInfoConverter.INSTANCE.entityToVO(userBaseInfo);
    }

    /**
     * 根据Id更新账号
     * @param id
     * @return
     */
    @Override
    public void updateUserAccountById(Integer id, String phone) {
        this.baseMapper.updateUserAccountById(id, phone);
    }

    @Override
    public void updateUserFirstStatusById(Integer id) {
        this.update(new UpdateWrapper<UserBaseInfo>().eq("id",id).set("first_status",1).eq("deleted",0));
    }


    /**
     * 根据邀请码获取上级id
     *
     * @param parentId
     * @return
     */
    @Override
    public String getParentNum(String parentId) {
        // 是否为微信openId
        if (parentId.length() > 14) {
            UserBaseInfoVO userBaseInfoByOpenId = getUserBaseInfoByOpenId(parentId);
            if (Objects.isNull(userBaseInfoByOpenId)) {
                throw new ServiceException("邀请码错误");
            }
            return userBaseInfoByOpenId.getId().toString();
        } else if (parentId.length() > 6) {
            // 保证字符长度
            String idStr = parentId.substring(3, parentId.length() - 3);
            UserBaseInfo parentUser = getUserBaseInfoByUserId(Integer.parseInt(idStr));
            if (parentUser == null) {
                throw new ServiceException("邀请码错误");
            }
            //验证用户id是否是数字
            if (idStr.matches("\\d+")) {
                //转换类型
                return idStr;
            }
            return null;
        }
        return null;
    }

    @Override
    public void updateUserInfoIsDeleteById(Integer id) {
        UserBaseInfo userBaseInfo = getById(id);
        if(userBaseInfo==null){
            return;
        }
        //倘若有人先注销了，那么接下来的操作就无意义了，
        String key = KEY_PREFIX_INFO+id;
        if(!redisService.tryLock(key)){
            return;
        }
        try{
            if(userBaseInfo.getDeleted()==1){
                return;
            }
            update(new UpdateWrapper<UserBaseInfo>().eq("id",id).set("deleted",1));
            redisService.releaseLock(key);
        } catch (Exception e) {
            redisService.releaseLock(key);
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public boolean isOnlyOneUserBaseInfoRecord(String openid) {
        return query().eq("open_id",openid).count()==1;
    }

    public void setUserCommissionId(UserBaseInfo userBaseInfo) {
        if(ObjectUtil.isNotEmpty(userBaseInfo.getParentId())){
            UserBaseInfo parentUser = getUserBaseInfoByUserId(userBaseInfo.getParentId());
            if(ObjectUtil.isNotEmpty(parentUser) && ObjectUtil.isNotEmpty(parentUser.getCommissionId())){
                userBaseInfo.setCommissionId(parentUser.getCommissionId());
            }
        }else{
            userBaseInfo.setCommissionId(userBaseInfo.getId());
        }
        this.updateById(userBaseInfo);
    }

}
