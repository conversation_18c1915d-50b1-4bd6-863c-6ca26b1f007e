package com.hncboy.chatgpt.front.controller.user;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.dto.WxLoginDTO;
import com.hncboy.chatgpt.front.framework.domain.query.LoginInfoParam;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.front.helper.LoginManager;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * zcWu
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Tag(name = "登录相关接口")
@RestController
@RequestMapping("/login")
@Slf4j
public class LoginController {

    private final LoginManager loginManager;
    private final WxMaService wxMaService;
    private final UserBaseInfoService userBaseInfoService;

    @Operation(summary = "chatoi用户注册")
    @PostMapping("/add")
    public R<String> add(@RequestBody @Validated LoginInfoParam loginInfoParam) {
        String s = loginManager.addUser(loginInfoParam);
        //boolean notEmpty = StringUtils.isNotEmpty(s);
        R<String> success = R.success();
        success.setMessage(StringUtils.isNotEmpty(s)?s : null);
        return success;
    }

    @Operation(summary = "短信登录")
    @PostMapping("/smsLogin")
    public R<UserBaseInfoVO> smsLogin(@RequestBody @Validated LoginInfoParam loginInfoParam) {
        UserBaseInfoVO userBaseInfoVO = loginManager.smsLogin(loginInfoParam);
        StpUtil.login(userBaseInfoVO.getId());
        //会话相关
        StpUtil.getSession().set(ApplicationConstant.USER, userBaseInfoVO);
        userBaseInfoVO.setToken(StpUtil.getTokenValue());
        return R.data(userBaseInfoVO);
    }

    @Operation(summary = "获取登录短信验证码")
    @GetMapping("/getSmsCode")
    public R<String> getSmsCode(final String phone, final String parentId,final String userType,final String ipAddress, HttpServletRequest request) {
        String smsCode = loginManager.getSmsCode(phone, parentId,userType,ipAddress, true, request);
        return R.data(smsCode);
    }


    @Operation(summary = "获取短信验证码")
    @GetMapping("/getSmsCodeOnly")
    public R<String> getSmsCodeOnly(final String phone, final String parentId,final String userType,final String ipAddress, HttpServletRequest request) {
        String smsCode = loginManager.getSmsCode(phone, parentId,userType,ipAddress, false, request);
        return R.data(smsCode);
    }


    @Operation(summary = "绑定手机号")
    @PostMapping("/bindPhone")
    public R<UserBaseInfoVO> bindPhone(@RequestBody @Validated LoginInfoParam loginInfoParam) {
        UserBaseInfoVO userBaseInfoVO = loginManager.bindPhone(loginInfoParam);
        StpUtil.login(userBaseInfoVO.getId());
        //会话相关
        StpUtil.getSession().set(ApplicationConstant.USER, userBaseInfoVO);
        userBaseInfoVO.setToken(StpUtil.getTokenValue());
        return R.data(userBaseInfoVO);
    }

    @GetMapping("logout")
    @Operation(summary = "退出登录")
    public R<String> logout() {
        StpUtil.logout();
        return R.success();
    }


    @Operation(summary = "微信小程序登录接口")
    @PostMapping("/miniLogin")
    public R<UserBaseInfoVO> mpLogin(@RequestBody @Validated WxLoginDTO wxLoginDTO) {
        try {
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(wxLoginDTO.getCode());
            wxLoginDTO.setSessionKey(sessionInfo.getSessionKey());
            wxLoginDTO.setOpenId(sessionInfo.getOpenid());

        } catch (WxErrorException e) {
            return R.fail("登录失败");
        }
        // 判断当前用户微信是否已注册
        UserBaseInfoVO userBaseInfoByOpenId = userBaseInfoService.getUserBaseInfoByOpenId(wxLoginDTO.getOpenId());
        //判断当前用户手机号是否已注册
        //UserBaseInfoVO userBaseInfoByAccount = userBaseInfoService.getUserBaseInfoByAccount(wxLoginDTO.getPhoneNumber());
        //如果都未注册，则是新用户，则注册一个新用户
        if (userBaseInfoByOpenId == null ) {
            //查询保存微信用户
            LoginInfoParam loginInfoParam = new LoginInfoParam();
            loginInfoParam.setOpenId(wxLoginDTO.getOpenId());
            loginInfoParam.setNickName("遇见"+System.currentTimeMillis());
            if(StringUtils.isNotEmpty(wxLoginDTO.getPhoneNumber())){
                loginInfoParam.setAccount(wxLoginDTO.getPhoneNumber());
            }
            //如果是新用户起有邀请码，则绑定邀请人
            if (StringUtils.isNotEmpty(wxLoginDTO.getParentId())) {
                loginInfoParam.setParentId(wxLoginDTO.getParentId());
            }

            userBaseInfoByOpenId = userBaseInfoService.initUserInfo2(loginInfoParam);
            //首次登录，为邀请人新增奖励
            if (ObjectUtil.isNotEmpty(userBaseInfoByOpenId.getParentId())){
                userBaseInfoService.addUserPoints(userBaseInfoByOpenId.getParentId(), wxLoginDTO.getPhoneNumber(),"invite_score");
            }


            StpUtil.login(userBaseInfoByOpenId.getId());
            //会话相关
            StpUtil.getSession().set(ApplicationConstant.USER, userBaseInfoByOpenId);
            userBaseInfoByOpenId.setToken(StpUtil.getTokenValue());


            return R.data(userBaseInfoByOpenId);
        }
        //如果微信未注册，但手机号已注册，则绑定微信
       /* if(userBaseInfoByOpenId==null){
            userBaseInfoByAccount.setOpenId(wxLoginDTO.getOpenId());
            userBaseInfoService.update(new UpdateWrapper<UserBaseInfo>()
                    .eq("account", wxLoginDTO.getPhoneNumber())
                    .set("open_id", wxLoginDTO.getOpenId()));
            StpUtil.login(userBaseInfoByAccount.getId());
            //会话相关
            StpUtil.getSession().set(ApplicationConstant.USER, userBaseInfoByAccount);
            userBaseInfoByAccount.setToken(StpUtil.getTokenValue());
            return R.data(userBaseInfoByAccount);
        }else{*/
            //反之，则认为该用户已注册，直接登录
            StpUtil.login(userBaseInfoByOpenId.getId());
            //会话相关
            StpUtil.getSession().set(ApplicationConstant.USER, userBaseInfoByOpenId);
            userBaseInfoByOpenId.setToken(StpUtil.getTokenValue());
            return R.data(userBaseInfoByOpenId);
        //}
    }

    @Operation(summary = "获取手机号")
    @PostMapping("/getPhone")
    public R getPhone(@RequestBody @Validated WxLoginDTO wxMiniLoginDTO) throws WxErrorException {
        WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(wxMiniLoginDTO.getCode());
        return R.data(phoneNoInfo);
    }

}
