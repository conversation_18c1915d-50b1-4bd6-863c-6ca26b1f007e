package com.hncboy.chatgpt.front.controller.wx;

import cn.dev33.satoken.stp.StpUtil;
import com.google.common.collect.Lists;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.enums.WxAppIdEnum;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.net.MalformedURLException;
import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Controller
@RequestMapping("/wx/redirect")
@RestController
@Tag(name = "微信登录相关")
public class WxRedirectController {

    private final WxMpService wxService;

    private final WxUserInfoService wxUserInfoService;

    @RequestMapping("/greet")
    public R<WxUserInfoVO> greetUser(@RequestParam String code, HttpServletResponse response) {
        try {
            wxService.switchover(WxAppIdEnum.zns.getCode());
            WxOAuth2AccessToken accessToken = wxService.getOAuth2Service().getAccessToken(code);
            WxOAuth2UserInfo user = wxService.getOAuth2Service().getUserInfo(accessToken, null);
            WxUserInfoVO wxUserInfo = wxUserInfoService.saveOrUpdateUserAppId(user,WxAppIdEnum.zns.getCode());
            StpUtil.login(user.getOpenid());
            //会话相关
            StpUtil.getSession().set(ApplicationConstant.USER, wxUserInfo);
            wxUserInfo.setToken(StpUtil.getTokenValue());
            return R.data(wxUserInfo);
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        return R.fail("用户获取失败");
    }


    @RequestMapping("/greetBot")
    public R<WxOAuth2UserInfo> greetUserRotBot(@RequestParam String code, HttpServletResponse response) {
        try {
            wxService.switchover(WxAppIdEnum.zns.getCode());
            WxOAuth2AccessToken accessToken = wxService.getOAuth2Service().getAccessToken(code);
            WxOAuth2UserInfo user = wxService.getOAuth2Service().getUserInfo(accessToken, null);
            return R.data(user);
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        return R.fail("用户获取失败");
    }


    @RequestMapping("/mockLogin")
    public R<WxUserInfoVO> mockLogin() {
        WxUserInfoVO wxUserInfo = wxUserInfoService.queryUserInfoByOpenId(
                "o4Kty1qAW8DPeT9klxnA9q78YSgg");
        StpUtil.login(wxUserInfo.getOpenId());
        //会话相关
        StpUtil.getSession().set(ApplicationConstant.USER, wxUserInfo);
        wxUserInfo.setToken(StpUtil.getTokenValue());
        return R.data(wxUserInfo);
    }


    @Operation(summary = "获取授权地址")
    @GetMapping("/getAuthUrl")
    public R<String> getUrl(String urls) {
        wxService.switchover(WxAppIdEnum.zns.getCode());
        String appId = this.wxService.getWxMpConfigStorage().getAppId();
        String url = this.wxService.switchoverTo(appId).getOAuth2Service()
                .buildAuthorizationUrl(String.format(urls), WxConsts.OAuth2Scope.SNSAPI_USERINFO, null);
        return R.data(url);
    }


    @Operation(summary = "获取临时二维码")
    @GetMapping("/getQrcodeUrl")
    public R<String> getQrcodeUrl() throws MalformedURLException, WxErrorException {
        wxService.switchover(WxAppIdEnum.zns.getCode());
        WxMpQrCodeTicket wxMpQrCodeTicket = this.wxService.getQrcodeService()
                .qrCodeCreateTmpTicket(818, 2592000);
        String url = this.wxService.getQrcodeService()
                .qrCodePictureUrl(wxMpQrCodeTicket.getTicket());
        return R.data(url);
    }


    @Operation(summary = "获取微信分享链接信息")
    @GetMapping("/createJsapiSignature")
    public R<WxJsapiSignature> createJsapiSignature(String url) throws WxErrorException {
        wxService.switchover(WxAppIdEnum.zns.getCode());
        WxJsapiSignature wxJsapiSignature = this.wxService.createJsapiSignature(url);
        return R.data(wxJsapiSignature);
    }


    @GetMapping("/initWxUserInfo")
    public void initWxUserInfo() {
        List<WxMpUser> wxMpUsers = Lists.newArrayList();
        try {
            wxService.switchover(WxAppIdEnum.zns.getCode());
            WxMpUserList wxMpUserList = this.wxService.getUserService().userList(null);
            List<String> openids = wxMpUserList.getOpenids();
            List<List<String>> partition = Lists.partition(openids, 100);
            for (List<String> stringList : partition) {
                wxMpUsers.addAll(this.wxService.getUserService().userInfoList(stringList));
            }
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
        for (WxMpUser wxMpUser : wxMpUsers) {
            wxUserInfoService.saveOrUpdateUser(wxMpUser, WxAppIdEnum.zns.getCode());
        }
    }

}
