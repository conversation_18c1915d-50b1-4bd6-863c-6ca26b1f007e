package com.hncboy.chatgpt.front.helper;

import cn.hutool.core.util.RandomUtil;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * zcWu
 */

public class QiNuOssHelper {

    public static String qnyUpload(MultipartFile file) {
        String accessKey = "hqMxHLfwwQsCkNNhns7d3snq074yQrvAq1n27lpY";
        String secretKey = "e0IRZdpcqWpdAvsU7_g8Wdp44-NBdgjAI735Dhj4";
        String bucketName = "alwzc";
        Configuration cfg = new Configuration();
        UploadManager uploadManager = new UploadManager(cfg);
        Auth auth = Auth.create(accessKey, secretKey);
        String token = auth.uploadToken(bucketName);
        String key = "qz/" + RandomUtil.randomString(20) + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        try {
            Response r = uploadManager.put(file.getBytes(), key, token);
            return "https://image.zjfdsr.com/" + key;
        } catch (QiniuException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public static String qnyUpload(String name, byte[] bys) {
        String accessKey = "hqMxHLfwwQsCkNNhns7d3snq074yQrvAq1n27lpY";
        String secretKey = "e0IRZdpcqWpdAvsU7_g8Wdp44-NBdgjAI735Dhj4";
        String bucketName = "alwzc";
        Configuration cfg = new Configuration();
        UploadManager uploadManager = new UploadManager(cfg);
        Auth auth = Auth.create(accessKey, secretKey);
        String token = auth.uploadToken(bucketName);
        String key = "ai/" + RandomUtil.randomString(5) + name;
        try {
            Response r = uploadManager.put(bys, key, token);
            return "http://image.zjfdsr.com/" + key;
        } catch (QiniuException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


}
