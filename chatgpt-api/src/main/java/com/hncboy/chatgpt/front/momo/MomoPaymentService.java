package com.hncboy.chatgpt.front.momo;

import cn.hutool.json.JSONUtil;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.dto.WxPayOrderDTO;
import com.hncboy.chatgpt.front.handler.pay.PayPrepayVO;
import com.hncboy.chatgpt.front.service.impl.PayServiceImpl;
import com.mservice.allinone.models.CaptureMoMoResponse;
import com.mservice.allinone.processor.allinone.CaptureMoMo;
import com.mservice.shared.sharedmodels.Environment;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class MomoPaymentService {
    private final static String TRADE_TYPE = "allinone";

    private final PayServiceImpl payService;

    @Getter
    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${momo.notify-url}")
    private String notifyUrl;
    @Value("${momo.return-url}")
    private String returnUrl;

    private Environment getEnv() {
        if ("prod".equals(profile)) {
            return Environment.selectEnv(Environment.EnvTarget.PROD, Environment.ProcessType.PAY_GATE);
        } else {
            return Environment.selectEnv(Environment.EnvTarget.DEV, Environment.ProcessType.PAY_GATE);
        }
    }

    public PayPrepayVO createPrepayOrder(WxPayOrderDTO wxPayOrderDTO) {
        PayPrepayVO vo = payService.createPrepayOrder(wxPayOrderDTO
                , ApplicationConstant.CHANNEL_MOMO, TRADE_TYPE, notifyUrl);
        if(vo == null){
            throw new RuntimeException("创建订单失败.");
        }
        try {
            Environment environment = getEnv();
            String extraData = "";
            CaptureMoMoResponse response = CaptureMoMo.process(environment
                    , vo.getOrdersId()
                    , ""+vo.getId()
                    , String.valueOf(vo.getProductPrice())
                    , ("tarot:momo:goodsId:" + vo.getProductId())
                    , returnUrl
                    , notifyUrl
                    , extraData);
            log.info("创建Momo支付订单成功，订单信息:{}", response);
            if(response.getErrorCode() != 0){
                throw new RuntimeException("创建Momo支付订单失败");
            }
            vo.setPayExtraMap(JSONUtil.parseObj(response));
        } catch (Exception e) {
            log.error("创建订单失败", e);
            throw new RuntimeException("创建订单失败..");
        }
        return vo;
    }

        /*
https://developers.momo.vn/v2/#/docs/en/aio/

请求示例
{
  "accessKey": "F8BBA842ECF85",
  "partnerCode": "MOMO",
  "requestType": "captureMoMoWallet",
  "notifyUrl": "https://momo.vn",
  "returnUrl": "https://momo.vn",
  "orderId": "MM1540456472575",
  "amount": "150000",
  "orderInfo": "SDK team.",
  "requestId": "MM1540456472575",
  "extraData": "email=<EMAIL>",
  "signature": "996ed81d68a1b05c99516835e404b2d0146d9b12fbcecbf80c7e51df51cac85e"
}


应答示例
{
  "requestId": "MM1540456472575",
  "errorCode": 0,
  "orderId": "MM1540456472575",
  "message": "Success",
  "localMessage": "Thành công",
  "requestType": "captureMoMoWallet",
  "payUrl": "https://test-payment.momo.vn/gw_payment/payment/qr?partnerCode=MOMO&accessKey=F8BBA842ECF85&requestId=MM1540456472575&amount=150000&orderId=MM1540456472575&signature=df2a347519abb91e9c1bd1bee80e675f4108cb6dbcac531979e805857293d486&requestType=captureMoMoWallet",
  "signature": "ee6a01b85ffc48a2b5d3df473da88c75cc5e879d1543d9e76ced279c10bcd646",
  "qrCodeUrl": "https://test-payment.momo.vn/gw_payment/s/zoVKZd",
  "deeplink": "momo://?action=payWithAppToken&amount=150000&fee=0&requestType=payment&orderLabel=M%C3%A3+%C4%91%C6%A1n+h%C3%A0ng&orderId=MM1540456472575&requestId=MM1540456472575&merchantnamelabel=Nh%C3%A0+cung+c%E1%BA%A5p&description=SDK+team.&partnerCode=MOMO&merchantcode=MOMO&language=vi&merchantname=MoMo+Payment&packageId=&extras=&extraData=email=<EMAIL>&deeplinkCallback=https%3A%2F%2Ftest-payment.momo.vn%2Fgw_payment%2Fm2%3Fid%3DM7EWVy&callbackUrl=https%3A%2F%2Ftest-payment.momo.vn%2Fgw_payment%2Fm2%3Fid%3DM7EWVy&urlSubmitToken=https%3A%2F%2Ftest-payment.momo.vn%2Fgw_payment%2Fpayment_with_app%3FpartnerCode%3DMOMO%26accessKey%3DF8BBA842ECF85%26requestId%3DMM1540456472575%26orderId%3DMM1540456472575%26orderInfo%3DSDK%2Bteam.%26amount%3D150000%26signature%3Ddf2a347519abb91e9c1bd1bee80e675f4108cb6dbcac531979e805857293d486%26requestType%3DcaptureMoMoWallet%26payType%3Dapp-in-app&appScheme=",
  "deeplinkWebInApp": "http://momo//?type=webinapp&action=payment&requestId=MM1540456472575&billId=MM1540456472575&partnerCode=MOMO&partnerName=MoMo Payment&amount=150000&description=SDK team.¬ifyUrl=https://momo.vn&returnUrl=https://momo.vn&code=momo&extraData=eyJzaWduYXR1cmUiOiI0OWUzMTZhNTVkN2UxM2Q0ZjEwNGFjZjM2YTM5MzllZjg0NDk3NWU2OTJiMWU1OGM3MDFjYWUyM2ZiM2QxNDY5In0=&signature=49e316a55d7e13d4f104acf36a3939ef844975e692b1e58c701cae23fb3d1469"
}
requestId  与原始请求相同
errorCode: 错误代码
message: 英文错误描述
localMessage: 越南语错误描述
requestType: captureMoMoWallet
payUrl : 使用此 URL 从合作伙伴的销售页面重定向到 MoMo 的支付页面。
qrCodeUrl : 使用此数据创建二维码，如果您希望用户直接在销售页面上扫描二维码，而不是重定向到 MoMo 的支付页面。
deeplink : 使用此 URL 打开 MoMo 应用（用户必须在移动设备上已安装 MoMo 应用）和支付确认页面。适用于合作伙伴的手机应用或手机网页。
deeplinkWebInApp : 使用此 URL 在 MoMo 应用中打开支付确认界面。适用于合作伙伴网站嵌入 MoMo 应用的情况。


Interface (Redirect)  接口（重定向）
用户将根据在 captureMoMoWallet 命令中设置的 returnUrl 字段，从 MoMo 的支付页面重定向回合作伙伴的销售页面，并将附加参数附加到 URL 中，格式如下：
returnUrl?{your_parameters}&partnerCode=$partnerCode&accessKey=$accessKey
&requestId=$requestId&amount=$amount&orderId=orderId
&orderInfo=$orderInfo&orderType=momo_wallet&transId=$transId&message=$message
&localMessage=$localMessage&responseTime=$responseTime&errorCode=$errorCode
&payType=$payType&extraData=$extraData&signature=$signature


IPN - 即时付款通知
MoMo 服务器将使用 notifyUrl 字段中通知的 API，向合作伙伴服务器发送配置如下 HTTP 请求。
curl -X 'POST' 'https://example.com/momo_ipn' -H 'content-type: application/json'
-d $'{
"partnerCode":"MOMOIOLD20190129",
"orderId":"01234567890123451633504872421",
"requestId":"01234567890123451633504872421",
"amount":1000,
"orderInfo":"Test Thue 1234556",
"orderType":"momo_wallet",
"transId":2588659987,
"resultCode":0,
"message":"Giao dịch thành công.",
"payType":"qr",
"responseTime":1633504902954,
"extraData":"eyJyZXN1bHRfbmFtZXNwYWNlIjoidW1hcmtldCIsImVycm9yIjoiIiwic3RhdGUiOjZ9",
"signature":"90482b3881bdf863d5f61ace078921bbc6dbb58b2fded35261c71c9af3b1ce4f"}'

requestId: Partner's request ID  合作伙伴请求 ID
orderId: Partner’s Order ID  合作伙伴订单 ID
errorCode: 合作伙伴的交易处理状态
message: 合作伙伴的交易状态描述
responseTime: Response time   响应时间  Format: YYYY-MM-DD HH:mm:ss  格式： YYYY-MM-DD HH:mm:ss     Time zone: GMT +7  时区： GMT +7
extraData: Additional Information  附加信息

*/

    public void callback(HttpServletRequest request, Map<String, Object> paramsMap) {
        log.info("支付回调结果: {}", paramsMap);

        String bizNo = paramsMap.get("orderId")+"";
        boolean paySuccess = "0".equals(paramsMap.get("errorCode"));
        payService.callback(request, paramsMap, bizNo, paySuccess);
    }



}
