package com.hncboy.chatgpt.front.service;

import com.hncboy.chatgpt.front.framework.domain.entity.HomeConfig;
import com.hncboy.chatgpt.front.framework.domain.vo.HomeConfigVO;

import java.util.List;

/**
 * Dao
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/11
 */
public interface HomeConfigService {


    /**
     * 查询所有配置
     *
     * @Param:@param id
     * @return:com.zhongjia.infrastructure.db.entity.HomeConfig
     * @Author: wuzhic
     * @Date:2023/4/11
     */
    List<HomeConfigVO> queryAllList(HomeConfigVO homeConfigVO);


    /**
     * 插入所有配置
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wuzhic
     * @Date:2023/4/11
     */
    HomeConfig insertEntity(HomeConfig record);

    /**
     * 根据ID查询配置
     *
     * @param id
     * @return HomeConfig
     * @Author: zc.wu
     * @Date: 2023/4/12 10:26
     */
    HomeConfig getHomeConfigById(Integer id);


    /**
     * 根据Id批量查询配置并拼接成字符串
     *
     * @param ids
     * @return HomeConfig
     * @Author: zc.wu
     * @Date: 2023/6/21 0021 下午 12:36
     */
    String queryConfigByIds(List<Integer> ids);


    /**
     * 更新可用次数
     *
     * @param id
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/17 18:06
     */

    void updateConfigNum(Integer id);


}
