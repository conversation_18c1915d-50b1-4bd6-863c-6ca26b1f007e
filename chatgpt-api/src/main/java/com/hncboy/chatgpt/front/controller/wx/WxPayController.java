package com.hncboy.chatgpt.front.controller.wx;

import cn.hutool.json.JSONObject;
import com.github.binarywang.wxpay.bean.entpay.EntPayBankRequest;
import com.github.binarywang.wxpay.bean.entpay.EntPayBankResult;
import com.github.binarywang.wxpay.bean.marketing.transfer.PartnerTransferRequest;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.github.binarywang.wxpay.bean.notify.WxScanPayNotifyResult;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.hncboy.chatgpt.front.framework.domain.dto.WxPayOrderDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxPayCodeVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxPayOrderVO;
import com.hncboy.chatgpt.front.service.WxPayOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * <AUTHOR> Wang
 */
@Tag(name = "微信支付")
@RestController
@RequestMapping("/pay")
@AllArgsConstructor
@Slf4j
public class WxPayController {

    private final WxPayOrderService wxPayOrderService;
    private WxPayService wxService;


    @Operation(summary = "支付回调通知处理")
    @PostMapping("/notify/order")
    public String parseOrderNotifyResult(@RequestBody String xmlData) throws WxPayException {
        log.info("支付回调：{}", xmlData);
        final WxPayOrderNotifyResult notifyResult = this.wxService.parseOrderNotifyResult(xmlData);
        
        wxPayOrderService.updateOrderStatus(notifyResult);
        return WxPayNotifyResponse.success("成功");
    }


    @Operation(summary = "抖音支付回调通知处理")
    @PostMapping("/dy/notify")
    public void douyinNotify(@RequestBody JSONObject jsonObject) {
        log.info("抖音支付回调通知处理：{}", jsonObject);
    }

    
    
    @Operation(summary = "退款回调通知处理")
    @PostMapping("/notify/refund")
    public String parseRefundNotifyResult(@RequestBody String xmlData) throws WxPayException {
        final WxPayRefundNotifyResult result = this.wxService.parseRefundNotifyResult(xmlData);
        // TODO 根据自己业务场景需要构造返回对象
        return WxPayNotifyResponse.success("成功");
    }


    @Operation(summary = "扫码支付回调通知处理")
    @PostMapping("/notify/scanpay")
    public String parseScanPayNotifyResult(String xmlData) throws WxPayException {
        final WxScanPayNotifyResult result = this.wxService.parseScanPayNotifyResult(xmlData);
        // TODO 根据自己业务场景需要构造返回对象
        return WxPayNotifyResponse.success("成功");
    }



    @Operation(summary = "生成订单二维码")
    @GetMapping("/createOrder")
    public WxPayCodeVO createOrder(WxPayOrderDTO wxPayOrderDTO)
            throws WxPayException {
        WxPayCodeVO order = wxPayOrderService.createOrder(wxPayOrderDTO);
        return order;
    }


    @Operation(summary = "唤起微信支付")
    @GetMapping("/createPrepayOrderWx")
    public WxPayCodeVO createPrepayOrder(WxPayOrderDTO wxPayOrderDTO, HttpServletRequest request)
            throws WxPayException {

        log.info("ip地址：{}", request.getRemoteAddr());

        return wxPayOrderService.createPrepayOrder(wxPayOrderDTO);
    }

    /**
     * 企业付款到银行卡.
     * <pre>
     * 用于企业向微信用户银行卡付款
     * 目前支持接口API的方式向指定微信用户的银行卡付款。
     * 文档详见：https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php?chapter=24_2
     * 接口链接：https://api.mch.weixin.qq.com/mmpaysptrans/pay_bank
     * </pre>
     *
     * @param request 请求对象
     * @return the ent pay bank result
     * @throws WxPayException the wx pay exception
     */
    //@PostMapping("/payBank")
    public EntPayBankResult payBank(PartnerTransferRequest request) throws WxPayException {

        PartnerTransferRequest partnerTransferRequest = new PartnerTransferRequest();
        List<PartnerTransferRequest.TransferDetail> transferDetailList = partnerTransferRequest.getTransferDetailList();

        this.wxService.getPartnerTransferService().batchTransfer(request);
        return null;
    }

}