package com.hncboy.chatgpt.front.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.front.framework.domain.entity.ChannelConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.Model;

/**
 * 模型列表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface ModelMapper extends BaseMapper<Model> {
    /**
     * 查询模型列表列表
     *
     * @param model 模型列表
     * @return 模型列表集合
     */
    public List<Model> selectModelList(Model model);

    public List<String> selectGidList();

}