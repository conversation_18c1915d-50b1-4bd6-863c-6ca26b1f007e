package com.hncboy.chatgpt.front.framework.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DifyChatCompleteResponse implements Serializable {
    private String id;
    private String message_id;
    private String conversation_id;
    private String mode;
    private String answer;
    private String metadata;
    //metadata (object) 元数据
    //usage (Usage) 模型用量信息
    //retriever_resources (array[RetrieverResource]) 引用和归属分段列表
    private String task_id;
    private String event;
    private LocalDateTime created_at;
    private String position;
    private String message_files;
    private String message;
    private String url;
    private String type;
}
