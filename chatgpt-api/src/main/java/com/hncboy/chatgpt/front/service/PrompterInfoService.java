package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.domain.dto.PrompterInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.PrompterInfoVO;

import java.util.List;

/**
 * 提词器信息 Dao
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/5/8
 */
public interface PrompterInfoService {


    /**
     * 根据关键词查找
     *
     * @param key
     * @return List<PrompterInfoVO>
     * @Author: zc.wu
     * @Date: 2023/5/8 11:12
     */
    List<PrompterInfoVO> queryListByKey(String key);


    /**
     * 分页查询数据
     *
     * @param dto
     * @return IPage<PrompterInfoVO>
     * @Author: zc.wu
     * @Date: 2023/5/8 20:13
     */
    IPage<PrompterInfoVO> queryListEntityPage(PrompterInfoDTO dto);


    /**
     * 插入提词器信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/5/8
     */
    boolean insertEntity(PrompterInfoDTO record);

    /**
     * 删除提词器信息
     *
     * @Param:@param id
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/5/8
     */
    boolean deleteById(Integer id);


    /**
     * 生成提词器
     *
     * @param industry
     * @return void
     * @Author: zc.wu
     * @Date: 2023/5/8 11:22
     */
    void saveIndustry(String industry);
}
