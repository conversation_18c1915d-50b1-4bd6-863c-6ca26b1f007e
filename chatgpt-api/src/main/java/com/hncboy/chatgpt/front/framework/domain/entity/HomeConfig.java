package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * ENTITY
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("home_config")
public class HomeConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 标题
     */
    private String title;


    /**
     * 描述
     */
    private String description;


    /**
     * 分类
     */
    private String tag;


    /**
     * 分类名称
     */
    private String tagName;


    /**
     * 输入提示
     */
    private String inputExample;


    /**
     * 图标
     */
    private String imgUrl;


    /**
     * 是否热门
     */
    private Integer hot;


    /**
     * 是否内置(0不是1是)
     */
    private Integer status;


    /**
     * 可用次数
     */
    private Integer num;


    /**
     * 搜索值
     */
    private String searchValue;


    /**
     * 创建者
     */
    private String createBy;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新者
     */
    private String updateBy;


    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 备注
     */
    private String remark;


    /**
     * 系统回答
     */
    private String sysContent;


}
