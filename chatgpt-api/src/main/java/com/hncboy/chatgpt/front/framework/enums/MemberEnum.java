package com.hncboy.chatgpt.front.framework.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/3/22 22:19 API 类型枚举
 */
@AllArgsConstructor
public enum MemberEnum {

    /**
     * 一个月
     */
    one(1191, "一个月", "限时特惠", 10, 1, true),

    /**
     * 三个月
     */
    two(1193, "三个月", "限时特惠", 28, 2, false),

    /**
     * 永久会员
     */
    three(1195, "永久会员", "限时特惠", 188, 999, false);

    /**
     * code 作为 key，封装为 Map
     */
    private static final Map<Integer, MemberEnum> CODE_MAP = Stream
            .of(MemberEnum.values())
            .collect(Collectors.toMap(MemberEnum::getGoodsId, Function.identity()));
    @Getter
    @EnumValue
    private final Integer goodsId;
    @Getter
    @EnumValue
    private final String title;
    @Getter
    @JsonValue
    private final String des;
    @Getter
    @JsonValue
    private final Integer price;
    @Getter
    @JsonValue
    private final Integer month;
    @Getter
    @JsonValue
    private final Boolean showTag;

    /**
     * 静态工厂反序列化
     *
     * @param key code
     * @return 启用停用枚举
     */
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static MemberEnum valueOfKey(Integer key) {
        return Optional.ofNullable(CODE_MAP.get(key))
                .orElseThrow(() -> new IllegalArgumentException(String.valueOf(key)));
    }


    public static List<Map<String, Object>> toList() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (MemberEnum item : MemberEnum.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("goodsId", item.getGoodsId());
            map.put("title", item.getTitle());
            map.put("des", item.getDes());
            map.put("price", item.getPrice());
            map.put("showTag", item.getShowTag());
            list.add(map);
        }
        return list;
    }


}
