package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;
import com.hncboy.chatgpt.front.mapper.ChatMessageMapper;
import com.hncboy.chatgpt.front.service.ChatMessageService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/3/25 16:33 聊天记录相关业务实现类
 */
@Service("FrontChatMessageServiceImpl")
public class ChatMessageServiceImpl extends ServiceImpl<ChatMessageMapper, ChatMessageDO> implements
        ChatMessageService {


    @Transactional(rollbackFor = Exception.class)
    @Override
    @Async
    public ChatMessageDO initChatMessage(ChatMessageDO chatMessageDO) {
        chatMessageDO.setCreateTime(new Date());
        chatMessageDO.setUpdateTime(new Date());
        save(chatMessageDO);
        return chatMessageDO;
    }


    /**
     * 查询对话结果
     *
     * @param openId
     * @return ChatMessageDO
     * @Author: zc.wu
     * @Date: 2023/4/11 10:21
     */
    @Override
    public List<ChatMessageDO> queryChatMessage(String openId) {
        LambdaQueryWrapper<ChatMessageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMessageDO::getOpenId, openId);
        List<ChatMessageDO> messageDO = this.list(queryWrapper);
        return messageDO;
    }


}
