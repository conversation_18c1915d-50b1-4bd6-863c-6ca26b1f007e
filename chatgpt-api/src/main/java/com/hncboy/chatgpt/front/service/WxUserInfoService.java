package com.hncboy.chatgpt.front.service;

import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog;
import com.hncboy.chatgpt.front.framework.domain.entity.WxUserInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.mp.bean.result.WxMpUser;

import java.util.List;

/**
 * 微信用户信息 Dao
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/7
 */
public interface WxUserInfoService extends IService<WxUserInfo> {


    /**
     * 保存用户
     *
     * @param wxUserInfo
     * @return boolean
     * @Author: zc.wu
     * @Date: 2023/4/7 17:46
     */
    WxUserInfoVO saveOrUpdateUser(WxMpUser wxUserInfo);
    WxUserInfoVO saveOrUpdateUser(WxMpUser wxUserInfo, String appId);


    /**
     * 更新vip到期时间
     *
     * @param openId
     * @return WxUserInfo
     * @Author: zc.wu
     * @Date: 2023/4/7 17:44
     */
    void updateVipTime(String openId, int month);


    /**
     * 效验用户可用次数
     *
     * @param openId
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/7 17:46
     */
    Boolean checkUserByType(String openId, String key);


    /**
     * 保存用户
     *
     * @param wxMpUser
     * @return boolean
     * @Author: zc.wu
     * @Date: 2023/4/7 17:46
     */
    WxUserInfoVO saveOrUpdateUser(WxOAuth2UserInfo wxMpUser);
    WxUserInfoVO saveOrUpdateUserAppId(WxOAuth2UserInfo userInfo, String appId);

    /**
     * 更新可用次数
     *
     * @param openId
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:33
     */
    void updateUserNum(String openId, Integer species);


    /**
     * 增加可用次数
     *
     * @param openId
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:33
     */
    void addUserNumMp(String openId, Integer species);


    /**
     * 根据openid查询用户
     *
     * @param openId
     * @return WxUserInfo
     * @Author: zc.wu
     * @Date: 2023/4/7 17:44
     */
    WxUserInfoVO queryUserInfoByOpenId(String openId);


    /**
     * 根据用户生成推广二维码
     *
     * @param openId
     * @return String
     * @Author: zc.wu
     * @Date: 2023/6/26 0026 下午 02:24
     */
    Integer createMpQrCodeTicket(String openId);


    /**
     * 保存用户
     *
     * @param userInfo
     * @param openId
     * @return
     */
    WxUserInfoVO saveOrUpdateUser(WxMaUserInfo userInfo, String openId);





    /**
     * 保存抖音用户
     *
     * @return boolean
     * @Author: zc.wu
     * @Date: 2023/4/7 17:46
     */
    WxUserInfoVO saveOrUpdateDyUser(JSONObject jsonObject);


    /**
     * 绑定父ID
     *
     * @param openId
     * @param parentId
     * @return void
     * @Author: zc.wu
     * @Date: 2023/7/10 0010 下午 06:03
     */
    void bandDingParent(String openId, String parentId);


    /**
     * 查询下级
     *
     * @return List<WxUserInfo>
     * @Author: zc.wu
     * @Date: 2023/7/11 0011 下午 03:35
     */
    List<WxUserInfoVO> querySonUserList();


    /**
     * 效验用户是否为VIp
     *
     * @param openId
     * @return boolean
     * @Author: zc.wu
     * @Date: 2023/7/18 0018 下午 05:28
     */
    boolean checkUserIsVip(String openId);


    /**
     * 保存微信code用户
     * @param wxMpUser
     * @return
     */
    WxUserInfoVO saveWxCodeUser(WxOAuth2UserInfo wxMpUser,String appId);
}
