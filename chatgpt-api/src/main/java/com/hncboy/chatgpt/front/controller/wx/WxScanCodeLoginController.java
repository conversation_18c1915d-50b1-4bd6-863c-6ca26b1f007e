package com.hncboy.chatgpt.front.controller.wx;


import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.front.framework.annotation.IgnoreAuth;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.dto.UserBaseInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.entity.WxUserInfo;
import com.hncboy.chatgpt.front.framework.domain.query.LoginInfoParam;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.enums.WxAppIdEnum;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.handler.response.ResultCode;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.front.handler.config.WebSocketConfig;
import com.hncboy.chatgpt.front.service.RedisService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.WX_SCENE_ID;
import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.WX_TOCLET_URL;

@AllArgsConstructor
@Controller
@RequestMapping("/scan")
@RestController
@Tag(name = "微信扫码登录")
@Slf4j
public class WxScanCodeLoginController {

    private final WxMpService wxService;
    private final RedisService redisService;
    private final UserBaseInfoService userBaseInfoService;
    private final WxUserInfoService wxUserInfoService;


    @IgnoreAuth
    @Operation(summary = "生成微信二维码")
    @GetMapping("/createQrCode")
    public R<Object> createQrCode(final String idCode, HttpServletRequest httpRequest) {
        try {
            String ipAddr = WebSocketConfig.getIpAddr(httpRequest);
            String key = "wx_qrcode_" + ipAddr + idCode;
            //根据IP缓存二维码
            if (redisService.exists(key)) {
                return R.data(redisService.get(key));
            }
            // 获取二维码ticket 60秒过期
            wxService.switchover(WxAppIdEnum.zns.getCode());
            WxMpQrCodeTicket wxMpQrCodeTicket = wxService.getQrcodeService().qrCodeCreateTmpTicket(WX_SCENE_ID, 60);
            // 链接后面拼上 Ticket 换取二维码图片
            wxMpQrCodeTicket.setUrl(WX_TOCLET_URL + wxMpQrCodeTicket.getTicket());
            redisService.set(key, wxMpQrCodeTicket, 60L);
            return R.data(wxMpQrCodeTicket);
        } catch (WxErrorException e) {
            log.error("二维码获取失败", e);
            return R.fail("二维码获取失败");
        }
    }


    @IgnoreAuth
    @Operation(summary = "查询微信是否登录成功")
    @PostMapping("/queryQrLogin")
    public R<UserBaseInfoVO> queryQrLogin(@RequestBody LoginInfoParam loginInfoParam, HttpServletRequest httpRequest) {
        String ipAddr = WebSocketConfig.getIpAddr(httpRequest);
        String key = "wx_qrcode_" + ipAddr + loginInfoParam.getIdCode();
        //二维码不存在 返回指定code
        if (!redisService.exists(key)) {
            return R.fail(ResultCode.QR_OVERDUE, "二维码过期");
        }
        if (StringUtils.isEmpty(loginInfoParam.getTicket())) {
            return R.fail("ticket不能为空");
        }
        if (!redisService.exists(loginInfoParam.getTicket())) {
            return R.fail("未扫码");
        }
        WxUserInfoVO wxUserInfoVO= (WxUserInfoVO) redisService.get(loginInfoParam.getTicket());
        loginInfoParam.setOpenId(wxUserInfoVO.getOpenId());
        loginInfoParam.setNickName(wxUserInfoVO.getNickName());
        loginInfoParam.setHeadImgUrl(wxUserInfoVO.getAvatarUrl());
        UserBaseInfoVO userBaseInfoVO = userBaseInfoService.initUserInfo(loginInfoParam);
        StpUtil.login(userBaseInfoVO.getId());
        //会话相关
        StpUtil.getSession().set(ApplicationConstant.USER, userBaseInfoVO);
        userBaseInfoVO.setToken(StpUtil.getTokenValue());
        //删除二维码相关缓存
        redisService.remove(key);
        redisService.remove(loginInfoParam.getTicket());
        return R.data(userBaseInfoVO);
    }

    /**
     * 暂时无使用
     * @param code
     * @param parentId
     * @param response
     * @return
     */
    @Operation(summary = "微信环境内根据code登录")
    @GetMapping("/loginByCode")
    public R<UserBaseInfoVO> greetUser(@RequestParam String code, @RequestParam String parentId, HttpServletResponse response) {
        try {
            wxService.switchover(WxAppIdEnum.zns.getCode());
            WxOAuth2AccessToken accessToken = wxService.getOAuth2Service().getAccessToken(code);
            WxOAuth2UserInfo user = wxService.getOAuth2Service().getUserInfo(accessToken, null);
            UserBaseInfoVO userBaseInfoByOpenId = null;
            if (Objects.nonNull(user)) {
                //查询用户是否存在
                userBaseInfoByOpenId= userBaseInfoService.getUserBaseInfoByOpenId(user.getOpenid());
                if (userBaseInfoByOpenId == null) {
                    //查询保存微信用户
                    WxUserInfoVO wxUserInfoVO = wxUserInfoService.saveWxCodeUser(user,WxAppIdEnum.zns.getCode());
                    LoginInfoParam loginInfoParam = new LoginInfoParam();
                    loginInfoParam.setOpenId(wxUserInfoVO.getOpenId());
                    loginInfoParam.setNickName(wxUserInfoVO.getNickName());
                    loginInfoParam.setHeadImgUrl(wxUserInfoVO.getAvatarUrl());
                    if (StringUtils.isNotEmpty(parentId)) {
                        loginInfoParam.setParentId(parentId);
                    }
                    userBaseInfoByOpenId = userBaseInfoService.initUserInfo(loginInfoParam);
                }else{
                    //存在则更新用户信息
                    UserBaseInfo userBaseInfo = new UserBaseInfo();
                    userBaseInfo.setOpenId(userBaseInfoByOpenId.getOpenId());
                    userBaseInfo.setNickName(user.getNickname());
                    userBaseInfo.setId(userBaseInfoByOpenId.getId());
                    userBaseInfoService.updateById(userBaseInfo);
                    //WxUserInfo openId = wxUserInfoService.getOne(new QueryWrapper<WxUserInfo>().eq("open_id", user.getOpenid()));
                    WxUserInfo wxUserInfo = new WxUserInfo();
                    wxUserInfo.setNickName(user.getNickname());
                    wxUserInfo.setOpenId(user.getOpenid());
                    wxUserInfo.setUnionId(user.getUnionId());
                    wxUserInfoService.updateById(wxUserInfo);

                }
                StpUtil.login(userBaseInfoByOpenId.getId());
                //会话相关
                StpUtil.getSession().set(ApplicationConstant.USER, userBaseInfoByOpenId);
                userBaseInfoByOpenId.setToken(StpUtil.getTokenValue());
                return R.data(userBaseInfoByOpenId);
            }
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        return R.fail("用户获取失败");
    }

    @Operation(summary = "获取用户授权")
    @GetMapping("/getOpenId")
    public R<UserBaseInfoVO> getOpenId(@RequestParam String code) {
        try {
            wxService.switchover(WxAppIdEnum.zns.getCode());
            WxOAuth2AccessToken accessToken = wxService.getOAuth2Service().getAccessToken(code);
            WxOAuth2UserInfo user = wxService.getOAuth2Service().getUserInfo(accessToken, null);
            UserBaseInfoVO userBaseInfoByOpenId = new UserBaseInfoVO();
            if (Objects.nonNull(user)) {
                userBaseInfoByOpenId.setOpenId(user.getOpenid());
                return R.data(userBaseInfoByOpenId);
            }
        } catch (WxErrorException e) {
          log.error("获取用户授权失败", e);
        }
        return R.fail("用户获取失败");
    }


}
