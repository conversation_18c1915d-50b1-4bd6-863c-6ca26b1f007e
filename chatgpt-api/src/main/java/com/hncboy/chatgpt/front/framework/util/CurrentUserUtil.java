package com.hncboy.chatgpt.front.framework.util;


import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.exception.AuthException;

/**
 * @Author: zc.wu
 * @Date: 2022/6/21 18:27
 */
public class CurrentUserUtil {

    /**
     * 获取当前用户信息
     *
     * @param
     * @return IotUserInfo
     * @Author: zc.wu
     * @Date: 2022/7/6 15:41
     */
    public static UserBaseInfoVO getCurrentUser() {
        UserBaseInfoVO userInfo = null;
        if (StpUtil.isLogin()) {
            userInfo = (UserBaseInfoVO) StpUtil.getSession(false).get(ApplicationConstant.USER);
        } else {
            throw new AuthException("用户未登录");
        }
        return userInfo;
    }


    public static String checkUserLogin() {
        try {
            return checkV2UserLogin().toString();
        } catch (Exception e) {
            WxUserInfoVO userInfo = null;
            if (StpUtil.isLogin()) {
                userInfo = (WxUserInfoVO) StpUtil.getSession(false).get(ApplicationConstant.USER);
                return userInfo.getOpenId();
            }
        }
        return null;
    }


    /**
     * 获取当前用户ID
     *
     * @param
     * @return Long
     * @Author: zc.wu
     * @Date: 2022/6/21 18:50
     */
    public static String getUserId() {
        try {
            return getV2UserId().toString();
        } catch (Exception e) {
            try {
                return getCurrentUser().getOpenId();
            } catch (Exception e1) {
                return null;
            }
        }
    }





    public static Integer checkV2UserLogin() {
        UserBaseInfoVO userInfo = null;
        if (StpUtil.isLogin()) {
            userInfo = (UserBaseInfoVO) StpUtil.getSession(false).get(ApplicationConstant.USER);
            return userInfo.getId();
        }
        return null;
    }


    /**
     * 获取当前用户ID
     *
     * @param
     * @return Long
     * @Author: zc.wu
     * @Date: 2022/6/21 18:50
     */
    public static Integer getV2UserId() {
        if(!StpUtil.isLogin()){
            throw new AuthException("请登录后再试");
        }
        try {
            return StpUtil.getLoginIdAsInt();
        } catch (Exception e) {
            throw new AuthException("请登录后再试");
        }
    }

}
