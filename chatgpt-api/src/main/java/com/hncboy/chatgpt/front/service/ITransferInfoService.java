package com.hncboy.chatgpt.front.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.entity.TransferInfo;

/**
 * 提现申请信息Service接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface ITransferInfoService extends IService<TransferInfo> {
    /**
     * 查询提现申请信息
     *
     * @param id 提现申请信息主键
     * @return 提现申请信息
     */
    public TransferInfo selectTransferInfoById(Long id);

    /**
     * 查询提现申请信息列表
     *
     * @param transferInfo 提现申请信息
     * @return 提现申请信息集合
     */
    public List<TransferInfo> selectTransferInfoList(TransferInfo transferInfo);

    /**
     * 新增提现申请信息
     *
     * @param transferInfo 提现申请信息
     * @return 结果
     */
    public int insertTransferInfo(TransferInfo transferInfo);

    /**
     * 修改提现申请信息
     *
     * @param transferInfo 提现申请信息
     * @return 结果
     */
    public int updateTransferInfo(TransferInfo transferInfo);
    
    /**
     * 删除提现申请信息信息
     *
     * @param id 提现申请信息主键
     * @return 结果
     */
    public int deleteTransferInfoById(Long id);
}