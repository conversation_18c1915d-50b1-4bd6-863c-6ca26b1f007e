package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.converter.IntelligentAgentConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.IntelligentAgentDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.IntelligentAgent;
import com.hncboy.chatgpt.front.framework.domain.entity.WriteAgent;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentAgentVO;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.mapper.IntelligentAgentMapper;
import com.hncboy.chatgpt.front.service.IntelligentAgentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能体信息实现
 *
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/2/28
 */
@Service
@Slf4j
public class IntelligentAgentServiceImpl extends ServiceImpl<IntelligentAgentMapper, IntelligentAgent> implements IntelligentAgentService {

    private static int page = 0;


    /**
     * 分页查询智能体信息列表
     *
     * @Param:@param dto
     * @return:IPage<IntelligentAgentVO>
     * @Author: wZhic
     * @Date:2024/2/28
     */
    @Override
    public IPage<IntelligentAgentVO> queryListEntityPage(IntelligentAgentDTO dto) {
        LambdaQueryWrapper<IntelligentAgent> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(dto.getTag()), IntelligentAgent::getTag, dto.getTag());
        wrapper.like(StrUtil.isNotEmpty(dto.getTitle()), IntelligentAgent::getTitle, dto.getTitle());
        wrapper.eq(Objects.nonNull(dto.getHot()), IntelligentAgent::getHot, dto.getHot());
        wrapper.eq(Objects.nonNull(dto.getFeatRecs()), IntelligentAgent::getFeatRecs, dto.getFeatRecs());
        wrapper.eq(Objects.nonNull(dto.getCharge()), IntelligentAgent::getCharge, dto.getCharge());
        wrapper.eq(Objects.nonNull(dto.getCreateBy()), IntelligentAgent::getCreateBy, CurrentUserUtil.getUserId());
        //默认查询启用的

        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        wrapper.le(IntelligentAgent::getStartTime, format);
        wrapper.gt(IntelligentAgent::getEndTime, format);
        
        wrapper.eq(IntelligentAgent::getStatus, 0);
        wrapper.orderByDesc(IntelligentAgent::getFeatRecs);
        wrapper.orderByDesc(IntelligentAgent::getHot);
        wrapper.orderByDesc(IntelligentAgent::getUseCnt);
        IPage<IntelligentAgent> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<IntelligentAgent> iPage = this.page(userPage, wrapper);
        IPage<IntelligentAgentVO> convert = iPage.convert(po -> IntelligentAgentConvert.INSTANCE.entityToVO(po));
        return convert;
    }


    /**
     * 查询智能体列表
     *
     * @param dto
     * @return List<IntelligentAgentVO>
     * @Author: zc.wu
     * @Date: 2024/2/28 0028 上午 11:16
     */
    @Override
    public List<IntelligentAgentVO> queryListEntityList(IntelligentAgentDTO dto) {
        LambdaQueryWrapper<IntelligentAgent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotEmpty(dto.getTag()), IntelligentAgent::getTag, dto.getTag());
        wrapper.eq(StrUtil.isNotEmpty(dto.getCreateBy()), IntelligentAgent::getCreateBy, dto.getCreateBy());
        wrapper.like(StrUtil.isNotEmpty(dto.getTitle()), IntelligentAgent::getTitle, dto.getTitle());
        wrapper.eq(Objects.nonNull(dto.getHot()), IntelligentAgent::getHot, dto.getHot());
        wrapper.eq(Objects.nonNull(dto.getCharge()), IntelligentAgent::getCharge, dto.getCharge());
        //默认查询启用的
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        wrapper.le(IntelligentAgent::getStartTime, format);
        wrapper.gt(IntelligentAgent::getEndTime, format);
        wrapper.eq(IntelligentAgent::getStatus, 0);
        wrapper.orderByDesc(IntelligentAgent::getUseCnt);
        return IntelligentAgentConvert.INSTANCE.entityListToVOList(this.list(wrapper));
    }



    /**
      * 根据ID查询智能体信息
      * @Author: zc.wu
      * @Date: 2024/3/1 0001 上午 11:53
       * @param id
      * @return IntelligentAgentVO
     */
    @Override
    public IntelligentAgentVO queryInfoById(Integer id) {
        return IntelligentAgentConvert.INSTANCE.entityToVO(this.getById(id));
    }


    /**
     * 根据ID查询智能体信息(包含核心系统回复 不能暴漏到前端 只能后台使用)
     * @Author: zc.wu
     * @Date: 2024/3/1 0001 上午 11:53
     * @param id
     * @return IntelligentAgentVO
     */
    @Override
    public IntelligentAgent queryInfoSysContentById(Integer id) {
        return this.getById(id);
    }

    /**
     * 根据模型名称获取默认智能体信息
     *
     * @Author: zd.zhong
     * @Date: 2024/3/18 0001 上午 11:53
     * @param modelName
     * @return IntelligentAgentVO
     */
    @Override
    public IntelligentAgentVO queryIntelligentByModelName(String modelName) {
        LambdaQueryWrapper<IntelligentAgent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntelligentAgent::getModelName, modelName);
        
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        wrapper.le(IntelligentAgent::getStartTime, format);
        wrapper.gt(IntelligentAgent::getEndTime, format);
        return IntelligentAgentConvert.INSTANCE.entityToVO(this.getOne(wrapper));
    }

    /**
     * 查询智能体分类列表
     *
     * @return List<String>
     * @Author: zc.wu
     * @Date: 2024/2/28 0028 上午 11:16
     */
    @Override
    public List<String> queryTagList() {
        List<String> list = this.baseMapper.queryAllTag();
        List<String> tempList = new ArrayList<>();
        list.stream().filter(Objects::nonNull).forEach(item -> {
            if (item.contains(";")) {
                String[] array  = item.split(";");
                tempList.addAll(Arrays.asList(array));
            } else {
                tempList.add(item);
            }
        });
        return tempList.stream().distinct().collect(Collectors.toList());
    }


    /**
     * 更新智能体信息
     *
     * @Param:@param record example
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/2/28
     */
    @Override
    public boolean updateEntity(IntelligentAgentDTO record) {
        IntelligentAgent entity = IntelligentAgentConvert.INSTANCE.dtoToEntity(record);
        return this.updateById(entity);
    }

    /**
     * 插入智能体信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/2/28
     */
    @Override
    public boolean insertEntity(IntelligentAgentDTO record) {
        IntelligentAgent entity = IntelligentAgentConvert.INSTANCE.dtoToEntity(record);
        entity.setCreateBy(Objects.requireNonNull(CurrentUserUtil.getV2UserId()).toString());
        return this.save(entity);
    }

    /**
     * 删除智能体信息
     *
     * @Param:@param example
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/2/28
     */
    @Override
    public boolean deleteById(Integer id) {
        return this.removeById(id);
    }


    /**
     * 远程接口抓取数据
     *
     * @param
     * @return void
     * @Author: zc.wu
     * @Date: 2024/2/28 0028 上午 11:36
     */
    @Override
    public void saveDataRemotely() {
        String s = HttpUtil.get("https://gpts.ddaiai.com/open/gpts");
        JSONObject object = JSONUtil.parseObj(s);
        object.getJSONArray("gpts").forEach(item -> {
            JSONObject itemJson = (JSONObject) item;
            String string = UnicodeUtil.toString(itemJson.getStr("name").toString());
//            if (string.contains("gpt") || StringUtils.isChinese(string)) {
            IntelligentAgentDTO intelligentAgentDTO = new IntelligentAgentDTO();
            intelligentAgentDTO.setTitle(itemJson.getStr("name").toString());
            List<IntelligentAgentVO> intelligentAgentVOS = this.queryListEntityList(intelligentAgentDTO);
            if (CollUtil.isEmpty(intelligentAgentVOS)) {
                intelligentAgentDTO.setGid(itemJson.getStr("gid").toString());
                intelligentAgentDTO.setModelName(UnicodeUtil.toString(itemJson.getStr("name").toString()));
                intelligentAgentDTO.setTitle(UnicodeUtil.toString(itemJson.getStr("name").toString()));
                intelligentAgentDTO.setDescription(UnicodeUtil.toString(itemJson.getStr("info").toString()));
                intelligentAgentDTO.setImgUrl(UnicodeUtil.toString(itemJson.getStr("logo").toString()));
                intelligentAgentDTO.setUseCnt(itemJson.getInt("use_cnt"));
                intelligentAgentDTO.setRemark(itemJson.getStr("id"));
                intelligentAgentDTO.setCharge(1);
                intelligentAgentDTO.setHot(1);
                intelligentAgentDTO.setMaxToken(1024);
                intelligentAgentDTO.setTemperature(0.2);
                intelligentAgentDTO.setNumContexts(5);
                IntelligentAgent entity = IntelligentAgentConvert.INSTANCE.dtoToEntity(intelligentAgentDTO);
                this.save(entity);
                log.info("数据符合要求--{}", string);

            }
//            } else {
//                log.warn("数据不符合要求--{}", string);
//            }
        });
//        page++;
//        saveDataRemotely();
    }

    @Override
    public List<IntelligentAgent> queryListByIds(List<Integer> ids) {
        if(CollUtil.isEmpty(ids)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<IntelligentAgent> wrapper = new LambdaQueryWrapper<>();
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        wrapper.le(IntelligentAgent::getStartTime, format);
        wrapper.gt(IntelligentAgent::getEndTime, format);
        wrapper.in(IntelligentAgent::getId, ids);
        return this.list(wrapper);
    }

    public void addUseCnt(Integer id) {
        this.baseMapper.addUseCnt(id);
    }


}
