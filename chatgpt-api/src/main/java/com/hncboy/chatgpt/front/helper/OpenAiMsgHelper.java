package com.hncboy.chatgpt.front.helper;

import com.hncboy.chatgpt.front.service.RedisService;
import com.unfbx.chatgpt.entity.chat.Message;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2023/4/6
 */
@Component
@Slf4j
public class OpenAiMsgHelper {


    public static String redisKey = "CHATGPT:";
    @Autowired
    private RedisService redisService;

    /**
     * 放入对话
     *
     * @param openId
     * @param msg
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/6 10:21
     */
    public List<Message> queryMsg(String openId, Message msg) {
        List<Message> keys = new ArrayList<>();
        if (redisService.exists(redisKey + openId)) {
            keys = (List<Message>) redisService.get(redisKey + openId);
        }
        keys.add(msg);
        redisService.set(redisKey + openId, keys, 600L);
        while (getLength(keys) > 3000) {
            keys.remove(0);
        }
        return keys;
    }


    public long getLength(List<Message> messages) {
        long lenght = 0;
        for (Message message : messages) {
            lenght += message.getContent().getBytes().length;
        }
        return lenght;
    }


    /**
     * 放入答案
     *
     * @param openId
     * @param msg
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/6 10:37
     */
    public void addAnswer(String openId, String msg) {
        Message message = Message.builder().role(Message.Role.SYSTEM).content(msg).build();
        queryMsg(openId, message);
    }

}
