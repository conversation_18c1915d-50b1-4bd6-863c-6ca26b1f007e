package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.RechargeLog;
import com.hncboy.chatgpt.front.service.RechargeLogService;
import com.hncboy.chatgpt.front.mapper.RechargeLogMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【recharge_log】的数据库操作Service实现
* @createDate 2024-03-26 18:30:52
*/
@Service
public class RechargeLogServiceImpl extends ServiceImpl<RechargeLogMapper, RechargeLog>
    implements RechargeLogService{

    @Override
    public void insert(RechargeLog rechargeLog) {
        this.baseMapper.insert(rechargeLog);
    }
}




