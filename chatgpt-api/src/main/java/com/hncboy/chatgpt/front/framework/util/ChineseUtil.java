package com.hncboy.chatgpt.front.framework.util;


import org.springframework.stereotype.Component;

@Component
public class ChineseUtil {

    /**
     * 判断给定的字符串是否包含汉字字符。
     *
     * @param input 需要检查的字符串
     * @return 如果字符串包含至少一个汉字，则返回true；否则返回false
     */
    public static boolean containsChineseCharacter(String input) {
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c >= 0x4E00 && c <= 0x9FA5) { // 汉字的Unicode编码范围
                return true;
            }
        }
        return false;
    }

}
