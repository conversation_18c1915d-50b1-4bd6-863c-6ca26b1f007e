package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 角色消息模版 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/8/2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("role_msg_template")
public class RoleMsgTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 内容
     */
    private String content;


    /**
     * 配置ID
     */
    private Integer configId;


    /**
     * 类型
     */
    private Integer type;


}
