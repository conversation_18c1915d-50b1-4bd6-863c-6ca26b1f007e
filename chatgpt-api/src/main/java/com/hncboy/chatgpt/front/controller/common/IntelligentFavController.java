package com.hncboy.chatgpt.front.controller.common;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.domain.dto.IntelligentAgentDTO;
import com.hncboy.chatgpt.front.framework.domain.dto.IntelligentFavDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentAgentVO;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentFavVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.service.IntelligentAgentService;
import com.hncboy.chatgpt.front.service.IntelligentFavService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 智能体收藏 控制器
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/1
 */
@Slf4j
@FrontPreAuth
@AllArgsConstructor
@RestController
@RequestMapping("/intelligentFav/")
@Tag(name = "智能体收藏")
public class IntelligentFavController {

    private final IntelligentFavService iIntelligentFavService;
    private final IntelligentAgentService intelligentAgentService;

    /**
     *  根据ID查询智能体收藏
     * @Param: id
     * @return: IntelligentFavVO
     * @Author: wZhic
     * @Date: 2024/3/1
     */
    @GetMapping("query/{id}")
    @Operation(summary = "根据ID查询智能体收藏")
    public R<IntelligentFavVO> queryById(@PathVariable("id") Integer id){
        IntelligentFavVO entityVo=iIntelligentFavService.queryEntityById(id);
        return R.data(entityVo);
    }

    /**
     *  分页查询智能体收藏列表
     * @Param: IntelligentFavDTO
     * @return: LIST<IntelligentFavVO>
     * @Author: wZhic
     * @Date: 2024/3/1
     */
    @PostMapping("page/list")
    @Operation(summary = "分页查询智能体收藏列表")
    public R<IPage<IntelligentFavVO>>page(@RequestBody IntelligentFavDTO dto){
        IntelligentAgentDTO intelligentAgentDTO = new IntelligentAgentDTO();
        intelligentAgentDTO.setFeatRecs(1);
        IPage<IntelligentAgentVO> agentVOList = intelligentAgentService.queryListEntityPage(intelligentAgentDTO);
        if (ObjectUtil.isNull(agentVOList)) {
            return R.data(iIntelligentFavService.queryListEntityPage(dto));
        } else {
            IPage<IntelligentFavVO> agentFavList = iIntelligentFavService.queryListEntityPage(dto);
            List<IntelligentAgentVO> list = agentVOList.getRecords();
            Collections.reverse(list);
            list.forEach(item -> {
                IntelligentFavVO intelligentFavVO = new IntelligentFavVO();
                intelligentFavVO.setId(999999);
                intelligentFavVO.setAgentId(item.getId());
                intelligentFavVO.setAgentVo(item);
                intelligentFavVO.setCreateBy(CurrentUserUtil.getV2UserId().toString());
                agentFavList.getRecords().add(0, intelligentFavVO);
            });
            return R.data(agentFavList);
        }
    }

    /**
     *  新增智能体收藏列表
     * @Param: IntelligentFavDTO
     * @Author: wZhic
     * @Date: 2024/3/1
     */
    @PostMapping("save")
    @Operation(summary = "新增智能体收藏")
    public R insert(@RequestBody @Validated IntelligentFavDTO dto){
        iIntelligentFavService.insertEntity(dto);
        return R.data(dto.getId(), "收藏成功");
    }

    /**
     *  根据ID删除智能体收藏
     * @Param: id
     * @Author: wZhic
     * @Date: 2024/3/1
     */
    @GetMapping("delete/{id}")
    @Operation(summary = "根据ID删除智能体收藏")
    public R deleteById(@PathVariable("id") Integer id) {
        return R.data(iIntelligentFavService.deleteById(id));
    }
}