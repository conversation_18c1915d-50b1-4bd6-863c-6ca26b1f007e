package com.hncboy.chatgpt.front.framework.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * The type Alipay cache structure.
 *
 * <AUTHOR>
 * @Date: 2024-07-04
 */
@Data
@Accessors(chain = true)
public class AlipayCacheStructure {

    private String url;

    private Date createdTime;

    private String ordersId;

    private String productType;

    private Double productPrice;

    private String productName;

}
