package com.hncboy.chatgpt.front.service;

import com.hncboy.chatgpt.front.framework.domain.dto.SysConfigDTO;
import com.hncboy.chatgpt.front.framework.domain.dto.UserConfigDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.UserConfigVO;

import java.util.List;

/**
 * 用户配置相关信息 Dao
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
public interface UserConfigService {

    /**
     * 查询系统配置
     *
     * @param
     * @return SysConfigDTO
     * @Author: zc.wu
     * @Date: 2023/6/3 0003 下午 02:25
     */
    SysConfigDTO querySysConfig();


    /**
     * 查询用户配置
     *
     * @param openid
     * @param type
     * @return List<UserConfigVO>
     * @Author: zc.wu
     * @Date: 2023/6/15 0015 下午 01:12
     */
    List<UserConfigVO> queryUserConfig(String openid, Integer type);


    /**
     * 查询用户模型
     *
     * @param openid
     * @return String
     * @Author: zc.wu
     * @Date: 2023/7/18 0018 下午 04:44
     */
    String queryUserModel(String openid);


    /**
     * 保存用户配置
     *
     * @param userConfigDTO
     * @return void
     * @Author: zc.wu
     * @Date: 2023/6/15 0015 下午 01:12
     */
    void saveUserConfig(UserConfigDTO userConfigDTO);


    /**
     * 保存更新用户配置
     *
     * @param userConfigDTO
     * @return void
     * @Author: zc.wu
     * @Date: 2023/7/18 0018 下午 04:59
     */
    void saveOrUpdate(UserConfigDTO userConfigDTO);


}
