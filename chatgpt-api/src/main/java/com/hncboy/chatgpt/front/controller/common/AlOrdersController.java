package com.hncboy.chatgpt.front.controller.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.domain.dto.AlOrdersDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.AlOrdersVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.service.AlOrdersService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 支付宝充值记录查询
 * @Version: v1.0.0
 * @Author: zyc
 * @Date: 2024/7/4
 */
@Slf4j
@FrontPreAuth
@AllArgsConstructor
@RestController
@RequestMapping("/ali")
@Tag(name = "支付宝充值记录")
public class AlOrdersController {

    private final AlOrdersService alOrdersService;

    @Operation(summary = "分页查询支付宝充值记录列表")
    @PostMapping("/page/list")
    public R<IPage<AlOrdersVO>> queryListEntityPage(
            @RequestBody AlOrdersDTO alOrdersDTO) {
        return R.data(alOrdersService.queryListEntityPage(alOrdersDTO));
    }

}
