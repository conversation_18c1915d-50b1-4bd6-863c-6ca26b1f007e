package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付订单信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wx_pay_order")
@Accessors(chain = true)
public class WxPayOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 商品ID
     */
    private Integer goodsId;
    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 商品描述
     */
    private String body;


    /**
     * 商户订单号
     */
    private String outTradeNo;


    /**
     * 总金额
     */
    private Double totalFee;


    /**
     * 状态
     */
    private Integer status;


    /**
     * 终端IP
     */
    private String spbillCreateIp;


    /**
     * 通知地址
     */
    private String notifyUrl;


    /**
     * 交易类型
     */
    private String tradeType;


    /**
     * 用户标识
     */
    private String openid;


    /**
     * 原始数据
     */
    private String originalMessage;


    /**
     * 是否关注公众账号
     */
    private String isSubscribe;


    /**
     * 微信支付订单号
     */
    private String transactionId;


    /**
     * 支付完成时间
     */
    private Date timeEnd;


    /**
     * 创建者
     */
    private String createBy;


    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 更新者
     */
    private String updateBy;


    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 失效时间
     */
    private Date expiresTime;


    /**
     * 备注
     */
    private String remark;

    /**
     * 购买数量
     */
    private Long num;

    /**
     * 单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)
     */
    private String unit;

    private String productType;

    private String tradeChannel;

    private String currency;


}
