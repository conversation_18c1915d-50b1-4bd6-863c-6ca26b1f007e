package com.hncboy.chatgpt.front.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.Model;
import com.hncboy.chatgpt.front.mapper.ModelMapper;
import com.hncboy.chatgpt.front.service.IModelService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 模型列表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Service
public class ModelServiceImpl extends ServiceImpl<ModelMapper, Model> implements IModelService {
    
    /**
     * 查询模型列表
     *
     * @param id 模型列表主键
     * @return 模型列表
     */
    @Override
    public Model selectModelById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询模型列表列表
     *
     * @param model 模型列表
     * @return 模型列表
     */
    @Override
    public List<Model> selectModelList(Model model) {
        return baseMapper.selectModelList(model);
    }

    /**
     * 新增模型列表
     *
     * @param model 模型列表
     * @return 结果
     */
    @Override
    public int insertModel(Model model) {
            return baseMapper.insert(model);
    }

    /**
     * 修改模型列表
     *
     * @param model 模型列表
     * @return 结果
     */
    @Override
    public int updateModel(Model model) {
        return baseMapper.updateById(model);
    }


    /**
     * 删除模型列表信息
     *
     * @param id 模型列表主键
     * @return 结果
     */
    @Override
    public int deleteModelById(Long id) {
       
        return baseMapper.deleteById(id);
    }




}