package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信用户信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/7
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("wx_user_info")
public class WxUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * openId
     */
    @TableId(type = IdType.INPUT)
    private String openId;


    /**
     * 用户昵称
     */
    private String nickName;


    /**
     * 渠道来源
     */
    private String subscribeScene;


    /**
     * 二维码扫码场
     */
    private String qrScene;


    /**
     * 用户性别
     */
    private String gender;


    /**
     * 语言
     */
    private String language;


    /**
     * 用户所在城市
     */
    private String city;


    /**
     * unionId
     */
    private String unionId;


    /**
     * 用户所在省份
     */
    private String province;


    /**
     * 用户所在国家
     */
    private String country;


    /**
     * 用户头像图片
     */
    private String avatarUrl;


    /**
     * vip到期时间
     */
    private Date vipEndTime;


    /**
     * 金币
     */
    private Integer applyNum;


    /**
     * 是否VIP  0否 1是
     */
    private Integer vip;


    /**
     * 父ID
     */
    private String parentId;


    /**
     * 搜索值
     */
    private String searchValue;


    /**
     * 创建者
     */
    private String createBy;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新者
     */
    private String updateBy;


    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 备注
     */
    private String remark;
    private String appId;

    /**
     * token
     */
    @TableField(exist = false)
    private String token;


}
