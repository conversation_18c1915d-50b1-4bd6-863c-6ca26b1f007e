package com.hncboy.chatgpt.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.front.framework.domain.entity.ChannelConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

/**
 * 通道配置 Mapper
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/27
 */
@Mapper
public interface ChannelConfigMapper extends BaseMapper<ChannelConfig> {

    @SelectProvider(type = SqlProvider.class, method = "selectChannelConfigWithSiteInfo")
    List<ChannelConfig> selectChannelConfigWithSiteInfo(String modelGid);

    @SelectProvider(type = SqlProvider.class, method = "updateStatusByModelGid")
    List<ChannelConfig> updateStatusByModelGid(String modelGid);
}