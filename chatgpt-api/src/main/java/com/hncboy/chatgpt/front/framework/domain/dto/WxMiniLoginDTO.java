package com.hncboy.chatgpt.front.framework.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class WxMiniLoginDTO implements Serializable {
    private static final long serialVersionUID = 130569783040564371L;

    @Schema(title = "code")
    private String code;

    @Schema(title = "openId")
    private String openId;

    @NotEmpty(message = "会话密钥不能为空")
    @Schema(title = "会话密钥")
    private String sessionKey;

    @NotEmpty(message = "数据签名不能为空")
    @Schema(title = "数据签名")
    private String signature;

    @Schema(title = "微信用户基本信息")
    private String rawData;

    @Schema(title = "消息密文")
    private String encryptedData;

    @NotEmpty(message = "加密算法的初始向量不能为空")
    @Schema(title = "加密算法的初始向量")
    private String iv;

    @Schema(title = "头像")
    private String avatarUrl;

    @Schema(title = "用户昵称")
    private String nickName;
}
