package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.entity.WxUserInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description:
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2023/4/12
 */
@Mapper
public interface UserBaseInfoConverter {

    UserBaseInfoConverter INSTANCE = Mappers.getMapper(UserBaseInfoConverter.class);


    UserBaseInfoVO entityToVO(UserBaseInfo userBaseInfo);

    List<UserBaseInfoVO> entityListToVOList(List<UserBaseInfo> entityList);

}
