package com.hncboy.chatgpt.front.framework.handler.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.front.framework.annotation.IgnoreAuth;
import com.hncboy.chatgpt.front.framework.config.ChatConfig;
import com.hncboy.chatgpt.front.framework.exception.AuthException;
import com.hncboy.chatgpt.front.framework.util.WebLogUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2023/3/23 00:19 FrontPreAuth 用户端切面
 */
@Aspect
@Component
public class FrontPreAuthAspect {

    @Resource
    private ChatConfig chatConfig;

    @Pointcut("@annotation(com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth) || @within(com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth)")
    public void pointcut() {

    }

    /**
     * 切 方法 和 类上的 @PreAuth 注解
     *
     * @param point 切点
     * @return Object
     * @throws Throwable 没有权限的异常
     */
    @Around("pointcut()")
    public Object checkAuth(ProceedingJoinPoint point) throws Throwable {
        // 获取目标方法
        Method method = ((MethodSignature) point.getSignature()).getMethod();

        // 检查方法或类上是否有 @IgnoreAuth 注解
        if (AnnotationUtils.findAnnotation(method, IgnoreAuth.class) != null ||
                AnnotationUtils.findAnnotation(method.getDeclaringClass(), IgnoreAuth.class) != null) {
            return point.proceed();
        }

        if (!StpUtil.isLogin()) {
            WebLogUtil.requestLog(point);
            throw new AuthException("请登录后再进行操作");
        }
        return point.proceed();
    }
}
