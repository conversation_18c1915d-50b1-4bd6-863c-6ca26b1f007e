package com.hncboy.chatgpt.front.framework.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/3/25 16:36
 * 聊天消息状态
 */
@AllArgsConstructor
public enum ChatMessageStatusEnum {

    /**
     * 针对
     */
    INIT(0),

    /**
     * 完成
     */
    PART_SUCCESS(1);


    @Getter
    @EnumValue
    private final Integer code;
}
