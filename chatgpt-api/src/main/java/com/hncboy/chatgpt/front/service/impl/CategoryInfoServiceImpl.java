package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.converter.CategoryInfoConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.CategoryInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.CategoryInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.CategoryInfoVO;
import com.hncboy.chatgpt.front.mapper.CategoryInfoMapper;
import com.hncboy.chatgpt.front.service.CategoryInfoService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分类信息实现
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Service
public class CategoryInfoServiceImpl extends
        ServiceImpl<CategoryInfoMapper, CategoryInfo> implements CategoryInfoService {

    /**
     * 根据ID查询分类信息
     *
     * @Param:@param id
     * @return:jialand.account.infrastructure.db.entity.CategoryInfo
     * @Author: wzhic
     * @Date:2023/4/19
     */
    @Override
    public CategoryInfoVO queryEntityById(Integer id) {
        CategoryInfo entity = this.getById(id);
        CategoryInfoVO entityVO = CategoryInfoConvert.INSTANCE.entityToVO(entity);
        return entityVO;
    }

    /**
     * 查询分类信息列表
     *
     * @Param:@param dto
     * @return:List<jialand.account.infrastructure.db.entity.CategoryInfo>
     * @Author: wzhic
     * @Date:2023/4/19
     */
    @Override
    public List<CategoryInfoVO> queryListEntityPage(Integer type) {
        LambdaQueryWrapper<CategoryInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CategoryInfo::getType, type);
        wrapper.orderByAsc(CategoryInfo::getSort);
        return CategoryInfoConvert.INSTANCE.entityListToVOList(this.list(wrapper));
    }

    /**
     * 查询单条分类信息
     *
     * @Param:@param dto
     * @return:List<jialand.account.infrastructure.db.entity.CategoryInfo>
     * @Author: wzhic
     * @Date:2023/4/19
     */
    @Override
    public CategoryInfoVO queryEntityByDto(CategoryInfoDTO dto) {
        LambdaQueryWrapper<CategoryInfo> wrapper = new LambdaQueryWrapper<>();
        return CategoryInfoConvert.INSTANCE.entityToVO(this.getOne(wrapper));
    }

    /**
     * 更新分类信息
     *
     * @Param:@param record example
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/19
     */
    @Override
    public boolean updateEntity(CategoryInfoDTO record) {
        CategoryInfo entity = CategoryInfoConvert.INSTANCE.dtoToEntity(record);
        return this.updateById(entity);
    }

    /**
     * 插入分类信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/19
     */
    @Override
    public boolean insertEntity(CategoryInfoDTO record) {
        CategoryInfo entity = CategoryInfoConvert.INSTANCE.dtoToEntity(record);
        return this.save(entity);
    }

    /**
     * 删除分类信息
     *
     * @Param:@param example
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/19
     */
    @Override
    public boolean deleteById(Integer id) {
        return this.removeById(id);
    }

}
