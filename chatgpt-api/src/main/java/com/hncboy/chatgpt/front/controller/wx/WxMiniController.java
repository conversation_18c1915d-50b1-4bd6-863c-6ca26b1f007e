package com.hncboy.chatgpt.front.controller.wx;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.dev33.satoken.stp.StpUtil;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.dto.WxMiniLoginDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.AlyOssUtils;
import com.hncboy.chatgpt.front.helper.QiNuOssHelper;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @create 2023-06-03 13:51
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/wx/mini")
@Tag(name = "微信小程序接口")
public class WxMiniController {

    private final WxMaService wxMaService;

    private final WxUserInfoService wxUserInfoService;


    @Operation(summary = "登录接口")
    @PostMapping("/login")
    public R<WxUserInfoVO> mpLogin(@RequestBody @Validated WxMiniLoginDTO wxMiniLoginDTO) {
        try {
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(wxMiniLoginDTO.getCode());
            wxMiniLoginDTO.setSessionKey(sessionInfo.getSessionKey());
            wxMiniLoginDTO.setOpenId(sessionInfo.getOpenid());
        } catch (WxErrorException e) {
            R.fail("登录失败");
        }
        // 用户信息校验
        if (!wxMaService.getUserService().checkUserInfo(wxMiniLoginDTO.getSessionKey(),
                wxMiniLoginDTO.getRawData(), wxMiniLoginDTO.getSignature())) {
            WxMaConfigHolder.remove();
            throw new RuntimeException("user check failed");
        }
        String openId = wxMiniLoginDTO.getOpenId();
        // 解密用户信息
        WxMaUserInfo userInfo = wxMaService.getUserService().getUserInfo(wxMiniLoginDTO.getSessionKey(),
                wxMiniLoginDTO.getEncryptedData(), wxMiniLoginDTO.getIv());
        userInfo.setAvatarUrl(wxMiniLoginDTO.getAvatarUrl());
        userInfo.setNickName(wxMiniLoginDTO.getNickName());
        WxUserInfoVO wxUserInfo = wxUserInfoService.saveOrUpdateUser(userInfo, openId);
        StpUtil.login(openId);
        //会话相关
        StpUtil.getSession().set(ApplicationConstant.USER, wxUserInfo);
        wxUserInfo.setToken(StpUtil.getTokenValue());
        WxMaConfigHolder.remove();
        return R.data(wxUserInfo);
    }

    /**
     * 上传文件
     *
     * @param file
     * @return Res
     * @Author: zc.wu
     * @Date: 2022/3/28 3:33 PM
     */
    @PostMapping("/upload")
    @ResponseBody
    public R uploadFile(@RequestParam("file") MultipartFile file) {
        String s = AlyOssUtils.ossUpload(file);
        if (StringUtils.isEmpty(s)) {
            return R.fail("上传失败");
        }
        return R.data(s);
    }


    /**
     * 上传图片文件并返回地址
     *
     * @param file
     * @return Res
     * @Author: zc.wu
     * @Date: 2022/6/27 11:28
     */
    @PostMapping("/uploadFile")
    @ResponseBody
    public R<Object> uploadLocalFile(@RequestParam("file") MultipartFile file) {
        String s = QiNuOssHelper.qnyUpload(file);
        if (StringUtils.isEmpty(s)) {
            return R.fail("上传失败");
        }
        return R.data(s);
    }
}
