package com.hncboy.chatgpt.front.helper;

import com.hncboy.chatgpt.front.api.apikey.ApiKeyChatClientBuilder;
import com.unfbx.chatgpt.entity.Tts.TextToSpeech;
import com.unfbx.chatgpt.entity.Tts.TtsFormat;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class ChatTTSHelper {

    public CompletableFuture<String> textToSpeed(String content, String voice) {
        CompletableFuture<String> future = new CompletableFuture<>();

        TextToSpeech textToSpeech = TextToSpeech.builder()
                .model(TextToSpeech.Model.TTS_1.getName())
                .input(content)
                .voice(voice)
                .responseFormat(TtsFormat.MP3.getName())
                .build();

        ApiKeyChatClientBuilder.buildOpenAiClient().textToSpeech(textToSpeech, new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        InputStream inputStream = response.body().byteStream();
                        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                            outputStream.write(buffer, 0, bytesRead);
                        }

                        byte[] audioData = outputStream.toByteArray();
                        String audioUrl = QiNuOssHelper.qnyUpload(UUID.randomUUID() + ".mp3", audioData); // 上传到七牛云
                        future.complete(audioUrl);
                    } else {
                        future.completeExceptionally(new RuntimeException("Failed to get audio data from OpenAI."));
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    future.completeExceptionally(e);
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                future.completeExceptionally(t);
            }
        });

        return future;
    }

}

