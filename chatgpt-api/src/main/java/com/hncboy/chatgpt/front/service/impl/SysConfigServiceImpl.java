package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.SysConfig;
import com.hncboy.chatgpt.front.mapper.SysConfigMapper;
import com.hncboy.chatgpt.front.service.SysConfigService;
import org.springframework.stereotype.Service;

/**
 * 用户配置相关信息实现
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements
        SysConfigService {


    /**
     * 根据指定key查询配置
     *
     * @param key
     * @return String
     * @Author: zc.wu
     */
    @Override
    public SysConfig querySysConfig(String key) {
        LambdaQueryWrapper<SysConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysConfig::getConfigKey, key);
        return this.getOne(queryWrapper);
    }

}
