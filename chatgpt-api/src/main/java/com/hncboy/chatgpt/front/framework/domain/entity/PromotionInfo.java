package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 活动信息 ENTITY
 *
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/8/6
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("promotion_info")
public class PromotionInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 标题
     */
    private String title;


    /**
     * 描述
     */
    private String description;


    /**
     * 开始时间
     */
    private Date startTime;


    /**
     * 结束时间
     */
    private Date endTime;


    /**
     * 图标
     */
    private String logoUrl;


    /**
     * 海报
     */
    private String posterUrl;
    
    private String h5Url;

    /**
     * 关联的产品
     */
    private String relatedProductId;


    /**
     * 启用状态(0启用1禁用)
     */
    private Integer status;


    /**
     * 备注
     */
    private String remark;


    /**
     * 创建者
     */
    private String createBy;
    
    private String uiConf;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


    /**
     * 更新者
     */
    private String updateBy;


    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


}
