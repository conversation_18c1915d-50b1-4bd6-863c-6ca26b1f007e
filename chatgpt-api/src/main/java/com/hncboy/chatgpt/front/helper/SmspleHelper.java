//package com.hncboy.chatgpt.front.helper;
//
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.aliyuncs.DefaultAcsClient;
//import com.aliyuncs.IAcsClient;
//import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
//import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
//import com.aliyuncs.exceptions.ClientException;
//import com.aliyuncs.profile.DefaultProfile;
//import com.google.gson.Gson;
//import com.hncboy.chatgpt.front.framework.config.AliyunSmsConfig;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.net.ssl.*;
//import java.io.*;
//import java.net.URL;
//import java.net.URLEncoder;
//import java.security.MessageDigest;
//import java.security.NoSuchAlgorithmException;
//import java.security.cert.CertificateException;
//import java.security.cert.X509Certificate;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
//import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.OK_CODE;
//
///**
// * @Description:短信发送帮助
// * @Version：v1.0.0
// * @Author: zc.wu
// * @Date:2022/6/17
// */
//@Component
//@Slf4j
//public class SmspleHelper {
//
//    @Autowired
//    private AliyunSmsConfig aliyunSmsConfig;
//
//    /**
//     * 发送登录短信
//     *
//     * @param phone 手机号
//     * @param code  验证码
//     * @return Boolean
//     * @Author: zc.wu
//     * @Date: 2022/6/17 14:08
//     */
//    public Boolean sendLoginMsg(String phone, String code) {
//        Map map = new HashMap<>();
//        map.put("code", code);
//        return sendSmsMsg(phone, aliyunSmsConfig.getLoginCode(), map);
//    }
//
//
//    /**
//     * 发送注册短信
//     *
//     * @param phone    手机号
//     * @param account  账户
//     * @param password 密码
//     * @return Boolean
//     * @Author: zc.wu
//     * @Date: 2022/6/17 14:07
//     */
//    public Boolean sendRegisteredMsg(String phone, String account, String password) {
//        Map map = new HashMap<>();
//        map.put("account", account);
//        map.put("password", password);
//        return sendSmsMsg(phone, aliyunSmsConfig.getRegisteredUserCode(), map);
//    }
//
//
//    /**
//     * 发送短信验证码
//     *
//     * @param phone        手机号
//     * @param templateCode 模版
//     * @param map          模版参数
//     * @return Boolean
//     * @Author: zc.wu
//     * @Date: 2022/6/17 14:05
//     */
//    public Boolean sendSmsMsg(String phone, String templateCode, Map map) {
//        DefaultProfile profile = DefaultProfile.getProfile(aliyunSmsConfig.getRegionId(),
//                aliyunSmsConfig.getAccessKeyId(), aliyunSmsConfig.getAccessKeySecret());
//        IAcsClient client = new DefaultAcsClient(profile);
//        SendSmsRequest request = new SendSmsRequest();
//        request.setSignName(aliyunSmsConfig.getSignName());
//        request.setTemplateCode(templateCode);
//        request.setTemplateParam(JSONUtil.toJsonStr(map));
//        request.setPhoneNumbers(phone);
//        try {
//            SendSmsResponse response = client.getAcsResponse(request);
//            log.info("短信发送结果:{}", new Gson().toJson(response));
//            if (response.getCode().equals(OK_CODE)) {
//                return true;
//            }
//        } catch (ClientException e) {
//            log.error("短信发送结果:{}", e.getErrMsg());
//            return false;
//        }
//        return false;
//    }
//
//
//    private static final String WSSE_HEADER_FORMAT = "UsernameToken Username=\"%s\",PasswordDigest=\"%s\"," +
//            "Nonce=\"%s\",Created=\"%s\"";
//    private static final String AUTH_HEADER_VALUE = "WSSE realm=\"SDP\",profile=\"UsernameToken\",type=\"Appkey\"";
//
//
//    public Boolean sendHySms(String phone, String code) {
//        try {
//            String s = sendSMS(phone, code);
//            if (JSONUtil.isTypeJSON(s)) {
//                JSONObject parse = JSONUtil.parseObj(s);
//                if (parse.getStr("code").equals("000000")) {
//                    return Boolean.TRUE;
//                }
//            }
//        } catch (Exception e) {
//            return Boolean.FALSE;
//        }
//        return Boolean.TRUE;
//    }
//
//
//    public String sendSMS(String receiver, String verificationCode) throws Exception {
//        String url = "https://smsapi.cn-north-4.myhuaweicloud.com:443/sms/batchSendSms/v1";
//        String appKey = "f153lOjOFz9Vu8d7RTUXD6biu0Tn";
//        String appSecret = "7MDALPZ3BPTmzlpmTwCVE5iM6Iz1";
//        String sender = "8824030427662";
//        String templateId = "101cfe35c3b64bdb935d69352a51e5db";
//        String signature = "方顶";
//        String statusCallBack = "";
//        String templateParas = "[\"" + verificationCode + "\"]";
//
//        String body = buildRequestBody(sender, receiver, templateId, templateParas, statusCallBack, signature);
//        String wsseHeader = buildWsseHeader(appKey, appSecret);
//
//        Writer out = null;
//        BufferedReader in = null;
//        StringBuffer result = new StringBuffer();
//        HttpsURLConnection connection = null;
//        InputStream is = null;
//
//        HostnameVerifier hv = (hostname, session) -> true;
//        trustAllHttpsCertificates();
//
//        try {
//            URL realUrl = new URL(url);
//            connection = (HttpsURLConnection) realUrl.openConnection();
//
//            connection.setHostnameVerifier(hv);
//            connection.setDoOutput(true);
//            connection.setDoInput(true);
//            connection.setUseCaches(true);
//            connection.setRequestMethod("POST");
//            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
//            connection.setRequestProperty("Authorization", AUTH_HEADER_VALUE);
//            connection.setRequestProperty("X-WSSE", wsseHeader);
//
//            connection.connect();
//            out = new OutputStreamWriter(connection.getOutputStream());
//            out.write(body);
//            out.flush();
//
//            int status = connection.getResponseCode();
//            if (status == 200) {
//                is = connection.getInputStream();
//            } else {
//                is = connection.getErrorStream();
//            }
//            in = new BufferedReader(new InputStreamReader(is, "UTF-8"));
//            String line;
//            while ((line = in.readLine()) != null) {
//                result.append(line);
//            }
//        } finally {
//            try {
//                if (out != null) out.close();
//                if (is != null) is.close();
//                if (in != null) in.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//        return result.toString();
//    }
//
//    private String buildRequestBody(String sender, String receiver, String templateId, String templateParas,
//                                    String statusCallBack, String signature) {
//        if (sender == null || receiver == null || templateId == null || sender.isEmpty() || receiver.isEmpty()
//                || templateId.isEmpty()) {
//            System.out.println("buildRequestBody(): sender, receiver or templateId is null.");
//            return null;
//        }
//        Map<String, String> map = new HashMap<>();
//        map.put("from", sender);
//        map.put("to", receiver);
//        map.put("templateId", templateId);
//        if (templateParas != null && !templateParas.isEmpty()) {
//            map.put("templateParas", templateParas);
//        }
//        if (statusCallBack != null && !statusCallBack.isEmpty()) {
//            map.put("statusCallback", statusCallBack);
//        }
//        if (signature != null && !signature.isEmpty()) {
//            map.put("signature", signature);
//        }
//
//        StringBuilder sb = new StringBuilder();
//        String temp;
//
//        for (String s : map.keySet()) {
//            try {
//                temp = URLEncoder.encode(map.get(s), "UTF-8");
//            } catch (UnsupportedEncodingException e) {
//                e.printStackTrace();
//                temp = "";
//            }
//            sb.append(s).append("=").append(temp).append("&");
//        }
//
//        return sb.deleteCharAt(sb.length() - 1).toString();
//    }
//
//    private String buildWsseHeader(String appKey, String appSecret) {
//        if (appKey == null || appSecret == null || appKey.isEmpty() || appSecret.isEmpty()) {
//            System.out.println("buildWsseHeader(): appKey or appSecret is null.");
//            return null;
//        }
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
//        String time = sdf.format(new Date());
//        String nonce = UUID.randomUUID().toString().replace("-", "");
//
//        MessageDigest md;
//        byte[] passwordDigest = null;
//
//        try {
//            md = MessageDigest.getInstance("SHA-256");
//            md.update((nonce + time + appSecret).getBytes());
//            passwordDigest = md.digest();
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
//
//        String passwordDigestBase64Str = Base64.getEncoder().encodeToString(passwordDigest);
//
//        return String.format(WSSE_HEADER_FORMAT, appKey, passwordDigestBase64Str, nonce, time);
//    }
//
//    private void trustAllHttpsCertificates() throws Exception {
//        TrustManager[] trustAllCerts = new TrustManager[]{
//                new X509TrustManager() {
//                    public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
//                    }
//
//                    public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
//                    }
//
//                    public X509Certificate[] getAcceptedIssuers() {
//                        return null;
//                    }
//                }
//        };
//        SSLContext sc = SSLContext.getInstance("SSL");
//        sc.init(null, trustAllCerts, null);
//        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
//    }
//
//
//}
