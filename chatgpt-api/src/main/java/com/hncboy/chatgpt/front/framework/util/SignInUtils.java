package com.hncboy.chatgpt.front.framework.util;

import cn.hutool.json.JSONArray;
import lombok.Data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * @描述:
 * @版本：v1.0.0
 * @作者: zc.wu
 * @时间:2021/10/15
 */
public class SignInUtils {

    public static void main(String[] args) throws Exception {
        // 注意时间倒叙
        List<SignIn> signInList = new ArrayList<>();
        signInList.add(new SignIn("2021-10-15"));
        signInList.add(new SignIn("2021-10-14"));
//    signInList.add(new SignIn("2021-10-13"));
        signInList.add(new SignIn("2021-10-12"));
        signInList.add(new SignIn("2021-10-11"));

//    int continuousSignInDay = getContinuousSignInDay(signInList);
//    System.out.println("连续签到日期：" + continuousSignInDay);
    }

    public static JSONArray GetRandomThreeInfoList(JSONArray list, int count) {
        JSONArray olist = new JSONArray();
        if (list.size() <= count) {
            return list;
        } else {
            Random random = new Random();
            for (int i = 0; i < count; i++) {
                int intRandom = random.nextInt(list.size() - 1);
                olist.add(list.get(intRandom));
                list.remove(list.get(intRandom));
            }
            return olist;
        }
    }

    /**
     * 连续签到天数
     *
     * @return int
     * <AUTHOR>
     * @Date 2019-08-15 17:16:01
     * @Param
     **/
    public static int getContinuousSignInDay(List<Date> signInList) {
        //continuousDay 连续签到数
        int continuousDay = 1;
        boolean todaySignIn = false;
        Date today = new Date();
        for (int i = 0; i < signInList.size(); i++) {
            int intervalDay = distanceDay(today, signInList.get(i));
            //当天签到
            if (intervalDay == 0 && i == 0) {
                todaySignIn = true;
            } else if (intervalDay == continuousDay) {
                continuousDay++;
            } else {
                //不连续，终止判断
                break;
            }
        }
        if (!todaySignIn) {
            continuousDay--;
        }
        return continuousDay;
    }

    /**
     * 两个日期对比间隔天数
     *
     * @param smallDay
     * @return boolean
     * <AUTHOR>
     * @Date 2019-08-13 18:42:41
     * @Param largeDay
     **/
    private static int distanceDay(Date largeDay, Date smallDay) {
        int day = (int) ((largeDay.getTime() - smallDay.getTime()) / (1000 * 60 * 60 * 24));
        return day;
    }

    @Data
    static class SignIn {

        private Date signInDay;

        public SignIn() {

        }

        public SignIn(String day) throws ParseException {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            signInDay = sdf.parse(day);
        }
    }

}
