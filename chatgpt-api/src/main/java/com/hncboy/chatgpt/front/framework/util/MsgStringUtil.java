package com.hncboy.chatgpt.front.framework.util;

import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description:
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2023/4/12
 */
public class MsgStringUtil {


    public static String msgStringHandel(String msg) {
        msg = msg.replaceAll(" ", "");
        msg = msg.replaceAll("帮我生成", "");
        return msg;
    }


    public static String reqMsgReplace(String msg) {
        msg = msg.replaceAll("gpt", "超级智能社").replaceAll("GPT", "超级智能社")
                .replaceAll("openai", "国内超级智能社团队").replaceAll("OPENAI", "")
                .replaceAll("chat gpt", "超级智能社").replaceAll("chat gpt", "超级智能社")
                .replaceAll("chatgpt", "超级智能社").replaceAll("ChatGPT", "超级智能社")
                .replaceAll("chatGpt", "超级智能社")
                .replaceAll("CHATGPT", "超级智能社").replaceAll("CHATGPT", "超级智能社")
                .replaceAll("Chat", "").replaceAll("OpenAI", "国内超级智能社团队").replaceAll("-3", "V1.0");
//        List<String> stringList = SensitiveWordHandler.checkWord(msg);
//        if (CollectionUtil.isNotEmpty(stringList)) {
//            for (String s : stringList) {
//                msg = msg.replaceAll(s, "* ");
//            }
//        }
        return msg;
    }

    /**
     * 外部渠道替换图片域名
     */
    public static String replaceDomainName(String text) {
        String replaceAll = text.replaceAll("((http://)|(https://))?([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,6}(/)", "");
        return replaceAll;
    }

    /**
     * 外部渠道替换图片域名
     */
    public static String replaceEng(String text) {
        if (StringUtils.isEmpty(text)) {
            return "";
        }
        //将text中的英文网址及参数替换成空
        if (text.contains(".jpeg")) {
            String[] split = text.split(".jpeg");
            return split[1];
        }
        //将text中的英文网址及参数替换成空
        if (text.contains(".png")) {
            String[] split = text.split(".png");
            return split[1];
        }
        return replaceDomainName(text);
    }

    /**
     * 将JSON字符串转换为键值对形式的字符串。
     *
     * @param jsonString 待转换的JSON字符串。假设该字符串格式正确，能够成功解析为JSON对象。
     * @return 转换后的键值对形式的字符串。每个键值对使用分号连接，键和值之间使用等号连接。
     */
    public static String convertToJsonKeyValue(String jsonString) {
        // 解析JSON字符串为JSON对象
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(jsonString);
        } catch (JSONException e) {
            // 如果解析失败，直接返回原字符串
            return jsonString;
        }

        // 遍历JSON对象，构建键值对字符串
        StringBuilder keyValueBuilder = new StringBuilder();
        for (String key : jsonObject.keySet()) {
            // 获取键对应的值
            String value = jsonObject.get(key).toString();
            if (JSONUtil.isTypeJSON(value)) {
                // 如果值是JSON对象，递归调用本方法进行转换
                value = "{" + convertToJsonKeyValue(value) + "}";
            }
            // 将键值对添加到结果字符串，每个键值对之后添加分号
            keyValueBuilder.append(key).append("=").append(value).append("; ");
        }

        // 移除结果字符串末尾的分号和空格
        if (keyValueBuilder.length() > 0) {
            keyValueBuilder.setLength(keyValueBuilder.length() - 2);
        }

        return keyValueBuilder.toString();
    }
}
