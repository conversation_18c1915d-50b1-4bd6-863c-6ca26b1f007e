package com.hncboy.chatgpt.front.framework.domain.dto;

import com.hncboy.chatgpt.front.framework.domain.query.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 促销活动信息 DTO
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/8/6
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PromotionInfoDTO extends PageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "关联产品ID")
    private String relatedProductId;

    @Schema(title = "开始时间")
    private Date startTime;

    @Schema(title = "结束时间")
    private Date endTime;

    @Schema(title = "海报")
    private String posterUrl;

    @Schema(title = "图标")
    private String logoUrl;

}
