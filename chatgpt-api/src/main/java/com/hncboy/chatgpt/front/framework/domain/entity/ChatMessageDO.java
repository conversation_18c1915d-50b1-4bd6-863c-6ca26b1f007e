package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/3/25 16:19
 * 聊天记录表实体类
 */
@Data
@TableName("chat_message")
public class ChatMessageDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String openId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 站点ID
     */
    private Integer siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 站点URL
     */
    private String siteUrl;

    /**
     * 父消息ID
     */
    private Long parentMsgId;

    /**
     * 消息类型枚举
     * 第一条消息一定是问题
     */
    private Integer messageType;

    /**
     * 模型GID
     */
    private String modelGid;

    /**
     * 智能体ID
     */
    private Integer agentId;

    /**
     * 智能体名称
     */
    private String agentName;

    /**
     * 聊天室 id
     */
    private Long chatRoomId;

    /**
     * ip
     */
    private String ip;


    /**
     * 消息内容
     * 包含上下文的对话这里只会显示出用户发送的
     */
    private String content;


    /**
     * 累计 Tokens
     */
    private Integer totalTokens;

    /**
     * 首个字符出现时间
     */
    private Date firstCharTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 消息状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
