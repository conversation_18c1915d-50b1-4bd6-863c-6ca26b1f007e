package com.hncboy.chatgpt.front.handler.config;

import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.hncboy.chatgpt.front.framework.config.WxPayProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <PERSON>
 */
@Slf4j
@Configuration
@ConditionalOnClass(WxPayService.class)
@EnableConfigurationProperties(WxPayProperties.class)
@AllArgsConstructor
public class WxPayConfiguration {
    private WxPayProperties properties;

    @Bean
    @ConditionalOnMissingBean
    public WxPayService wxService() {
        // 代码里 getConfigs()处报错的同学，请注意仔细阅读项目说明，你的IDE需要引入lombok插件！！！！
        final List<WxPayProperties.PayConfig> configs = this.properties.getConfigs();
        if (configs == null) {
            throw new RuntimeException(
                    "大哥，拜托先看下项目首页的说明（readme文件），添加下相关配置，注意别配错了！");
        }
        WxPayService wxPayService = new WxPayServiceImpl();

        Map<String, WxPayConfig> collect = configs.stream().map(a -> {
            WxPayConfig payConfig = new WxPayConfig();
            payConfig.setAppId(StringUtils.trimToNull(a.getAppId()));
            payConfig.setMchId(StringUtils.trimToNull(a.getMchId()));
            payConfig.setMchKey(StringUtils.trimToNull(a.getMchKey()));
            payConfig.setApiV3Key(StringUtils.trimToNull(a.getApiV3Key()));
            payConfig.setNotifyUrl(StringUtils.trimToNull(a.getNotifyUrl()));
            payConfig.setSubAppId(StringUtils.trimToNull(a.getSubAppId()));
            payConfig.setSubMchId(StringUtils.trimToNull(a.getSubMchId()));
            payConfig.setKeyPath(StringUtils.trimToNull(a.getKeyPath()));
            payConfig.setPrivateKeyPath(StringUtils.trimToNull(a.getPrivateKeyPath()));
            payConfig.setPrivateCertPath(StringUtils.trimToNull(a.getPrivateCertPath()));
            // 可以指定是否使用沙箱环境
            payConfig.setUseSandboxEnv(false);
            return payConfig;
        }).collect(Collectors.toMap(WxPayConfig::getMchId, a -> a, (o, n) -> o));
        log.info("WxPayService初始化成功"+ collect.size() + "个微信商户信息");
        for (String s : collect.keySet()) {
            log.info("WxPayService初始化成功,mchId: {}", s);
        }


        wxPayService.setMultiConfig(collect);
        return wxPayService;
    }

}
