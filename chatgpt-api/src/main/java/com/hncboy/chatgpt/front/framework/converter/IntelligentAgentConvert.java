package com.hncboy.chatgpt.front.framework.converter;

import java.util.List;

import com.hncboy.chatgpt.front.framework.domain.dto.IntelligentAgentDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.IntelligentAgent;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentAgentVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 智能体信息 领域对象转换器
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/2/28
 */
@Mapper
public interface IntelligentAgentConvert {

   IntelligentAgentConvert INSTANCE = Mappers.getMapper(IntelligentAgentConvert.class);

  /**
   * IntelligentAgentDTO转IntelligentAgent
   * @Author: wZhic
   * @Date:2024/2/28
   */
  IntelligentAgent dtoToEntity(IntelligentAgentDTO dto);

  /**
   * IntelligentAgent 转IntelligentAgentVO
   * @Author: wZhic
   * @Date:2024/2/28
   */
  IntelligentAgentVO entityToVO(IntelligentAgent entity);

  /**
   * List<IntelligentAgent> 转List<IntelligentAgentVO>
   * @Author: wZhic
   * @Date:2024/2/28
   */
  List<IntelligentAgentVO> entityListToVOList(List<IntelligentAgent> entityList);

  /**
   * 查询DTO转换
   * @Author: wZhic
   * @Date:2024/2/28
   */
   IntelligentAgent queryDtoToEntity(IntelligentAgentDTO dto);


}
