package com.hncboy.chatgpt.front.handler.mp.handler;

import com.hncboy.chatgpt.front.framework.enums.WxAppIdEnum;
import com.hncboy.chatgpt.front.handler.mp.builder.TextBuilder;
import com.hncboy.chatgpt.front.mapper.RechargeLogMapper;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Component
@RequiredArgsConstructor
public class SubscribeHandler4 extends AbstractHandler {

    private final WxUserInfoService wxUserInfoService;
    private final RechargeLogMapper rechargeLogMapper;
    private final UserBaseInfoService userBaseInfoService;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage, Map<String, Object> context,
                                    WxMpService weixinService, WxSessionManager sessionManager) throws WxErrorException {
        this.logger.info("新关注用户 OPENID: " + wxMessage);
        //WxUserInfoVO wxUserInfoVO = null;
        // 获取微信用户基本信息
        String msg =  "\uD83D\uDC97欢迎宝宝关注我们\uD83D\uDC4F\n" +
                "———————————————\n" +
                "\uD83D\uDD34塔罗币领取步骤（只可领取一次）\n" +
                "1.请务必先登录平台\n" +
                "【塔罗牌测试入口\uD83D\uDD17】\uD83D\uDC49https://zns.zjfdsr.com/tarot/?parentId=24225570\n" +
                "2.回到公众号回复数【6】即可领取30塔罗币\uD83D\uDCAC\n" +
                "———————————————\n" +
                "\uD83D\uDD34在下方文章评论区留言\n" +
                "会有资深塔罗师免费在线解答哦～\n" +
                "【文章直达链接\uD83D\uDD17】\uD83D\uDC49https://mp.weixin.qq.com/s/OYnb7s0_c8_lIOvRRj2vfw\n" +
                "———————————————\n" +
                "\uD83D\uDC81任何使用问题请联系平台“我的”页添加客服vx";
        try {
            WxMpUser userWxInfo = weixinService.getUserService()
                    .userInfo(wxMessage.getFromUser(), null);
            if (userWxInfo != null) {
                wxUserInfoService.saveOrUpdateUser(userWxInfo,WxAppIdEnum.tarot_yuexin.getCode());
                /*// 查询是否有相同的union_id 但是不同的openId,且是智能社会公众号用户，且无充值记录，则赠送30塔罗币
                WxUserInfo openIdUser = wxUserInfoService.getOne(new QueryWrapper<WxUserInfo>()
                        .notIn("open_id", userWxInfo.getOpenId())
                        .eq("union_id", userWxInfo.getUnionId())
                        .eq("app_id", WxAppIdEnum.zns.getCode())
                );

                if(openIdUser != null){
                    UserBaseInfoVO userBaseInfoByOpenId = userBaseInfoService.getUserBaseInfoByOpenId(openIdUser.getOpenId());
                    if (userBaseInfoByOpenId != null) {
                        List<RechargeLog> rechargeLogs = rechargeLogMapper.selectList(new QueryWrapper<RechargeLog>()
                                .eq("channel", "bestow")
                                .eq("user_id", userBaseInfoByOpenId.getId())
                        );
                        // 如果存在，则判断是否已经赠送过30塔罗币，如果没有则赠送,并生成充值记录
                        if(rechargeLogs.isEmpty()){
                            userBaseInfoService.update(new UpdateWrapper<UserBaseInfo>()
                                    .eq("id", userBaseInfoByOpenId.getId())
                                    .set("tarot_coins", userBaseInfoByOpenId.getTarotCoins()+30));

                            RechargeLog rechargeLog = new RechargeLog();
                            rechargeLog.setChannel("bestow");
                            rechargeLog.setUserId(userBaseInfoByOpenId.getId());
                            rechargeLog.setRechargeTime(new Date());
                            rechargeLog.setRechargeAmt(0.0);
                            rechargeLog.setStartTime(new Date());
                            rechargeLog.setEndTime(new Date());
                            try {
                                rechargeLogMapper.insert(rechargeLog);
                            } catch (Exception e) {
                                // 处理数据库插入异常
                                this.logger.error("赠送塔罗币时插入充值记录失败", e);
                            }
                            msg = "\uD83D\uDC97欢迎宝宝关注我们\uD83D\uDC4F\n" +
                                    "———————————————\n" +
                                    "\uD83D\uDD34塔罗币领取步骤\n" +
                                    "1.请务必先登录平台\n" +
                                    "【星月塔塔平台入口\uD83D\uDD17】\uD83D\uDC49https://zns.zjfdsr.com/tarot\n" +
                                    "2.回到公众号回复数【6】即可领取30塔罗币\uD83D\uDCAC\n" +
                                    "———————————————\n" +
                                    "\uD83D\uDD34在下方文章评论区留言\n" +
                                    "资深塔罗师免费在线解答哦～\n" +
                                    "【文章直达链接\uD83D\uDD17】\uD83D\uDC49https://mp.weixin.qq.com/s/5SvcdvLhAGM9Km7wdg4kKQ\n" +
                                    "———————————————\n" +
                                    "\uD83D\uDC81任何使用问题请联系平台“我的”页添加客服vx";
                        }
                    }else{
                        this.logger.warn("未找到用户基础信息，无法赠送塔罗币。OpenId: " + openIdUser.getOpenId());
                    }

                }*/

            }
        } catch (WxErrorException e) {
            if (e.getError().getErrorCode() == 48001) {
                this.logger.info("该公众号没有获取用户信息权限！");
            }
        }

        try {


            return new TextBuilder().build(msg, wxMessage, weixinService);
        } catch (Exception e) {
            this.logger.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 处理特殊请求，比如如果是扫码进来的，可以做相应处理
     */
   /* private String handleSpecial(WxMpXmlMessage wxMessage, WxUserInfoVO wxUserInfoVO) {
        if (StrUtil.isNotEmpty(wxMessage.getEventKey())) {
            String code = wxMessage.getEventKey().replaceAll("qrscene_", "");
            //场景值是扫码登录
            if (code.equals(WX_SCENE_ID.toString())) {
                //半个小时过期
                redisService.set(wxMessage.getTicket(), wxUserInfoVO, 1800L);
                return "扫码登录成功";
            } else if (code.startsWith("bind")) {
                //绑定微信
                String s = code.replaceAll("bind", "");
                UserBaseInfo userBaseInfo = userBaseInfoMapper.selectById(Integer.valueOf(s));
                String openId = wxMessage.getFromUser();
                // 检查openId是否已绑定
                UserBaseInfoVO userBaseInfoVO = userBaseInfoService.getUserBaseInfoByOpenId(openId);
                if (Objects.nonNull(userBaseInfoVO)) {
                    return "当前微信号已绑定过其他账号，如需帮助，请联系客服。";
                }
                userBaseInfo.setOpenId(openId);
                if(StrUtil.isNotBlank(wxUserInfoVO.getNickName())) {
                    userBaseInfo.setNickName(wxUserInfoVO.getNickName());
                }
                if(StrUtil.isNotBlank(wxUserInfoVO.getAvatarUrl())) {
                    userBaseInfo.setHeadSculpture(wxUserInfoVO.getAvatarUrl());
                }
                userBaseInfo.setUpdateTime(new Date());
                userBaseInfoMapper.updateById(userBaseInfo);
                return "绑定成功";
            }
        }
        return null;
    }*/

}
