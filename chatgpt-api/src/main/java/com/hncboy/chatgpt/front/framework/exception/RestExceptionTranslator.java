package com.hncboy.chatgpt.front.framework.exception;

import cn.dev33.satoken.exception.NotLoginException;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.handler.response.ResultCode;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolationException;

import static com.hncboy.chatgpt.front.framework.handler.response.ResultCode.INTERNAL_SERVER_ERROR;

/**
 * <AUTHOR>
 * @date 2023/3/23 10:55
 * 异常处理器
 */
@Slf4j
@Configuration
@RestControllerAdvice
public class RestExceptionTranslator {

    /**
     * 管理端登录异常处理
     * HTTP 状态为 401
     *
     * @param e 异常信息
     * @return 返回值
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public R<Void> handleError(NotLoginException e) {
        log.error("管理端登录异常", e);
        return R.fail(ResultCode.UN_AUTHORIZED, e.getMessage());
    }


    @ExceptionHandler(WxErrorException.class)
    @ResponseStatus(HttpStatus.OK)
    public R<Void> handleError(WxErrorException e) {
        log.error("微信返回异常", e);
        return R.fail(ResultCode.WX_AUTHORIZED, e.getMessage());
    }

    /**
     * 业务异常处理
     * HTTP 状态为 200
     *
     * @param e 异常信息
     * @return 返回值
     */
    @ExceptionHandler(ServiceException.class)
    @ResponseStatus(HttpStatus.OK)
    public R<Void> handleError(ServiceException e) {
        log.error("业务异常", e);
        return R.fail(e.getResultCode(), e.getMessage());
    }

    /**
     * 余额异常
     * HTTP 状态为 200
     *
     * @param e 异常信息
     * @return 返回值
     */
    @ExceptionHandler(BalanceException.class)
    @ResponseStatus(HttpStatus.OK)
    public R<Void> handleError(BalanceException e) {
        log.error("余额异常", e);
        return R.fail(e.getResultCode(), e.getMessage());
    }

    /**
     * 鉴权异常处理
     * HTTP 状态为 200
     *
     * @param e 异常信息
     * @return 返回值
     */
    @ExceptionHandler(AuthException.class)
    @ResponseStatus(HttpStatus.OK)
    public R<Void> handleError(AuthException e) {
        log.error("鉴权异常", e);
        return R.fail(e.getResultCode(), e.getMessage());
    }

    /**
     * 其他异常处理
     * HTTP 状态为 200
     *
     * @param e 异常信息
     * @return 返回值
     */
    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.OK)
    public R<Void> handleError(Throwable e) {
        log.error("服务器异常", e);
        return R.fail(INTERNAL_SERVER_ERROR, INTERNAL_SERVER_ERROR.getMessage());
    }
    
    @ExceptionHandler(WxPayException.class)
    @ResponseStatus(HttpStatus.OK)
    public R<Void> handleError(WxPayException e) {
        log.error("微信支付", e);
        
        return R.fail(INTERNAL_SERVER_ERROR, e.getMessage());
    }

    @ExceptionHandler(BindException.class)
    public R validatedBindException(BindException e) {
        log.error(e.getMessage(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return R.fail(INTERNAL_SERVER_ERROR, message);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public R constraintViolationException(ConstraintViolationException e) {
        log.error(e.getMessage(), e);
        return R.fail(INTERNAL_SERVER_ERROR, e.getMessage());
    }

}
