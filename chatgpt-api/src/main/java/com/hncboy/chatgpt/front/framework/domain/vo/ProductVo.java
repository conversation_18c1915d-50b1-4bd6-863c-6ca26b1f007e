package com.hncboy.chatgpt.front.framework.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 雨纷纷旧故里草木深
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Accessors(chain = true)
public class ProductVo {

    private Long productId;

    private String productName;

    private String remark;

    private Long num;

    private Double productPrice;

    private String topIcon;

    private LocalDateTime createTime;
    //描述
    private String description;
}
