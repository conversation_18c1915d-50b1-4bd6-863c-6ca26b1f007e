package com.hncboy.chatgpt.front.helper;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessV2Request;
import com.hncboy.chatgpt.front.framework.domain.request.DifyCompletionRequest;
import com.hncboy.chatgpt.front.framework.domain.request.DifyProcessRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

@Component
@Slf4j
public class DifyMsgBuildHelper {

    /**
     * 构建对话完成请求
     *
     * @param chatProcessRequest 聊天过程请求，包含聊天室信息和提示信息
     * @return DifyProcessRequest 对话完成请求对象，设置了响应模式、用户、对话ID和查询信息
     */
    public DifyProcessRequest buildDifyChatCompletion(ChatProcessV2Request chatProcessRequest) {
        // 创建对话完成请求对象
        DifyProcessRequest baseChatCompletion = new DifyProcessRequest();
        // 设置响应模式为流式
        baseChatCompletion.setResponse_mode("streaming");
        // 设置请求的用户为管理员
        baseChatCompletion.setUser("admin");
        // 设置对话ID，来源于聊天室请求对象
        baseChatCompletion.setConversation_id(chatProcessRequest.getChatRoomDO().getConversationId());
        // 设置查询信息，来源于聊天过程请求的提示信息
        baseChatCompletion.setQuery(chatProcessRequest.getPrompt());
        return baseChatCompletion;
    }

    /**
     * 构建文本生成对话完成请求
     *
     * @param chatProcessRequest 聊天过程请求，包含聊天室信息和提示信息
     * @return DifyProcessRequest 对话完成请求对象，设置了响应模式、用户、对话ID和查询信息
     */
    public DifyCompletionRequest buildDifyCompletion(ChatProcessV2Request chatProcessRequest) {
        // 创建对话完成请求对象
        DifyCompletionRequest baseCompletion = new DifyCompletionRequest();
        // 设置响应模式为流式
        baseCompletion.setResponse_mode("streaming");
        // 设置请求的用户为管理员
        baseCompletion.setUser("admin");
        // 设置请求信息
        String message = chatProcessRequest.getPrompt();
        if (!JSONUtil.isTypeJSON(message)) {
            throw new RuntimeException("请求信息必须是JSON格式");
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            Map<String, Object> map = objectMapper.readValue(message, new TypeReference<Map<String, Object>>(){});
            baseCompletion.setInputs(map);
        } catch (IOException e) {
            log.error(e.getMessage());
            throw new RuntimeException("Json转map失败");
        }
        return baseCompletion;
    }

}
