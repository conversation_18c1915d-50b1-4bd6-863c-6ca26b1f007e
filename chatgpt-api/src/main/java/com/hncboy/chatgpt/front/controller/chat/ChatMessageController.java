package com.hncboy.chatgpt.front.controller.chat;

import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.helper.ChatMsgBuildHelper;
import com.hncboy.chatgpt.front.helper.ChatMsgV2BuildHelper;
import com.hncboy.chatgpt.front.service.IModelService;
import com.unfbx.chatgpt.OpenAiStreamClient;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import com.unfbx.chatgpt.entity.models.Model;
import com.unfbx.chatgpt.entity.models.Permission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/23 19:47 对话转发相关接口
 */
@AllArgsConstructor
@Tag(name = "对话转发")
@RestController
@RequestMapping("/v1")
@Slf4j
public class ChatMessageController {

    private final ChatMsgBuildHelper chatMsgBuildHelper;
    private final ChatMsgV2BuildHelper chatMsgV2BuildHelper;
    private static OpenAiStreamClient openAiStreamClient;

    private final IModelService modelService;


    @Operation(summary = "转发对话请求")
    @PostMapping("/chat/completions")
    public ResponseBodyEmitter completionMessages(@RequestBody ChatCompletion chatCompletion,
                                                      HttpServletResponse response) {
        log.info("chatProcessRequest:{}", chatCompletion);
        log.info("chatProcessRequest:{}", chatCompletion.getModel());
        log.info("chatProcessRequest:{}", chatCompletion.getMessages());
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE + ";charset=UTF-8");
//        OpenAIWebSocketEventSourceListener eventSourceListener;
//        return chatMsgV2BuildHelper.buildMessageBody(chatProcessRequest);
//        eventSourceListener = new OpenAIWebSocketEventSourceListener(this.session, chatCompletion);
//        openAiStreamClient.streamChatCompletion(chatCompletion, eventSourceListener);
        return null;
    }

    @Operation(summary = "获取模型列表")
    @GetMapping("/models")
    public R<List<Model>> models() {
        List<Model> models = new ArrayList<>();
        com.hncboy.chatgpt.front.framework.domain.entity.Model model2 = new com.hncboy.chatgpt.front.framework.domain.entity.Model();
        model2.setStatus("0");
        List<com.hncboy.chatgpt.front.framework.domain.entity.Model> list = modelService.selectModelList(model2);
        for (com.hncboy.chatgpt.front.framework.domain.entity.Model model : list) {
            Model model1 = new Model();
            model1.setId(model.getGid());
            model1.setObject("model");
            model1.setOwnedBy(model.getOwnedBy());

            //model1.setPermission(permissions);
            model1.setRoot(model.getGid());
            model1.setParent(null);

            models.add(model1);

        }

        /*Model model = new Model();
        model.setId("gpt-4o-mini");
        model.setObject("model");
        model.setOwnedBy("openai");

        // 创建Permission对象并设置值
        Permission permission = new Permission();
        permission.setId("modelperm-LwHkVFn8AcMItP432fKKDIKJ");
        permission.setObject("model_permission");
        permission.setCreated(1626777600);
        permission.setAllowCreateEngine(true);
        permission.setAllowSampling(true);
        permission.setAllowLogprobs(true);
        permission.setAllowSearchIndices(false);
        permission.setAllowView(true);
        permission.setAllowFineTuning(false);
        permission.setOrganization("*");
        permission.setGroup(null);
        permission.setBlocking(false);

        // 将Permission对象加入List
        List<Permission> permissions = new ArrayList<>();
        permissions.add(permission);
        model.setPermission(permissions);
        model.setRoot("gpt-4o-mini");
        model.setParent(null);

        // 将Model对象加入List
        models.add(model);*/

        return R.data(models);
    }
}
