package com.hncboy.chatgpt.front.handler.mp.handler;

import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.stereotype.Component;

import java.util.Map;

import static me.chanjar.weixin.common.api.WxConsts.EventType;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Component
public class MenuHandler4 extends AbstractHandler {

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService weixinService,
                                    WxSessionManager sessionManager) {
        if (EventType.VIEW.equals(wxMessage.getEvent())) {
            return null;
        }
        String msg = "\uD83D\uDC97正在赶来的路上啦～请您耐心等到，若等待太久您可先去忙别的事情哟，看到会马上回复您的信息帮您解决问题，实在非常着急的话，\uD83D\uDC81请联系“我的”页添加客服vx，万分感谢！";

        return WxMpXmlOutMessage.TEXT().content(msg)
                .fromUser(wxMessage.getToUser()).toUser(wxMessage.getFromUser())
                .build();
    }

}
