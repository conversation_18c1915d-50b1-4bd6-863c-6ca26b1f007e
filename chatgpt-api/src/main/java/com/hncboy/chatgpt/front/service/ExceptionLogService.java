package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;
import com.hncboy.chatgpt.front.framework.domain.entity.ExceptionLog;
import com.hncboy.chatgpt.front.framework.domain.entity.SiteInfo;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessV2Request;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;

/**
* <AUTHOR>
* @description 针对表【error_log】的数据库操作Service
* @createDate 2024-04-15 14:58:04
*/
public interface ExceptionLogService extends IService<ExceptionLog> {

    /**
     * 保存聊天处理过程中的异常日志。
     *
     * @param chatProcessRequest 聊天处理的请求对象，包含处理过程中的所有必要信息。
     * @param e 发生的异常对象，用于记录具体的异常信息。
     */
    void saveChatExceptionLog(ChatProcessV2Request chatProcessRequest, Throwable e,
                              ChatMessageDO chatMessageDO, UserBaseInfoVO userBaseInfoVO, SiteInfo siteInfo,
                              String Response, String errMessage);


    /**
     * 保存聊天处理过程中的异常日志。
     *
     * @param chatProcessRequest 聊天处理的请求对象，包含处理过程中的所有必要信息。
     */
    void saveTarotExceptionLog(ChatProcessV2Request chatProcessRequest, Throwable e,
                               ChatMessageDO chatMessageDO, UserBaseInfoVO userBaseInfoVO, SiteInfo siteInfo,
                               String Response, String errMessage);

    void saveExceptionLog(String messageType, ChatProcessV2Request chatProcessRequest, Throwable e,
                          ChatMessageDO chatMessageDO, UserBaseInfoVO userBaseInfoVO,
                          SiteInfo siteInfo, String response, String errMessage);

/*    void saveExceptionLog2(String messageType, ChatProcessV2Request chatProcessRequest,
                           TarotSpread tarotSpread, UserBaseInfoVO userBaseInfoVO,
                           SiteInfo siteInfo, String response, String errMessage);*/
}
