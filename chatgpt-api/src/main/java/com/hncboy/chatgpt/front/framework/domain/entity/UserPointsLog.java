package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("user_points_log")
public class UserPointsLog implements Serializable {
    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 关联订单
     */
    private String relOrder;

    /**
     * 用户添加积分
     */
    private Integer points;

    /**
     * 积分类型
     */
    private String pointsType;

    /**
     * 描述
     */
    private String remark;
    /**
     * 创建人
     */
    private  String createBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改人
     */
    private String updateBy;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productName;

    /**
     * 受邀人账号
     */
    @TableField(exist = false)
    private String account;
    @TableField(exist = false)
    private String totalPoints;

}
