package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @描述:APP用户签到 ENTITY
 * @版本：v1.0.0
 * @作者: zc.wu
 * @时间:2021/10/15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("app_sign")
public class AppSign implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String userId;

    @Schema(title = "是否中断")
    private String interruptStatus;

    @Schema(title = "签到日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date checkDate;

    @Schema(title = "补签")
    private String repairCheck;

    @Schema(title = "备注")
    private String remark;


}
