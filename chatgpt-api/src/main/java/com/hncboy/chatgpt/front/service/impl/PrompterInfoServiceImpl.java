package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.converter.PrompterInfoConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.PrompterInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.PrompterInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.PrompterInfoVO;
import com.hncboy.chatgpt.front.mapper.PrompterInfoMapper;
import com.hncboy.chatgpt.front.service.PrompterInfoService;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 提词器信息实现
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/5/8
 */
@Service
public class PrompterInfoServiceImpl extends ServiceImpl<PrompterInfoMapper, PrompterInfo> implements PrompterInfoService {


    /**
     * 根据关键词查找
     *
     * @param key
     * @return List<PrompterInfoVO>
     * @Author: zc.wu
     * @Date: 2023/5/8 11:12
     */
    @Override
    public List<PrompterInfoVO> queryListByKey(String key) {
        LambdaQueryWrapper<PrompterInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.and(
                wp -> wp.like(PrompterInfo::getKeyWord, key).or()
                        .like(PrompterInfo::getIndustryName, key));
        lambdaQueryWrapper.last("limit 50");
        List<PrompterInfo> list = this.list(lambdaQueryWrapper);
        return PrompterInfoConvert.INSTANCE.entityListToVOList(list);
    }


    /**
     * 分页查询数据
     *
     * @param dto
     * @return IPage<PrompterInfoVO>
     * @Author: zc.wu
     * @Date: 2023/5/8 20:13
     */
    @Override
    public IPage<PrompterInfoVO> queryListEntityPage(PrompterInfoDTO dto) {
        LambdaQueryWrapper<PrompterInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.and(
                wp -> wp.like(PrompterInfo::getKeyWord, dto.getKeyWord()).or()
                        .like(PrompterInfo::getIndustryName, dto.getKeyWord()));
        IPage<PrompterInfo> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<PrompterInfo> iPage = this.page(userPage, wrapper);
        return iPage.convert(PrompterInfoConvert.INSTANCE::entityToVO);
    }


    /**
     * 插入提词器信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/5/8
     */
    @Override
    public boolean insertEntity(PrompterInfoDTO record) {
        PrompterInfo entity = PrompterInfoConvert.INSTANCE.dtoToEntity(record);
        return this.save(entity);
    }

    /**
     * 删除提词器信息
     *
     * @Param:@param example
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/5/8
     */
    @Override
    public boolean deleteById(Integer id) {
        return this.removeById(id);
    }

    /**
     * 生成提词器
     *
     * @param industry
     * @return void
     * @Author: zc.wu
     * @Date: 2023/5/8 11:22
     */
    @Override
    public void saveIndustry(String industry) {
        List<File> files = FileUtil.loopFiles("F:\\迅雷下载\\awesome-chatgpt-prompts-zh-main\\question");
        for (File file : files) {
            System.out.println("-----------文件分割----------");
            List<String> stringList = FileUtil.readLines(file, "UTF-8");
            List<PrompterInfo> prompterInfoList = new ArrayList<>();
            for (String s : stringList) {
                String[] split = s.split("\\|");
                if (split.length < 3) {
                    System.out.println("跳过===" + s);
                    continue;
                }
                if ("---".equals(split[1])) {
                    continue;
                }
                if (split[1].length() > 100) {
                    continue;
                }
                if (split[2].length() > 499) {
                    continue;
                }
                s = s.replaceAll("\\.", "").replaceAll(" ", "")
                        .replaceAll("GPT-3.5", "智能社");
                PrompterInfo record = new PrompterInfo();
                record.setKeyWord(split[2]);
                record.setIndustry("cosmetics");
                record.setIndustryName(split[1].replaceAll("_", ""));
                prompterInfoList.add(record);
            }
            this.saveBatch(prompterInfoList);
        }
    }


}
