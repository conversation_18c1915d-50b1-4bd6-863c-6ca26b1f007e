package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.alimt20181012.Client;
import com.aliyun.alimt20181012.models.TranslateGeneralRequest;
import com.aliyun.alimt20181012.models.TranslateGeneralResponse;
import com.aliyun.alimt20181012.models.TranslateGeneralResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.dto.TranslateDTO;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.handler.config.SensitiveWordHandler;
import com.hncboy.chatgpt.front.helper.SensitiveWordEmitterChain;
import com.hncboy.chatgpt.front.service.TranslateGeneralService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class TranslateGeneralServiceImpl implements TranslateGeneralService {
    /**
     * 阿里云机器翻译
     * @param sourceText
     * @return
     */
    @Override
    public TranslateDTO alTranslate(String sourceText) throws Exception {
        // 敏感词检查
        if (!SensitiveWordEmitterChain.doChain(sourceText)) {
            List<String> prompts = SensitiveWordHandler.checkWord(sourceText);
            throw new ServiceException("您的输入包含敏感词【" + CollUtil.join(prompts, ",") + "】，请重新输入");
        }

        Config config = new Config()
                .setAccessKeyId(ApplicationConstant.ALIBABA_CLOUD_ACCESS_KEY_ID)
                .setAccessKeySecret(ApplicationConstant.ALIBABA_CLOUD_ACCESS_KEY_SECRET);
        config.endpoint = ApplicationConstant.endpoint;

        Client client = new Client(config);

        TranslateGeneralRequest translateGeneralRequest = new TranslateGeneralRequest()
                .setFormatType("text")//翻译文本格式
                .setSourceLanguage("zh")//原文语言
                .setTargetLanguage("en")// 目标语言
                .setScene("general")
                .setSourceText(sourceText);
        RuntimeOptions runtime = new RuntimeOptions();
        TranslateDTO translateDTO = new TranslateDTO();
        try {

            TranslateGeneralResponse response = client
                    .translateGeneralWithOptions(translateGeneralRequest, runtime);
            log.info("返回信息 {}",response.getBody());
            TranslateGeneralResponseBody body = response.getBody();
            TranslateGeneralResponseBody.TranslateGeneralResponseBodyData data = body.getData();

            if (data != null){
                BeanUtils.copyProperties(data,translateDTO);
            }else {
                throw new ServiceException("翻译失败，请联系管理员");
            }
        } catch (TeaException error) {
            log.error("错误信息{}",error.getMessage());
            log.error("诊断地址{}",error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);

        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);

            log.error("错误信息{}",error.getMessage());
            log.error("诊断地址{}",error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
        }
        return translateDTO;
    }
}
