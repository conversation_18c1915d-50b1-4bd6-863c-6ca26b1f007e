package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 写作应用 ENTITY
 *
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/6/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("write_agent")
public class WriteAgent implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 标题
     */
    private String title;


    /**
     * 描述
     */
    private String description;


    /**
     * 分类
     */
    private String tag;


    /**
     * 启用状态(0启用1禁用)
     */
    private Integer status;


    /**
     * 应用名称
     */
    private String agentName;


    /**
     * 模型ID
     */
    private String gid;


    /**
     * 界面配置
     */
    private String layoutConf;


    /**
     * 图标
     */
    private String imgUrl;


    /**
     * 可用次数
     */
    private Integer useCnt;


    /**
     * 输入提示
     */
    private String inputExample;


    /**
     * 系统回答
     */
    private String sysContent;


    /**
     * 是否收费(0不是1是)
     */
    private Integer charge;


    /**
     * 消耗点数
     */
    private String consume;


    /**
     * 是否热门
     */
    private Integer hot;


    /**
     * 回复数
     */
    private Integer maxToken;


    /**
     * 随机数
     */
    private Double temperature;


    /**
     * interface
     */
    private String layout_conf;


    /**
     * 备注
     */
    private String remark;


    /**
     * 创建者
     */
    private String createBy;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 更新者
     */
    private String updateBy;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 开始时间
     */
    private Date startTime;


    /**
     * 结束时间
     */
    private Date endTime;


}
