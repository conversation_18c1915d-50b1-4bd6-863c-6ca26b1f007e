package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.dto.PrompterInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.PrompterInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.PrompterInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 提词器信息 领域对象转换器
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/5/8
 */
@Mapper
public interface PrompterInfoConvert {

    PrompterInfoConvert INSTANCE = Mappers.getMapper(PrompterInfoConvert.class);

    /**
     * PrompterInfoDTO转PrompterInfo
     *
     * @Author: wzhic
     * @Date:2023/5/8
     */
    PrompterInfo dtoToEntity(PrompterInfoDTO dto);

    /**
     * PrompterInfo 转PrompterInfoVO
     *
     * @Author: wzhic
     * @Date:2023/5/8
     */
    PrompterInfoVO entityToVO(PrompterInfo entity);

    /**
     * List<PrompterInfo> 转List<PrompterInfoVO>
     *
     * @Author: wzhic
     * @Date:2023/5/8
     */
    List<PrompterInfoVO> entityListToVOList(List<PrompterInfo> entityList);

    /**
     * 查询DTO转换
     *
     * @Author: wzhic
     * @Date:2023/5/8
     */
    PrompterInfo queryDtoToEntity(PrompterInfoDTO dto);


}
