package com.hncboy.chatgpt.front.mapper;

public class SqlProvider {

    public String selectChannelConfigWithSiteInfo(String modelGid) {
        return "SELECT c.* " +
                "FROM channel_config c " +
                "JOIN site_info s ON c.site_id = s.id " +
                "WHERE s.status = '0' " +
                "AND model_gid = '" + modelGid + "' " +
                "ORDER BY c.status, c.priority ASC";
    }
    public String updateStatusByModelGid(String modelGid) {
        return "UPDATE channel_config " +
                "SET status = '0' " +
                "WHERE model_gid = '" + modelGid + "' ";
    }
}
