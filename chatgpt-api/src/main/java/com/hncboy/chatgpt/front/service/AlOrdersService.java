package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.dto.AlOrdersDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.AlOrders;
import com.hncboy.chatgpt.front.framework.domain.vo.AlOrdersVO;

/**
* <AUTHOR>
* @description 针对表【al_order(充值记录)】的数据库操作Service
* @createDate 2024-07-04
*/
public interface AlOrdersService extends IService<AlOrders> {

    /**
     * 分页查询充值记录信息列表
     *
     * @Param:@param example
     * @return:IPage<IntelligentAgentVO>
     * @Author: zyc
     * @Date:2024-07-04
     */
    IPage<AlOrdersVO> queryListEntityPage(AlOrdersDTO dto);

}
