package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 模型列表对象 model
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@TableName("model")
@Data
public class Model implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 渠道 */
    private String channel;

    /** 模型ID */
    private String gid;

    /** 模型名称 */
    private String modelName;

    /** 归属 */
    private String ownedBy;

    /** 状态 */
    private String status;


    private String createBy ;
    private String createTime ;
    private String updateBy ;
    private String updateTime ;

    private String detection;

    
}