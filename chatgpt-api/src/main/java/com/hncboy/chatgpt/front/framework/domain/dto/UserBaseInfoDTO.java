package com.hncboy.chatgpt.front.framework.domain.dto;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户基础信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2024/2/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_base_info")
public class UserBaseInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    private Integer id;
    /**
     * 账号(手机号或其他账号)
     */
    private String account;


    /**
     * 昵称
     */
    private String nickName;



    /**
     * 最后登录时间
     */
    private LocalDateTime loginTime;


    /**
     * IP地址
     */
    private String ip;


    /**
     * 头像
     */
    private String headSculpture;


    /**
     * 地址
     */
    private String address;


    /**
     * 邮箱
     */
    private String email;


    /**
     * 状态0正常1禁用
     */
    private Integer status;


    /**
     * 剩余可用次数(充值)
     */
    private Integer useNum;

    /**
     * 绘画次数
     */
    private Integer drawNum;

    /**
     * 音乐创作次数
     */
    private Integer musicNum;

    /**
     * 写作次数
     */
    private Integer writeNum;


    /**
     * 免费可用次数(赠送)
     */
    private Integer freeNum;

    /**
     * 每日免费次数
     */
    private Integer dailyFreeTime;

    /**
     * 备注
     */
    private String remark;


    /**
     * 微信openId
     */
    private String openId;

    private Integer commissionId;

    @AllArgsConstructor
    public enum  UserStatusEnum {

        /**
         * 正常
         */
        normal(0),

        /**
         * 禁用
         */
        disable(1);


        @Getter
        @EnumValue
        private final Integer status;
    }


}
