package com.hncboy.chatgpt.front.framework.domain.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

import java.io.Serializable;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentAgentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 绘图室表
* @TableName draw_room
*/
@Data
@TableName("draw_room")
public class DrawRoomDO implements Serializable {

    /**
    * 主键
    */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
    * 绘图室名称
    */
    @NotBlank(message="[绘图室名称]不能为空")
    @Size(max= 255,message="编码长度不能超过255")
    @Length(max= 255,message="编码长度不能超过255")
    private String title;

    /**
    * 绘图室简介
    */
    @Size(max= 2550,message="编码长度不能超过2550")
    @Length(max= 2550,message="编码长度不能超过2,550")
    private String description;

    /**
    * IP
    */
    @Size(max= 64,message="编码长度不能超过64")
    @Length(max= 64,message="编码长度不能超过64")
    private String ip;

    /**
    * 是否公开
    */
    @Size(max= 3,message="编码长度不能超过3")
    @Length(max= 3,message="编码长度不能超过3")
    private String open;

    /**
    * 角色ID
    */
    private Integer roleId;

    /**
    * 绘图室图片
    */
    @Size(max= 500,message="编码长度不能超过500")
    @Length(max= 500,message="编码长度不能超过500")
    private String imageUrl;

    /**
    * 用户ID
    */
    @NotBlank(message="[用户ID]不能为空")
    @Size(max= 64,message="编码长度不能超过64")
    @Length(max= 64,message="编码长度不能超过64")
    private String openId;

     /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(title = "智能体信息")
    @TableField(exist = false)
    private IntelligentAgentVO agentVo;
}
