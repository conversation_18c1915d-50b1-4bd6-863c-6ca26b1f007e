package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.CommissionIdentity;
import com.hncboy.chatgpt.front.mapper.CommissionIdentityMapper;
import com.hncboy.chatgpt.front.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 参与身份 实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CommissionIdentityServiceImpl extends ServiceImpl<CommissionIdentityMapper, CommissionIdentity> implements CommissionIdentityService {

    private final CommissionIdentityMapper commissionIdentityMapper;

    @Override
    public CommissionIdentity getByUserInfoId(Integer userInfoId) {
        Date now = new Date();
        return commissionIdentityMapper.selectOne(Wrappers.<CommissionIdentity>lambdaQuery()
                        .eq(CommissionIdentity::getUserInfoId, userInfoId)
                        .le(CommissionIdentity::getStartTime, now)
                        .ge(CommissionIdentity::getEndTime, now)
                        .eq(CommissionIdentity::getStatus, "0") // 0表示有效
                        .last("limit 1")
                );
    }
}
