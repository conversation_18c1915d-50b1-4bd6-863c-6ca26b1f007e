package com.hncboy.chatgpt.front.controller.common;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.domain.dto.UserPointsLogDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.SysConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog;
import com.hncboy.chatgpt.front.framework.domain.vo.UserPointsLogVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.service.SysConfigService;
import com.hncboy.chatgpt.front.service.UserPointsLogService;
import com.hncboy.chatgpt.tarot.domain.vo.InviteDataVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

/**
 * 积分明细相关
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/8/8
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/userPointsLog")
@Tag(name = "积分明细相关")
public class UserPointsLogController {

    private final UserPointsLogService UserPointsLogService;
    private final SysConfigService sysConfigService;

    @Operation(summary = "分页查询积分明细列表")
    @PostMapping("/page/list")
    public R<IPage<UserPointsLogVO>> queryListEntityPage(
            @RequestBody UserPointsLogDTO userPointsLogServiceDTO) {
        return R.data(UserPointsLogService.queryListEntityPage(userPointsLogServiceDTO));
    }

    @Operation(summary = "分页查询塔罗邀请奖励列表")
    @PostMapping("/page/tarotList")
    public R pageQueryTarotByUserId(
            @RequestBody UserPointsLogDTO userPointsLogServiceDTO) {
        HashMap<String, Object> map = new HashMap<>();
        userPointsLogServiceDTO.setPointsType("tarot_invite_points");
        IPage<UserPointsLogVO> userPointsLogVOIPage = UserPointsLogService.pageQueryTarotByUserId(userPointsLogServiceDTO);
        map.put("userPointsLogVOIPage", userPointsLogVOIPage);
        // 已邀请人数
        int pointsType = UserPointsLogService.count(new QueryWrapper<UserPointsLog>()
                .eq("points_type", "tarot_invite_points")
                .eq("user_id", CurrentUserUtil.getV2UserId())
        );
        map.put("totalPoints", ObjectUtil.isNull(pointsType)?0 : pointsType );
        //已获得塔罗币总数
        UserPointsLog one = UserPointsLogService.getOne(new QueryWrapper<UserPointsLog>()
                .eq("points_type", "tarot_invite_points")
                .eq("user_id", CurrentUserUtil.getV2UserId())
                .select("sum(points) as totalPoints")
        );
        if(ObjectUtil.isNull(one)) {
            one = new UserPointsLog();
            one.setTotalPoints(0+"");
        }
        map.put("availablePoints",  ObjectUtil.isNotNull(one.getTotalPoints())? one.getTotalPoints():0);

        return R.data(map);
    }


    @Operation(summary = "塔罗邀请积分显示数据")
    @PostMapping("/troat/invite")
    public R invitePointsInfo(){
        //配置的赠送积分数
        SysConfig sysConfig = sysConfigService.querySysConfig("tarot_invite_points");
        InviteDataVO inviteDataVO = new InviteDataVO();
        inviteDataVO.setTotalPoint(sysConfig.getConfigValue()+"塔罗币");
        //已邀请人数
        int inviteCount = UserPointsLogService.count(new QueryWrapper<UserPointsLog>()
                .eq("points_type", "tarot_invite_points")
                .eq("user_id", CurrentUserUtil.getV2UserId())
        );
        inviteDataVO.setInviteCount(inviteCount);
        return R.data(inviteDataVO);
    }
}


