package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户基础信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2024/2/26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_merge_info")
public class UserMergeInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 合并后用户id
     */
    private String mergeId;

    /**
     *  被合并用户id
     */
    private String wasMergedId;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}
