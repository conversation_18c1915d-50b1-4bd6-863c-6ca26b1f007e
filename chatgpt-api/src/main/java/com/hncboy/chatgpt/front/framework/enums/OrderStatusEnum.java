package com.hncboy.chatgpt.front.framework.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付枚举
 */
@AllArgsConstructor
public enum OrderStatusEnum {

    /**
     * 未支付
     */
    unpaid(0, "未支付"),

    /**
     * 已支付
     */
    paid(1, "已支付");


    @Getter
    @EnumValue
    private final Integer code;

    @Getter
    @JsonValue
    private final String message;
}
