package com.hncboy.chatgpt.front.framework.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * VO
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class HomeConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "分类标题")
    private String code;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "分类")
    private String tag;

    @Schema(title = "分类名称")
    private String tagName;

    @Schema(title = "输入提示")
    private String inputExample;

    @Schema(title = "图标")
    private String imgUrl;

    @Schema(title = "是否热门")
    private Integer hot;

    @Schema(title = "可用次数")
    private Integer num;

    @Schema(title = "是否内置(0不是1是)")
    private Integer status;

    @Schema(title = "搜索值")
    private String searchValue;

    @Schema(title = "创建者")
    private String createBy;

    @Schema(title = "创建者名称")
    private String createByName;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "更新者")
    private String updateBy;

    @Schema(title = "openId")
    private String openId;

    @Schema(title = "doesItExist")
    private Boolean doesItExist = false;

    @Schema(title = "更新时间")
    private Date updateTime;

    @Schema(title = "备注")
    private String remark;


}
