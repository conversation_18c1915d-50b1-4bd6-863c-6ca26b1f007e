package com.hncboy.chatgpt.front.framework.util;

/**
 * zcWu
 */

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import javax.websocket.Session;

@Slf4j
public class WebsocketUtil {

    public static String getHeader(Session session, String headerName) {
        final String header = (String) session.getUserProperties().get(headerName);
        if (StrUtil.isBlank(header)) {
            log.error("获取header失败，不安全的链接，即将关闭");
            return null;
        }
        return header;
    }


}
