package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-06-01 20:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("draw_message")
public class DrawMessage implements Serializable {

    private static final long serialVersionUID = 8156131231160867524L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * open_id
     */
    private String openId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 绘图室id
     */
    private String drawRoomId;

    /**
     * 事件
     */
    private String action;

    /**
     * 定制请求
     */
    private String customId;

    /**
     * 机器人类型
     */
    private String botType;

    /**
     * 按钮
     */
    private String buttons;

    /**
     * 是否热门
     */
    private String hot;

    /**
     * 提示词
     */
    private String prompt;

    /**
     * 提示词-英文
     */
    private String promptEn;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 自定义参数
     */
    private String state;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime finishTime;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 任务进度
     */
    private String progress;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 创建者
     */
    private String createBy;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 更新者
     */
    private String updateBy;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
