package com.hncboy.chatgpt.front.handler.pay;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "PayPrepayVO对象", description = "预支付订单信息")
public class PayPrepayVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    private String ordersId;
    private LocalDateTime createdTime;
    private Integer productId;

    /**
     * 类型(CHAT-对话;DRAW-绘图;COMMON-通用; MUSIC; WRITE; MIXED)
     */
    private String productType;
    /**
     * 名称
     */
    private String productName;
    /**
     * 金额
     */
    private Double productPrice;
    /**
     * 单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)
     */
    private String unit;
    /**
     * 描述
     */
    private String remark;
    /**
     * 数量
     */
    private Long num;
    /**
     * 付款状态
     */
    private Integer payStatus;

    @Schema(title = "拉起支付所需参数(通用)")
    private Map<String, Object> payExtraMap;

}
