package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "recharge_log")
@Accessors(chain = true)
public class RechargeLog implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    private String orderId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 组合套餐信息
     */
    private String packageInfo;

    /**
     * 充值金额
     */
    private Double rechargeAmt;

    /**
     * 购买数量
     */
    private String unit;


    /**
     * 数量
     */
    private Integer times;

    /**
     * 支付通道
     */
    private String channel;

    /**
     * 充值时间
     */
    private Date rechargeTime;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
