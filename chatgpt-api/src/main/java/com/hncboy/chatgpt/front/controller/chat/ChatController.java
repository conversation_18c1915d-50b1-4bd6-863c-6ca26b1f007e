package com.hncboy.chatgpt.front.controller.chat;

import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatRoomDO;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.vo.ChatRoomVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.helper.ChatMsgBuildHelper;
import com.hncboy.chatgpt.front.service.ChatMessageService;
import com.hncboy.chatgpt.front.service.ChatRoomService;
import com.hncboy.chatgpt.front.service.RedisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/22 19:47 聊天相关接口
 */
@FrontPreAuth
@AllArgsConstructor
@Tag(name = "聊天相关接口")
@RestController
@RequestMapping
public class ChatController {

    private final ChatMessageService chatMessageService;

    private final ChatRoomService chatRoomService;

    private final RedisService redisService;

    private final ChatMsgBuildHelper chatMsgBuildHelper;



    @Operation(summary = "创建聊天室")
    @PostMapping("/createChatRoom")
    public R<ChatRoomDO> createChatRoom(@RequestBody ChatRoomDO chatRoomDO) {
        return R.data(chatRoomService.createChatRoom(chatRoomDO));
    }


    @Operation(summary = "更新聊天室")
    @PostMapping("/updateChatRoom")
    public R<Object> updateChatRoom(@RequestBody ChatRoomDO chatRoomDO) {
        return R.data(chatRoomService.updateChatRoom(chatRoomDO));
    }


    @Operation(summary = "查询用户聊天室列表")
    @GetMapping("/getChatRoom")
    public R<List<ChatRoomVO>> getChatRoom(@RequestParam("type") String type, @RequestParam("ids") String idsString) {
        ChatRoomDO chatRoomDO = new ChatRoomDO();
        chatRoomDO.setType(type);
        if (idsString != null) {
            String[] split;
            split = idsString.split(",");
            chatRoomDO.setIds(Arrays.asList(split));
        }
        return R.data(chatRoomService.getChatRoom(chatRoomDO));
    }

    @Operation(summary = "删除房间")
    @GetMapping("deleteRoom/{roomId}")
    public R deleteRoom(@PathVariable("roomId") Integer roomId) {
        if (roomId == 173 || roomId == 193) {
            return R.fail("该房间不能删除");
        }
        return R.data(chatRoomService.removeById(roomId));
    }

    @Operation(summary = "新版图片生成模型")
    @PostMapping("/chatImage")
    public R<String> chatImage(@RequestBody ChatProcessRequest chatProcessRequest) {
        return R.data(chatMsgBuildHelper.buildImageMsg(chatProcessRequest));
    }

    @Operation(summary = "api调用构建消息回复")
    @PostMapping("/buildApiMsg")
    public R<String> buildApiMsg(@RequestBody ChatProcessRequest chatProcessRequest) {
        return R.data(chatMsgBuildHelper.buildApiMsg(chatProcessRequest));
    }

    @Operation(summary = "查询对话历史")
    @PostMapping("/queryChatMessage")
    public R<List<ChatMessageDO>> queryChatMessage() {
        return R.data(chatMessageService.queryChatMessage(CurrentUserUtil.getUserId()));
    }

    @Operation(summary = "停止消息")
    @GetMapping("stopMsg/{chatMsgId}")
    public void stopMsg(@PathVariable("chatMsgId") Integer chatMsgId) {
        if (Objects.isNull(chatMsgId)) {
            return;
        }
        ApplicationConstant.timedCache.put(chatMsgId, "1");
        //启动定时任务，每5毫秒清理一次过期条目，注释此行首次启动仍会清理过期条目
        ApplicationConstant.timedCache.schedulePrune(30000);
    }


}
