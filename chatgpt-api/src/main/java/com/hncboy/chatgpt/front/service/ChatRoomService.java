package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatRoomDO;
import com.hncboy.chatgpt.front.framework.domain.vo.ChatRoomVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-3-25
 * 聊天室相关业务接口
 */
public interface ChatRoomService extends IService<ChatRoomDO> {

    /**
     * 创建聊天室
     *
     * @param chatMessageDO 聊天记录
     * @return 聊天室
     */
    ChatRoomDO createChatRoom(ChatRoomDO chatMessageDO);

    /**
     * 更新聊天室
     *
     * @param chatRoomDO 聊天室
     * @return 聊天室
     */
    boolean updateChatRoom(ChatRoomDO chatRoomDO);


    /**
     * 查询用户聊天室
     *
     * @param
     * @return List<ChatRoomDO>
     * @Author: zc.wu
     * @Date: 2023/7/31 0031 下午 04:25
     */
    List<ChatRoomVO> getChatRoom(ChatRoomDO chatRoomDO);
}
