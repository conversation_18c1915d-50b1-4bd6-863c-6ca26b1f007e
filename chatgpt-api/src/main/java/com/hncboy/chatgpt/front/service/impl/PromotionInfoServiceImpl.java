package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.PromotionInfo;
import com.hncboy.chatgpt.front.mapper.PromotionInfoMapper;
import com.hncboy.chatgpt.front.service.PromotionInfoService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【promotion_info(促销活动信息)】的数据库操作Service实现
* @createDate 2024-08-06 10:47:46
*/
@Service
public class PromotionInfoServiceImpl extends ServiceImpl<PromotionInfoMapper, PromotionInfo>
    implements PromotionInfoService{

}




