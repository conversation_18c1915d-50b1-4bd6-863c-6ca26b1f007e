package com.hncboy.chatgpt.front.framework.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 应用导航栏信息 VO
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/7/3
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WriteAgentLeftTagVO implements Serializable {

    @Schema(title = "主键")
    private Integer id;

    @Schema(title = "应用标题")
    private String title;

    @Schema(title = "图标")
    private String imageUrl;
}
