package com.hncboy.chatgpt.front.controller.common;


import com.hncboy.chatgpt.front.framework.domain.entity.SysConfig;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.service.SysConfigService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.hncboy.chatgpt.front.framework.util.CurrentUserUtil.getV2UserId;


/**
 * 参数配置 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/config")
public class SysConfigController
{
    @Autowired
    private SysConfigService configService;


    /**
     * 获取海报图片
     */
    @GetMapping(value = "/getFirstUrl")
    public R getFirstUrl()
    {
        SysConfig sysConfig = configService.querySysConfig("first_url");
        //userBaseInfoService.updateUserFirstStatusById(getV2UserId());
        return R.data(sysConfig);
    }


    /**
     * 根据参数键名查询参数值
     */
    @GetMapping(value = "/configKey/{configKey}")
    public R getConfigKey(@PathVariable String configKey)
    {
        SysConfig sysConfig = configService.querySysConfig(configKey);
        //userBaseInfoService.updateUserFirstStatusById(getV2UserId());
        return R.data(sysConfig);
    }

}
