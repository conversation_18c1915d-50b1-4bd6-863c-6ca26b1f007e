package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.DrawMessage;
import com.hncboy.chatgpt.front.mapper.DrawMessageMapper;
import com.hncboy.chatgpt.front.service.DrawMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/3/20 16:33 聊天记录相关业务实现类
 */
@Service
@RequiredArgsConstructor
public class DrawMessageServiceImpl extends ServiceImpl<DrawMessageMapper, DrawMessage> implements
        DrawMessageService {


    @Transactional(rollbackFor = Exception.class)
    @Override
    @Async
    public DrawMessage initDrawMessage(DrawMessage drawMessage) {
        drawMessage.setCreateTime(LocalDateTime.now());
        drawMessage.setUpdateTime(LocalDateTime.now());
        save(drawMessage);
        return drawMessage;
    }


    /**
     * 查询对话结果
     *
     * @param openId
     * @return DrawMessage
     * @Author: zd.z
     * @Date: 2024/3/20 10:21
     */
    @Override
    public List<DrawMessage> queryDrawMessage(String openId) {
        LambdaQueryWrapper<DrawMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DrawMessage::getOpenId, openId);
        List<DrawMessage> drawMessages = this.list(queryWrapper);
        return drawMessages;
    }


}
