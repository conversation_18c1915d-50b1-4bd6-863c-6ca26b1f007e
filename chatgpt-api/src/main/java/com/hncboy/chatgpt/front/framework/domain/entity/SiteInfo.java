package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 站点信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("site_info")
public class SiteInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 站点名称
     */
    private String name;


    /**
     * 站点地址
     */
    private String url;


    /**
     * 秘钥
     */
    private String apiKey;


    /**
     * API KEY名称
     */
    private String apiKeyName;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 备注
     */
    private String remark;


    /**
     * 状态0启动1禁用
     */
    private Integer status;


    /**
     * HTTP 代理主机
     */
    private String httpProxyHost;

    /**
     * HTTP 代理端口
     */
    private Integer httpProxyPort;


    /**
     * 通道配置ID
     */
    @TableField(exist = false)
    private Integer channelConfigId;


    /**
     * 通道配置状态
     */
    @TableField(exist = false)
    private Integer channelConfigStatus;

}
