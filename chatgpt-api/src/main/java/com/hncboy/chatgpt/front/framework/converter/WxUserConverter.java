package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.entity.WxUserInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description:
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2023/4/12
 */
@Mapper
public interface WxUserConverter {

    WxUserConverter INSTANCE = Mappers.getMapper(WxUserConverter.class);


    WxUserInfoVO entityToVO(WxUserInfo wxUserInfo);


    List<WxUserInfoVO> entityListToVOList(List<WxUserInfo> entityList);

}
