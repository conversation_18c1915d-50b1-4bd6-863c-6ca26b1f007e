package com.hncboy.chatgpt.front.schedule;

import cn.hutool.core.date.DateUtil;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.util.DingTalkHookUtil;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.service.TarotReadingRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "dingtalk", name = "schedule_enabled", havingValue = "true")
public class TarotSchedule {

    @Autowired
    private UserBaseInfoService userBaseInfoService;
    @Autowired
    private TarotReadingRecordService tarotReadingRecordService;
    // 时间格式化器（精确到秒）
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final String FORMATTER1 = "yyyyMMdd HH:mm:ss";
    private static final int INTERVAL = 30;
    @Resource
    private DingTalkHookUtil dingTalkHookUtil;
    /**
     * 定时获取用户注销、注册的信息
     */
//    @Scheduled(fixedDelay = 1000 * 20 )//用来测试
    @Scheduled(cron = "0 0,30 * * * ?")//倘若改变间隔，这行要改，INTERVAL也要改
    public void getUserInfo() {
        LocalDateTime now = roundToMinutes(LocalDateTime.now());
        LocalDateTime lastTime = now.minusMinutes(INTERVAL);
        Date createTime = userBaseInfoService.lambdaQuery()//最新创建的用户的创建时间
                .orderByDesc(UserBaseInfo::getCreateTime).last("limit 1").one().getCreateTime();
        Integer newCreatedUserCount = userBaseInfoService.lambdaQuery()
                .lt(UserBaseInfo::getCreateTime, now)
                .ge(UserBaseInfo::getCreateTime, lastTime).count();
        Integer newDeletedUserCount = userBaseInfoService.lambdaQuery()
                .eq(UserBaseInfo::getDeleted, 1)
                .lt(UserBaseInfo::getUpdateTime, now)
                .ge(UserBaseInfo::getUpdateTime, lastTime).count();
        Integer readingTimes = tarotReadingRecordService.lambdaQuery()
                .lt(TarotReadingRecord::getCreateTime, now)
                .ge(TarotReadingRecord::getCreateTime, lastTime).count();
        Integer readingFailureTimes = tarotReadingRecordService.lambdaQuery()
                .eq(TarotReadingRecord::getStatus,0)
                .lt(TarotReadingRecord::getCreateTime, now)
                .ge(TarotReadingRecord::getCreateTime, lastTime).count();

        LocalDateTime nowMinus1s = now.minusSeconds(1);
        String nowStr = nowMinus1s.format(FORMATTER);
        String lastTimeStr = lastTime.format(FORMATTER);
        String createTimeStr = new SimpleDateFormat(FORMATTER1).format(createTime);

        String message = "=== 塔罗运行监控 ===\n" +
                "当前时间:  "+DateUtil.format(LocalDateTime.now(),FORMATTER1) +"\n" +
                "时间区间:  "+lastTimeStr+" ~ " +nowStr+"\n" +
                "时间跨度:  "+INTERVAL+"分钟\n" +
                "最后一次新用户注册时间:  "+createTimeStr+"\n" +
                "新用户:  "+newCreatedUserCount+"个\n" +
                "注销用户:  "+newDeletedUserCount+"个\n" +
                "解读次数:  共"+readingTimes+"次，成功"+(readingTimes-readingFailureTimes)+"次";
        dingTalkHookUtil.sendDingTalkForMonitor(message);
        log.info(message);
    }

    /**
     * 将时间向下取整到最近的 20 分钟区间
     *
     * @param time 原始时间（如 11:01:23）
     * @return 取整后的时间（如 11:00:00）
     */
    public static LocalDateTime roundToMinutes(LocalDateTime time) {
        return time.withMinute((time.getMinute() / INTERVAL) * INTERVAL)
                .truncatedTo(ChronoUnit.MINUTES); // 替代withSecond(0).withNano(0)
    }


}
