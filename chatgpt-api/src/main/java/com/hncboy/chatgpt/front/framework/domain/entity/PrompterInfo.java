package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 提词器信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/5/8
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("prompter_info")
public class PrompterInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 行业
     */
    private String industry;


    /**
     * 行业名称
     */
    private String industryName;


    /**
     * 提词器
     */
    private String keyWord;


    /**
     * 状态
     */
    private Integer status;


}
