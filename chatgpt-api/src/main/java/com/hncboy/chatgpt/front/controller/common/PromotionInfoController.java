package com.hncboy.chatgpt.front.controller.common;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hncboy.chatgpt.front.framework.converter.PromotionInfoConvert;
import com.hncboy.chatgpt.front.framework.domain.entity.PromotionInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.PromotionInfoVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.service.PromotionInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 促销活动相关
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/8/6
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/promotionInfo")
@Tag(name = "促销活动相关")
public class PromotionInfoController {

    private final PromotionInfoService promotionInfoService;


    @Operation(summary = "获取活动信息")
    @GetMapping("/info")
    public R<PromotionInfoVO> getPromotionInfo() {
        // 查询最新的一条活动信息
        LambdaQueryWrapper<PromotionInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PromotionInfo::getStatus, 0);
        Date date = new Date();
        wrapper.le(PromotionInfo::getStartTime, date);
        wrapper.ge(PromotionInfo::getEndTime, date);
        wrapper.last("limit 1");
        wrapper.orderByDesc(PromotionInfo::getStartTime);
        PromotionInfo promotionInfo = promotionInfoService.getOne(wrapper);
        return R.data(PromotionInfoConvert.INSTANCE.entityToVO(promotionInfo));
    }

}


