package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 用户配置相关信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_config")
public class UserConfig implements Serializable {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private String openid;


    /**
     * 配置信息
     */
    private String content;


    /**
     * 配置类型 1：系统配置 2：用户配置
     */
    private Integer type;


}
