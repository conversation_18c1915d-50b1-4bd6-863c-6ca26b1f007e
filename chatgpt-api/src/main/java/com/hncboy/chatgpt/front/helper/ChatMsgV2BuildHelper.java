package com.hncboy.chatgpt.front.helper;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hncboy.chatgpt.front.api.listener.DifySSEEventSourceListener;
import com.hncboy.chatgpt.front.api.listener.OpenAISSEEventSourceListener;
import com.hncboy.chatgpt.front.framework.config.ChatConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.*;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessV2Request;
import com.hncboy.chatgpt.front.framework.domain.request.DifyCompletionRequest;
import com.hncboy.chatgpt.front.framework.domain.request.DifyProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.vo.*;
import com.hncboy.chatgpt.front.framework.enums.ChatMessageStatusEnum;
import com.hncboy.chatgpt.front.framework.enums.ChatMessageTypeEnum;
import com.hncboy.chatgpt.front.framework.exception.BalanceException;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.handler.response.ResultCode;
import com.hncboy.chatgpt.front.framework.util.*;
import com.hncboy.chatgpt.front.handler.config.SensitiveWordHandler;
import com.hncboy.chatgpt.front.mapper.ChannelConfigMapper;
import com.hncboy.chatgpt.front.mapper.ModelMapper;
import com.hncboy.chatgpt.front.mapper.SiteInfoMapper;
import com.hncboy.chatgpt.front.service.*;
import com.unfbx.chatgpt.OpenAiStreamClient;
import com.unfbx.chatgpt.entity.chat.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.thread.ThreadUtil.sleep;
import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.*;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ChatMsgV2BuildHelper {

    private final ChatConfig chatConfig;
    private final ChatMessageService chatMessageService;
    private final ChatRoomService chatRoomService;
    private final IntelligentAgentService intelligentAgentService;
    private final WriteMessageService writeMessageService;
    private final WriteAgentService writeAgentService;
    private final RedisService redisService;
    private final UserBaseInfoService userBaseInfoService;
    private final ChannelConfigHelper channelConfigHelper;
    private final ExceptionLogService exceptionLogService;
    private final DifyMsgBuildHelper difyMsgBuildHelper;
    private final SysConfigService sysConfigService;
    private final ModelMapper modelMapper;

    private final static Long timeout = 60 * 5 * 1000L;
    private long startTime;
    private long endTime;

    public static String redisKeyPrefix = "FREE_CHAT:";


    /**
     * 构建http流式请求
     *
     * @param chatProcessRequest
     * @return SseEmitter
     * @Author: zc.wu
     * @Date: 2024/2/26 0026 下午 02:37
     */
    @NotNull
    @Transactional(rollbackFor = Exception.class)
    public ResponseBodyEmitter buildMessageBody(ChatProcessV2Request chatProcessRequest) {
        // 初始化 ResponseBodyEmitter
        ResponseBodyEmitter emitter = new ResponseBodyEmitter(timeout);
        if (!StpUtil.isLogin()) {
            return ChatReplyMessageVO.sendAndComplete(emitter, "您还未登录，请登录后操作", ResultCode.UN_AUTHORIZED.getCode());
        }
        ChatRoomDO chatRoomDO = chatProcessRequest.getChatRoomDO();
        if (StrUtil.isNotBlank(chatProcessRequest.getChatRoomId())) {
            chatRoomDO = chatRoomService.getById(chatProcessRequest.getChatRoomId());
        }
        // 验证聊天室信息是否为空
        if (chatRoomDO == null) {
            return ChatReplyMessageVO.sendAndComplete(emitter, "对话信息不能为空", ResultCode.FAILURE.getCode());
        }
        chatProcessRequest.setChatRoomDO(chatRoomDO);
        List<Message> msgList = chatProcessRequest.getMessageList();
        if (CollUtil.isNotEmpty(msgList)) {
            chatProcessRequest.setPrompt(msgList.get(msgList.size() - 1).getContent());
        }
        // 检查敏感词
        if (!SensitiveWordEmitterChain.doChain(chatProcessRequest)) {
            List<String> prompts = SensitiveWordHandler.checkWord(chatProcessRequest.getPrompt());
            return ChatReplyMessageVO.sendAndComplete(emitter
                    , "您的输入包含敏感词【" + CollUtil.join(prompts, ",") + "】，请重新输入", ResultCode.FAILURE.getCode());
        }
        // 获取用户信息
        UserBaseInfoVO userBaseInfoVO = userBaseInfoService.queryUserInfoById(CurrentUserUtil.getV2UserId());
        // 处理智能体
        IntelligentAgent intelligentAgentVO = null;
        if (chatProcessRequest.getIntelligentAgentId() != null) {
            //临时艾特
            intelligentAgentVO =
                    intelligentAgentService.queryInfoSysContentById(chatProcessRequest.getIntelligentAgentId());
        } else if (chatRoomDO.getRoleId() != null) {
            intelligentAgentVO =
                    intelligentAgentService.queryInfoSysContentById(chatProcessRequest.getChatRoomDO().getRoleId());
        }
        // 配置聊天处理请求
        try {
            configureChatProcessRequest(chatProcessRequest, userBaseInfoVO, intelligentAgentVO);
        } catch (BalanceException e) {
            return ChatReplyMessageVO.sendAndComplete(emitter, e.getMessage(), ResultCode.BALANCE.getCode());
        } catch (ServiceException e) {
            return ChatReplyMessageVO.sendAndComplete(emitter, e.getMessage(), ResultCode.FAILURE.getCode());
        }
        String model = chatProcessRequest.getModel();
        // 判断是否为 Dify 应用
        boolean isDifyApp = model.startsWith("app-");
        // 获取站点信息
        SiteInfo siteInfo = channelConfigHelper.queryChannelInfoByGid(model);
        if (isDifyApp) {
            // 初始化 DifySSEEventSourceListener
            DifySSEEventSourceListener eventSourceListener = new DifySSEEventSourceListener(emitter);
            DifyProcessRequest baseDifyChatCompletion = difyMsgBuildHelper.buildDifyChatCompletion(chatProcessRequest);
            if (StrUtil.isBlank(siteInfo.getApiKey())) {
                siteInfo.setApiKey(model);
            }
            this.buildDifyStreamClient(siteInfo)
                    .difyStreamChatCompletion(baseDifyChatCompletion, eventSourceListener);
            // 保存聊天提示信息
            ChatMessageDO chatMessageDO = savePromptMsg(chatProcessRequest, intelligentAgentVO, siteInfo);
            listenProcess(chatProcessRequest, eventSourceListener, chatMessageDO, userBaseInfoVO, siteInfo);
        } else {
            // 初始化 OpenAISSEEventSourceListener
            OpenAISSEEventSourceListener eventSourceListener = new OpenAISSEEventSourceListener(emitter);
            BaseChatCompletion baseChatCompletion;
            if (CollUtil.isNotEmpty(chatProcessRequest.getImgMessages())) {
                // 如果时图片请求 构造专门的请求模型
                baseChatCompletion = buildImageCompletion(chatProcessRequest);
            } else {
                // 构建普通对话完成对象并流式处理
                baseChatCompletion = buildChatCompletion(chatProcessRequest);
            }
            // 保存聊天提示信息
            ChatMessageDO chatMessageDO = savePromptMsg(chatProcessRequest, intelligentAgentVO, siteInfo);
            this.buildOpenAiStreamClient(chatConfig, model, siteInfo)
                    .streamChatCompletion(baseChatCompletion, eventSourceListener);
            listenProcess(chatProcessRequest, eventSourceListener, chatMessageDO, userBaseInfoVO, siteInfo);
        }
        // 更新 Redis 计数
        setRedisNum(USER_NOW_COUNT);
        return emitter;
    }

    /**
     * OpenAI 流式处理
     *
     * @param chatProcessRequest
     * @param eventSourceListener
     * @param chatMessageDO
     * @param userBaseInfoVO
     * @param siteInfo
     */
    private void listenProcess(ChatProcessV2Request chatProcessRequest, OpenAISSEEventSourceListener eventSourceListener,
                               ChatMessageDO chatMessageDO, UserBaseInfoVO userBaseInfoVO, SiteInfo siteInfo) {
        // 处理对话完成事件
        eventSourceListener.setOnComplate(s -> listenHandler(chatProcessRequest, chatMessageDO, userBaseInfoVO, siteInfo, s));
    }

    /**
     * Dify 流式处理
     *
     * @param chatProcessRequest
     * @param eventSourceListener
     * @param chatMessageDO
     * @param userBaseInfoVO
     * @param siteInfo
     */
    private void listenProcess(ChatProcessV2Request chatProcessRequest, DifySSEEventSourceListener eventSourceListener,
                               ChatMessageDO chatMessageDO, UserBaseInfoVO userBaseInfoVO, SiteInfo siteInfo) {
        // 处理对话完成事件
        eventSourceListener.setOnComplate(s -> listenHandler(chatProcessRequest, chatMessageDO, userBaseInfoVO, siteInfo, s));
    }

    private void listenHandler(ChatProcessV2Request chatProcessRequest, ChatMessageDO chatMessageDO,
                               UserBaseInfoVO userBaseInfoVO, SiteInfo siteInfo, OnComplateResVO s) {
        log.info("回答完成：{}", s);

        if (s.getError() != null) {
            String response = s.getResponse();
            String error = s.getError();
            try {
                // 保存异常日志
                exceptionLogService.saveChatExceptionLog(chatProcessRequest, s.getT(),
                        chatMessageDO, userBaseInfoVO, siteInfo, response, error);
            } catch (Exception e) {
                log.error("保存异常日志失败", e);
            }
            try {
                if (!error.contains("手工终止")) {
                    // 发送钉钉消息
                    dingTalkHookUtil.sendChannelExceptionMessage(chatProcessRequest, chatMessageDO,
                            userBaseInfoVO, siteInfo, response, error);
                }
            } catch (Exception e) {
                log.error("发送钉钉消息失败", e);
            }
            if (!error.contains("接收超时") && !error.contains("手工终止")) {
                // 当前通道异常，设置通道状态为不可用
                if (siteInfo.getChannelConfigId() != null) {
                    channelConfigHelper.updateChannelStatusById(siteInfo.getChannelConfigId(), STATUS_DISABLE);
                }
            }
        } else {
            // 当前通道正常，设置通道状态为可用
            if (STATUS_DISABLE.equals(siteInfo.getChannelConfigStatus())) {
                channelConfigHelper.updateChannelStatusById(siteInfo.getChannelConfigId(), STATUS_ENABLE);
                try {
                    // 发送钉钉消息
                    dingTalkHookUtil.sendChannelRecoverMessage(chatMessageDO.getModelGid(), siteInfo, '0');
                } catch (Exception e) {
                    log.error("发送钉钉消息失败", e);
                }
            }
            // 音乐创作模型
            if (chatProcessRequest.getModel().startsWith("suno")) {
                userBaseInfoService.updateUserMusicNumMp(userBaseInfoVO.getId(), 1);
            }
        }
        if (chatMessageDO.getMessageType() == 9) {
            // 写作应用
            this.saveWriteContent(chatMessageDO.getId().intValue(), s);
        } else {
            // 保存系统回答
            saveSysChatMsg(s, chatMessageDO.getId(), userBaseInfoVO.getId(), chatProcessRequest);
        }
        // Dify 保存会话ID 至聊天室中
        ChatRoomDO chatRoomDO = chatProcessRequest.getChatRoomDO();
        if (chatRoomDO != null && chatRoomDO.getConversationId() == null && s.getConversationId() != null) {
            ChatRoomDO entity = new ChatRoomDO();
            entity.setId(chatRoomDO.getId());
            entity.setConversationId(s.getConversationId());
            chatRoomService.updateById(entity);
        }
    }


    /**
     * 查询渠道配置
     *
     * @param chatConfig
     * @param model
     * @return OpenAiStreamClient
     * @Author: zc.wu
     * @Date: 2024/3/27 16:26
     */
    public OpenAiStreamClient buildOpenAiStreamClient(ChatConfig chatConfig, String model, SiteInfo siteInfo) {
        startTime = System.currentTimeMillis();
        String apiHost = chatConfig.getOpenaiApiBaseUrl();
        String openaiApiKey = chatConfig.getOpenaiApiKey();
        if (Objects.nonNull(siteInfo)) {
            apiHost = siteInfo.getUrl();
            openaiApiKey = siteInfo.getApiKey();
        }
        Proxy proxy = Proxy.NO_PROXY;
        if (Objects.nonNull(siteInfo) && StrUtil.isNotEmpty(siteInfo.getHttpProxyHost())) {
            //加入代理
            proxy = getProxy(siteInfo.getHttpProxyHost(), siteInfo.getHttpProxyPort());
        }
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS).proxy(proxy)
                .build();
        log.info("=== current model:   {}", model);
        log.info("=== current hostUrl: {}", apiHost);
        OpenAiStreamClient openAiStreamClient = OpenAiStreamClient.builder().okHttpClient(okHttpClient)
                .apiKey(Arrays.asList(openaiApiKey))
                .apiHost(apiHost).build();
        endTime = System.currentTimeMillis();
        log.info("=== 耗时: [{} - {} = {}]", endTime, startTime, endTime - startTime);
        return openAiStreamClient;
    }

    /**
     * 查询渠道配置
     *
     * @param siteInfo
     * @return OpenAiStreamClient
     * @Author: zd.zhong
     * @Date: 2024/7/2
     */
    public DifyStreamClient buildDifyStreamClient(SiteInfo siteInfo) {
        startTime = System.currentTimeMillis();
        Proxy proxy = Proxy.NO_PROXY;
        if (Objects.nonNull(siteInfo) && StrUtil.isNotEmpty(siteInfo.getHttpProxyHost())) {
            //加入代理
            proxy = getProxy(siteInfo.getHttpProxyHost(), siteInfo.getHttpProxyPort());
        }
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS).proxy(proxy)
                .build();
        log.info("=== current siteName:  {}", siteInfo.getName());
        log.info("=== current hostUrl:   {}", siteInfo.getUrl());
        DifyStreamClient difyStreamClient = DifyStreamClient.builder().okHttpClient(okHttpClient)
                .apiKey(Arrays.asList(siteInfo.getApiKey()))
                .apiHost(siteInfo.getUrl()).build();
        endTime = System.currentTimeMillis();
        log.info("=== final time [{} - {} = {}]", endTime, startTime, endTime - startTime);
        return difyStreamClient;
    }

    /**
     * 配置聊天处理请求
     *
     * @param chatProcessRequest
     * @param userBaseInfoVO
     * @param intelligentAgentVO
     * @return void
     * @Author: zc.wu
     * @Date: 2024/3/1 0001 下午 01:37
     */
    private void configureChatProcessRequest(ChatProcessV2Request chatProcessRequest,
                                             UserBaseInfoVO userBaseInfoVO,
                                             IntelligentAgent intelligentAgentVO) {
        if (intelligentAgentVO == null) {
            //默认对话 newchat的时候 是可以不选角色的
//            setDefaultModel(chatProcessRequest);
            // 应用信息不能为空，当没有这个应用信息时，按已下架返回。
            throw new ServiceException("该应用可能已下架。");
        }
        else {
            if (StrUtil.isEmpty(intelligentAgentVO.getGid())) {
                //自己的智能体可能没有配置模型 设置一个默认模型
                setDefaultModel(chatProcessRequest);
            } else {
                chatProcessRequest.setModel(intelligentAgentVO.getGid());
            }
            if (MODEL_MUSIC.equals(intelligentAgentVO.getModelName())) {
                // 检查音乐创作的可用次数
                if (userBaseInfoVO.getMusicNum() <= 0) {
                    throw new BalanceException("您的音乐创作次数已用完，请充值后使用。");
                }
            } else if (!userBaseInfoService.checkUserIsVip(userBaseInfoVO)) {
                String date = DateUtil.format(new Date(), "yyyy-MM-dd");
                String redisKey = redisKeyPrefix + date + ":" + userBaseInfoVO.getId();
                int dailyFreeTimeNum = 0;
                if (redisService.exists(redisKey)) {
                    dailyFreeTimeNum = Optional.ofNullable(redisService.get(redisKey)).map(o -> Integer.parseInt(o.toString())).orElse(0);
                } else {
                    if (userBaseInfoVO.getDailyFreeTime() != null) {
                        dailyFreeTimeNum = userBaseInfoVO.getDailyFreeTime();
                    } else {
                        SysConfig dailyFreeTime = sysConfigService.querySysConfig("daily_free_time");
                        if (Objects.nonNull(dailyFreeTime)) {
                            dailyFreeTimeNum = Integer.parseInt(dailyFreeTime.getConfigValue());
                        }
                    }
                    redisService.set(redisKey, dailyFreeTimeNum, DateUtils.getLastSeconds());
                }
                if (chatProcessRequest.getNotContinue() == null || chatProcessRequest.getNotContinue() != 1 || dailyFreeTimeNum <= 0) {
                    if (dailyFreeTimeNum <= 0) {
                        throw new BalanceException("您的今天免费对话剩余【" + dailyFreeTimeNum + "】次，明日可继续使用。建议您现在购买，可立即激活使用。");
                    } else {
                        throw new BalanceException("您的会员已过期，请购买后使用。");
                    }
                } else {
                    dailyFreeTimeNum = dailyFreeTimeNum - 1;
                    redisService.set(redisKey, dailyFreeTimeNum, DateUtils.getLastSeconds());
                }
            }
            // 检查应用状态
            checkAgentStatus(intelligentAgentVO.getStatus());
            if (!(intelligentAgentVO.getGid().startsWith("gpt-4-gizmo-") ||
                    intelligentAgentVO.getGid().startsWith("app-")) &&
                    StrUtil.isNotEmpty(intelligentAgentVO.getSysContent())) {
                if (ObjectUtil.isEmpty(chatProcessRequest.getMessageList())) {
                    chatProcessRequest.setMessageList(new ArrayList<>());
                }
                //添加系统消息
                chatProcessRequest.getMessageList().add(Message.builder().role(BaseMessage.Role.SYSTEM)
                        .content(intelligentAgentVO.getSysContent())
                        .build());
            }
        }
    }


    private Proxy getProxy(String host, Integer prot) {
        ChatConfig chatConfig = SpringUtil.getBean(ChatConfig.class);
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, prot));
        return proxy;
    }


    /**
     * 设置默认模型
     *
     * @param chatProcessRequest
     */
    private void setDefaultModel(ChatProcessV2Request chatProcessRequest) {
        //根据配置中的默认模型设置
        chatProcessRequest.setModel(SpringUtil.getBean(ChatConfig.class).getOpenaiApiModel());
    }


    public BaseChatCompletion buildImageCompletion(ChatProcessV2Request chatProcessRequest) {
        BaseChatCompletion baseChatCompletion;
        List<MessagePicture> messagePictureList = Lists.newArrayList();
        chatProcessRequest.getImgMessages().forEach(item -> {
            List<Content> contentList = new ArrayList<>();
            if (item.containsKey("isImg") && item.getBool("isImg")) {
                //代表是图片请求
                JSONArray objectList = item.getJSONArray("content");
                JSONObject objectText = objectList.getJSONObject(0);
                Content textContent =
                        Content.builder().text(objectText.getStr("text")).type(Content.Type.TEXT.getName()).build();
                chatProcessRequest.setPrompt(objectText.getStr("text"));
                contentList.add(textContent);
                for (int i = 1; i < objectList.size(); i++) {
                    JSONObject objectImage = objectList.getJSONObject(i);
                    if (objectImage.getStr("type").equals("image_url")) {
                        JSONObject urlImage = objectImage.getJSONObject("image_url");
                        ImageUrl imageUrl = ImageUrl.builder().url(urlImage.getStr("url")).build();
                        Content imageContent =
                                Content.builder().imageUrl(imageUrl).type(Content.Type.IMAGE_URL.getName()).build();
                        contentList.add(imageContent);
                    }
                }
            } else {
                //普通的上下文要带上
                Content textContent =
                        Content.builder().text(item.getStr("content")).type(Content.Type.TEXT.getName()).build();
                contentList.add(textContent);
            }
            MessagePicture imgMessages = MessagePicture.builder().role(Message.Role.USER).content(contentList).build();
            messagePictureList.add(imgMessages);
        });
        log.debug("\n==== model:\t\t\t\t[{}]\n==== temperature:\t\t[{}]\n==== maxTokens:\t\t\t[{}]\n==== imageMessageCount:\t[{}]",
                chatProcessRequest.getModel(), chatProcessRequest.getTemperature(),
                chatProcessRequest.getMaxTokens(), messagePictureList.size());
        log.debug("=== messageList：\n{}", messagePictureList);
        baseChatCompletion = ChatCompletionWithPicture
                .builder()
                .messages(messagePictureList)
                .model(chatProcessRequest.getModel())
                .build();
        return baseChatCompletion;
    }

    private ChatCompletion buildChatCompletion(ChatProcessV2Request chatProcessRequest) {
        log.debug("\n==== model:\t\t\t[{}]\n==== temperature:\t[{}]\n==== maxTokens:\t\t[{}]\n==== messageCount:\t[{}]",
                chatProcessRequest.getModel(), chatProcessRequest.getTemperature(),
                chatProcessRequest.getMaxTokens(), chatProcessRequest.getMessageList().size());
        log.debug("=== messageList：\n{}", chatProcessRequest.getMessageList());
        return ChatCompletion.builder()
                .model(chatProcessRequest.getModel())
                .temperature(chatProcessRequest.getTemperature())
                .maxTokens(chatProcessRequest.getMaxTokens())
                .messages(chatProcessRequest.getMessageList())
                .stream(true)
                .build();
    }

    private ChatMessageDO savePromptMsg(ChatProcessV2Request chatProcessRequest, String remark) {
        return savePromptMsg(chatProcessRequest, null, null, remark);
    }

    private ChatMessageDO savePromptMsg(ChatProcessV2Request chatProcessRequest, IntelligentAgent intelligentAgentVO,
                                        SiteInfo siteInfo) {
        if (chatProcessRequest.getNotContinue() != null) {
            return savePromptMsg(chatProcessRequest, intelligentAgentVO, siteInfo, "未购买继续使用");
        } else {
            return savePromptMsg(chatProcessRequest, intelligentAgentVO, siteInfo, null);
        }
    }

    /**
     * 保存提示消息
     *
     * @param chatProcessRequest
     * @return ChatMessageDO
     * @Author: zc.wu
     * @Date: 2024/3/1 0001 上午 11:31
     */
    private ChatMessageDO savePromptMsg(ChatProcessV2Request chatProcessRequest, IntelligentAgent intelligentAgentVO,
                                        SiteInfo siteInfo, String remark) {
        ChatMessageDO chatMessageDO = new ChatMessageDO();
        chatMessageDO.setMessageType(ChatMessageTypeEnum.QUESTION.getCode());
        if (Objects.nonNull(chatProcessRequest.getChatRoomDO())) {
            chatMessageDO.setChatRoomId(chatProcessRequest.getChatRoomDO().getId());
        }
        if (ObjectUtil.isEmpty(intelligentAgentVO) && chatProcessRequest.getChatRoomDO().getRoleId() != null) {
            intelligentAgentVO =
                    intelligentAgentService.queryInfoSysContentById(chatProcessRequest.getChatRoomDO().getRoleId());
        }
        if (ObjectUtil.isNotEmpty(intelligentAgentVO)) {
            chatMessageDO.setAgentId(intelligentAgentVO.getId());
            chatMessageDO.setAgentName(intelligentAgentVO.getModelName());
            //更新应用的使用次数
            intelligentAgentService.addUseCnt(intelligentAgentVO.getId());
        }
        chatMessageDO.setModelGid(chatProcessRequest.getModel());
        chatMessageDO.setUserId(CurrentUserUtil.getV2UserId());
        chatMessageDO.setContent(chatProcessRequest.getPrompt());
        chatMessageDO.setIp(chatProcessRequest.getIp());
        if (ObjectUtil.isNotEmpty(siteInfo)) {
            chatMessageDO.setSiteId(siteInfo.getId());
            chatMessageDO.setSiteName(siteInfo.getName());
            chatMessageDO.setSiteUrl(siteInfo.getUrl());
        }
        if (StrUtil.isNotBlank(remark)) {
            chatMessageDO.setRemark(remark);
        }
        ChatMessageDO resDo = chatMessageService.initChatMessage(chatMessageDO);
        //登记对话记录后，更新聊天室的更新时间，用于排序
        ChatRoomDO chatRoomDO = new ChatRoomDO();
        chatRoomDO.setId(chatMessageDO.getChatRoomId());
        chatRoomDO.setUpdateTime(new Date());
        chatRoomService.updateById(chatRoomDO);
        return resDo;
    }


    /**
     * 保存系统回答
     *
     * @param onComplateRes
     * @param chatProcessRequest
     * @return ChatMessageDO
     * @Author: zc.wu
     * @Date: 2024/1/12 0012 下午 01:17
     */
    public ChatMessageDO saveSysChatMsg(OnComplateResVO onComplateRes, Long parentMsgId,
                                        Integer userId, ChatProcessV2Request chatProcessRequest) {
        String msg = onComplateRes.getMessage();
        if (StrUtil.isBlank(msg)) {
            msg = Optional.ofNullable(onComplateRes.getError()).orElse("");
        }
        // 插入此次聊天记录
        ChatMessageDO chatMessageDO = new ChatMessageDO();
        if (onComplateRes.getFirstCharTime() != null) {
            chatMessageDO.setFirstCharTime(onComplateRes.getFirstCharTime());
        }
        chatMessageDO.setMessageType(ChatMessageTypeEnum.ANSWER.getCode());
        if (Objects.nonNull(chatProcessRequest.getChatRoomDO())) {
            chatMessageDO.setChatRoomId(chatProcessRequest.getChatRoomDO().getId());
        }
        chatMessageDO.setModelGid(chatProcessRequest.getModel());
        chatMessageDO.setUserId(userId);
        chatMessageDO.setContent(chatProcessRequest.getPrompt());
        chatMessageDO.setParentMsgId(parentMsgId);
        chatMessageDO.setContent(msg.replaceAll("null", ""));
        chatMessageDO.setStatus(ChatMessageStatusEnum.PART_SUCCESS.getCode());
        //更新父消息状态
        ChatMessageDO chatMessageParent = new ChatMessageDO();
        chatMessageParent.setId(parentMsgId);
        chatMessageParent.setStatus(ChatMessageStatusEnum.PART_SUCCESS.getCode());
        chatMessageService.updateById(chatMessageParent);
        //保存回复
        return chatMessageService.initChatMessage(chatMessageDO);
    }


    /**
     * 设置redis可用次数
     *
     * @param redisKey
     * @return void
     */
    private void setRedisNum(String redisKey) {
        //放入可用次数
        String key = redisKey + CurrentUserUtil.getV2UserId();
        if (redisService.exists(key)) {
            Integer operateNum = (Integer) redisService.get(key);
            redisService.set(key, operateNum + 1);
        } else {
            redisService.set(key, 1);
        }
    }

    private final SiteInfoMapper siteInfoMapper;
    private final ChannelConfigMapper channelConfigMapper;
    private static int cntSuccess = 0;
    private static int cntFail = 0;
    @Resource
    private DingTalkHookUtil dingTalkHookUtil;

    public String detectChannelStatus() {
        // 获取当前站点配置清单
        LambdaQueryWrapper<SiteInfo> siteInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        siteInfoLambdaQueryWrapper.eq(SiteInfo::getStatus, STATUS_ENABLE);
        Map<Integer, SiteInfo> siteInfoMap = siteInfoMapper.selectList(siteInfoLambdaQueryWrapper)
                .stream().collect(Collectors.toMap(SiteInfo::getId, Function.identity()));

        List<String> strings = modelMapper.selectGidList();
        log.info("开始进行通道探测：model需要探测通道为：{}", strings);

        // 获取失败的通道配置清单
        LambdaQueryWrapper<ChannelConfig> channelConfigLambdaQueryWrapper = new LambdaQueryWrapper<>();
        channelConfigLambdaQueryWrapper.eq(ChannelConfig::getStatus, STATUS_DISABLE);
        channelConfigLambdaQueryWrapper.in(ChannelConfig::getSiteId, siteInfoMap.keySet());
        channelConfigLambdaQueryWrapper.in(ChannelConfig::getModelGid, strings);
        List<ChannelConfig> channelConfigList = channelConfigMapper.selectList(channelConfigLambdaQueryWrapper);


        int cntAll = channelConfigList.size();
        cntSuccess = 0;
        cntFail = 0;
        int cntIgnore = 0;
        Object o = new Object();
        log.info("开始进行通道探测：共 {} 个通道需要探测;", cntAll);
        SysConfig gptsModelGidConfig = sysConfigService.querySysConfig("detect_gpts_model");
        String gptsModelGid = null;
        if (Objects.nonNull(gptsModelGidConfig)) {
            gptsModelGid = gptsModelGidConfig.getConfigValue();
        }
        StringBuffer sb = new StringBuffer();

        // 根据失败清单的逐个对站点进行探测
        for (ChannelConfig channelConfig : channelConfigList) {
            if (!siteInfoMap.containsKey(channelConfig.getSiteId())) {
                cntIgnore = cntIgnore + 1;
                log.info("通道未启用，跳过通道{}上模型{}的探测;",
                        channelConfig.getSiteName(), channelConfig.getModelGid());
                continue;
            }
            // 组装探测消息
            ChatProcessV2Request chatProcessRequest = new ChatProcessV2Request();
            SiteInfo siteInfo = siteInfoMap.get(channelConfig.getSiteId());
            chatProcessRequest.setMessageList(new ArrayList<>());
            chatProcessRequest.getMessageList().add(Message.builder().role(BaseMessage.Role.USER)
                    .content("1+1=?")
                    .build());
            String model = channelConfig.getModelGid();
            if (model.startsWith("gpt-4-gizmo")) {
                if (gptsModelGid == null) {
                    cntIgnore = cntIgnore + 1;
                    log.info("未配置GPTS探测模型, 跳过通道{}上模型{}的探测;",
                            channelConfig.getSiteName(), channelConfig.getModelGid());
                    continue;
                }
                chatProcessRequest.setModel(gptsModelGid);
            } else {
                chatProcessRequest.setModel(channelConfig.getModelGid());
            }
            // 构建请求
            BaseChatCompletion baseChatCompletion;
            baseChatCompletion = buildChatCompletion(chatProcessRequest);
            // 初始化 ResponseBodyEmitter
            ResponseBodyEmitter emitter = new ResponseBodyEmitter(timeout);
            // 初始化 OpenAISSEEventSourceListener
            OpenAISSEEventSourceListener eventSourceListener = new OpenAISSEEventSourceListener(emitter);
            this.buildOpenAiStreamClient(chatConfig, chatProcessRequest.getModel(), siteInfo)
                    .streamChatCompletion(baseChatCompletion, eventSourceListener);
            eventSourceListener.setOnComplate(s -> {
                log.info("回答完成：{}", s);
                String dingTalkMessage = "模型【" + channelConfig.getModelGid() + "】在通道【" +
                        channelConfig.getSiteName() + "】上使用apikey【" +
                        Optional.ofNullable(channelConfig.getApiKeyName()).orElse("");
                if (s.getError() != null) {
                    synchronized (o) {
                        cntFail = cntFail + 1;
                    }
                    dingTalkMessage = dingTalkMessage + "】探测失败;";
                    log.info(dingTalkMessage);
                } else {
                    synchronized (o) {
                        cntSuccess = cntSuccess + 1;
                    }
                    dingTalkMessage = dingTalkMessage + "】探测成功;";
                    log.info(dingTalkMessage);
                    // 当前通道正常，设置通道状态为可用
                    if (STATUS_DISABLE.equals(channelConfig.getStatus())) {
                        channelConfigHelper.updateChannelStatusById(channelConfig.getId(), STATUS_ENABLE);
                        try {
                            // 发送钉钉消息
                            dingTalkHookUtil.sendChannelRecoverMessage(chatProcessRequest.getModel(), siteInfo, '1');
                        } catch (Exception e) {
                            log.error("发送钉钉消息失败", e);
                        }
                    }
                }
                sb.append(dingTalkMessage).append("\n");
            });
        }
        //等待探测结束
        long cnt = -1;
        while (timeout > cnt * 1000 && cntAll > cntSuccess + cntFail + cntIgnore) {
            sleep(1000);
            cnt = cnt + 1;
        }

        if(cntSuccess+cntFail+cntIgnore<channelConfigList.size()){
            cntFail+=1;
        }

        String message = String.format("通道探测结束：共探测 [%d] 个通道，成功 [%d] 个，失败 [%d] 个， 跳过 [%d] 个;\n",
        channelConfigList.size(), cntSuccess, cntFail, cntIgnore);
        log.info(message);
        sb.append(message);
        if (cntAll > 0 ) {
            dingTalkHookUtil.sendDingTalk(String.valueOf(sb));
        }
        return "\n" + sb;
    }

    @NotNull
    @Transactional(rollbackFor = Exception.class)
    public ResponseBodyEmitter buildCompletionMessageBody(ChatProcessV2Request chatProcessRequest) {
        // 初始化 ResponseBodyEmitter
        ResponseBodyEmitter emitter = new ResponseBodyEmitter(timeout);
        if (!StpUtil.isLogin()) {
            return ChatReplyMessageVO.sendAndComplete(emitter, "您还未登录，请登录后操作", ResultCode.UN_AUTHORIZED.getCode());
        }
        List<Message> msgList = chatProcessRequest.getMessageList();
        if (CollUtil.isNotEmpty(msgList)) {
            chatProcessRequest.setPrompt(msgList.get(msgList.size() - 1).getContent());
        }
        // 检查敏感词
        if (!SensitiveWordEmitterChain.doChain(chatProcessRequest)) {
            List<String> prompts = SensitiveWordHandler.checkWord(chatProcessRequest.getPrompt());
            return ChatReplyMessageVO.sendAndComplete(emitter
                    , "您的输入包含敏感词【" + CollUtil.join(prompts, ",") + "】，请重新输入", ResultCode.FAILURE.getCode());
        }
        // 获取用户信息
        UserBaseInfoVO userBaseInfoVO = userBaseInfoService.queryUserInfoById(CurrentUserUtil.getV2UserId());
        // 处理智能体
        WriteAgentVO writeAgentVO = writeAgentService.getWriteAgentInfoById(chatProcessRequest.getIntelligentAgentId());
        // 配置聊天处理请求
        try {
            configureChatProcessRequest(chatProcessRequest,userBaseInfoVO, writeAgentVO);
        } catch (BalanceException e) {
            return ChatReplyMessageVO.sendAndComplete(emitter, e.getMessage(), ResultCode.BALANCE.getCode());
        } catch (ServiceException e) {
            return ChatReplyMessageVO.sendAndComplete(emitter, e.getMessage(), ResultCode.FAILURE.getCode());
        }
        SiteInfo siteInfo = channelConfigHelper.queryChannelInfoByGid(chatProcessRequest.getModel());
        // 初始化 DifySSEEventSourceListener
        DifySSEEventSourceListener eventSourceListener = new DifySSEEventSourceListener(emitter);
        DifyCompletionRequest baseDifyChatCompletion = difyMsgBuildHelper.buildDifyCompletion(chatProcessRequest);

        // 保存对话记录
        WriteMessage writeMessage = saveInputs(chatProcessRequest, writeAgentVO, siteInfo);
        ChatMessageDO chatMessageDO = new ChatMessageDO();
        // 写作类应用
        chatMessageDO.setMessageType(9);
        chatMessageDO.setId(writeMessage.getId().longValue());
        chatMessageDO.setModelGid(writeMessage.getModel());
        chatMessageDO.setAgentId(writeMessage.getAgentId());
        chatMessageDO.setSiteUrl(writeMessage.getSiteUrl());
        chatMessageDO.setSiteName(writeMessage.getSiteName());
        this.buildDifyStreamClient(siteInfo).difyStreamCompletion(baseDifyChatCompletion, eventSourceListener);
        listenProcess(chatProcessRequest, eventSourceListener, chatMessageDO, userBaseInfoVO, siteInfo);

        // 更新 Redis 计数
        setRedisNum(USER_NOW_COUNT);
        return emitter;
    }

    /**
     * 配置写作处理请求
     *
     * @param userBaseInfoVO
     * @param writeAgentVO
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/7/3
     */
    private void configureChatProcessRequest(ChatProcessV2Request chatProcessRequest,
                                             UserBaseInfoVO userBaseInfoVO,
                                             WriteAgentVO writeAgentVO) {

        if (writeAgentVO == null) {
            throw new ServiceException("请选择写作应用");
        }
        //验证可行性
        String inputs = chatProcessRequest.getPrompt();
        // 检查应用状态
        checkAgentStatus(writeAgentVO.getStatus());
        Integer Consume = getConsume(inputs,writeAgentVO);
        // 检查写作点数是否足额
        if (userBaseInfoVO.getWriteNum() < Consume) {
            throw new BalanceException("您的写作点数不足，请充值");
        }
        // 扣除写作点数
        userBaseInfoService.updateUserWriteNumMp(userBaseInfoVO.getId(), Consume);
    }


    //0: 可见也可用
    //1：页面提示：该应用正在优化，现临时停用，优化后重新启用。
    //2: 隐藏应用，可用，但不可见
    //3: 写作应用，不可用，不可见，提示：该对话助手已迁移到写作模块，请前往写作模块里使用
    //9：页面提示：该应用可能已下架。。。。。。。
    //其他: 该应用目前已不可用，可能已下架，如需使用，请联系管理员。
    private void checkAgentStatus(Integer status) {
        if (status != 0 && status != 2) {
            if (status == 1) {
                throw new ServiceException("该应用正在优化，现临时停用，优化后重新启用。");
            }if (status == 3) {
                throw new ServiceException("该对话助手已迁移到写作模块，请前往写作模块里使用");
            } else if (status == 9) {
                throw new ServiceException("该应用可能已下架。");
            } else {
                throw new ServiceException("该应用目前已不可用，可能已下架，如需使用，请联系管理员。");
            }
        }
    }

    /**

     *
     * @param chatProcessRequest
     * @param writeAgentVO
     * @param siteInfo
     * @return
     */
    private WriteMessage saveInputs(ChatProcessV2Request chatProcessRequest, WriteAgentVO writeAgentVO, SiteInfo siteInfo) {
        String inputs = chatProcessRequest.getPrompt();
        JSONObject jsonObject = JSONUtil.parseObj(inputs);
        String topic = jsonObject.getStr("topic");
        if (StringUtils.isEmpty(topic)) {
            throw new ServiceException("主题不能为空");
        }
        WriteMessage writeMessage = new WriteMessage();
        writeMessage.setInputs(inputs);
        writeMessage.setTopic(topic);
        writeMessage.setAgentId(writeAgentVO.getId());
        writeMessage.setAgentName(writeAgentVO.getAgentName());
        writeMessage.setAgentTitle(writeAgentVO.getTitle());
        writeMessage.setConsume(getConsume(inputs,writeAgentVO));
        writeMessage.setModel(writeAgentVO.getGid());
        writeMessage.setSiteId(siteInfo.getId());
        writeMessage.setSiteName(siteInfo.getName());
        writeMessage.setSiteUrl(siteInfo.getUrl());
        writeMessage.setUserId(CurrentUserUtil.getV2UserId());
        writeMessage.setIp(WebUtil.getIp());
        writeMessageService.save(writeMessage);
        writeAgentService.addUseCnt(writeAgentVO.getId());
        return writeMessage;
    }
    /**
     * 获取写作消耗点数
     *
     * @Author: 赵雨晨
     * @Date: 2023/7/16
     * @return
     */
    private Integer getConsume(String inputs, WriteAgentVO writeAgentVO) {
        //获取应用返回json中是否包含wordsCount字段
        //获取wirte_agent表中的consume字段，该字段为字符串类型的Json，若该应用无额外收费要求则Json为{'Default':Integer}
        //该应用采用分段计费Json为{'字数':Integer}
        //强制要求有分段计费的应用中，涉及到计费的字段名为wordsCount，否则报错：请选择正确的字数！
        //没有分段计费要求的应用默认wordsCount的值为Default
        if (!JSONUtil.isTypeJSON(inputs)) {
            throw new ServiceException("输入内容必须是json格式");
        }
        JSONObject jsonObject = JSONUtil.parseObj(inputs);
        String wordsCount = jsonObject.getStr("wordsCount");
        if (StringUtils.isEmpty(wordsCount)) {
            wordsCount = "Default";
        }
        //JSON字符串转为Map<String, Integer>
        ObjectMapper objectMapper = new ObjectMapper();
        String consumeJson = writeAgentVO.getConsume();
        Map<String, Integer> consumeMap = null;
        try {
            consumeMap = objectMapper.readValue(consumeJson, new TypeReference<Map<String, Integer>>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        Integer Consume = consumeMap.getOrDefault(wordsCount,consumeMap.getOrDefault("Default",-1));
        if (Consume == -1) {
            throw new BalanceException("请选择正确的字数！");
        }
        return Consume;
    }


    private void saveWriteContent(Integer id, OnComplateResVO onCompleteRes) {
        String content = onCompleteRes.getMessage();
        if (StrUtil.isBlank(content)) {
            content = Optional.ofNullable(onCompleteRes.getError()).orElse("");
        }
        WriteMessage writeMessage = new WriteMessage();
        if (onCompleteRes.getFirstCharTime() != null) {
            writeMessage.setFirstCharTime(onCompleteRes.getFirstCharTime());
        }
        writeMessage.setId(id);
        writeMessage.setContent(content);
        if (!content.isEmpty() && onCompleteRes.getError() == null) {
            writeMessage.setStatus("1");
        }
        else{
            writeMessage.setStatus("9");
            writeMessage.setIsDelete(1);
            WriteMessageVO writeMessageVO = writeMessageService.getWriteMessageInfoById(id);
            userBaseInfoService.updateUserWriteNumMp(writeMessageVO.getUserId(),writeMessageVO.getConsume()*-1);
        }
        writeMessageService.saveOrUpdate(writeMessage);
    }

    /**
     * 获取写作余额与预消耗点数
     * 余额充足返回0，余额不足返回-1
     * @return
     * @Author: 赵雨晨
     * @Date: 2023/7/16
     */
    public Object getWriteBalanceAndConsume(ChatProcessV2Request wirteProcessRequest) {
        UserBaseInfoVO userBaseInfoVO = userBaseInfoService.queryUserInfoById(CurrentUserUtil.getV2UserId());
        // 处理智能体
        WriteAgentVO writeAgentVO = writeAgentService.getWriteAgentInfoById(wirteProcessRequest.getIntelligentAgentId());
        if (writeAgentVO == null) {
            throw new ServiceException("请选择写作应用");
        }
        //验证可行性
        String inputs = wirteProcessRequest.getPrompt();
        // 检查应用状态
        checkAgentStatus(writeAgentVO.getStatus());
        Integer Consume = getConsume(inputs,writeAgentVO);
        int status = 0;
        // 检查写作点数是否足额
        if (userBaseInfoVO.getWriteNum() < Consume) {
            status = -1;
        }
        Map<Object, Object> consumeMap = new HashMap<>();
        consumeMap.put("userConsume", userBaseInfoVO.getWriteNum());
        consumeMap.put("writeConsume", Consume);
        consumeMap.put("status", status);
        return consumeMap;
    }
}
