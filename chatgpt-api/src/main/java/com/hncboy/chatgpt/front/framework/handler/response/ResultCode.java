package com.hncboy.chatgpt.front.framework.handler.response;

import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2023/3/23 12:34
 * 结果状态码
 */
@Getter
@AllArgsConstructor
public enum ResultCode implements IResultCode {

    /**
     * 操作成功
     */
    SUCCESS(HttpServletResponse.SC_OK, "操作成功"),

    /**
     * 业务异常
     */
    FAILURE(HttpServletResponse.SC_BAD_REQUEST, "业务异常"),


    /**
     * 余额不足
     */
    BALANCE(1099, "余额不足"),


    /**
     * 二维码过期
     */
    QR_OVERDUE(2001, "二维码过期"),


    /**
     * 请求未授权
     */
    UN_AUTHORIZED(HttpServletResponse.SC_UNAUTHORIZED, "请求未授权"),

    WX_AUTHORIZED(HttpServletResponse.SC_PAYMENT_REQUIRED, "公众号异常，请反馈管理员"),

    /**
     * 服务器异常
     */
    INTERNAL_SERVER_ERROR(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "系统繁忙，请稍后再试"),

    /**
     * 请求重复
     */
    THE_REQUEST_IS_DUPLICATED(2, "您上次的问题还在解读中，请稍后在提问历程里查看结果");

    /**
     * 状态码
     */
    private final int code;

    /**
     * 信息
     */
    private final String message;
}
