package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.dto.PayCodeDTO;
import com.hncboy.chatgpt.front.framework.domain.dto.WxPayOrderDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.WxPayOrder;
import com.hncboy.chatgpt.front.framework.domain.vo.WxPayOrderVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 支付订单信息 领域对象转换器
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/21
 */
@Mapper
public interface WxPayOrderConvert {

    WxPayOrderConvert INSTANCE = Mappers.getMapper(WxPayOrderConvert.class);

    /**
     * WxPayOrderDTO转WxPayOrder
     *
     * @Author: wzhic
     * @Date:2023/4/21
     */
    WxPayOrder dtoToEntity(WxPayOrderDTO dto);

    /**
     * WxPayOrder 转WxPayOrderVO
     *
     * @Author: wzhic
     * @Date:2023/4/21
     */
    WxPayOrderVO entityToVO(WxPayOrder entity);

    /**
     * List<WxPayOrder> 转List<WxPayOrderVO>
     *
     * @Author: wzhic
     * @Date:2023/4/21
     */
    List<WxPayOrderVO> entityListToVOList(List<WxPayOrder> entityList);

    /**
     * 查询DTO转换
     *
     * @Author: wzhic
     * @Date:2023/4/21
     */
    WxPayOrder queryDtoToEntity(WxPayOrderDTO dto);

    WxPayOrderDTO queryDtoToEntity(PayCodeDTO dto);


}
