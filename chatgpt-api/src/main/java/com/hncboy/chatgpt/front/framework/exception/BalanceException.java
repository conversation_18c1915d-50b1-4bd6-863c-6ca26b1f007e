package com.hncboy.chatgpt.front.framework.exception;

import com.hncboy.chatgpt.front.framework.handler.response.IResultCode;
import com.hncboy.chatgpt.front.framework.handler.response.ResultCode;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/3/23 00:28
 * 业务异常
 */
public class BalanceException extends RuntimeException {

    @Getter
    private final IResultCode resultCode;

    public BalanceException(String message) {
        super(message);
        this.resultCode = ResultCode.BALANCE;
    }
}
