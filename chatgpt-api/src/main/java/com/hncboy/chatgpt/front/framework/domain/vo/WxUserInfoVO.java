package com.hncboy.chatgpt.front.framework.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信用户信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/7
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WxUserInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * openId
     */
    private String openId;


    /**
     * 用户昵称
     */
    private String nickName;


    /**
     * 渠道来源
     */
    private String subscribeScene;


    /**
     * 二维码扫码场
     */
    private String qrScene;


    /**
     * 用户性别
     */
    private String gender;


    /**
     * 语言
     */
    private String language;


    /**
     * 用户所在城市
     */
    private String city;


    /**
     * unionId
     */
    private String unionId;


    /**
     * 用户所在省份
     */
    private String province;


    /**
     * 用户所在国家
     */
    private String country;


    /**
     * 用户头像图片
     */
    private String avatarUrl;


    /**
     * vip到期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date vipEndTime;


    /**
     * 金币
     */
    private Integer applyNum;


    /**
     * 是否VIP
     */
    private Integer vip;


    /**
     * 搜索值
     */
    private String searchValue;


    /**
     * 备注
     */
    private String remark;

    /**
     * token
     */
    private String token;


    /**
     * 可用次数
     */
    private Integer operateNum = 0;


    /**
     * 可用次数
     */
    private Integer imageNum = 0;


    /**
     * 今日剩余次数
     */
    private Integer remainingToday = 0;


    /**
     * 父ID
     */
    private String parentId;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建者
     */
    private String createBy;


    /**
     * 分佣
     */
    private Double monery = 0d;


    /**
     * 模型
     */
    private String model = "1";

}
