package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.dto.UserPointsLogDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog;
import com.hncboy.chatgpt.front.framework.domain.vo.UserPointsLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 积分明细 领域对象转换器
 * @Version: v1.0.0
 * @Author: zzd
 * @Date: 2024/6/27 14:06
 */
@Mapper
public interface UserPointsLogConvert {

   UserPointsLogConvert INSTANCE = Mappers.getMapper(UserPointsLogConvert.class);

  /**
   * UserPointsLogDTO 转 UserPointsLog
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  UserPointsLog dtoToEntity(UserPointsLogDTO dto);

  /**
   * UserPointsLog 转 UserPointsLogVO
   * @Author: zzd
   * @Date: 2024/8/8
   */
  UserPointsLogVO entityToVO(UserPointsLog entity);

  /**
   * List<UserPointsLog> 转 List<UserPointsLogVO>
   * @Author: zzd
   * @Date: 2024/8/8
   */
  List<UserPointsLogVO> entityListToVOList(List<UserPointsLog> entityList);

  /**
   * 查询DTO转换
   * @Author: zzd
   * @Date: 2024/8/8
   */
   UserPointsLog queryDtoToEntity(UserPointsLogDTO dto);


}
