package com.hncboy.chatgpt.front.api.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hncboy.chatgpt.front.framework.domain.vo.ChatReplyMessageVO;
import com.hncboy.chatgpt.front.framework.domain.vo.OnComplateResVO;
import com.hncboy.chatgpt.front.framework.handler.response.ResultCode;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.appError;
import static org.ietf.jgss.GSSException.FAILURE;

@Slf4j
public class OpenAISSEEventSourceListener extends EventSourceListener {

    private long tokens;
    private final long initTime;
    private long startTime;
    private long endTime;

    protected OnComplateResVO onComplateResVO = new OnComplateResVO();
    protected String lastMessage = "";
    /**
     * C当收到所有新消息时调用。
     *
     * @param message the new message
     */
    @Setter
    @Getter
    protected Consumer<OnComplateResVO> onComplate = s -> {

    };

    private ResponseBodyEmitter sseEmitter;

    public OpenAISSEEventSourceListener(ResponseBodyEmitter sseEmitter) {
        initTime = System.currentTimeMillis();
        this.sseEmitter = sseEmitter;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void onOpen(EventSource eventSource, Response response) {
        startTime = System.currentTimeMillis();
        log.info("OpenAI建立sse连接...耗时: [{}]", startTime - initTime);
    }

    /**
     * {@inheritDoc}
     */
    @SneakyThrows
    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        log.info("OpenAI返回数据：{}", data);
        try {
            if(!StringUtils.isNotEmpty(data)) {
                data="error";
                sseEmitter.complete();
                return;
            }
            if (onComplateResVO.getFirstCharTime() == null) {
                onComplateResVO.setFirstCharTime(new Date());
            }
            String str = "";
            if (JSONUtil.isTypeJSON(data) && !data.equals("[DONE]")) {
                ChatCompletionResponse completionResponse = JSONUtil.toBean(data, ChatCompletionResponse.class);
                if (Objects.nonNull(completionResponse) && CollUtil.isNotEmpty(completionResponse.getChoices())) {
                    if (completionResponse.getChoices().get(0).getDelta() != null) {
                        str = Optional.ofNullable(completionResponse.getChoices().get(0).getDelta().getContent()).orElse("");
                    }
                }
                JSONObject object = JSONUtil.parseObj(data);
                if (object.containsKey("model")) {
                    object.remove("model");
                    data = object.toString();
                }
            }
            sseEmitter.send(("data: " + data + "\n\n"));
            if (data.equals("[DONE]")) {
                log.info("OpenAI返回数据结束了");
                if (StrUtil.isNotBlank(lastMessage)) {
                    onComplateResVO.setMessage(lastMessage);
                    onComplate.accept(onComplateResVO);
                }
                // 传输完成后自动关闭sse
                sseEmitter.complete();
                return;
            }
            lastMessage = lastMessage + str;
        } catch (Exception e) {
            log.error("=== 消息发送异常，异常内容 ===", e);
            sseEmitter.send(ChatReplyMessageVO.buildFailureMessage("消息发送异常", ResultCode.FAILURE.getCode()));
            sseEmitter.complete();
        }
    }


    @Override
    public void onClosed(EventSource eventSource) {
        endTime = System.currentTimeMillis();
        log.info("流式输出返回值总共{}tokens", tokens() - 2);
        log.info("OpenAI关闭sse连接...耗时: [{}]", endTime - initTime);
    }


    @SneakyThrows
    @Override
    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        endTime = System.currentTimeMillis();
        String msg = appError;
        String errorMsg = "";
        if (Objects.isNull(response)) {
            log.error("=== OpenAI sse连接异常 ===", t);
//            msg = "不好意思，和我交流的人太多了，我有点忙，可以晚点和我聊，或者试试再问我一次。";
        } else {
//            msg = "不好意思，和我交流的人太多了，我有点忙，可以晚点和我聊，或者试试再问我一次。";
            ResponseBody body = response.body();
            if (Objects.nonNull(body)) {
                errorMsg = body.string();
                log.error("=== OpenAI  sse连接异常data：[{}]，异常： ===", errorMsg, t);
            } else {
                log.error("=== OpenAI  sse连接异常data：[{}]，异常： ===", response, t);
            }
            log.error("=== OpenAI  sse连接异常End ===");
        }
        if (t != null && "ResponseBodyEmitter has already completed".equals(t.getMessage())) {
            Long finalTimes = endTime - initTime;
            String timeStr = String.format("耗时: [" + endTime + "-" + startTime + "-" + initTime + "->" + finalTimes + "]");
            if (finalTimes.compareTo(Objects.requireNonNull(sseEmitter.getTimeout())) < 0) {
                msg = "手工终止";
            } else {
                msg = "接收超时";
            }
            log.error("{}, {}", msg, timeStr);
        }
        onComplateResVO.setMessage(lastMessage);
        onComplateResVO.setT(t);
        onComplateResVO.setError(msg);
        onComplateResVO.setResponse(errorMsg);
        onComplate.accept(onComplateResVO);
        sseEmitter.send(ChatReplyMessageVO.buildFailureMessage(msg, FAILURE));
        sseEmitter.complete();
        eventSource.cancel();
    }

    /**
     * tokens
     *
     * @return
     */
    public long tokens() {
        return tokens;
    }
}
