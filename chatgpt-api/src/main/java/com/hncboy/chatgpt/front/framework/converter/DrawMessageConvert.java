package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.dto.DrawMessageDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.DrawMessage;
import com.hncboy.chatgpt.front.framework.domain.vo.DrawMessageVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-03-20 20:31
 */
@Mapper
public interface DrawMessageConvert {

    DrawMessageConvert INSTANCE = Mappers.getMapper(DrawMessageConvert.class);

    /**
     * List<DrawMessage> 转List<DrawMessageVO>
     *
     * @param entityList
     * @return
     */
    List<DrawMessageVO> entityListToVOList(List<DrawMessage> entityList);

    /**
     * DrawMessageDTO转DrawMessage
     *
     * @param dto
     * @return
     */
    DrawMessage dtoToEntity(DrawMessageDTO dto);


    DrawMessageVO entityListToVO(DrawMessage entityList);
}
