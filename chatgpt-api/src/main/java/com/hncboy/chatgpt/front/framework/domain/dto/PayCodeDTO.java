package com.hncboy.chatgpt.front.framework.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 支付订单信息 DTO
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/21
 */
@Data
public class PayCodeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;
    private String orderNo;

    @Schema(title = "商品ID")
    private Integer goodsId;
    
    private Long productId;
    
    private String type;

    @Schema(title = "商品描述")
    private String body;

    @Schema(title = "商户订单号")
    private String outTradeNo;

    @Schema(title = "总金额")
    private Double totalFee;

    @Schema(title = "状态")
    private Integer status;

    @Schema(title = "终端IP")
    private String spbillCreateIp;

    @Schema(title = "通知地址")
    private String notifyUrl;

    @Schema(title = "交易类型")
    private String tradeType;

    @Schema(title = "用户标识")
    private String openid;

    @Schema(title = "原始数据")
    private String originalMessage;

    @Schema(title = "是否关注公众账号")
    private String isSubscribe;

    @Schema(title = "微信支付订单号")
    private String transactionId;

    @Schema(title = "支付完成时间")
    private String timeEnd;

    @Schema(title = "创建者")
    private String createBy;

    @Schema(title = "创建时间")
    private LocalDateTime createTime;

    @Schema(title = "更新者")
    private String updateBy;

    @Schema(title = "更新时间")
    private LocalDateTime updateTime;

    @Schema(title = "备注")
    private String remark;

    private Integer userId;

    private String userValue;
    


}
