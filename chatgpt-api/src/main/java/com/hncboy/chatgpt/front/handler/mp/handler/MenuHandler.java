package com.hncboy.chatgpt.front.handler.mp.handler;

import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.stereotype.Component;

import java.util.Map;

import static me.chanjar.weixin.common.api.WxConsts.EventType;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Component
public class MenuHandler extends AbstractHandler {

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService weixinService,
                                    WxSessionManager sessionManager) {
        if (EventType.VIEW.equals(wxMessage.getEvent())) {
            return null;
        }
        String msg = "" +
                "\uD83D\uDC4F欢迎关注我们！\n" +
                "\n" +
                "浙江方顶数融科技有限公司专注于构建人工智能应用架构。" +
                "通过多智能体作业平台及一系列应用产品和工具链，" +
                "我们帮助企业以可控且可评估的方式实施人工智能技术，" +
                "从而提升运营效率并提高员工工作效率。" +
                "此外，我们与企业共同构建领域模型，助力企业转型其产品和服务，创造更大的价值。";

        return WxMpXmlOutMessage.TEXT().content(msg)
                .fromUser(wxMessage.getToUser()).toUser(wxMessage.getFromUser())
                .build();
    }

}
