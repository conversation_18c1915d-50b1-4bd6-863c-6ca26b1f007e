package com.hncboy.chatgpt.front.framework.converter;

import java.util.List;

import com.hncboy.chatgpt.front.framework.domain.dto.IntelligentFavDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.IntelligentFav;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentFavVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 智能体收藏 领域对象转换器
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/1
 */
@Mapper
public interface IntelligentFavConvert {

   IntelligentFavConvert INSTANCE = Mappers.getMapper(IntelligentFavConvert.class);

  /**
   * IntelligentFavDTO转IntelligentFav
   * @Author: wZhic
   * @Date:2024/3/1
   */
  IntelligentFav dtoToEntity(IntelligentFavDTO dto);

  /**
   * IntelligentFav 转IntelligentFavVO
   * @Author: wZhic
   * @Date:2024/3/1
   */
  IntelligentFavVO entityToVO(IntelligentFav entity);

  /**
   * List<IntelligentFav> 转List<IntelligentFavVO>
   * @Author: wZhic
   * @Date:2024/3/1
   */
  List<IntelligentFavVO> entityListToVOList(List<IntelligentFav> entityList);

  /**
   * 查询DTO转换
   * @Author: wZhic
   * @Date:2024/3/1
   */
   IntelligentFav queryDtoToEntity(IntelligentFavDTO dto);


}