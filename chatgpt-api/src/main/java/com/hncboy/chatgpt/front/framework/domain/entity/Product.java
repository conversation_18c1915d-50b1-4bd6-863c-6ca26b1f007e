package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hncboy.chatgpt.tarot.i18n.I18nStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 雨纷纷旧故里草木深
 *
 * <AUTHOR>
 * @version 1.0
 */
@Data
@TableName(value = "product")
@Accessors(chain = true)
public class Product {

    @TableId(type = IdType.AUTO)
    private Long productId;

    /**
     * 名称
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String productName;

    /**
     * 类型(CHAT-对话;DRAW-绘图;COMMON-通用)
     */
    private String type;

    /**
     * 单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)
     */
    private String unit;
    /**
     * 描述
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String remark;

    /**
     * 数量
     */
    private Long num;

    /**
     * 按钮名称
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String buttonName;

    /**
     * 金额
     */
    private Double productPrice;

    /**
     * 组合套餐信息
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String packageInfo;

    /**
     * 排序
     */
    private Integer sort;


    /**
     * 开始时间
     */
    private Date startTime;


    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 启用状态(0启用1禁用)
     */
    private Integer status;
    /**
     * 充值渠道
     */
    private String channel;
    /**
     * 推荐充值 0:首选 1:非首选
     */
    private String preferredRecharge;

    /**
     * 顶部图标
     */
    private String topIcon;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 用户注册时间开始 */
    private Date userRegTimeS;
    /** 用户注册时间截止 */
    private Date userRegTimeE;

    //描述
    @JsonSerialize(using = I18nStringSerializer.class)
    private String description;

    private String currency;
}
