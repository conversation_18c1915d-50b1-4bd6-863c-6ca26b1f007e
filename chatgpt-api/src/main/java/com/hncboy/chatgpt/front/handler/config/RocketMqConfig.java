//package com.hncboy.chatgpt.front.handler.config;
//
//import org.apache.rocketmq.client.producer.DefaultMQProducer;
//import org.apache.rocketmq.spring.core.RocketMQTemplate;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * zcWu
// */
//@Configuration
//public class RocketMqConfig {
//
//
//    @Value("${rocketmq.consumer.group}")
//    private String producerGroup;
//
//    @Value("${rocketmq.name-server}")
//    private String nameServer;
//
//
//    /**
//     * 由于使用的Spring版本是3.0.0以上，与rocketMq不是很兼容，对于rocketMqTemplate
//     * 的自动注入存在差异，如果不采用这种方式注入则会报出缺少bean的信息
//     */
//    @Bean("RocketMqTemplate")
//    public RocketMQTemplate rocketMqTemplate() {
//        RocketMQTemplate rocketMqTemplate = new RocketMQTemplate();
//        DefaultMQProducer defaultMqProducer = new DefaultMQProducer();
//        defaultMqProducer.setProducerGroup(producerGroup);
//        defaultMqProducer.setNamesrvAddr(nameServer);
//        rocketMqTemplate.setProducer(defaultMqProducer);
//        return rocketMqTemplate;
//    }
//}
