package com.hncboy.chatgpt.front.helper;

import cn.hutool.core.lang.Console;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * zcWu
 */
@Component
@Slf4j
public class DouYinContentCheckHelper {

    private final String rediksKey = "DouYinToken";
    @Autowired
    private RedisService redisService;

    public String getDyToken() {
        if (redisService.exists(rediksKey)) {
            return (String) redisService.get(rediksKey);
        }
        String url = "https://developer.toutiao.com/api/apps/v2/token";
        Map map = Maps.newHashMap();
        map.put("appid", "tt46bc3001d98088b001");
        map.put("secret", "1a0d3cc40a2312b8a66cf727fdaa650d73de48e6");
        map.put("grant_type", "client_credential");
        String post = HttpUtil.post(url, JSONUtil.toJsonStr(map));
        JSONObject object = JSONUtil.parseObj(post);
        if (object.getInt("err_no") == 0) {
            String access_token = object.getJSONObject("data").getStr("access_token");
            redisService.set(rediksKey, access_token, 7000L);
            return access_token;
        }
        return null;
    }

    public void checkContent(String content) {
        if (StringUtils.isEmpty(content)) {
            return;
        }
//        if (content.contains("64") || content.contains("6.4")
//                || content.contains("89") || content.contains("1989")
//                || content.contains("天安门") || content.contains("yellow umbrella")
//                || content.contains("6 4") || content.contains("香港街头")) {
//            throw new ServiceException("言论存在风险：请修改后再试");
//        }
//        String url = "https://developer.toutiao.com/api/v2/tags/text/antidirt";
//        String jsonStr = "{\n" +
//                "  \"tasks\": [\n" +
//                "    {\n" +
//                "      \"content\": \"" + content + "\"\n" +
//                "    }\n" +
//                "  ]\n" +
//                "}";
//        String result = HttpRequest.post(url)
//                .header("X-Token", getDyToken())
//                .body(jsonStr)
//                .timeout(20000)//超时，毫秒
//                .execute().body();
//        Console.log("文本检测：" + result);
//        JSONObject object = JSONUtil.parseObj(result);
//        if (object.containsKey("code")) {
//            log.error("文本检测失败：" + object.getStr("message"));
//            return;
//        }
//        Boolean hit = object.getJSONArray("data").getJSONObject(0).getJSONArray("predicts").getJSONObject(0).getBool("hit");
//        if (hit) {
//            throw new ServiceException("言论存在风险：请修改后再试");
//        }
    }


    public void checkImage2(String imageData) {
        if (StringUtils.isEmpty(imageData)) {
            return;
        }
        Map map = Maps.newHashMap();
        map.put("app_id", "tt46bc3001d98088b001");
        map.put("access_token", getDyToken());
//        map.put("image", null);
        map.put("image", imageData);
        String post = HttpUtil.post("https://developer.toutiao.com/api/apps/censor/image", JSONUtil.toJsonStr(map));
        JSONObject object = JSONUtil.parseObj(post);
        if (object.getInt("error") != 0) {
            log.error("图片检测失败：" + object.getStr("message"));
            throw new ServiceException("图片检测失败：" + object.getStr("message"));
        }
        JSONArray hit = object.getJSONArray("predicts");
        hit.forEach(item -> {
            if (JSONUtil.parseObj(item).getBool("hit")) {
                throw new ServiceException("图片存在风险，请重新上传");
            }
        });
    }


    public void checkImage(String imageData) {
        if (StringUtils.isEmpty(imageData)) {
            return;
        }
        String url = "https://developer.toutiao.com/api/v2/tags/image/";
        String jsonStr = "{\n" +
                "  \"targets\": [\"ad\", \"porn\", \"politics\", \"disgusting\"],\n" +
                "  \"tasks\": [\n" +
                "    {\n" +
                "      \"image_data\": \"" + imageData + "\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        String result = HttpRequest.post(url)
                .header("X-Token", getDyToken())
                .body(jsonStr)
                .timeout(20000)//超时，毫秒
                .execute().body();
        Console.log("图片检测：" + result);
        JSONObject object = JSONUtil.parseObj(result);
        if (object.containsKey("code")) {
            log.error("图片检测失败：" + object.getStr("message"));
            throw new ServiceException("图片检测失败：" + object.getStr("message"));
        }
        JSONArray hit = object.getJSONArray("data").getJSONObject(0).getJSONArray("predicts");
        hit.forEach(item -> {
            if (JSONUtil.parseObj(item).getBool("hit")) {
                throw new ServiceException("图片内容检测不通过，请更换后再试");
            }
        });
    }
}
