package com.hncboy.chatgpt.front.service.impl;

import java.util.List;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hncboy.chatgpt.front.framework.converter.IntelligentAgentConvert;
import com.hncboy.chatgpt.front.framework.converter.IntelligentFavConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.IntelligentFavDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.IntelligentAgent;
import com.hncboy.chatgpt.front.framework.domain.entity.IntelligentFav;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentFavVO;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.mapper.IntelligentFavMapper;
import com.hncboy.chatgpt.front.service.IntelligentAgentService;
import com.hncboy.chatgpt.front.service.IntelligentFavService;
import com.mysql.cj.result.Row;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
/**
 * 智能体收藏实现
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/1
 */
@Service
@AllArgsConstructor
public class IntelligentFavServiceImpl  extends ServiceImpl<IntelligentFavMapper, IntelligentFav> implements IntelligentFavService {

    private final IntelligentAgentService intelligentAgentService;

    /**
     * 根据ID查询智能体收藏
     * @Param:@param id
     * @return:com.zhongjia.infrastructure.db.entity.IntelligentFav
     * @Author: wZhic
     * @Date:2024/3/1
     */
      @Override
      public IntelligentFavVO queryEntityById(Integer id){
             IntelligentFav  entity= this.getById(id);
             IntelligentFavVO entityVO = IntelligentFavConvert.INSTANCE.entityToVO(entity);
             entityVO.setAgentVo(intelligentAgentService.queryInfoById(entityVO.getAgentId()));
            return entityVO;
      }

    /**
     * 分页查询智能体收藏列表
     * @Param:@param dto
     * @return:List<com.zhongjia.infrastructure.db.entity.IntelligentFav>
     * @Author: wZhic
     * @Date:2024/3/1
     */
    @Override
    public IPage<IntelligentFavVO> queryListEntityPage(IntelligentFavDTO dto){
        LambdaQueryWrapper<IntelligentFav> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(IntelligentFav::getCreateBy, CurrentUserUtil.getUserId());
        wrapper.orderByDesc(IntelligentFav::getCreateTime);
        IPage<IntelligentFav> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<IntelligentFav> iPage = this.page(userPage, wrapper);
        IPage<IntelligentFavVO> convert = iPage.convert(po -> IntelligentFavConvert.INSTANCE.entityToVO(po));
        if(CollUtil.isNotEmpty(convert.getRecords())){
            Map<Integer, IntelligentAgent> agentMap = intelligentAgentService
                    .queryListByIds(convert.getRecords().stream().map(IntelligentFavVO::getAgentId).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(IntelligentAgent::getId, Function.identity()));
            convert.getRecords()
                    .forEach(row->row.setAgentVo(IntelligentAgentConvert.INSTANCE.entityToVO(agentMap.get(row.getAgentId()))));
        }
        return convert;
    }

    /**
     * 查询单条智能体收藏
     * @Param:@param dto
     * @return:List<com.zhongjia.infrastructure.db.entity.IntelligentFav>
     * @Author: wZhic
     * @Date:2024/3/1
     */
    @Override
    public IntelligentFavVO queryEntityByDto(IntelligentFavDTO dto){
        LambdaQueryWrapper<IntelligentFav> wrapper=new LambdaQueryWrapper<>();
        return IntelligentFavConvert.INSTANCE.entityToVO(this.getOne(wrapper));
    }

    /**
     * 更新智能体收藏
     * @Param:@param record example
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/3/1
     */
    @Override
    public boolean updateEntity(IntelligentFavDTO record){
        IntelligentFav entity =  IntelligentFavConvert.INSTANCE.dtoToEntity(record);
        return this.updateById(entity);
    }

    /**
     * 插入智能体收藏
     * @Param:@param record
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/3/1
     */
    @Override
    public boolean insertEntity(IntelligentFavDTO record){
        LambdaQueryWrapper<IntelligentFav> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IntelligentFav::getAgentId, record.getAgentId());
        wrapper.eq(IntelligentFav::getCreateBy, CurrentUserUtil.getUserId());
        List<IntelligentFav> entityList = this.list(wrapper);
        if(entityList.size()>0){
            record.setId(entityList.get(0).getId());
            return true;
        }
        IntelligentFav entity =  IntelligentFavConvert.INSTANCE.dtoToEntity(record);
        entity.setCreateBy(CurrentUserUtil.getUserId());
        boolean result = this.save(entity);
        record.setId(entity.getId());
        return result;
    }

    /**
     * 删除智能体收藏
     * @Param:@param example
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/3/1
     */
    @Override
    public boolean deleteById(Integer id){
        IntelligentFav entity = this.getById(id);
        if(ObjectUtil.isEmpty(entity) || !Objects.equals(CurrentUserUtil.getUserId(), entity.getCreateBy())){
            return false;
        }
        return this.removeById(id);
    }

}