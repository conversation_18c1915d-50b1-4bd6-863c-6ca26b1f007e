package com.hncboy.chatgpt.front.framework.util;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description:
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2022/3/14
 */
@Slf4j
@Component
public class AlyOssUtils {


    //空间
    private static String bucketName = "alwzcoss";

    //文件存储目录
    private static String filedir = "mjImage/";


    public static String imageUploadToLoacl(String imgUrl) {
        if (StringUtils.isEmpty(imgUrl)) {
            return null;
        }
        //new一个URL对象
        URL url = null;
        try {
//            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 50482));
            url = new URL(imgUrl);
            //打开链接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置请求方式为"GET"
            conn.setRequestMethod("GET");
            //超时响应时间为5秒
            conn.setConnectTimeout(50 * 1000);
            //通过输入流获取图片数据
            InputStream inStream = conn.getInputStream();
            String substring = ".png";
            Random random = new Random();
            String name = random.nextInt(10000) + System.currentTimeMillis() + substring;
            imgUrl = uploadFileOSSWithLength(inStream, name, filedir, conn.getContentLengthLong());
            inStream.close();
            conn.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return imgUrl;
    }


    private static String uploadFileOSSWithLength(InputStream instream, String fileName,
                                                  String path, Long fileLength) {
        String ret = "";
        try {
            //创建上传Object的Metadata
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(instream.available());
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setHeader("Pragma", "no-cache");
            objectMetadata.setContentDisposition("inline;filename=" + fileName);
            objectMetadata.setContentLength(fileLength);
            //上传文件
            OSSClient ossClient = new OSSClient("oss-cn-beijing.aliyuncs.com", "LTAIwXinkXGEynVc",
                    "vWB5YSXEu1auWM2X8oPKFhWh1fqPiX");
            PutObjectResult putResult = ossClient.putObject(bucketName, path + fileName, instream,
                    objectMetadata);
            ret = getImgUrl(fileName, ossClient);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (instream != null) {
                    instream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return ret;
    }

    public static String uploadFileBase64(byte[] bytes, String fileName) {
        String result = "";
        try {
            //转化为输入流
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            OSSClient ossClient = new OSSClient("oss-cn-beijing.aliyuncs.com", "LTAIwXinkXGEynVc",
                    "vWB5YSXEu1auWM2X8oPKFhWh1fqPiX");
            result = uploadFile2OSS(inputStream, fileName, ossClient);
        } catch (Exception e) {
            log.error("图片上传失败{}", e.getMessage());
            //上传失败
        }
        return result;
    }

    public static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        //创建一个Buffer字符串
        byte[] buffer = new byte[1024];
        //每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len = 0;
        //使用一个输入流从buffer里把数据读取出来
        while ((len = inStream.read(buffer)) != -1) {
            //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outStream.write(buffer, 0, len);
        }
        //关闭输入流
        inStream.close();
        //把outStream里的数据写入内存
        return outStream.toByteArray();
    }

    public static String ossUpload(MultipartFile file) {
        OSSClient ossClient = null;
        String imgUrl = null;
        try {
            ossClient = new OSSClient("oss-cn-beijing.aliyuncs.com", "LTAIwXinkXGEynVc",
                    "vWB5YSXEu1auWM2X8oPKFhWh1fqPiX");
            String name = uploadImg2Oss(file, ossClient);
            imgUrl = getImgUrl(name, ossClient);

        } catch (Exception e) {
            log.error("图片上传失败{}", e.getMessage());
        } finally {
            ossClient.shutdown();
        }
        return imgUrl;
    }


    public static String delFile(String file) {
        OSSClient ossClient = null;
        String imgUrl = null;
        try {
            ossClient = new OSSClient("oss-cn-beijing.aliyuncs.com", "LTAIwXinkXGEynVc",
                    "vWB5YSXEu1auWM2X8oPKFhWh1fqPiX");
            deleteObject(file, ossClient);
        } catch (Exception e) {
            log.error("图片删除失败{}", e.getMessage());
        } finally {
            ossClient.shutdown();
        }
        return imgUrl;
    }


    public static String uploadImg2Oss(MultipartFile file, OSSClient ossClient) {
        String originalFilename = StringUtils.isEmpty(file.getOriginalFilename()) ? file.getName()
                : file.getOriginalFilename();
        String substring = "";
        if (!StringUtils.isEmpty(originalFilename)) {
            substring = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
        }
        Random random = new Random();
        String name = random.nextInt(10000) + System.currentTimeMillis() + substring;
        try {
            InputStream inputStream = file.getInputStream();
            uploadFile2OSS(inputStream, name, ossClient);
            return name;
        } catch (Exception e) {
            e.getMessage();
        }
        return null;
    }


    /**
     * 删除文件
     *
     * @param fileName
     * @param ossClient
     */
    public static void deleteObject(String fileName, OSSClient ossClient) {
        String suffixes = "jpeg|jpg|png|gif";
        Pattern pat = Pattern.compile("[\\w]+[\\.](" + suffixes + ")");//正则判断
        Matcher mc = pat.matcher(fileName);//条件匹配
        while (mc.find()) {
            fileName = mc.group();//截取文件名后缀名
        }
        ossClient.deleteObject(bucketName, filedir + fileName);
    }


    /**
     * 上传到OSS服务器  如果同名文件会覆盖服务器上的
     *
     * @param instream 文件流
     * @param fileName 文件名称 包括后缀名
     * @return 出错返回"" ,唯一MD5数字签名
     */
    public static String uploadFile2OSS(InputStream instream, String fileName,
                                        OSSClient ossClient) {
        String ret = "";
        try {
            //创建上传Object的Metadata
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(instream.available());
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setHeader("Pragma", "no-cache");
            objectMetadata.setContentType(
                    getcontentType(fileName.substring(fileName.lastIndexOf("."))));
            objectMetadata.setContentDisposition("inline;filename=" + fileName);
            //上传文件
            PutObjectResult putResult = ossClient.putObject(bucketName, filedir + fileName,
                    instream, objectMetadata);
            ret = putResult.getETag();
        } catch (IOException e) {
            e.getMessage();
        } finally {
            try {
                if (instream != null) {
                    instream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return ret;
    }

    /**
     * Description: 判断OSS服务文件上传时文件的contentType
     *
     * @param FilenameExtension 文件后缀
     * @return String
     */
    public static String getcontentType(String FilenameExtension) {
        if (FilenameExtension.equalsIgnoreCase(".bmp")) {
            return "image/bmp";
        }
        if (FilenameExtension.equalsIgnoreCase(".gif")) {
            return "image/gif";
        }
        if (FilenameExtension.equalsIgnoreCase(".jpeg") || FilenameExtension.equalsIgnoreCase(
                ".jpg") || FilenameExtension.equalsIgnoreCase(".png")) {
            return "image/jpeg";
        }
        if (FilenameExtension.equalsIgnoreCase(".html")) {
            return "text/html";
        }
        if (FilenameExtension.equalsIgnoreCase(".txt")) {
            return "text/plain";
        }
        if (FilenameExtension.equalsIgnoreCase(".vsd")) {
            return "application/vnd.visio";
        }
        if (FilenameExtension.equalsIgnoreCase(".pptx") || FilenameExtension.equalsIgnoreCase(
                ".ppt")) {
            return "application/vnd.ms-powerpoint";
        }
        if (FilenameExtension.equalsIgnoreCase(".docx") || FilenameExtension.equalsIgnoreCase(
                ".doc")) {
            return "application/msword";
        }
        if (FilenameExtension.equalsIgnoreCase(".xml")) {
            return "text/xml";
        }
        if (FilenameExtension.equalsIgnoreCase(".zip")) {
            return "application/zip";
        }
        if (FilenameExtension.equalsIgnoreCase(".tar")) {
            return "application/x-tar";
        }
        if (FilenameExtension.equalsIgnoreCase(".avi")) {
            return "video/avi";
        }
        if (FilenameExtension.equalsIgnoreCase(".mp4")) {
            return "video/mpeg4";
        }
        if (FilenameExtension.equalsIgnoreCase(".mp3")) {
            return "audio/mp3";
        }
        if (FilenameExtension.equalsIgnoreCase(".mp2")) {
            return "audio/mp2";
        }
        return "image/jpeg";
    }


    /**
     * 获得图片路径
     *
     * @param fileUrl
     * @return
     */
    public static String getImgUrl(String fileUrl, OSSClient ossClient) {
        if (!StringUtils.isEmpty(fileUrl)) {
            String[] split = fileUrl.split("/");
            return getUrl(filedir + split[split.length - 1], ossClient);
        }
        return null;
    }


    /**
     * 获得url链接
     *
     * @param key
     * @return
     */
    public static String getUrl(String key, OSSClient ossClient) {
        // 设置URL过期时间为10年  3600l* 1000*24*365*10
        Date expiration = new Date(System.currentTimeMillis() + 3600L * 1000 * 24 * 365 * 10);
        // 生成URL
        URL url = ossClient.generatePresignedUrl(bucketName, key, expiration);
        if (url != null) {
            return url.toString().replaceAll("http://", "https://");
        }
        return null;
    }

}
