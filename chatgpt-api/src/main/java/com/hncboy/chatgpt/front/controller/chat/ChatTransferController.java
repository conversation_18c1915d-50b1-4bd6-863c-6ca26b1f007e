package com.hncboy.chatgpt.front.controller.chat;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.annotation.IgnoreAuth;
import com.hncboy.chatgpt.front.framework.domain.entity.DrawRoomDO;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessV2Request;
import com.hncboy.chatgpt.front.framework.domain.vo.DrawRoomVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.helper.ChatMsgV2BuildHelper;
import com.hncboy.chatgpt.front.helper.HttpUrlTransferHelper;
import com.hncboy.chatgpt.front.helper.QiNuOssHelper;
import com.hncboy.chatgpt.front.service.DrawRoomService;
import com.hncboy.chatgpt.front.service.TranslateGeneralService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@FrontPreAuth
@AllArgsConstructor
@Tag(name = "聊天转接接口")
@RestController
@RequestMapping("/chat/v2")
@Slf4j
public class ChatTransferController {

    private final ChatMsgV2BuildHelper chatMsgV2BuildHelper;
    private final HttpUrlTransferHelper httpUrlTransferHelper;
    private final DrawRoomService drawRoomService;

    @IgnoreAuth
    @Operation(summary = "构建http流式中转请求")
    @PostMapping("/buildStreamingRequests")
    public ResponseBodyEmitter buildStreamingRequests(@RequestBody ChatProcessV2Request chatProcessRequest,
                                                      HttpServletResponse response) {
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE+";charset=UTF-8");
        return chatMsgV2BuildHelper.buildMessageBody(chatProcessRequest);
    }

    @IgnoreAuth
    @Operation(summary = "探测通道状态")
    @GetMapping("/detectChannelStatus")
    public R<String> detectChannelStatus() {
        return R.data(chatMsgV2BuildHelper.detectChannelStatus());
    }


    @Operation(summary = "文件上传")
    @PostMapping("/upload")
    @ResponseBody
    public Object uploadFile(@RequestParam("file") MultipartFile file) throws IOException {
        String qnyUrl = QiNuOssHelper.qnyUpload(file);
        String fileName = file.getOriginalFilename();
        File tmpFile = new File(String.format("%s%s%s%s%s", FileUtil.getUserHomePath()
                , FileUtil.FILE_SEPARATOR, "uploadcg"
                , FileUtil.FILE_SEPARATOR, fileName));
        FileUtil.mkParentDirs(tmpFile);
        file.transferTo(tmpFile);
        FileUtil.del(tmpFile);
        String res = "{\"param\":\"\",\"code\":\"\"}";
        JSONObject jsonObject = JSONUtil.parseObj(res);
        jsonObject.set("preview_url", qnyUrl);
        jsonObject.set("url", qnyUrl);
        jsonObject.set("filename", fileName);
        return JSONUtil.toJsonStr(jsonObject);
    }


    @Operation(summary = "MJ绘画请求中转")
    @PostMapping("/mj/transfer")
    @ResponseBody
    public Object mjTransfer(@RequestBody JSONObject jsonObject) {
        if (!jsonObject.containsKey("url")) {
            return R.fail("url不能为空");
        }
        return httpUrlTransferHelper.mjHttpTransFer(jsonObject);
    }


    @GetMapping("/queryMJRes/{requestId}")
    @Operation(summary = "查询绘画结果")
    public R<String> queryMJRes(@PathVariable("requestId") String requestId){
        return R.data(httpUrlTransferHelper.queryMJRes(requestId));
    }


    @Operation(summary = "gpt语音等请求中转")
    @PostMapping("/gpt/transfer")
    @ResponseBody
    public Object gptHttpTransFer(@RequestBody JSONObject jsonObject) {
        if (!jsonObject.containsKey("url")) {
            return R.fail("url不能为空");
        }
        return httpUrlTransferHelper.gptHttpTransFer(jsonObject);
    }


    @Operation(summary = "创建聊天室")
    @PostMapping("/createDrawRoom")
    public R<DrawRoomVO> createDrawRoom(@RequestBody DrawRoomDO drawRoomDO) {
        return R.data(drawRoomService.createDrawRoom(drawRoomDO));
    }

    @Operation(summary = "查询用户聊天室列表")
    @GetMapping("/getDrawRoom")
    public R<List<DrawRoomVO>> getDrawRoom(@RequestParam("ids") String idsString) {
        return R.data(drawRoomService.getDrawRoom(idsString));
    }

    @Operation(summary = "更新聊天室")
    @PostMapping("/updateDrawRoom")
    public R<Object> updateDrawRoom(@RequestBody DrawRoomDO drawRoomDO) {
        return R.data(drawRoomService.updateDrawRoom(drawRoomDO));
    }

    @Operation(summary = "删除房间")
    @GetMapping("deleteRoom/{roomId}")
    public R deleteRoom(@PathVariable("roomId") Integer roomId) {
        return R.data(drawRoomService.removeById(roomId));
    }

    /**
     * 机器翻译测试
     * @param sourceText
     * @return
     */
    private  final TranslateGeneralService translateGeneralService;
    @Operation(summary = "机器翻译")
    @GetMapping("/translate")
    public R translateGeneral(String sourceText) throws Exception {
        return R.data(translateGeneralService.alTranslate(sourceText));
    }

    @IgnoreAuth
    @Operation(summary = "构建http流式中转请求-文本生成")
    @PostMapping("/buildStreamingCompletion")
    public ResponseBodyEmitter buildStreamingCompletion(@RequestBody ChatProcessV2Request chatProcessRequest,
                                                      HttpServletResponse response) {
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        return chatMsgV2BuildHelper.buildCompletionMessageBody(chatProcessRequest);
    }

    /**
     * 获取应用消耗点数与用户当前点数情况
     *
     * @return {"userConsume":用户余额,"writeConsume":当前应用消耗点数,"status":若余额足够0，不够-1}
     * @Author: zd.zhong
     * @Date: 2024/7/3
     */
    @IgnoreAuth
    @Operation(summary = "获取应用消耗点数与用户当前点数情况")
    @PostMapping("/writeConsume")
    public R writeConsume(@RequestBody ChatProcessV2Request wirteProcessRequest) {
        return R.data(chatMsgV2BuildHelper.getWriteBalanceAndConsume(wirteProcessRequest));
    }

}
