package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.dto.WriteAgentDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.WriteAgent;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteAgentVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【write_agent(写作应用)】的数据库操作Service
* @createDate 2024-06-27 21:33:40
*/
public interface WriteAgentService extends IService<WriteAgent> {

    /**
     * 查询智能体信息列表
     *
     * @Param: dto
     * @return: List<IntelligentAgentVO>
     * @Author: zd.zhong
     * @Date: 024/7/3
     */
    List<WriteAgentVO> queryListEntity(WriteAgentDTO dto);

    /**
     * 分页查询智能体信息列表
     *
     * @Param: example
     * @return: IPage<IntelligentAgentVO>
     * @Author: zd.zhong
     * @Date: 2024/7/3
     */
    IPage<WriteAgentVO> queryListEntityPage(WriteAgentDTO dto);

    /**
     * 查询写作应用分类列表
     *
     * @return List<String>
     * @Author: zzd
     * @Date: 2024/7/3
     */
    List<String> queryTagList();

    /**
     * 查询写作应用信息
     *
     * @return WriteAgentVO
     * @Author: zzd
     * @Date: 2024/7/2
     */
    WriteAgentVO getWriteAgentInfoById(Integer id);

    /**
     * 更新写作应用使用次数
     *
     * @param id
     * @return void
     * @Author: zzd
     * @Date: 2024/7/3
     */
    void addUseCnt(Integer id);
}
