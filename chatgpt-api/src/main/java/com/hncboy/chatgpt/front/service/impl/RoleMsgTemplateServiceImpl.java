package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.RoleMsgTemplate;
import com.hncboy.chatgpt.front.mapper.RoleMsgTemplateMapper;
import com.hncboy.chatgpt.front.service.RoleMsgTemplateService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色消息模版实现
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/8/2
 */
@Service
public class RoleMsgTemplateServiceImpl extends ServiceImpl<RoleMsgTemplateMapper, RoleMsgTemplate> implements RoleMsgTemplateService {


    /**
     * 根据配置查询模版
     *
     * @param configId
     * @return List<RoleMsgTemplate>
     * @Author: zc.wu
     * @Date: 2023/8/2 0002 下午 05:53
     */
    @Override
    public List<RoleMsgTemplate> queryRoleMsgTemplateList(Integer configId) {
        QueryWrapper<RoleMsgTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("config_id", configId);
        return this.list(queryWrapper);
    }

}
