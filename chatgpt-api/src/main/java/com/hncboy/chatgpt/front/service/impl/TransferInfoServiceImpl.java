package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

import com.hncboy.chatgpt.front.framework.domain.entity.TransferInfo;
import com.hncboy.chatgpt.front.mapper.TransferInfoMapper;
import com.hncboy.chatgpt.front.service.ITransferInfoService;
import org.springframework.stereotype.Service;

/**
 * 提现申请信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
public class TransferInfoServiceImpl extends ServiceImpl<TransferInfoMapper, TransferInfo> implements ITransferInfoService {
    
    /**
     * 查询提现申请信息
     *
     * @param id 提现申请信息主键
     * @return 提现申请信息
     */
    @Override
    public TransferInfo selectTransferInfoById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询提现申请信息列表
     *
     * @param transferInfo 提现申请信息
     * @return 提现申请信息
     */
    @Override
    public List<TransferInfo> selectTransferInfoList(TransferInfo transferInfo) {
        return baseMapper.selectList(null);
    }

    /**
     * 新增提现申请信息
     *
     * @param transferInfo 提现申请信息
     * @return 结果
     */
    @Override
    public int insertTransferInfo(TransferInfo transferInfo) {
            return baseMapper.insert(transferInfo);
    }

    /**
     * 修改提现申请信息
     *
     * @param transferInfo 提现申请信息
     * @return 结果
     */
    @Override
    public int updateTransferInfo(TransferInfo transferInfo) {
        transferInfo.setUpdateTime(new Date());
        return baseMapper.updateById(transferInfo);
    }


    /**
     * 删除提现申请信息信息
     *
     * @param id 提现申请信息主键
     * @return 结果
     */
    @Override
    public int deleteTransferInfoById(Long id) {
       
        return baseMapper.deleteById(id);
    }




}