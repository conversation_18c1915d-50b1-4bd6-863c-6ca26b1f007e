package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通道配置 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("channel_config")
public class ChannelConfig implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    private Integer id;


    /**
     * 模型GID
     */
    private String modelGid;


    /**
     * 站点ID
     */
    private Integer siteId;


    /**
     * 站点名称
     */
    private String siteName;


    /**
     * 状态0启用1禁用
     */
    private Integer status;


    /**
     * 优先级 0-100
     */
    private Integer priority;


    /**
     * 失败次数
     */
    private Integer failedTimes;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    /**
     * 备注
     */
    private String remark;

    /**
     * API KEY名称
     */
    private String apiKeyName;


}
