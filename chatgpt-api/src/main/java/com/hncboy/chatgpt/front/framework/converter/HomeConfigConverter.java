package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.entity.HomeConfig;
import com.hncboy.chatgpt.front.framework.domain.vo.HomeConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Description:
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2023/4/12
 */
@Mapper
public interface HomeConfigConverter {

    HomeConfigConverter INSTANCE = Mappers.getMapper(HomeConfigConverter.class);


    List<HomeConfigVO> entityListToVOList(List<HomeConfig> entityList);

}
