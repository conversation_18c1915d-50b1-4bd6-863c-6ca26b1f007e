package com.hncboy.chatgpt.front.framework.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-3-27
 * 聊天室展示参数
 */
@Data
@Schema(title = "聊天室展示参数")
public class ChatRoomVO {

    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 用户 id
     */
    private String openId;


    /**
     * ip
     */
    private String ip;


    /**
     * 房间图片
     */
    private String imageUrl;


    /**
     * 配置ID
     */
    private Integer roleId;


    /**
     * 房间名称
     */
    private String title;


    /**
     * 是否公开
     */
    private String open;


    /**
     * 系统回答
     */
    private String inputExample;


    /**
     * 房间简介
     */
    private String description;

    /**
     * 会话 ID
     */
    private String conversationId;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(title = "智能体信息")
    private IntelligentAgentVO agentVo;

}
