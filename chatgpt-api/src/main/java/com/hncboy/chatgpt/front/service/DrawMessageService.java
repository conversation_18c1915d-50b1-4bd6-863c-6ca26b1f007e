package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.entity.DrawMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/20 16:30 绘图记录相关业务接口
 */
public interface DrawMessageService extends IService<DrawMessage> {

    /**
     * 初始化绘图消息
     *
     * @param drawMessage 消息处理请求参数
     * @return 聊天消息
     */
    DrawMessage initDrawMessage(DrawMessage drawMessage);

    /**
     * 查询绘图结果
     *
     * @param openId
     * @return DrawMessage
     * @Author: zzd
     * @Date: 2024/3/20 10:21
     */
    List<DrawMessage> queryDrawMessage(String openId);


}
