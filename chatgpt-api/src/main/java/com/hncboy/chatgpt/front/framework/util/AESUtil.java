package com.hncboy.chatgpt.front.framework.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

public class AESUtil {

    private static final String ALGORITHM = "AES";
    private static final int KEY_SIZE = 256; // AES-256


    public static SecretKeySpec getKeyFromPassword(String password) throws NoSuchAlgorithmException {
        // 假设我们使用AES-256，因此需要256位/32字节的密钥
        byte[] keyBytes = new byte[32];
        byte[] passwordBytes = password.getBytes();

        // 使用SHA-256哈希算法生成密码的哈希值
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] hash = md.digest(passwordBytes);

        // 如果哈希值比所需密钥长，则截断；如果短，则填充（这里仅截断）
        System.arraycopy(hash, 0, keyBytes, 0, Math.min(hash.length, keyBytes.length));

        // 创建SecretKeySpec
        return new SecretKeySpec(keyBytes, "AES");
    }

    // 生成AES密钥
    public static SecretKey generateKey() throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
        keyGenerator.init(KEY_SIZE, new SecureRandom());
        return keyGenerator.generateKey();
    }

    public static byte[] getKeyBytes(SecretKey key) throws Exception {
        return key.getEncoded();
    }

    // 加密
    public static String encrypt(String data,  SecretKeySpec secretKeySpec) throws Exception {
        //SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM);

        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] encrypted = cipher.doFinal(data.getBytes());
        return Base64.getEncoder().encodeToString(encrypted);
    }

    // 解密
    public static String decrypt(String encryptedData, SecretKeySpec secretKeySpec) throws Exception {
        //SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM);

        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
        byte[] original = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(original);
    }


    public static String generateSecureRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder result = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(chars.length());
            result.append(chars.charAt(index));
        }
        return result.toString();
    }


    // 主函数，用于演示
    public static void main(String[] args) throws Exception {

        SecretKeySpec keyFromPassword = getKeyFromPassword("1234567890123456");
        System.out.println("keyFromPassword: " + keyFromPassword.getEncoded());
        /*SecretKey key = generateKey();
        byte[] keyBytes = getKeyBytes(key);
        String originalText = "Hello, World!";
        String encryptedText = encrypt(originalText, keyBytes);
        String decryptedText = decrypt(encryptedText, keyBytes);

        System.out.println("Original: " + originalText);
        System.out.println("Encrypted: " + encryptedText);
        System.out.println("Decrypted: " + decryptedText);*/
    }

}
