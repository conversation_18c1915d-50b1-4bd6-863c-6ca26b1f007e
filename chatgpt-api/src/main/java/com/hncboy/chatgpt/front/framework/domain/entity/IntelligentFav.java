package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能体收藏 ENTITY
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("intelligent_fav")
public class IntelligentFav implements Serializable{

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 智能体ID
     */
    private Integer agentId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    }
