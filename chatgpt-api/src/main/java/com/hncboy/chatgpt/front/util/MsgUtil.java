package com.hncboy.chatgpt.front.util;


import com.alibaba.fastjson.JSONObject;
import com.bcloud.msg.http.HttpSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.URI;
import org.apache.commons.httpclient.methods.PostMethod;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class MsgUtil {
    private static String[] HexCode = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

    public static Boolean sendMsgDefault( String mobiles,String content){
        return sendMsg(mobiles,"【方顶】验证码："+content+"，有效期5分钟。感谢您的支持！如非本人操作，请忽略。");
    }
    public static Boolean sendMsg( String mobiles,String content){
        String uri = "http://47.99.70.9/msg/HttpBatchSendSM";//应用地址
        String account = "ydxx001";//账号
        String pswd = "ydxx001@123";//密码
        boolean needstatus = true;//是否需要状态报告，需要true，不需要false
        String product = "";//产品ID
        String extno = "";//扩展码
        String respType = "json";//返回json格式响应
        boolean encrypt = true;// 密码使用时间戳加密
        try {
            String returnString = HttpSender.send(uri, account, pswd, mobiles, content, needstatus, product, extno, respType, encrypt);
            System.out.println(returnString);
            // 处理返回值,参见HTTP协议文档
            JSONObject json = JSONObject.parseObject(returnString);
            String result = json.getObject("result", String.class);
            if("0".equals(result)){
                System.out.println("发送成功");
                return true;
            }else{
                System.out.println("发送失败");
                return false;
            }
        } catch (Exception e) {
            log.error("短信发送失败", e);
            //e.printStackTrace();
            return false;
        }
    }

    public static void sendParams( String params,String content){
        String uri = "http://47.99.70.9/msg/HttpVarSM";//应用地址
        String account = "ydxx001";//账号
        String pswd = "ydxx001@123";//密码
        boolean needstatus = true;//是否需要状态报告，需要true，不需要false
        String product = "";//产品ID
        String extno = "";//扩展码
        String respType = "json";//返回json格式响应
        boolean encrypt = true;// 密码使用时间戳加密
        try {
            String returnString = sendParams(uri, account, pswd, params, content, needstatus, product, extno, respType, encrypt);
            System.out.println(returnString);
            // 处理返回值,参见HTTP协议文档
            JSONObject json = JSONObject.parseObject(returnString);
            String result = json.getObject("result", String.class);
            if("0".equals(result)){
                System.out.println("发送成功");
            }else{
                System.out.println("发送失败");
            }
        } catch (Exception e) {
            log.error("短信发送失败", e);
            //e.printStackTrace();
        }
    }




    private static String sendParams(String uri, String account, String pswd, String params, String msg, boolean needstatus, String product, String extno, String resptype, boolean encrypt) throws Exception {
        HttpClient client = new HttpClient();
        PostMethod method = new PostMethod();
        try {
            method.setURI(new URI(uri, false));
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            String ts = df.format(new Date(System.currentTimeMillis()));
            method.getParams().setParameter("http.method.retry-handler", new DefaultHttpMethodRetryHandler());
            method.getParams().setParameter("http.protocol.content-charset", "UTF-8");
            method.setRequestBody(new NameValuePair[] {
                new NameValuePair("account", account),
                new NameValuePair("params", params),
                new NameValuePair("msg", msg),
                new NameValuePair("needstatus", String.valueOf(needstatus)),
                new NameValuePair("product", product),
                new NameValuePair("extno", extno),
                new NameValuePair("resptype", resptype)
            });
            if (encrypt) {
                method.addParameter(new NameValuePair("ts", ts));
                method.addParameter(new NameValuePair("pswd", getMd5Str(String.valueOf(account) + pswd + ts)));
            } else {
                method.addParameter(new NameValuePair("pswd", pswd));
            }
            int result = client.executeMethod(method);
            if (result == 200) {
                return new String(method.getResponseBody(), "UTF-8");
            }
            throw new Exception("HTTP ERROR Status: " + method.getStatusCode() + ":" + method.getStatusText());
        } finally {

            method.releaseConnection();
        }
    }

    private static String getMd5Str(String password) {
        try {
            return getMd5Str(password.getBytes("UTF-8"));
        }
        catch (UnsupportedEncodingException e) {
            return getMd5Str(password.getBytes());
        }
    }

    private static String getMd5Str(byte[] data) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        }
        catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage());
        }
        md.update(data);
        return byteArrayToHexString(md.digest());
    }
    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n += 256;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return String.valueOf(HexCode[d1]) + HexCode[d2];
    }

    private static String byteArrayToHexString(byte[] b) {
        StringBuffer result = new StringBuffer();
        int i = 0;
        while (i < b.length) {
            result = result.append(byteToHexString(b[i]));
            ++i;
        }
        return result.toString();
    }

}
