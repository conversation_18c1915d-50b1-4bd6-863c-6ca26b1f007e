package com.hncboy.chatgpt.front.framework.util;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hncboy.chatgpt.front.framework.constant.OrderConstant;
import com.hncboy.chatgpt.front.framework.domain.dto.ProductPackageDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.*;
import com.hncboy.chatgpt.front.framework.domain.vo.PayOutComeVo;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.mapper.UserPointsLogMapper;
import com.hncboy.chatgpt.front.service.SysConfigService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.front.service.UserPointsLogService;
import lombok.RequiredArgsConstructor;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.*;
import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.TYPE_DRAW;

/**
 * 支付工具类
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PayUtil {

    @Autowired
    private  UserBaseInfoService userBaseInfoService;
    @Autowired
    private UserPointsLogService userPointsLogService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private UserPointsLogMapper userPointsLogMapper;

        /**
     * 充值处理
     * @param product
     * @param orders
     */
    public void chargeUp(Product product, Orders orders) {
        if (StringUtils.isNotEmpty(product.getPackageInfo())) {
            packageChargeUp(product.getPackageInfo(), orders);
        } else {
            chargeUp(product.getType(), orders);
        }
    }
    
    /**
     * 促销充值处理
     * @param packageInfo
     * @param orders
     */
    public void packageChargeUp(String packageInfo, Orders orders) {
        if (!JSONUtil.isTypeJSON(packageInfo)) {
            throw new ServiceException("产品配置错误");
        }
        ProductPackageDTO productPackageDTO = JSONUtil.toBean(packageInfo, ProductPackageDTO.class);
        List<Product> detail = productPackageDTO.getDetail();
        detail.forEach(product -> {
            //从产品中补充单位信息
            orders.setUnit(product.getUnit());
            orders.setNum(product.getNum());
            //充值处理
            chargeUp(product.getType(), orders);
        });

    }

    /**
     * 充值处理
     * @param type
     * @param orders
     */
    public void chargeUp(String type, Orders orders) {
        if (TYPE_CHAT.equals(type)) {
            //添加会员日期
            userBaseInfoService.updateVipTime(orders.getUserId(), orders.getUnit(), orders.getNum());
        } else if (TYPE_MUSIC.equals(type)) {
            //添加音乐次数
            userBaseInfoService.addUserMusicNumMp(orders.getUserId(), orders.getNum().intValue());
        } else if (TYPE_WRITE.equals(type)) {
            //添加写作次数
            userBaseInfoService.addUserWriteNumMp(orders.getUserId(), orders.getNum().intValue());
        } else if (TYPE_DRAW.equals(type)){
            //添加绘图次数
            userBaseInfoService.addUserDrawNumMp(orders.getUserId(), orders.getNum().intValue());
        } else if (TYPE_TAROT.equals(type)){
            //添加塔罗次数
            userBaseInfoService.addUserTarotNumMp(orders.getUserId(), orders.getNum().intValue());
        } else {
            throw new ServiceException("未知产品类型");
        }
    }

    public void addRechargePoints(Product product, Orders orders) {
        //如果充值产品是塔罗币则不添加积分
        if (TYPE_TAROT.equals(product.getType())) {
            return;
        }

        //查询用户有没有parent_id 邀请人，给邀请人添加积分，并积分记录
        //如果没有邀请人就不产生积分
        final UserBaseInfo user = userBaseInfoService.getUserBaseInfoByUserId(orders.getUserId());
        if (Objects.nonNull(user) && user.getParentId() != null) {
            //用户有邀请人
            UserBaseInfo parentUser = userBaseInfoService.getUserBaseInfoByUserId(user.getParentId());
            if (parentUser != null) {
                // 检查是否已加过积分
                if (userPointsLogService.checkPointsLog(user.getParentId(), orders.getOrdersId()) == 0) {
                    //用户积分记录生成
                    final SysConfig sysConfig = sysConfigService.querySysConfig("charge_points");
                    String config = sysConfig.getConfigValue();
                    //根据配置转换充值积分目前是设置100%
                    Integer points = convertRechargeToPoints(product.getProductPrice(), config);
                    int newPoints;
                    if (parentUser.getPoints() == null) {
                        newPoints = points;
                    } else {
                        newPoints = points + parentUser.getPoints();
                    }
                    //对积分修改
                    //更新用户--邀请人的积分
                    userBaseInfoService.updateUserPoints(user.getParentId(), newPoints);
                    final UserPointsLog userPointsLog = new UserPointsLog().setUserId(user.getParentId())
                            .setRelOrder(orders.getOrdersId()).setPoints(points).setPointsType(sysConfig.getConfigKey())
                            .setRemark(sysConfig.getRemark()).setCreateTime(new Date())
                            .setCreateBy(CurrentUserUtil.getUserId());
                    userPointsLogMapper.insert(userPointsLog);
                } else {
                    log.warn("已产生过积分, 邀请人id[{}]-订单号[{}]", user.getParentId(), orders.getOrdersId());
                }
            }
        }
    }

    /**
     * 根据数据库的配置，充值金额转化积分百分比
     *
     * @param rechargeAmt
     * @param percentageConfig
     * @return
     */
    public static int convertRechargeToPoints(double rechargeAmt, String percentageConfig) {
        if (percentageConfig.endsWith("%")) {
            percentageConfig = percentageConfig.substring(0, percentageConfig.length() - 1);
        }
        try {
            double percentage = Double.parseDouble(percentageConfig) / 100.0;
            return (int) Math.round(rechargeAmt * percentage);
        } catch (Exception e) {
            log.error("充值金额转化积分百分比异常", e);
            String msg = "系统异常,请联系管理员";
            throw new ServiceException(msg);
        }

    }
    
    
}
