package com.hncboy.chatgpt.front.framework.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-06-01 20:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "DrawMessage对象", description = "绘图记录")
public class DrawMessageVO implements Serializable {

    private static final long serialVersionUID = 5249172213472957489L;

    private Integer id;

    @Schema(title = "open_id")
    private String openId;

    @Schema(title = "任务id")
    private String taskId;

    @Schema(title = "绘图室id")
    private String drawRoomId;

    @Schema(title = "提示词")
    private String prompt;

    @Schema(title = "提示词-英文")
    private String promptEn;

    @Schema(title = "任务描述")
    private String description;

    @Schema(title = "自定义参数")
    private String state;

    @Schema(title = "提交时间")
    private LocalDateTime submitTime;

    @Schema(title = "图片地址")
    private String imageUrl;

    @Schema(title = "是否热门")
    private String hot;

    @Schema(title = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @Schema(title = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime finishTime;

    @Schema(title = "任务状态")
    private String status;

    @Schema(title = "任务进度")
    private String progress;

    @Schema(title = "失败原因")
    private String failReason;

    @Schema(title = "创建者")
    private String createBy;

    @Schema(title = "创建时间")
    private LocalDateTime createTime;

    @Schema(title = "更新者")
    private String updateBy;

    @Schema(title = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(title = "用户信息")
    private WxUserInfoVO wxUserInfoVO;
}
