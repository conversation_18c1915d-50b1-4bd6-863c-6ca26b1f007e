package com.hncboy.chatgpt.front.framework.domain.dto;

import java.time.LocalDateTime;
import java.util.Date;

import com.hncboy.chatgpt.front.framework.domain.query.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 智能体收藏 DTO
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/1
 */
@Data
public class IntelligentFavDTO extends PageParam {

  private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "智能体ID")
    @NotNull(message = "智能体ID不能为空")
    private Integer agentId;

    @Schema(title = "创建者")
    private String createBy;

    @Schema(title = "创建时间")
    private Date createTime;


}
