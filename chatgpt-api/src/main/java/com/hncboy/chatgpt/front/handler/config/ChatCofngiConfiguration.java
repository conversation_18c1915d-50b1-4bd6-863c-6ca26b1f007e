package com.hncboy.chatgpt.front.handler.config;

import cn.hutool.core.util.StrUtil;
import com.hncboy.chatgpt.front.framework.config.ChatConfig;
import com.unfbx.chatgpt.OpenAiStreamClient;
import com.unfbx.chatgpt.function.KeyRandomStrategy;
import com.unfbx.chatgpt.interceptor.OpenAILogger;
import lombok.AllArgsConstructor;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * wechat mp configuration
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@AllArgsConstructor
@Configuration
//@EnableConfigurationProperties(ChatConfig.class)
public class ChatCofngiConfiguration {

    private final ChatConfig config;

    @Bean
    public OpenAiStreamClient openAiStreamClient() {
        //本地开发需要配置代理地址
        Proxy proxy = Proxy.NO_PROXY;
        if (StrUtil.isNotBlank(config.getHttpProxyHost())) {
            proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(config.getHttpProxyHost(), config.getHttpProxyPort()));
        }
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor(new OpenAILogger());
        OkHttpClient okHttpClient = new OkHttpClient
                .Builder()
                .proxy(proxy)
                .addInterceptor(httpLoggingInterceptor)
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(600, TimeUnit.SECONDS)
                .readTimeout(600, TimeUnit.SECONDS)
                .build();
        return OpenAiStreamClient
                .builder()
                .apiHost(config.getOpenaiApiBaseUrl())
                .apiKey(Collections.singletonList(config.getOpenaiApiKey()))
                //自定义key使用策略 默认随机策略
                .keyStrategy(new KeyRandomStrategy())
                .okHttpClient(okHttpClient)
                .build();
    }

}
