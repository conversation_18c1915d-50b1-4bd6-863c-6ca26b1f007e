package com.hncboy.chatgpt.front.controller.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.converter.WriteMessageConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.WriteMessageDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.WriteMessage;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteMessageVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.service.WriteAgentService;
import com.hncboy.chatgpt.front.service.WriteMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: zd.zhong
 * @Date: 2024/7/1
 */
@Slf4j
@FrontPreAuth
@AllArgsConstructor
@RestController
@RequestMapping("/writeMessage")
@Tag(name = "写作记录相关")
public class WriteMessageController {

    private final WriteMessageService writeMessageService;
    private final WriteAgentService writeAgentService;

    @Operation(summary = "分页查询写作记录列表")
    @PostMapping("/page/list")
    public R<IPage<WriteMessageVO>> queryListEntityPage(
            @RequestBody WriteMessageDTO dto) {
        return R.data(writeMessageService.queryListEntityPage(dto));
    }


    @Operation(summary = "获取写作记录")
    @GetMapping("/info")
    public R<WriteMessageVO> getWriteMessageInfo(@RequestParam Integer id) {
        WriteMessage writeMessage = writeMessageService.getById(id);
        WriteMessageVO writeMessageVO = new WriteMessageVO();
        if (writeMessage != null) {
            writeMessageVO = WriteMessageConvert.INSTANCE.entityToVO(writeMessage);
            writeMessageVO.setWriteAgent(writeAgentService.getWriteAgentInfoById(writeMessage.getAgentId()));
        }
        return R.data(writeMessageVO);
    }


    @Operation(summary = "删除写作记录")
    @GetMapping("/delete")
    public R<Void> deleteWriteMessage(@RequestParam Integer id) {
        writeMessageService.softDeleteById(id);
        return R.success();
    }
}
