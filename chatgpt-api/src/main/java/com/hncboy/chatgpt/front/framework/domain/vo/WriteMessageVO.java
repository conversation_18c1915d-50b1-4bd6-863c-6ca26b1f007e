package com.hncboy.chatgpt.front.framework.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: zd.zhong
 * @Date: 2024/7/1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "WriteMessage对象", description = "写作记录")
public class WriteMessageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "用户ID")
    private Integer userId;

    @Schema(title = "主题")
    private String topic;

    @Schema(title = "输入")
    private String inputs;

    @Schema(title = "内容")
    private String content;

    @Schema(title = "模型")
    private String model;

    @Schema(title = "应用名称")
    private String agentName;

    @Schema(title = "应用标题")
    private String agentTitle;

    @Schema(title = "应用ID")
    private Integer agentId;

    @Schema(title = "消耗点数")
    private Integer consume;

    @Schema(title = "站点ID")
    private Integer siteId;

    @Schema(title = "站点名称")
    private String siteName;

    @Schema(title = "删除状态")
    private Integer isDelete;

    @Schema(title = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh")
    private Date createTime;

    @Schema(title = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh")
    private Date updateTime;

    @Schema(title = "写作应用")
    private WriteAgentVO writeAgent;

    @Schema(title = "应用图标")
    private String imgUrl;

}
