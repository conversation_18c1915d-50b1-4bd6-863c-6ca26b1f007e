package com.hncboy.chatgpt.front.framework.util;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.hncboy.chatgpt.front.framework.domain.entity.SysOperLog;
import com.hncboy.chatgpt.front.mapper.SysOperLogMapper;
import io.swagger.v3.oas.annotations.Operation;
import org.aspectj.lang.JoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.hncboy.chatgpt.front.handler.config.WebSocketConfig.getIpAddr;

public class WebLogUtil {
    private static Logger log = LoggerFactory.getLogger("WEB_LOG");

    //排除不打印日志的几口
    private static final List<String> excludeList = ListUtil.of(
            "/super/ai/tarot/spread/randomTarot",
            "/super/ai/tarot/meaning/list" ,
            "/super/ai/tarot/spread/list"
    );

    private static ConcurrentHashMap<String,String> methodDescMap = new ConcurrentHashMap<>();


    public static void requestLog(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        //类名
        String className = joinPoint.getTarget().getClass().getName();
        //请求方法
        String methodName = joinPoint.getSignature().getName();
        //请求参数
        Object[] args =joinPoint.getArgs();
        List<Object> argList = new ArrayList<>();
        if (args != null) {
            for (Object item : args) {
                if (item instanceof ServletRequest) {
                    continue;
                }
                if (item instanceof ServletResponse) {
                    continue;
                }
                if (item instanceof MultipartFile){
                    continue;
                }
                argList.add(item);
            }
        }
        String param = JSON.toJSONString(argList);
        //接口描述
        String methodDesc = "";
        try {
            methodDesc = getMethodDesc(className,methodName,args.length);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if(ObjectUtil.isEmpty(attributes)){
            StringBuffer sb = new StringBuffer();
            sb.append("\n");
            sb.append(String.format("****************Common Request Server******************")).append("\n");
            sb.append(String.format("Description    :%s",methodDesc)).append("\n");
            sb.append(String.format("RequestParams  :%s",param)).append("\n");
            if (log.isDebugEnabled()) {
                sb.append(String.format("ClassName      :%s",className)).append("\n");
                sb.append(String.format("RequestMethod  :%s",methodName)).append("\n");
            }
            log.info(sb.toString());
            return;
        }

        //针对get请求
        HttpServletRequest request = attributes.getRequest();
        String decode = "";
        if (request.getQueryString() != null) {
            try {
                decode = URLDecoder.decode(request.getQueryString(),"UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.error("WebLogAspect 编码转换错误",e);
            }
        } else {
            Map<String,String[]> formParams = request.getParameterMap();
            if (formParams != null) {
                for (String key : formParams.keySet()) {
                    String[] values = formParams.get(key);
                    for (int i = 0; i < values.length; i++) {
                        String value = values[i];
                        decode += key + "=" + value + "&";
                    }
                }
            }
        }
        Map<String,Object> methodParamMap = transStringToMap(decode,"&","=");
        StringBuffer sb = new StringBuffer();
        sb.append("\n");
        sb.append(String.format("****************Http Request Server******************")).append("\n");
        sb.append(String.format("Description    :%s",methodDesc)).append("\n");
        sb.append(String.format("RequestParams  :%s", StrUtil.isBlank(decode) ? param : methodParamMap)).append("\n");
        sb.append(String.format("RequestUri     :%s", StrUtil.sub(request.getRequestURI(), 0,255))).append("\n");
        if (log.isDebugEnabled()) {
            sb.append(String.format("ClassName      :%s",className)).append("\n");
            sb.append(String.format("RequestMethod  :%s",methodName)).append("\n");
            sb.append(String.format("ContentType    :%s",request.getContentType() == null ? "" : request.getContentType())).append("\n");
            sb.append(String.format("RequestType    :%s",request.getMethod())).append("\n");
            sb.append(String.format("UserAgent      :%s",request.getHeader("User-Agent"))).append("\n");
        }
        log.info(sb.toString());
    }
    
    public static void responseLog(Object ret, long startTime){
        ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        String responseStr = "";
        if (ret instanceof String || ret instanceof Number) {
            responseStr = String.valueOf(ret);
        } else {
            responseStr = JSON.toJSONString(ret);
        }
        //接口描述
        StringBuffer sb = new StringBuffer();
        sb.append("\n");
        String requestUri="";
        if(ObjectUtil.isEmpty(attributes)){
            sb.append(String.format("****************Common Response Server******************")).append("\n");
        }else{
            HttpServletRequest request = attributes.getRequest();
            sb.append(String.format("****************Http Response Server******************")).append("\n");
            requestUri = String.format("RequestUri     :%s", StrUtil.sub(request.getRequestURI(), 0, 255));
            sb.append(requestUri).append("\n");
        }
        //排除响应结果
        if(!excludeList.contains(requestUri)){
            sb.append(String.format("Response       :%s",responseStr)).append("\n");
        }else{
            log.info("超长Response跳过输出！判断依据：{}",excludeList);
        }

        sb.append(String.format("CostTime       :%s",(System.currentTimeMillis()-startTime)+"ms")).append("\n");
        log.info(sb.toString());
    }
    
    public static void throwableLog(Throwable ex, long startTime){
        ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        StringBuffer sb = new StringBuffer();
        sb.append("\n");
        if(ObjectUtil.isEmpty(attributes)){
            sb.append(String.format("****************Common Exception异常******************")).append("\n");
        }else{
            HttpServletRequest request = attributes.getRequest();
            sb.append(String.format("****************Http Exception异常******************")).append("\n");
            sb.append(String.format("RequestUri     :%s",StrUtil.sub(request.getRequestURI(), 0,255))).append("\n");
        }
        sb.append(String.format("CostTime       :%s",(System.currentTimeMillis()-startTime)+"ms")).append("\n");
        sb.append(String.format("Exception      :%s",ex.getClass().getName())).append("\n");
        sb.append(String.format("ExceptionMsg   :%s",ex.getMessage())).append("\n");
        log.error(sb.toString());
    }

    /**
     * 获取接口方法中ApiOperation 的中文描述
     * @param className
     * @param methodName
     * @param argLen
     * @return
     * @throws Exception
     */
    public static String getMethodDesc(String className,String methodName,int argLen) throws Exception{
        String key = className+"."+methodName+"."+argLen;
        if (methodDescMap.containsKey(key)) {
            return methodDescMap.get(key);
        }

        Class targetClass = Class.forName(className);
        Method[] methods = targetClass.getMethods();
        String desc = "";
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                Class[] clazzs = method.getParameterTypes();
                if (clazzs.length == argLen) {
                    Operation apiOperation = method.getAnnotation(Operation.class);
                    if (apiOperation != null) {
                        desc = apiOperation.summary();
                    }
                }
            }
        }

        methodDescMap.put(key,desc);
        return desc;
    }

    /**
     * 将http请求中的参数转换为Map
     * @param mapString key=val&key=val 格式的请求参数
     * @param separator
     * @param pairSeparator
     * @return
     */
    public static Map<String,Object> transStringToMap(String mapString, String separator, String pairSeparator) {
        Map<String,Object> map = new HashMap<>();
        String[] fSplit = mapString.split(separator);

        for (int i = 0; i < fSplit.length; i++) {
            if (fSplit[i] == null || fSplit[i].length() == 0) {
                continue;
            }
            String[] sSplit = fSplit[i].split(pairSeparator);
            String value = fSplit[i].substring(fSplit[i].indexOf('=') + 1,fSplit[i].length());
            map.put(sSplit[0],value);
        }
        return map;
    }



//    public void handleLog(final JoinPoint joinPoint, final Exception e, Object jsonResult, long startTime)
//    {
//        try
//        {
//            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
//            // 获取当前的用户
//            //LoginUser loginUser = SecurityUtils.getLoginUser();
//
//            // *========数据库日志=========*//
//            SysOperLog operLog = new SysOperLog();
//            operLog.setStatus(0);
//            // 请求的地址
//            String ip="";
//            if (attributes != null) {
//                HttpServletRequest request = attributes.getRequest();
//                ip=getIpAddr(request);
//
//                operLog.setOperIp(ip);
//                operLog.setOperUrl(StringUtils.substring(request.getRequestURI(), 0, 255));
//            }
//
//
//            operLog.setOperName("zns");
//
//
//            if (e != null)
//            {
//                operLog.setStatus(1);
//                operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
//            }
//            // 设置方法名称
//            String className = joinPoint.getTarget().getClass().getName();
//            String methodName = joinPoint.getSignature().getName();
//            operLog.setMethod(className + "." + methodName + "()");
//            // 设置请求方式
//            operLog.setRequestMethod(attributes.getRequest().getMethod());
//            // 处理设置注解上的参数
//            //getControllerMethodDescription(joinPoint, controllerLog, operLog, jsonResult);
//            // 设置消耗时间
//            operLog.setCostTime(System.currentTimeMillis() - startTime);
//            // 保存数据库
//            operLogMapper.insertOperlog(operLog);
//            //AsyncManager.me().execute(AsyncFactory.recordOper(operLog));
//            log.info("操作日志记录成功{}",operLog);
//        }
//        catch (Exception exp)
//        {
//            // 记录本地异常日志
//            log.error("异常信息:{}", exp.getMessage());
//            exp.printStackTrace();
//        }
//    }





}
