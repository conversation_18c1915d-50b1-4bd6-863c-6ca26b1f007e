package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/25 16:30 聊天记录相关业务接口
 */
public interface ChatMessageService extends IService<ChatMessageDO> {

    /**
     * 初始化聊天消息
     *
     * @param chatMessageDO
     * @return 聊天消息
     */
    ChatMessageDO initChatMessage(ChatMessageDO chatMessageDO);

    /**
     * 查询对话结果
     *
     * @param openId
     * @return ChatMessageDO
     * @Author: zc.wu
     * @Date: 2023/4/11 10:21
     */
    List<ChatMessageDO> queryChatMessage(String openId);


}
