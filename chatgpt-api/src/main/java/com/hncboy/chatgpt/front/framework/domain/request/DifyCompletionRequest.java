package com.hncboy.chatgpt.front.framework.domain.request;

import com.unfbx.chatgpt.entity.files.File;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * DifyCompletionRequest 类用于封装处理请求的数据
 */
@Data
public class DifyCompletionRequest {

    /**
     * 输入参数
     */
    private Map<String, Object> inputs;

    /**
     * 响应模式
     */
    private String response_mode;

    /**
     * 会话ID
     */
    private String conversation_id;

    /**
     * 用户标识
     */
    private String user;

    /**
     * 文件列表
     */
    private List<File> files;
}
