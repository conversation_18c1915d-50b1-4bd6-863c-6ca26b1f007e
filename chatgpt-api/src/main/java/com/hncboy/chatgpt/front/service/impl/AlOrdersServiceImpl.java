package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.converter.AlOrdersConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.AlOrdersDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.AlOrders;
import com.hncboy.chatgpt.front.framework.domain.vo.AlOrdersVO;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.mapper.AlOrdersMapper;
import com.hncboy.chatgpt.front.service.AlOrdersService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.TYPE_TAROT;


/**
* <AUTHOR>
* @description 针对表【ai_orders(支付宝充值记录)】的数据库操作Service实现
* @createDate 2024-07-04 13:48:06
*/
@Service
public class AlOrdersServiceImpl extends ServiceImpl<AlOrdersMapper, AlOrders>
    implements AlOrdersService {



    /**
     * 分页查询支付宝充值记录列表
     *
     * @param dto
     * @return: IPage<AlOrdersVO>
     * @Author: zyc
     * @Date: 2024/7/4
     */
    @Override
    public IPage<AlOrdersVO> queryListEntityPage(AlOrdersDTO dto) {
        Page<AlOrders> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        LocalDateTime localDateTime = LocalDateTime.now();
        AlOrders alOrders = new AlOrders();
        alOrders.setProductType(dto.getProductType());
        alOrders.setUserId(CurrentUserUtil.getUserId());
        alOrders.setExpiresTime(localDateTime);

        if(TYPE_TAROT.equals(dto.getProductType())){
            alOrders.setState(1);
            //alOrders.set
        }

        IPage<AlOrders> page = baseMapper.page(userPage, alOrders);


       /* LambdaQueryWrapper<AlOrders> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(Objects.nonNull(dto.getProductType()), AlOrders::getProductType, dto.getProductType());
        wrapper.eq(AlOrders::getUserId, CurrentUserUtil.getUserId());
        wrapper.and(w -> w.eq(AlOrders::getState, 1).or(
                wq -> wq.eq(AlOrders::getState, 0).gt(AlOrders::getExpiresTime, localDateTime)
        ));
        wrapper.orderByDesc(AlOrders::getCreatedTime);


        IPage<AlOrders> iPage = this.page(userPage, wrapper);
        IPage<AlOrdersVO> convert = iPage.convert(AlOrdersConvert.INSTANCE::entityToVO);*/
        IPage<AlOrdersVO> convert = page.convert(AlOrdersConvert.INSTANCE::entityToVO);
        return convert;
    }

}




