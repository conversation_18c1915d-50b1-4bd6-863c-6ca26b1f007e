package com.hncboy.chatgpt.front.handler.mp.handler;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hncboy.chatgpt.front.framework.config.ChatConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.*;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.enums.WxAppIdEnum;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.framework.util.DownloadURLFile;
import com.hncboy.chatgpt.front.handler.mp.builder.NewsBuilder;
import com.hncboy.chatgpt.front.handler.mp.builder.TextBuilder;
import com.hncboy.chatgpt.front.helper.OpenAiMsgHelper;
import com.hncboy.chatgpt.front.mapper.RechargeLogMapper;
import com.hncboy.chatgpt.front.mapper.UserPointsLogMapper;
import com.hncboy.chatgpt.front.service.SysConfigService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.front.service.UserPointsLogService;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import com.unfbx.chatgpt.OpenAiClient;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import com.unfbx.chatgpt.entity.chat.Message;
import com.unfbx.chatgpt.entity.images.ImageResponse;
import com.unfbx.chatgpt.interceptor.OpenAILogger;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutNewsMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 文本消息处理
 */
@Component
@Slf4j
public class MsgHandler2 extends AbstractHandler {

    @Resource
    private ChatConfig chatConfig;

    @Autowired
    @Lazy
    private WxMpService wxMpService;

    @Autowired
    private OpenAiMsgHelper openAiMsgHelper;

    private OpenAiClient client;

    @Autowired
    private WxUserInfoService wxUserInfoService;
    @Autowired
    private RechargeLogMapper rechargeLogMapper;
    @Autowired
    private UserBaseInfoService userBaseInfoService;

    @Autowired
    private UserPointsLogService userPointsLogService;
    @Autowired
    private  SysConfigService sysConfigService;
    @Autowired
    private  UserPointsLogMapper userPointsLogMapper;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage, Map<String, Object> context,
                                    WxMpService weixinService, WxSessionManager sessionManager) {
        String msg = wxMessage.getContent();
        /*if (msg.contains("照片") || msg.contains("图片")) {
            CompletableFuture.runAsync(() -> imageMsgHandler(wxMessage));
        }*/
        //默认赠送塔罗币

        String msg1 = "";
        String userOpenId = null;
        try {
            WxMpUser userWxInfo = weixinService.getUserService()
                    .userInfo(wxMessage.getFromUser(), null);
            if (msg.equals("6")) {
                if (userWxInfo != null) {
                    userOpenId = userWxInfo.getOpenId();
                    wxUserInfoService.saveOrUpdateUser(userWxInfo, WxAppIdEnum.tarot.getCode());
                    // 查询是否有相同的union_id 但是不同的openId,且是智能社会公众号用户，且无充值记录，则赠送30塔罗币
                    WxUserInfo openIdUser = wxUserInfoService.getOne(new QueryWrapper<WxUserInfo>()
                            .notIn("open_id", userWxInfo.getOpenId())
                            .eq("union_id", userWxInfo.getUnionId())
                            .eq("app_id", WxAppIdEnum.zns.getCode())
                    );

                    if (openIdUser != null) {
                        //处理增加塔罗币，并返回消息
                        msg1 = handelAddTarotCoins(openIdUser.getOpenId());
                        if(StrUtil.isNotBlank(msg1)){
                            return new TextBuilder().build(msg1, wxMessage, weixinService);
                        }
                    }

                }
            }
        } catch (WxErrorException e) {
            if (e.getError().getErrorCode() == 48001) {
                this.logger.info("该公众号没有获取用户信息权限！");
            } else {
                this.logger.error("消息处理异常：", e);
            }
        }
        //1、客服消息
        WxMpKefuMessage.WxArticle article = new WxMpKefuMessage.WxArticle();
        article.setTitle("\uD83D\uDC81\u200D♀添加客服");
        article.setDescription("关于产品使用的所有问题，点击此链接\uD83D\uDD17扫码添加客服小姐姐，为您提供专业解答与协助");
        String imgUrl = "https://image.zjfdsr.com/qz/uWTo5JGCCf1TD1lFBhaw.jpg";
        article.setPicUrl(imgUrl);
        article.setUrl(imgUrl);
        //发送客服消息
        new NewsBuilder().buildAsync(article, userOpenId, weixinService);

        //2、塔罗师师消息
        WxMpXmlOutNewsMessage.Item item = new WxMpXmlOutNewsMessage.Item();
        item.setTitle("\uD83E\uDDD9\u200D♀\uFE0F添加塔罗师");
        item.setDescription("若对评论区问题有进一步疑问，或有其他占卜问题，请扫码添加我们的专业塔罗师1v1为您解答\uD83D\uDD2E");
        String imgUrl2 = "https://image.zjfdsr.com/qz/GlsYepSdgVT5Cg2PFOeH.jpg";
        item.setPicUrl(imgUrl2);
        item.setUrl(imgUrl2);

        return new NewsBuilder().build(item, wxMessage, weixinService);
    }

    /**
     * 处理添加塔罗币
     * @param openId
     * @return
     */
    public String handelAddTarotCoins(String openId) {
        String msg1="";
        UserBaseInfoVO userBaseInfoByOpenId = userBaseInfoService.getUserBaseInfoByOpenId(openId);
        if (userBaseInfoByOpenId != null) {
            //默认参数
            Integer tarotCoins=30;
            String pointsType="tarot_bestow";
            SysConfig bestow = sysConfigService.querySysConfig(pointsType);
            if(bestow!=null){
                tarotCoins = Integer.parseInt(bestow.getConfigValue());
            }
            //改为查user_points_log 表
            List<UserPointsLog> userPointsLogs = userPointsLogMapper.selectList(new LambdaQueryWrapper<UserPointsLog>()
                    .eq(UserPointsLog::getUserId, userBaseInfoByOpenId.getId())
                    .eq(UserPointsLog::getPointsType, pointsType)
            );
            // 如果存在，则判断是否已经赠送过30塔罗币，如果没有则赠送,并生成充值记录
            if (userPointsLogs.isEmpty()) {
                userBaseInfoService.update(new UpdateWrapper<UserBaseInfo>()
                        .eq("id", userBaseInfoByOpenId.getId())
                        .set("tarot_coins", userBaseInfoByOpenId.getTarotCoins() + tarotCoins));

                //调整为存入 user_points_log 表
                final UserPointsLog userPointsLog = new UserPointsLog()
                        .setUserId(userBaseInfoByOpenId.getId())
                        .setRelOrder(userBaseInfoByOpenId.getAccount())
                        .setPoints(tarotCoins)
                        .setPointsType(pointsType)
                        .setRemark(bestow!=null?bestow.getRemark():"公众号赠币")
                        .setCreateTime(new Date())
                        .setCreateBy(CurrentUserUtil.getUserId());
                try {
                    userPointsLogMapper.insert(userPointsLog);
                } catch (Exception e) {
                    // 处理数据库插入异常
                    this.logger.error("赠送塔罗币时插入充值记录失败", e);
                    msg1 = "不好意思，赠送塔罗币时出现了一点小问题，请稍后再试。";
                    return msg1;
                }
                msg1 = "\uD83C\uDF89\uD83C\uDF8930塔罗币已赠送到您的账户！您可进入平台“充值记录”中查看【星月塔塔平台入口\uD83D\uDD17】\uD83D\uDC49https://zns.zjfdsr.com/tarot";
                return msg1;
            }
        } else {
            this.logger.warn("未找到用户基础信息，无法赠送塔罗币。OpenId: " + openId);
        }
        return msg1;
    }


    @PostConstruct
    public void getClient() {
        //国内访问需要做代理，国外服务器不需要
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 50482));
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor(
                new OpenAILogger());
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .addInterceptor(httpLoggingInterceptor).connectTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(120, TimeUnit.SECONDS).readTimeout(120, TimeUnit.SECONDS).build();
        client = OpenAiClient.builder()
                .apiKey(Arrays.asList(chatConfig.getOpenaiApiKey()))
                .okHttpClient(okHttpClient).build();
    }


    /**
     * 自然语句处理
     *
     * @param wxMpXmlMessage
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/6 13:47
     */
    public void textMsgHandler(WxMpXmlMessage wxMpXmlMessage) {
        StringBuffer content = new StringBuffer();
        //聊天模型：gpt-3.5
        Message message = Message.builder().role(Message.Role.USER)
                .content(wxMpXmlMessage.getContent()).build();
        //构建会话
        List<Message> msg = openAiMsgHelper.queryMsg(wxMpXmlMessage.getFromUser(), message);
        ChatCompletion chatCompletion = ChatCompletion.builder().messages(msg).build();
        ChatCompletionResponse chatCompletionResponse = null;
        try {
            chatCompletionResponse = client.chatCompletion(chatCompletion);
            chatCompletionResponse.getChoices().forEach(e -> {
                System.out.println(e.getMessage());
                content.append(e.getMessage().getContent());
            });
            if (StringUtils.isNotBlank(content.toString())) {
                openAiMsgHelper.addAnswer(wxMpXmlMessage.getFromUser(), content.toString());
            }
        } catch (Exception e) {
            content.append("请求超时啦，请再试试看呢");
            log.error("请求出错", e);
        }
        String res = content.toString();
        System.out.println(res.length());
        if (res.toString().length() > 600) {
            res = res.toString().substring(0, 600);
        }
        WxMpKefuMessage wxMpMassOpenIdsMessage = new WxMpKefuMessage();
        wxMpMassOpenIdsMessage.setToUser(wxMpXmlMessage.getFromUser());
        wxMpMassOpenIdsMessage.setMsgType(WxConsts.MassMsgType.TEXT);
        wxMpMassOpenIdsMessage.setContent(res);
        try {
            boolean send = wxMpService.getKefuService().sendKefuMessage(wxMpMassOpenIdsMessage);
            log.info("发送客服消息结果:{}", send);
        } catch (WxErrorException e) {
            log.error("发送客服消息错误:{}", e.getMessage());
        }
    }

    /**
     * 根据描述找图片
     *
     * @param wxMpXmlMessage
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/6 13:46
     */
    public void imageMsgHandler(WxMpXmlMessage wxMpXmlMessage) {
        ImageResponse imageResponse = client.genImages(wxMpXmlMessage.getContent());
        imageResponse.getData().forEach(image -> {
            String url = image.getUrl();
            try {
                File down = DownloadURLFile.downloadByUrl(url, "D:\\image\\");
                WxMediaUploadResult uploadResult = wxMpService.getMaterialService()
                        .mediaUpload("image", down);
                WxMpKefuMessage wxMpMassOpenIdsMessage = new WxMpKefuMessage();
                wxMpMassOpenIdsMessage.setToUser(wxMpXmlMessage.getFromUser());
                wxMpMassOpenIdsMessage.setMsgType(WxConsts.MassMsgType.IMAGE);
                wxMpMassOpenIdsMessage.setMediaId(uploadResult.getMediaId());
                wxMpService.getKefuService().sendKefuMessage(wxMpMassOpenIdsMessage);
            } catch (WxErrorException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
