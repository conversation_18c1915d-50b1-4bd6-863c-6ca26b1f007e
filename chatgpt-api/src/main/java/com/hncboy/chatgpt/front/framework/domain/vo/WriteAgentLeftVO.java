package com.hncboy.chatgpt.front.framework.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 应用导航栏信息 VO
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/7/3
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WriteAgentLeftVO implements Serializable {

    @Schema(title = "图标链接")
    private String iconUrl;

    @Schema(title = "分类")
    private String tag;

    @Schema(title = "排序")
    private Integer order;

    @Schema(title = "应用列表")
    private List<WriteAgentLeftTagVO> data;
}
