package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.SensitiveWordDO;
import com.hncboy.chatgpt.front.mapper.SensitiveWordMapper;
import com.hncboy.chatgpt.front.service.SensitiveWordService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/3/28 20:47
 * 敏感词业务实现类
 */
@Service("CommonSensitiveWordServiceImpl")
public class SensitiveWordServiceImpl extends ServiceImpl<SensitiveWordMapper, SensitiveWordDO> implements SensitiveWordService {
}
