package com.hncboy.chatgpt.front.framework.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 系统配置
 *
 * <AUTHOR>
 */
@Data
public class SysConfigDTO {

    /**
     * gpt 开关
     */
    @Schema(title = "gpt 开关")
    private String gpt;

    /**
     * gpt 提示消息
     */
    @Schema(title = "gpt 提示消息")
    private String gptTitle;

    /**
     * gpt消耗金币
     */
    @Schema(title = "gpt消耗金币")
    private Integer conversations;


    /**
     * 绘画消耗金币
     */
    @Schema(title = "绘画消耗金币")
    private Integer drawingTimes;


    /**
     * 通知消息
     */
    @Schema(title = "通知消息")
    private String notificationMessage;


    /**
     * 签到积分
     */
    @Schema(title = "签到积分")
    private Integer signMonery;


    /**
     * 首页标题
     */
    @Schema(title = "首页标题")
    private String indexTitle;


    /**
     * 分享标题
     */
    @Schema(title = "分享标题")
    private String shareTitle;


}
