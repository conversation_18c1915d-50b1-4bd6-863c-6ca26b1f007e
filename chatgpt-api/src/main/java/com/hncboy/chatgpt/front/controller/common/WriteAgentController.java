package com.hncboy.chatgpt.front.controller.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.annotation.IgnoreAuth;
import com.hncboy.chatgpt.front.framework.converter.WriteAgentConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.WriteAgentDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.WriteAgent;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteAgentLeftTagVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteAgentLeftVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteAgentVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.mapper.WriteAgentMapper;
import com.hncboy.chatgpt.front.service.WriteAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * zcWu
 */
@Slf4j
@FrontPreAuth
@AllArgsConstructor
@RestController
@RequestMapping("/writeAgent")
@Tag(name = "写作应用相关")
public class WriteAgentController {

    private final WriteAgentService writeAgentService;
    private WriteAgentMapper baseMapper;

    @IgnoreAuth
    @Operation(summary = "分页查询写作应用列表")
    @PostMapping("/page/list")
    public R<IPage<WriteAgentVO>> queryListEntityPage(
            @RequestBody WriteAgentDTO writeAgentDTO) {
        return R.data(writeAgentService.queryListEntityPage(writeAgentDTO));
    }


    @IgnoreAuth
    @Operation(summary = "查询写作应用分类列表")
    @GetMapping("/queryTagList")
    public R queryTagList() {
        List<String> tagList = writeAgentService.queryTagList();
        tagList.remove("全部");
        return R.data(tagList);
    }


    @IgnoreAuth
    @Operation(summary = "获取应用信息")
    @GetMapping("/info")
    public R<WriteAgentVO> getWriteAgentInfo(@RequestParam Integer id) {
        WriteAgent writeAgent = writeAgentService.getById(id);
        return R.data(WriteAgentConvert.INSTANCE.entityToVO(writeAgent));
    }

    @IgnoreAuth
    @Operation(summary = "获取应用导航列表")
    @PostMapping("/getNavigateList")
    public R<List<WriteAgentLeftVO>> getNavigateList(@RequestBody WriteAgentDTO dto) {
        List<WriteAgentVO> writeAgentVOList = writeAgentService.queryListEntity(dto);
        Map<String, List<WriteAgentLeftTagVO>> categorizedMap = new HashMap<>();
        List<WriteAgentLeftTagVO> allList = new ArrayList<>();
        if (writeAgentVOList != null) {
            writeAgentVOList.forEach(writeAgentVO -> {
                WriteAgentLeftTagVO writeAgentLeftVO = new WriteAgentLeftTagVO();
                writeAgentLeftVO.setId(writeAgentVO.getId());
                writeAgentLeftVO.setTitle(writeAgentVO.getTitle());
                writeAgentLeftVO.setImageUrl(writeAgentVO.getImgUrl());
                allList.add(writeAgentLeftVO);
                if (categorizedMap.containsKey(writeAgentVO.getTag())) {
                    categorizedMap.get(writeAgentVO.getTag()).add(writeAgentLeftVO);
                } else {
                    List<WriteAgentLeftTagVO> list = new ArrayList<>();
                    list.add(writeAgentLeftVO);
                    categorizedMap.put(writeAgentVO.getTag(), list);
                }
            });
            Set<Integer> ids = new LinkedHashSet<>();
            List<WriteAgentLeftTagVO> deduplicatedList = new ArrayList<>();

            for (WriteAgentLeftTagVO item : allList) {
                Integer id = item.getId();
                if (!ids.contains(id)) {
                    ids.add(id);
                    deduplicatedList.add(item);
                }
            }
            categorizedMap.put("全部", deduplicatedList);
        }
        // 优化：提前获取并处理tag与icon的映射关系
        Map<String, String> tagToIconMap = new HashMap<>();
        List<WriteAgentMapper.TagIcon> tagIcons = this.baseMapper.queryAllTag();
        for (WriteAgentMapper.TagIcon tagIcon : tagIcons) {
            tagToIconMap.put(tagIcon.getTag(), tagIcon.getIcon());
        }
        List<WriteAgentLeftVO> resultList = new ArrayList<>();
        List<String> tagList = writeAgentService.queryTagList();

        categorizedMap.forEach((tag, items) -> {
            WriteAgentLeftVO writeAgentLeftVO = new WriteAgentLeftVO();
            writeAgentLeftVO.setTag(tag);
            writeAgentLeftVO.setData(items);

            // 优化：使用HashMap替代indexOf提高性能
            writeAgentLeftVO.setOrder(tagList.contains(tag) ? tagList.indexOf(tag) : 9999);
            writeAgentLeftVO.setIconUrl(tagToIconMap.getOrDefault(tag, "通用")); // 设置默认图标以防找不到对应图标
            resultList.add(writeAgentLeftVO);
        });

        resultList.sort(
            (o1, o2) -> {
                if (o1.getOrder() == o2.getOrder()) {
                    return 0;
                }
                return o1.getOrder() > o2.getOrder() ? 1 : -1;
            }
        );
        return R.data(resultList);
    }
}


