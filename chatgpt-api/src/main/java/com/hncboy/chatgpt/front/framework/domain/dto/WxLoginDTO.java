package com.hncboy.chatgpt.front.framework.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class WxLoginDTO implements Serializable {
    private static final long serialVersionUID = 130569783040564371L;

    @Schema(title = "code")
    private String code;

    @Schema(title = "openId")
    private String openId;

    @Schema(title = "手机号")
    private String phoneNumber;

    @Schema(title = "会话密钥")
    private String sessionKey;

    @Schema(title = "邀请人id")
    private String parentId;

}
