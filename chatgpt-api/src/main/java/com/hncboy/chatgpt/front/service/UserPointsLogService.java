package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.dto.UserPointsLogDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog;
import com.hncboy.chatgpt.front.framework.domain.vo.UserPointsLogVO;

/**
* <AUTHOR>
* @description 针对表【user_points_log】的数据库操作Service
* @createDate 2024-03-28 11:22:39
*/
public interface UserPointsLogService extends IService<UserPointsLog> {

    Integer checkPointsLog(Integer userId, String relOrder);
    Integer checkPointsLog(Integer userId);

    /**
     * 分页查询积分明细列表
     * @Param: example
     * @return: IPage<UserPointsLogVO>
     * @Author: zd.zhong
     * @Date: 2024/8/8
     */
    IPage<UserPointsLogVO> queryListEntityPage(UserPointsLogDTO dto);

    /**
     * 分页查询塔罗牌积分明细列表
     * @param dto
     * @return
     */
    IPage<UserPointsLogVO> pageQueryTarotByUserId(UserPointsLogDTO dto);

}
