package com.hncboy.chatgpt.front.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-3-22
 * 聊天配置参数
 */
@Data
@Component
@ConfigurationProperties(prefix = "chat")
public class ChatConfig {

    /**
     * HTTP 代理主机
     */
    private String httpProxyHost;

    /**
     * HTTP 代理端口
     */
    private Integer httpProxyPort;

    /**
     * OpenAI API Key
     *
     * @link <a href="https://beta.openai.com/docs/api-reference/authentication"/>
     */
    private String openaiApiKey;

    /**
     * OpenAI API Base URL
     *
     * @link <a href="https://api.openai.com"/>
     */
    private String openaiApiBaseUrl;

    /**
     * OpenAI API Model
     *
     * @link <a href="https://beta.openai.com/docs/models"/>
     */
    private String openaiApiModel;

    /**
     * OpenAI API Key
     *
     * @link <a href="https://beta.openai.com/docs/api-reference/authentication"/>
     */
    private String gptsOpenaiApiKey;

    /**
     * OpenAI API Base URL
     *
     * @link <a href="https://api.openai.com"/>
     */
    private String gptsOpenaiApiBaseUrl;

    /**
     * OpenAI API Key
     *
     * @link <a href="https://beta.openai.com/docs/api-reference/authentication"/>
     */
    private String gpt35OpenaiApiKey;

    /**
     * OpenAI API Base URL
     *
     * @link <a href="https://api.openai.com"/>
     */
    private String gpt35OpenaiApiBaseUrl;

    /**
     * OpenAI API Key
     *
     * @link <a href="https://beta.openai.com/docs/api-reference/authentication"/>
     */
    private String gpt4OpenaiApiKey;

    /**
     * OpenAI API Base URL
     *
     * @link <a href="https://api.openai.com"/>
     */
    private String gpt4OpenaiApiBaseUrl;

    /**
     * OpenAI API Key
     *
     * @link <a href="https://beta.openai.com/docs/api-reference/authentication"/>
     */
    private String gpt4allOpenaiApiKey;

    /**
     * OpenAI API Base URL
     *
     * @link <a href="https://api.openai.com"/>
     */
    private String gpt4allOpenaiApiBaseUrl;
}
