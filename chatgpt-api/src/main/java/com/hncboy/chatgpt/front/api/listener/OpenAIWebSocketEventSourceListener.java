package com.hncboy.chatgpt.front.api.listener;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.vo.ChatReplyMessageVO;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;

import javax.websocket.Session;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 描述：OpenAI流式输出Socket接收
 *
 * <AUTHOR>
 * @date 2023-03-23
 */
@Slf4j
public class OpenAIWebSocketEventSourceListener extends EventSourceListener {

    protected String lastMessage = "";
    /**
     * C当收到所有新消息时调用。
     *
     * @param message the new message
     */
    @Setter
    @Getter
    protected Consumer<String> onComplate = s -> {

    };
    private Session session;
    private ChatProcessRequest chatProcessRequest;

    public OpenAIWebSocketEventSourceListener(Session session, ChatProcessRequest chatProcessRequest) {
        this.chatProcessRequest = chatProcessRequest;
        this.session = session;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void onOpen(EventSource eventSource, Response response) {
        log.info("OpenAI建立sse连接...");
    }

    /**
     * {@inheritDoc}
     */
    @SneakyThrows
    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
//        log.info("OpenAI返回数据：{}", data, chatProcessRequest);
        if (Objects.nonNull(chatProcessRequest.getMsgId())) {
            //判断该消息是否已经停止
            boolean b = ApplicationConstant.timedCache.containsKey(Math.toIntExact(chatProcessRequest.getMsgId()));
            if (b) {
                return;
            }
        }
        if (data.equals("[DONE]")) {
            log.info("OpenAI返回数据结束了");
            if (StrUtil.isNotBlank(lastMessage)) {
                onComplate.accept(lastMessage);
            }
            ChatReplyMessageVO chatReplyMessageVO = new ChatReplyMessageVO();
            chatReplyMessageVO.setText(chatProcessRequest.getSpeech() ? "[TTSDONE]" : "[DONE]");
            chatReplyMessageVO.setStatus(ChatReplyMessageVO.MsgStatusEnum.normal.getStatus());
            session.getBasicRemote().sendText(JSONUtil.toJsonStr(chatReplyMessageVO));
            return;
        }
        ObjectMapper mapper = new ObjectMapper();
        ChatCompletionResponse completionResponse = mapper.readValue(data, ChatCompletionResponse.class); // 读取Json
        String delta = mapper.writeValueAsString(completionResponse.getChoices().get(0).getDelta());
        JSONObject jsonObject = JSONUtil.parseObj(delta);
        String str = jsonObject.getStr("content");
        if (StrUtil.isNotBlank(str)) {
            ChatReplyMessageVO chatReplyMessageVO = new ChatReplyMessageVO();
            lastMessage = lastMessage + str;
            chatReplyMessageVO.setText(lastMessage);
            chatReplyMessageVO.setId(chatProcessRequest.getMsgId());
            chatReplyMessageVO.setStatus(ChatReplyMessageVO.MsgStatusEnum.normal.getStatus());
            if (chatProcessRequest.getSpeech()) {
                //语音不发文本消息
                return;
            }
            synchronized (session) {
                session.getBasicRemote().sendText(JSONUtil.toJsonStr(chatReplyMessageVO));
            }
        }
    }


    @Override
    public void onClosed(EventSource eventSource) {
        log.info("OpenAI关闭sse连接...");
    }


    @SneakyThrows
    @Override
    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        if (Objects.isNull(response)) {
            log.error("OpenAI  sse连接异常data：{}，异常：{}", response, t);
            return;
        }
        ResponseBody body = response.body();
        String res = body.string();
        if (Objects.nonNull(body)) {
            log.error("OpenAI  sse连接异常data：{}，异常：{}", res, t);
        } else {
            log.error("OpenAI  sse连接异常data：{}，异常：{}", response, t);
        }
        if (res.contains("rate_limit_exceeded")) {
            ChatReplyMessageVO chatReplyMessageVO = new ChatReplyMessageVO();
            Pattern pattern = Pattern.compile("Please try again in (.*?)s");
            Matcher matcher = pattern.matcher(res);
            if (matcher.find()) {
                chatReplyMessageVO.setText("请求频率过高，请稍后再试，" + "还有 " + matcher.group(1) + " 秒");
            } else {
                chatReplyMessageVO.setText("请求频率过高，请稍后再试");
            }
            chatReplyMessageVO.setStatus(ChatReplyMessageVO.MsgStatusEnum.error.getStatus());
            session.getBasicRemote().sendText(JSONUtil.toJsonStr(chatReplyMessageVO));
            eventSource.cancel();
            return;
        }
        if (res.contains("context_length_exceeded")) {
            ChatReplyMessageVO chatReplyMessageVO = new ChatReplyMessageVO();
            chatReplyMessageVO.setText("当前对话上下文已经达到最大值，请联系管理员");
            chatReplyMessageVO.setStatus(ChatReplyMessageVO.MsgStatusEnum.error.getStatus());
            session.getBasicRemote().sendText(JSONUtil.toJsonStr(chatReplyMessageVO));
            eventSource.cancel();
            return;
        }
        ChatReplyMessageVO chatReplyMessageVO = new ChatReplyMessageVO();
        chatReplyMessageVO.setText("系统异常，请稍后再试");
        chatReplyMessageVO.setStatus(ChatReplyMessageVO.MsgStatusEnum.error.getStatus());
        session.getBasicRemote().sendText(JSONUtil.toJsonStr(chatReplyMessageVO));
        eventSource.cancel();
    }


}
