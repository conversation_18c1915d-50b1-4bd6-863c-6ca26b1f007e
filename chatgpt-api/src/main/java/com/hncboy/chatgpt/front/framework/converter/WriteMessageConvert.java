package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.entity.WriteMessage;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteMessageVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: zd.zhong
 * @Date: 2024/7/1
 * 聊天记录相关转换
 */
@Mapper
public interface WriteMessageConvert {

    WriteMessageConvert INSTANCE = Mappers.getMapper(WriteMessageConvert.class);

    /**
     * WriteMessageDTO 转 WriteMessage
     * @Author: zd.zhong
     * @Date: 2024/7/1
     */
    WriteMessage dtoToEntity(WriteMessage dto);

    /**
     * WriteMessage 转 WriteMessageVO
     * @Author: zd.zhong
     * @Date: 2024/7/1
     */
    WriteMessageVO entityToVO(WriteMessage entity);

    /**
     * List<WriteMessage> 转List<WriteMessageVO>
     * @Author: zd.zhong
     * @Date: 2024/7/1
     */
    List<WriteMessageVO> entityListToVOList(List<WriteMessage> entityList);

    /**
     * 查询DTO转换
     * @Author: zd.zhong
     * @Date: 2024/7/1
     */
    WriteMessage queryDtoToEntity(WriteMessage dto);

}
