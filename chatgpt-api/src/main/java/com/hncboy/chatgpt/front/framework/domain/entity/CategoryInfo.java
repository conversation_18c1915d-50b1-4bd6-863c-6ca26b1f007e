package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 分类信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("category_info")
public class CategoryInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;


    /**
     * 类别
     */
    private String name;


    /**
     * 类型1首页2MJ
     */
    private Integer type;


    /**
     * 代码
     */
    private String code;


    /**
     * 排序
     */
    private Integer sort;


    /**
     * 状态
     */
    private Integer status;


    /**
     * 状态
     */
    private String remark;


}
