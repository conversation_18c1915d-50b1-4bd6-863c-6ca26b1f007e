package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.dto.AppSignDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.AppSign;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.SignInUtils;
import com.hncboy.chatgpt.front.mapper.AppSignMapper;
import com.hncboy.chatgpt.front.service.AppSignService;
import com.hncboy.chatgpt.front.service.UserConfigService;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @描述:APP用户签到实现
 * @版本：v1.0.0
 * @作者: zc.wu
 * @时间:2021/10/15
 */
@Service
public class AppSignServiceImpl extends ServiceImpl<AppSignMapper, AppSign> implements
        AppSignService {

    @Autowired
    private WxUserInfoService wxUserInfoService;

    @Autowired
    private UserConfigService userConfigService;

    /**
     * @描述:根据ID查询APP用户签到
     * @参数:@param id
     * @返回:infrastructure.db.dataobject.AppSign
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    @Override
    public AppSign queryEntityById(BigDecimal id) {
        return this.getById(id);
    }

    /**
     * @描述:查询APP用户签到列表
     * @参数:@param dto
     * @返回:List<infrastructure.db.dataobject.AppSign>
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    @Override
    public List<AppSign> queryListEntityPage(AppSignDTO dto) {
        LambdaQueryWrapper<AppSign> wrapper = new LambdaQueryWrapper<>();
        return this.list(wrapper);
    }

    /**
     * @描述:查询单条APP用户签到
     * @参数:@param dto
     * @返回:List<infrastructure.db.dataobject.AppSign>
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    @Override
    public AppSign queryEntityByDto(AppSignDTO dto) {
        LambdaQueryWrapper<AppSign> wrapper = new LambdaQueryWrapper<>();
        return this.getOne(wrapper);
    }

    /**
     * @描述:更新APP用户签到
     * @参数:@param record example
     * @返回:boolean
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    @Override
    public boolean updateEntity(AppSign record) {
        UpdateWrapper updateWrapper = new UpdateWrapper();

        return this.update(record, updateWrapper);
    }

    /**
     * @描述:插入APP用户签到
     * @参数:@param record
     * @返回:boolean
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    @Override
    public boolean insertEntity(AppSign record) {

        return this.save(record);
    }

    /**
     * @描述:删除APP用户签到
     * @参数:@param example
     * @返回:boolean
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    @Override
    public boolean deleteById(BigDecimal id) {
        return this.removeById(id);
    }


    /**
     * @param userId
     * @描述:签到
     * @返回:boolean
     * @作者: zc.wu
     * @时间:2021/10/15 12:02 下午
     */
    @Override
    public synchronized boolean signByUser(String userId) {
        //签到
        LambdaQueryWrapper<AppSign> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppSign::getUserId, userId).eq(AppSign::getCheckDate, DateUtil.today());
        List<AppSign> list = this.list(wrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new ServiceException("您今天已经签过到啦");
        }
        wxUserInfoService.addUserNumMp(userId, userConfigService.querySysConfig().getSignMonery());
        AppSign appSign = new AppSign();
        appSign.setUserId(userId);
        appSign.setInterruptStatus(
                DateUtil.today() + "签到奖励" + userConfigService.querySysConfig().getSignMonery()
                        .toString());
        appSign.setCheckDate(new Date());
        return this.save(appSign);
    }

    @Override
    public boolean toDaySign(String userId) {
        //签到
        LambdaQueryWrapper<AppSign> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppSign::getUserId, userId).eq(AppSign::getCheckDate, DateUtil.today());
        List<AppSign> list = this.list(wrapper);
        return CollectionUtil.isEmpty(list) ? false : true;
    }

    /**
     * @param userId
     * @描述:计算连续签到天数
     * @返回:int
     * @作者: zc.wu
     * @时间:2021/10/15 3:54 下午
     */
    @Override
    public int querySignByUser(String userId) {
        //签到
        LambdaQueryWrapper<AppSign> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppSign::getUserId, userId);
        wrapper.orderByDesc(AppSign::getCheckDate);
        List<AppSign> list = this.list(wrapper);
        if (CollectionUtil.isEmpty(list)) {
            return 0;
        }
        List<Date> collect = list.stream().map(AppSign::getCheckDate).collect(Collectors.toList());
        int continuousSignInDay = SignInUtils.getContinuousSignInDay(collect);
        return continuousSignInDay;
    }

    /**
     * @param userId
     * @描述:获取签到详情
     * @返回:int
     * @作者: zc.wu
     * @时间:2021/10/15 3:54 下午
     */
    @Override
    public List<AppSign> querySignMonthByUser(String userId, int type) {
        //签到
        LambdaQueryWrapper<AppSign> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppSign::getUserId, userId);
        DateTime dateSatartTime = null;
        DateTime dateEndTime = null;
        if (type == 1) {
            DateTime dateTime = DateUtil.offsetMonth(new Date(), -1);
            dateSatartTime = DateUtil.beginOfMonth(dateTime);
            dateEndTime = DateUtil.endOfMonth(dateTime);
        } else {
            Date date = new Date();
            dateSatartTime = DateUtil.beginOfMonth(date);
            dateEndTime = DateUtil.endOfMonth(date);
        }
        wrapper.between(AppSign::getCheckDate, dateSatartTime, dateEndTime);
        List<AppSign> list = this.list(wrapper);
        list.forEach(item -> {
            item.setRemark(DateUtil.formatDate(item.getCheckDate()));
        });
        return list;
    }


}
