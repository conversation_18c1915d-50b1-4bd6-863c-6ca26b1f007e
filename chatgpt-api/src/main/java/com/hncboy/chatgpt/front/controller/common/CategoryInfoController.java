package com.hncboy.chatgpt.front.controller.common;

import com.hncboy.chatgpt.front.framework.domain.vo.CategoryInfoVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.service.CategoryInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 分类信息 控制器
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Slf4j
@RestController
@RequestMapping("/category")
@Tag(name = "首页配置")
public class CategoryInfoController {

    @Autowired
    private CategoryInfoService iCategoryInfoService;


    @Operation(summary = "查询分类信息列表")
    @GetMapping("/list")
    public R<List<CategoryInfoVO>> chatConfig() {
        return R.data(iCategoryInfoService.queryListEntityPage(1));
    }

    @Operation(summary = "查询Mj分类信息列表")
    @GetMapping("/mjCategory")
    public R<List<CategoryInfoVO>> mjCategory() {
        return R.data(iCategoryInfoService.queryListEntityPage(2));
    }


}
