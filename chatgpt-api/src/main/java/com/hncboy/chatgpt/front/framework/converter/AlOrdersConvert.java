package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.entity.AlOrders;
import com.hncboy.chatgpt.front.framework.domain.vo.AlOrdersVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 写作应用信息 领域对象转换器
 * @Version: v1.0.0
 * @Author: zzd
 * @Date: 2024/6/27 14:06
 */
@Mapper
public interface AlOrdersConvert {

   AlOrdersConvert INSTANCE = Mappers.getMapper(AlOrdersConvert.class);

  /**
   * AliOrder转AliOrder
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  AlOrders dtoToEntity(AlOrders dto);

  /**
   * AlOrders 转AliOrderVO
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  AlOrdersVO entityToVO(AlOrders entity);

  /**
   * List<AlOrders> 转List<AlOrdersVO>
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  List<AlOrdersVO> entityListToVOList(List<AlOrders> entityList);

  /**
   * 查询DTO转换
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  AlOrders queryDtoToEntity(AlOrders dto);


}
