package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("commission_identity")
public class CommissionIdentity implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 类型
     */
    private String type ;

    /**
     * 编号
     */
    private String code ;

    /**
     * 状态
     */
    private String status ;

    /**
     * 生效时间
     */
    private Date startTime ;

    /**
     * 过期时间
     */
    private Date endTime ;

    /**
     * 分佣比例
     */
    private Integer percentage ;

    /**
     * 姓名
     */
    private String name ;

    /**
     * 手机号
     */
    private String phone ;

    /**
     * 微信公众号
     */
    private String wxMpUrl ;

    /**
     * 用户信息ID
     */
    private Integer userInfoId ;

    /**
     * openId
     */
    private String openId ;

    /**
     * 邀请码
     */
    private String inviteCode ;

    /**
     * 创建时间
     */
    private Date createTime ;

    /**
     * 更新时间
     */
    private Date updateTime ;
}
