package com.hncboy.chatgpt.front.controller.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.annotation.IgnoreAuth;
import com.hncboy.chatgpt.front.framework.domain.dto.IntelligentAgentDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentAgentVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.service.IntelligentAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * zcWu
 */
@Slf4j
@FrontPreAuth
@AllArgsConstructor
@RestController
@RequestMapping("/intelligentAgent")
@Tag(name = "智能体相关")
public class IntelligentAgentController {

    private final IntelligentAgentService intelligentAgentService;

    @IgnoreAuth
    @Operation(summary = "分页查询智能体列表")
    @PostMapping("/page/list")
    public R<IPage<IntelligentAgentVO>> chatApiProcess(
            @RequestBody IntelligentAgentDTO intelligentAgentDTO) {
        return R.data(intelligentAgentService.queryListEntityPage(intelligentAgentDTO));
    }

    @Operation(summary = "新增智能体信息")
    @PostMapping("/save")
    public R saveIntelligentAgent(
            @RequestBody @Validated IntelligentAgentDTO intelligentAgentDTO) {
        intelligentAgentService.insertEntity(intelligentAgentDTO);
        return R.success();
    }


    @Operation(summary = "查询新建对话时默认智能体信息")
    @GetMapping("/getDefaultAgent")
    public R getDefaultAgent() {
        return R.data(intelligentAgentService.queryIntelligentByModelName("ZNS"));
    }


    @Operation(summary = "根据名字查询智能体信息")
    @GetMapping("/getAgentByName/{name}")
    public R getMusicAgent(@PathVariable("name") String name) {
        return R.data(intelligentAgentService.queryIntelligentByModelName(name));
    }


    @IgnoreAuth
    @Operation(summary = "查询智能体分类列表")
    @GetMapping("/queryTagList")
    public R queryTagList() {
        return R.data(intelligentAgentService.queryTagList());
    }


    @Operation(summary = "远程接口抓取数据")
    @GetMapping("/saveDataRemotely")
    public R saveDataRemotely() {
        intelligentAgentService.saveDataRemotely();
        return R.success();
    }


}
