package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.dto.WriteMessageDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.WriteMessage;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteMessageVO;

/**
 * @Author: zd.zhong
* @description 针对表【write_message(聊天消息表)】的数据库操作Service
* @createDate 2024-07-02 09:26:03
*/
public interface WriteMessageService extends IService<WriteMessage> {

    /**
     * 分页查询聊天消息列表（默认查询当前登陆用户的）
     * @param dto
     * @return
     */
    IPage<WriteMessageVO> queryListEntityPage(WriteMessageDTO dto);

    /**
     * 删除写作记录
     * @param id
     */
    void softDeleteById(Integer id);

    /**
     * 查询写作应用消耗
     *
     * @return WriteMessageVO
     * @Author: 赵雨晨
     * @Date: 2024/7/17
     */
    WriteMessageVO getWriteMessageInfoById(Integer id);
}
