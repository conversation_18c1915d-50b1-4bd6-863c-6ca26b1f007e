package com.hncboy.chatgpt.front.controller.wx;

import com.hncboy.chatgpt.front.framework.enums.WxAppIdEnum;
import com.hncboy.chatgpt.front.handler.mp.WxMpMessageRouter2;
import com.hncboy.chatgpt.front.handler.mp.WxMpMessageRouter3;
import com.hncboy.chatgpt.front.handler.mp.WxMpMessageRouter4;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary <PERSON></a>
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/wx/portal/{appid}")
@Tag(name = "微信路由")
public class WxPortalController {
    private final WxMpService wxService;
    private final WxMpMessageRouter messageRouter;
    private final WxMpMessageRouter2 messageRouter2;
    private final WxMpMessageRouter3 messageRouter3;
    private final WxMpMessageRouter4 messageRouter4;

    @GetMapping(produces = "text/plain;charset=utf-8")
    public String authGet(@PathVariable String appid,
                          @RequestParam(name = "signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {

        log.info("\n接收到来自微信服务器的认证消息：[{}, {}, {}, {}]", signature,
                timestamp, nonce, echostr);
        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            throw new IllegalArgumentException("请求参数非法，请核实!");
        }

        if (!this.wxService.switchover(appid)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
       }

        if (wxService.checkSignature(timestamp, nonce, signature)) {
            return echostr;
        }

        return "非法请求";
    }

    @PostMapping(produces = "application/xml; charset=UTF-8")
    public String post(@PathVariable String appid,
                       @RequestBody String requestBody,
                       @RequestParam("signature") String signature,
                       @RequestParam("timestamp") String timestamp,
                       @RequestParam("nonce") String nonce,
                       @RequestParam("openid") String openid,
                       @RequestParam(name = "encrypt_type", required = false) String encType,
                       @RequestParam(name = "msg_signature", required = false) String msgSignature) {
        log.info("\n接收微信请求：[appid=[{}], openid=[{}], signature=[{}], encType=[{}], msgSignature=[{}],"
                        + " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                appid, openid, signature, encType, msgSignature, timestamp, nonce, requestBody);

        if (!this.wxService.switchover(appid)) {
            throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
        }


        if(WxAppIdEnum.tarot.getCode().equals(appid)){
            if (!wxService.checkSignature(timestamp, nonce, signature)) {
                log.info("请求zns订阅号签名参数 timestamp:{},nonce:{},signature:{}",timestamp, nonce, signature);
                throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
            }

            String out = null;
            if (encType == null) {
                // 明文传输的消息
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
                WxMpXmlOutMessage outMessage = this.route2(inMessage);
                if (outMessage == null) {
                    return "";
                }

                out = outMessage.toXml();
            } else if ("aes".equalsIgnoreCase(encType)) {
                // aes加密的消息
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromEncryptedXml(requestBody, wxService.getWxMpConfigStorage(),
                        timestamp, nonce, msgSignature);
                log.debug("\nzns订阅号消息解密后内容为：\n{} ", inMessage.toString());
                WxMpXmlOutMessage outMessage = this.route2(inMessage);
                if (outMessage == null) {
                    return "";
                }

                out = outMessage.toEncryptedXml(wxService.getWxMpConfigStorage());
            }

            log.debug("\nzns订阅号组装回复信息：{}", out);
            return out;
        }
        else if(WxAppIdEnum.tarot_main.getCode().equals(appid)){
            if (!wxService.checkSignature(timestamp, nonce, signature)) {
                log.info("请求tarot订阅号签名参数 timestamp:{},nonce:{},signature:{}",timestamp, nonce, signature);
                throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
            }

            String out = null;
            if (encType == null) {
                // 明文传输的消息
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
                WxMpXmlOutMessage outMessage = this.route3(inMessage);
                if (outMessage == null) {
                    return "";
                }

                out = outMessage.toXml();
            } else if ("aes".equalsIgnoreCase(encType)) {
                // aes加密的消息
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromEncryptedXml(requestBody, wxService.getWxMpConfigStorage(),
                        timestamp, nonce, msgSignature);
                log.debug("\ntarot订阅号消息解密后内容为：\n{} ", inMessage.toString());
                WxMpXmlOutMessage outMessage = this.route3(inMessage);
                if (outMessage == null) {
                    return "";
                }

                out = outMessage.toEncryptedXml(wxService.getWxMpConfigStorage());
            }

            log.debug("\ntarot订阅号组装回复信息：{}", out);
            return out;
        }
        else if(WxAppIdEnum.tarot_yuexin.getCode().equals(appid)){
            if (!wxService.checkSignature(timestamp, nonce, signature)) {
                log.info("请求tarot月信订阅号签名参数 timestamp:{},nonce:{},signature:{}",timestamp, nonce, signature);
                throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
            }

            String out = null;
            if (encType == null) {
                // 明文传输的消息
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
                WxMpXmlOutMessage outMessage = this.route4(inMessage);
                if (outMessage == null) {
                    return "";
                }

                out = outMessage.toXml();
            } else if ("aes".equalsIgnoreCase(encType)) {
                // aes加密的消息
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromEncryptedXml(requestBody, wxService.getWxMpConfigStorage(),
                        timestamp, nonce, msgSignature);
                log.debug("\ntarot月信订阅号消息解密后内容为：\n{} ", inMessage.toString());
                WxMpXmlOutMessage outMessage = this.route4(inMessage);
                if (outMessage == null) {
                    return "";
                }

                out = outMessage.toEncryptedXml(wxService.getWxMpConfigStorage());
            }

            log.debug("\ntarot月信订阅号组装回复信息：{}", out);
            return out;
        }
        else{
            if (!wxService.checkSignature(timestamp, nonce, signature)) {
                throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
            }

            String out = null;
            if (encType == null) {
                // 明文传输的消息
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
                WxMpXmlOutMessage outMessage = this.route(inMessage);
                if (outMessage == null) {
                    return "";
                }

                out = outMessage.toXml();
            } else if ("aes".equalsIgnoreCase(encType)) {
                // aes加密的消息
                WxMpXmlMessage inMessage = WxMpXmlMessage.fromEncryptedXml(requestBody, wxService.getWxMpConfigStorage(),
                        timestamp, nonce, msgSignature);
                log.debug("\nzns服务号消息解密后内容为：\n{} ", inMessage.toString());
                WxMpXmlOutMessage outMessage = this.route(inMessage);
                if (outMessage == null) {
                    return "";
                }

                out = outMessage.toEncryptedXml(wxService.getWxMpConfigStorage());
            }

            log.debug("\nzns服务号组装回复信息：{}", out);
            return out;
        }



    }

    private WxMpXmlOutMessage route(WxMpXmlMessage message) {
        try {
            return this.messageRouter.route(message);
        } catch (Exception e) {
            log.error("路由zns服务号消息时出现异常！", e);
        }
        return null;
    }

    private WxMpXmlOutMessage route2(WxMpXmlMessage message) {
        try {
            return this.messageRouter2.route(message);
        } catch (Exception e) {
            log.error("路由zns订阅号消息时出现异常！", e);
        }
        return null;
    }

    private WxMpXmlOutMessage route3(WxMpXmlMessage message) {
        try {
            return this.messageRouter3.route(message);
        } catch (Exception e) {
            log.error("路由tarot订阅号消息时出现异常！", e);
        }
        return null;
    }

    private WxMpXmlOutMessage route4(WxMpXmlMessage message) {
        try {
            return this.messageRouter4.route(message);
        } catch (Exception e) {
            log.error("路由tarot月信订阅号消息时出现异常！", e);
        }
        return null;
    }

}
