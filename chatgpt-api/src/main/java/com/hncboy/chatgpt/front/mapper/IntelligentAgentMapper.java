package com.hncboy.chatgpt.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.front.framework.domain.entity.IntelligentAgent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 智能体信息 Mapper
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/2/28
 */
@Mapper
public interface IntelligentAgentMapper extends BaseMapper<IntelligentAgent> {




    @Select("SELECT DISTINCT tag  FROM chat_agent where tag is not null and status = '0'")
    List<String> queryAllTag();

    /**
     * 更新应用使用次数
     *
     * @param id
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/5/16 14:51
     */
    @Update("update chat_agent set use_cnt=use_cnt+1 where id=#{id}")
    void addUseCnt(@Param("id") Integer id);

}
