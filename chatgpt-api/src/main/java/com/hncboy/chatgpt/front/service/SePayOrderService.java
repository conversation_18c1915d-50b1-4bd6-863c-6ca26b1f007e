package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.entity.SePayOrder;
import com.hncboy.chatgpt.tarot.domain.entity.Transaction;

import javax.servlet.http.HttpServletRequest;

/**
 * 支付订单信息 Dao
 *
 * @Version：v1.0.0
 * @Author: shy
 * @Date:2025/4/14
 */
public interface SePayOrderService extends IService<SePayOrder> {


    SePayOrder generateSePayQrCode(Long productId, HttpServletRequest httpRequest);

    void callback(Transaction transaction);

    SePayOrder getOrderByOrderNo(String orderNo);
}
