package com.hncboy.chatgpt.front.controller.common;

import com.hncboy.chatgpt.front.framework.annotation.FrontPreAuth;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.AlyOssUtils;
import com.hncboy.chatgpt.front.helper.QiNuOssHelper;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @create 2023-06-03 13:51
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/common")
@Tag(name = "公共接口")
@FrontPreAuth
public class CommonController {
    /**
     * 上传文件
     *
     * @param file
     * @return Res
     * @Author: zc.wu
     * @Date: 2022/3/28 3:33 PM
     */
    @PostMapping("/upload")
    @ResponseBody
    public R uploadFile(@RequestParam("file") MultipartFile file) {
        String s = AlyOssUtils.ossUpload(file);
        if (StringUtils.isEmpty(s)) {
            return R.fail("上传失败");
        }
        return R.data(s);
    }


    /**
     * 上传图片文件并返回地址
     *
     * @param file
     * @return Res
     * @Author: zc.wu
     * @Date: 2022/6/27 11:28
     */
    @PostMapping("/uploadFile")
    @ResponseBody
    public R<Object> uploadLocalFile(@RequestParam("file") MultipartFile file) {
        String s = QiNuOssHelper.qnyUpload(file);
        if (StringUtils.isEmpty(s)) {
            return R.fail("上传失败");
        }
        return R.data(s);
    }
}
