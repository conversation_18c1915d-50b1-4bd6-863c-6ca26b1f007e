package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.converter.HomeConfigConverter;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatRoomDO;
import com.hncboy.chatgpt.front.framework.domain.entity.HomeConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.WxUserInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.HomeConfigVO;
import com.hncboy.chatgpt.front.mapper.ChatRoomMapper;
import com.hncboy.chatgpt.front.mapper.HomeConfigMapper;
import com.hncboy.chatgpt.front.mapper.WxUserInfoMapper;
import com.hncboy.chatgpt.front.service.HomeConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 实现
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/11
 */
@Service
@RequiredArgsConstructor
public class HomeConfigServiceImpl extends ServiceImpl<HomeConfigMapper, HomeConfig> implements
        HomeConfigService {

    private final WxUserInfoMapper wxUserInfoMapper;

    private final ChatRoomMapper chatRoomMapper;

    /**
     * 查询所有配置
     *
     * @Param:@param id
     * @return:com.zhongjia.infrastructure.db.entity.HomeConfig
     * @Author: wuzhic
     * @Date:2023/4/11
     */
    @Override
    public List<HomeConfigVO> queryAllList(HomeConfigVO homeConfig) {
        LambdaQueryWrapper<HomeConfig> lambdaQueryWrapper = new LambdaQueryWrapper();
        if (StrUtil.isNotBlank(homeConfig.getCode()) && StrUtil.isEmpty(homeConfig.getTitle())) {
            if (homeConfig.getCode().equals("hot")) {
                lambdaQueryWrapper.eq(HomeConfig::getHot, "1");
            } else {
                lambdaQueryWrapper.eq(HomeConfig::getTag, homeConfig.getCode());
            }
        }
        lambdaQueryWrapper.like(StrUtil.isNotEmpty(homeConfig.getTitle()), HomeConfig::getTitle, homeConfig.getTitle());
        lambdaQueryWrapper.eq(Objects.nonNull(homeConfig.getStatus()), HomeConfig::getStatus, homeConfig.getStatus());
        lambdaQueryWrapper.orderByDesc(HomeConfig::getCreateTime);
        List<HomeConfigVO> homeConfigVOS = HomeConfigConverter.INSTANCE.entityListToVOList(this.list(lambdaQueryWrapper));
        List<String> collect = homeConfigVOS.stream().map(HomeConfigVO::getCreateBy).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            //根据openid查询用户信息
            List<WxUserInfo> nickName = wxUserInfoMapper.selectBatchIds(collect);
            homeConfigVOS.stream().forEach(homeConfigVO -> {
                nickName.stream().forEach(wxUserInfo -> {
                    if (homeConfigVO.getCreateBy().equals(wxUserInfo.getOpenId())) {
                        homeConfigVO.setCreateByName(wxUserInfo.getNickName());
                    }
                });
            });
        }
        if (StrUtil.isNotBlank(homeConfig.getOpenId())) {
            List<ChatRoomDO> chatRoomDOS = chatRoomMapper.selectList(new LambdaQueryWrapper<ChatRoomDO>()
                    .eq(ChatRoomDO::getOpenId, homeConfig.getOpenId()));
            if (CollUtil.isNotEmpty(chatRoomDOS)) {
                List<Integer> roList = chatRoomDOS.stream().map(ChatRoomDO::getRoleId).collect(Collectors.toList());
                //判断是否已经存在
                homeConfigVOS.stream().forEach(homeConfigVO -> {
                    if (roList.contains(homeConfigVO.getId())) {
                        homeConfigVO.setDoesItExist(true);
                    }
                });
            }
        }
        return homeConfigVOS;
    }

    /**
     * 根据Id批量查询配置并拼接成字符串
     *
     * @param ids
     * @return HomeConfig
     * @Author: zc.wu
     * @Date: 2023/6/21 0021 下午 12:36
     */
    @Override
    public String queryConfigByIds(List<Integer> ids) {
        if (ids.contains(89) && ids.contains(90)) {
            ids.removeIf(id -> id.equals(89));
        }
        LambdaQueryWrapper<HomeConfig> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(HomeConfig::getId, ids);
        List<HomeConfig> list = this.list(lambdaQueryWrapper);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        StringBuffer stringBuffer = new StringBuffer();
        list.stream().forEach(homeConfig -> {
            stringBuffer.append(homeConfig.getDescription() + " ");
        });
        return stringBuffer.toString();
    }


    @Override
    public HomeConfig getHomeConfigById(Integer id) {
        updateConfigNum(id);
        return this.getById(id);
    }


    @Override
    @Async
    public void updateConfigNum(Integer id) {
        this.baseMapper.updateConfigNum(id);
    }


    /**
     * 插入所有配置
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wuzhic
     * @Date:2023/4/11
     */
    @Override
    public HomeConfig insertEntity(HomeConfig record) {
        record.setStatus(0);
        record.setCreateTime(new Date());
        this.save(record);
        return record;
    }


}
