package com.hncboy.chatgpt.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog;
import com.hncboy.chatgpt.front.framework.domain.vo.UserPointsLogVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【user_points_log】的数据库操作Mapper
* @createDate 2024-03-28 11:22:39
* @Entity com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog
*/
public interface UserPointsLogMapper extends BaseMapper<UserPointsLog> {

    /*@Select({
            "<script>",
            "SELECT log.*, ",
            "(CASE WHEN log.points_type = 'invite_points' THEN log.rel_order ELSE user.account END) AS account, ",
            "ord.product_name FROM user_points_log log",
            "LEFT JOIN al_orders ord ON log.rel_order = ord.orders_id",
            "LEFT JOIN user_base_info user ON ord.user_id = user.id",
            "WHERE log.user_id = #{userId}",
            "<if test='type != null and type != \"\"'>",
            "AND points_type = #{type}",
            "</if>",
            "ORDER BY log.create_time DESC",
            "</script>"
    })*/
    IPage<UserPointsLogVO> pageQueryPointsByUserId(
            IPage<UserPointsLogVO> page,
            @Param("userId") Integer userId,
            @Param("type") String type
    );

    IPage<UserPointsLogVO> pageQueryTarotByUserId(
            IPage<UserPointsLogVO> page,
            @Param("userId") Integer userId,
            @Param("type") String type
    );
}




