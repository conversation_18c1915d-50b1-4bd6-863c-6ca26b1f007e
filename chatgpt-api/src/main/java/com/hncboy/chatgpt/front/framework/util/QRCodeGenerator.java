package com.hncboy.chatgpt.front.framework.util;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.zxing.WriterException;

import java.awt.*;
import java.io.IOException;

public class QRCodeGenerator {


    public static String generateQRCode(final String url) throws WriterException, IOException {
        QrConfig qrConfig = new QrConfig(300, 300);
        return QrCodeUtil.generateAsBase64(url, qrConfig, ImgUtil.IMAGE_TYPE_PNG);
    }
    public static String generateQRCode(final String url, Image logo) throws WriterException, IOException {
        QrConfig qrConfig = new QrConfig(300, 300);
        return QrCodeUtil.generateAsBase64(url, qrConfig, ImgUtil.IMAGE_TYPE_PNG, logo);
    }


}
