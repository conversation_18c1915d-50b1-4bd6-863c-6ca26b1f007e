package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.converter.UserConfigConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.SysConfigDTO;
import com.hncboy.chatgpt.front.framework.domain.dto.UserConfigDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.UserConfig;
import com.hncboy.chatgpt.front.framework.domain.vo.UserConfigVO;
import com.hncboy.chatgpt.front.framework.enums.ApiKeyModelEnum;
import com.hncboy.chatgpt.front.mapper.UserConfigMapper;
import com.hncboy.chatgpt.front.service.UserConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户配置相关信息实现
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Service
public class UserConfigServiceImpl extends ServiceImpl<UserConfigMapper, UserConfig> implements
        UserConfigService {


    public static void main(String[] args) {
        SysConfigDTO sysConfigDTO = new SysConfigDTO();
        sysConfigDTO.setGpt("Y");
        sysConfigDTO.setGptTitle("超级智能社为您服务：\\n\\r聊什么，你说了算\\n\\r例1：编程代码相关问题\\n\\r例2：讲故事，写文章无所不能\\n\\r例3：专业领域的技能，医生律师产品经理等等能力待你发掘");
        sysConfigDTO.setConversations(20);
        sysConfigDTO.setDrawingTimes(15);
        sysConfigDTO.setNotificationMessage("当前系统正在维护中，预计维护时间为2021年4月20日至2021年4月21日，给您带来的不便敬请谅解。");
        System.out.println(JSONUtil.toJsonStr(sysConfigDTO));
    }

    @Override
    public SysConfigDTO querySysConfig() {
        LambdaQueryWrapper<UserConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserConfig::getOpenid, "SYSTEM");
        queryWrapper.eq(UserConfig::getType, 1);
        UserConfig userConfig = this.getOne(queryWrapper);
        if (userConfig == null) {
            return null;
        }
        return JSONUtil.toBean(userConfig.getContent(), SysConfigDTO.class);
    }

    @Override
    public void saveUserConfig(UserConfigDTO userConfigDTO) {
        UserConfig userConfig = UserConfigConvert.INSTANCE.dtoToEntity(userConfigDTO);
        this.save(userConfig);
    }

    @Override
    public void saveOrUpdate(UserConfigDTO userConfigDTO) {
        UserConfig userConfig = UserConfigConvert.INSTANCE.dtoToEntity(userConfigDTO);
        //已存在更新
        List<UserConfigVO> userConfigVOS = queryUserConfig(userConfig.getOpenid(), userConfig.getType());
        if (CollUtil.isNotEmpty(userConfigVOS)) {
            userConfig.setId(userConfigVOS.get(0).getId());
        }
        this.saveOrUpdate(userConfig);
    }

    @Override
    public List<UserConfigVO> queryUserConfig(String openid, Integer type) {
        LambdaQueryWrapper<UserConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserConfig::getOpenid, openid);
        queryWrapper.eq(UserConfig::getType, type);
        List<UserConfig> list = this.list(queryWrapper);
        return UserConfigConvert.INSTANCE.entityListToVOList(list);
    }

    /**
     * 查询用户模型
     *
     * @param openid
     * @return String
     * @Author: zc.wu
     * @Date: 2023/7/18 0018 下午 04:44
     */
    @Override
    public String queryUserModel(String openid) {
        LambdaQueryWrapper<UserConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserConfig::getOpenid, openid);
        queryWrapper.eq(UserConfig::getType, 3);
        List<UserConfig> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            String content = list.get(0).getContent();
            String model = JSONUtil.parseObj(content).getStr("model");
            if (StrUtil.isNotEmpty(model)) {
                return model.equals(ApiKeyModelEnum.GPT_3_5_TURBO_16k_0613.getName()) ? "1" : "2";
            }
        }
        return "1";
    }

}
