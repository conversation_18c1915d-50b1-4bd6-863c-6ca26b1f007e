package com.hncboy.chatgpt.front.framework.domain.dto;

import com.hncboy.chatgpt.front.framework.domain.query.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * 智能体信息 DTO
 * @Version：v1.0.0
 * @Author: zyc
 * @Date:2024/7/4
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AlOrdersDTO extends PageParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(title = "主键ID")
    private String ordersId;

    @Schema(title = "用户ID")
    private String userId;

    @Schema(title = "产品ID")
    private BigInteger productId;

    @Schema(title = "产品类型")
    private String productType;

    @Schema(title = "产品名称")
    private String productName;

    @Schema(title = "产品价格")
    private Double productPrice;

    @Schema(title = "数量")
    private BigInteger num;

    @Schema(title = "单位")
    private String unit;

    @Schema(title = "支付状态")
    private Integer state;

    @Schema(title = "支付时间")
    private LocalDateTime payTime;

    @Schema(title = "支付失败原因")
    private String reasonFailure;

    @Schema(title = "过期时间")
    private LocalDateTime expiresTime;

    @Schema(title = "创建时间")
    private LocalDateTime createdTime;

    @Schema(title = "更新时间")
    private LocalDateTime updateTime;

}
