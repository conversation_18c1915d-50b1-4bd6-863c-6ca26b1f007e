package com.hncboy.chatgpt.front.framework.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 用户配置相关信息 VO
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(title = "UserConfig对象", description = "用户配置相关信息")
public class UserConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "用户ID")
    private String openid;

    @Schema(title = "配置信息")
    private String content;

    @Schema(title = "配置类型")
    private Integer type;


}
