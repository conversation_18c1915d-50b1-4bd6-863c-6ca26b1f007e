package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通道配置 ENTITY
 *
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/4/15
 */
@Data
@TableName("exception_log")
public class ExceptionLog implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    private Long id;


    /**
     * 消息类型 CHAT-聊天
     */
    private String messageType;


    /**
     * 消息ID
     */
    private Long messageId;


    /**
     * 用户ID
     */
    private Integer userId;


    /**
     * 账号(手机号或其他账号)
     */
    private String account;


    /**
     * 用户名
     */
    private String name;


    /**
     * 模型GID
     */
    private String modelGid;


    /**
     * 模型名称
     */
    private String modelName;


    /**
     * 站点ID
     */
    private Integer siteId;


    /**
     * 站点地址
     */
    private String url;


    /**
     * 秘钥
     */
    private String apiKey;


    /**
     * 请求信息
     */
    private String request;


    /**
     * 应答消息  （界面展示）
     */
    private String resMessage;


    /**
     * 应答信息
     */
    private String response;


    /**
     * 异常信息
     */
    private String exception;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
