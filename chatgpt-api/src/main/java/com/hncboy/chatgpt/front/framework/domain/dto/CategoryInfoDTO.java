package com.hncboy.chatgpt.front.framework.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 分类信息 DTO
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Data
public class CategoryInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "类别")
    private String name;

    @Schema(title = "代码")
    private String code;

    @Schema(title = "排序")
    private Integer sort;

    @Schema(title = "类型1首页2MJ")
    private Integer type;

    @Schema(title = "状态")
    private Integer status;

    @Schema(title = "状态")
    private String remark;


}
