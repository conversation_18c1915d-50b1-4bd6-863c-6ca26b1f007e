package com.hncboy.chatgpt.front.framework.exception;

import com.hncboy.chatgpt.front.framework.handler.response.IResultCode;
import com.hncboy.chatgpt.front.framework.handler.response.ResultCode;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/3/23 12:49
 * 鉴权异常
 */
public class AuthException extends RuntimeException {

    @Getter
    private final IResultCode resultCode;

    public AuthException(String message) {
        super(message);
        this.resultCode = ResultCode.UN_AUTHORIZED;
    }
}
