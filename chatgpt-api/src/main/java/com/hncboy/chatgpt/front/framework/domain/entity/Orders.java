package com.hncboy.chatgpt.front.framework.domain.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 用户实体类
 *
 * <AUTHOR>
 * @version 1.2
 */
@Data
@TableName(value = "al_orders")
@Accessors(chain = true)
public class Orders {


    @TableId(type = IdType.INPUT)
    private String ordersId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品价格
     */
    private Double productPrice;

    /**
     * 购买数量
     */
    private Long num;

    /**
     * 单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)
     */
    private String unit;

    /**
     * 组合套餐信息
     */
    private String packageInfo;

    /**
     * 订单状态
     */
    private Integer state;

    /**
     * 支付状态
     */
    private String reasonFailure;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 过期时间
     */
    private Date expiresTime;

    @TableField(fill = FieldFill.INSERT)
    private Date createdTime;

    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}
