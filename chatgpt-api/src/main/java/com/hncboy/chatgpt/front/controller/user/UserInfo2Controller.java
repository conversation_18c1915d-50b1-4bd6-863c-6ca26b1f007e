package com.hncboy.chatgpt.front.controller.user;

import com.hncboy.chatgpt.front.framework.domain.entity.SiteInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.helper.ChannelConfigHelper;
import com.hncboy.chatgpt.front.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;



/**
 * @Description:
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2023/4/10
 */

@AllArgsConstructor
@Tag(name = "用户相关接口")
@RestController
@RequestMapping("/user")
@Slf4j
public class UserInfo2Controller {

    private final UserBaseInfoService userBaseInfoService;
    private final ChannelConfigHelper channelConfigHelper;

    @Operation(summary = "通过手机号获取用户信息")
    @GetMapping("/v1/infoPhone")
    public R<UserBaseInfoVO> getUserInfoPhone(String phone,String model) {
        //List<ChannelConfig> channelConfigs = channelConfigMapper.selectChannelConfigWithSiteInfo(model);
        UserBaseInfoVO userBaseInfoVO = userBaseInfoService.queryUserInfoByPhone(phone);
        if(StringUtils.isNotEmpty(model)) {
            SiteInfo siteInfo = channelConfigHelper.queryChannelInfoByGid(model);
            userBaseInfoVO.setSiteInfo(siteInfo);
        }
        return R.data(userBaseInfoVO);
    }

}
