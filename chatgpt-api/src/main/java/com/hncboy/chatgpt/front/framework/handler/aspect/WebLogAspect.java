package com.hncboy.chatgpt.front.framework.handler.aspect;

import cn.hutool.core.thread.threadlocal.NamedThreadLocal;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;

import com.hncboy.chatgpt.front.framework.domain.entity.SysOperLog;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.front.framework.util.WebLogUtil;
import com.hncboy.chatgpt.front.mapper.SysOperLogMapper;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.hncboy.chatgpt.front.framework.util.WebLogUtil.transStringToMap;
import static com.hncboy.chatgpt.front.handler.config.WebSocketConfig.getIpAddr;

/**
 * @Auther: wqm
 * @Description API 日志记录
 */
@Aspect
@Component
public class WebLogAspect {
    private static Logger log = LoggerFactory.getLogger("WEB_LOG_ASPECT");
    private static final ThreadLocal<Long> TIME_THREADLOCAL = new NamedThreadLocal<Long>("Cost Time");
    @Autowired
    private SysOperLogMapper operLogMapper;

    @Pointcut("execution(public * com.hncboy.chatgpt..*Controller.*(..))")
    public void webLog(){
    }

    /**
     * 记录请求日志
     * @param joinPoint
     * @throws Throwable
     */
    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
        WebLogUtil.requestLog(joinPoint);
        TIME_THREADLOCAL.set(System.currentTimeMillis());
    }

    /**
     * 记录响应日志
     * @param ret
     */
    @AfterReturning(returning = "ret",pointcut = "webLog()")
    public void doAfterReturn(JoinPoint joinPoint,  Object ret) {
        long l = TIME_THREADLOCAL.get();
        WebLogUtil.responseLog(ret,l );
        handleLog(joinPoint,null,ret, l );
    }

    @AfterThrowing(pointcut = "webLog()",throwing = "ex")
    public void doAfterThrowing(JoinPoint joinPoint,Throwable ex) {
        log.error("异常信息:{}",ex);
        long l = TIME_THREADLOCAL.get();
        WebLogUtil.throwableLog(ex, l);
        handleLog(joinPoint, (Exception) ex,null, l );
    }


    public void handleLog(final JoinPoint joinPoint, final Exception e, Object jsonResult, long startTime)
    {
        try
        {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            // 获取当前的用户
            //LoginUser loginUser = SecurityUtils.getLoginUser();

            // *========数据库日志=========*//
            SysOperLog operLog = new SysOperLog();
            operLog.setStatus(0);
            // 请求的地址
            String ip="";
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                ip=getIpAddr(request);

                operLog.setOperIp(ip);
                operLog.setOperUrl(StringUtils.substring(request.getRequestURI(), 0, 255));
            }


            operLog.setOperName("zns");


            if (e != null)
            {
                operLog.setStatus(1);
                operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
            }
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            operLog.setMethod(className + "." + methodName + "()");
            // 设置请求方式
            operLog.setRequestMethod(attributes.getRequest().getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, operLog, jsonResult);
            // 设置消耗时间
            operLog.setCostTime(System.currentTimeMillis() - startTime);
            // 保存数据库
            operLogMapper.insertOperlog(operLog);
            //AsyncManager.me().execute(AsyncFactory.recordOper(operLog));
            log.info("操作日志记录成功{}",operLog);
        }
        catch (Exception exp)
        {
            // 记录本地异常日志
            log.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
    }


    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param operLog 操作日志
     * @throws Exception
     */
    public static void getControllerMethodDescription(JoinPoint joinPoint, SysOperLog operLog, Object jsonResult) throws Exception
    {
        // 设置action动作

        // 获取参数的信息，传入到数据库中。
        setRequestValue(joinPoint, operLog);
        // 是否需要保存response，参数和值
        operLog.setJsonResult(StringUtils.substring(JSON.toJSONString(jsonResult), 0, 2000));

    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private static void setRequestValue(JoinPoint joinPoint, SysOperLog operLog) throws Exception
    {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Object[] args =joinPoint.getArgs();
        List<Object> argList = new ArrayList<>();
        if (args != null) {
            for (Object item : args) {
                if (item instanceof ServletRequest) {
                    continue;
                }
                if (item instanceof ServletResponse) {
                    continue;
                }
                if (item instanceof MultipartFile){
                    continue;
                }
                argList.add(item);
            }
        }
        String param = JSON.toJSONString(argList);

        HttpServletRequest request = attributes.getRequest();
        String decode = "";
        if (request.getQueryString() != null) {
            try {
                decode = URLDecoder.decode(request.getQueryString(),"UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.error("WebLogAspect 编码转换错误",e);
            }
        } else {
            Map<String,String[]> formParams = request.getParameterMap();
            if (formParams != null) {
                for (String key : formParams.keySet()) {
                    String[] values = formParams.get(key);
                    for (int i = 0; i < values.length; i++) {
                        String value = values[i];
                        decode += key + "=" + value + "&";
                    }
                }
            }
        }
        Map<String,Object> methodParamMap = transStringToMap(decode,"&","=");
        String o = StrUtil.isBlank(decode) ? param : methodParamMap.toString();
        operLog.setOperParam(StringUtils.substring(o, 0, 2000));
    }


}