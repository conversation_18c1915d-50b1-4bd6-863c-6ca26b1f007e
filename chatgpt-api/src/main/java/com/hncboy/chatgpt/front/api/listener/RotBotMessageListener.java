//package com.hncboy.chatgpt.front.api.listener;
//
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.hncboy.chatgpt.base.domain.request.MqMsg;
//import com.hncboy.chatgpt.front.handler.wss.GPTSocketServer;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.spring.annotation.ConsumeMode;
//import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
//import org.apache.rocketmq.spring.core.RocketMQListener;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//@RocketMQMessageListener(topic = "super-ai-msg-topic", consumerGroup = "my-group",consumeMode = ConsumeMode.CONCURRENTLY)
//public class RotBotMessageListener implements RocketMQListener<String> {
//
//
//    @Override
//    public void onMessage(String mqMsg) {
////        log.info("MQ接收到消息:{}", mqMsg);
//        if (!JSONUtil.isTypeJSON(mqMsg)) {
//            return;
//        }
//        JSONObject jsonObject = JSONUtil.parseObj(mqMsg);
//        GPTSocketServer.sendWebsocketMessage(jsonObject.toString(), jsonObject.getStr("sender"));
//    }
//}
