package com.hncboy.chatgpt.front.framework.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * The type Alipay pay code vo.
 *
 * <AUTHOR>
 * @Date: 2024-07-04
 */
@Data
@Accessors(chain = true)
public class AlipayPayCodeVo implements Serializable {

    private String ordersId;

    private String productType;

    private String productName;

    private Date createdTime;

    private Double productPrice;

    private String qrCode;


}
