package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentAgentVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-3-25
 * 聊天室表实体类
 */
@Data
@TableName("chat_room")
public class ChatRoomDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 用户 id
     */
    private String openId;


    /**
     * ip
     */
    private String ip;


    /**
     * 房间图片
     */
    private String imageUrl;


    /**
     * 配置ID
     */
    private Integer roleId;


    /**
     * 房间名称
     */
    private String title;


    /**
     * 是否公开
     */
    private String open;


    /**
     * 系统回答
     */
    private String sysContent;


    /**
     * 房间简介
     */
    private String description;


    /**
     * 会话 ID
     */
    private String conversationId;


    /**
     * 类型
     */
    private String type;

    /**
     * id列表
     */
    @TableField(exist = false)
    private List<String> ids;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(title = "智能体信息")
    @TableField(exist = false)
    private IntelligentAgentVO agentVo;

}
