package com.hncboy.chatgpt.front.handler.mp.builder;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutImageMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutNewsMessage;

import java.util.List;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Slf4j
public class NewsBuilder extends AbstractBuilder {


    @Override
    public WxMpXmlOutMessage build(String content, WxMpXmlMessage wxMessage, WxMpService service) {
        return null;
    }

    /**
     * 图文消息  紧支持单条
     * @param item
     * @param wxMessage
     * @param service
     * @return
     */
    public WxMpXmlOutMessage build(WxMpXmlOutNewsMessage.Item item, WxMpXmlMessage wxMessage, WxMpService service) {

        WxMpXmlOutNewsMessage m = WxMpXmlOutMessage.NEWS()
                .addArticle(item)
                .fromUser(wxMessage.getToUser()).toUser(wxMessage.getFromUser())
                .build();
        return m;
    }
    /**
     * 客服接口 图文消息
     * @param article
     * @param userOpenId
     * @param wxMpService
     * @return
     */
    public void buildAsync(WxMpKefuMessage.WxArticle article, String userOpenId, WxMpService wxMpService) {
        if(StrUtil.isBlank(userOpenId)){
            log.error("发送客服消息错误:userOpenId为空");
            return;
        }
        WxMpKefuMessage m = WxMpKefuMessage.NEWS()
                .addArticle(article)
                .toUser(userOpenId)
                .build();
        try {
            wxMpService.getKefuService().sendKefuMessage(m);
        } catch (WxErrorException e) {
           log.error("发送客服消息错误:{}", e);
        }

    }
}
