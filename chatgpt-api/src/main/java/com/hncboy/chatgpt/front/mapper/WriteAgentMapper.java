package com.hncboy.chatgpt.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.front.framework.domain.entity.WriteAgent;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【write_agent(智能体信息)】的数据库操作Mapper
* @createDate 2024-06-27 21:33:40
* @Entity com.hncboy.chatgpt.front.framework.domain.entity.WriteAgent
*/
public interface WriteAgentMapper extends BaseMapper<WriteAgent> {
    /**
     * 更新应用使用次数
     *
     * @description 获取Tag及对应的Icon列表
     * @return void
     * @Author: 赵雨晨
     * @Date: 2024-07-12 17:08
     */
    class TagIcon {
        private String tag;
        private String icon;

        // Getters and Setters
        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public String getIcon() {
            return icon;
        }

    }

/*    @Select("SELECT * FROM (" +
            "SELECT wc.category_name AS tag, wc.icon_url AS icon, wc.sort_order AS sort_order " +
            "FROM write_category wc " +
            "UNION ALL " +
            "SELECT wa.tag, 'https://image.zjfdsr.com/qz/gmim4exwtdg3ldj9628j.png' AS icon, NULL AS sort_order " +
            "FROM write_agent wa " +
            "LEFT JOIN write_category wc ON wa.tag = wc.category_name " +
            "WHERE wc.category_name IS NULL AND wa.status = '0' AND wa.tag NOT LIKE '%|%' " +
            ") AS combined_tags " +
            "ORDER BY sort_order IS NULL, sort_order, tag")*/
    @Select("select * from ( " +
            "select wc.category_name as tag, wc.icon_url as icon, wc.sort_order as sort_order " +
            "from write_category wc where wc.status=0 and wc.start_time <= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') " +
            "union all select wa.tag, 'https://image.zjfdsr.com/qz/gmim4exwtdg3ldj9628j.png' as icon, null as sort_order " +
            "from write_agent wa left join (" +
            "select * from  write_category  " +
            "where status=0 and start_time <= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') and end_time >= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')) wc " +
            "on wa.tag = wc.category_name where " +
            "wc.category_name is null and wa.status = '0' and wa.tag not like '%|%' and wa.start_time <= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s') and wa.end_time >= DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')) as combined_tags " +
            "order by sort_order is null, sort_order, tag ")
    List<TagIcon> queryAllTag();







    /**
     * 更新应用使用次数
     *
     * @param id
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/5/16 14:51
     */
    @Update("update write_agent set use_cnt=use_cnt+1 where id=#{id}")
    void addUseCnt(@Param("id") Integer id);
}




