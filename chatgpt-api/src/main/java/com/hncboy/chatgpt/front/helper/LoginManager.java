package com.hncboy.chatgpt.front.helper;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hncboy.chatgpt.front.framework.domain.entity.*;
import com.hncboy.chatgpt.front.framework.domain.query.LoginInfoParam;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.enums.GroupsEnum;
import com.hncboy.chatgpt.front.framework.exception.AuthException;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.*;
import com.hncboy.chatgpt.front.mapper.AlOrdersMapper;
import com.hncboy.chatgpt.front.mapper.UserBaseInfoMapper;
import com.hncboy.chatgpt.front.service.*;
import com.hncboy.chatgpt.front.util.MsgUtil;
import com.hncboy.chatgpt.tarot.domain.entity.TarotDailyInsight;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;
import com.hncboy.chatgpt.tarot.domain.entity.UserCheckInRecord;
import com.hncboy.chatgpt.tarot.service.TarotDailyInsightService;
import com.hncboy.chatgpt.tarot.service.TarotReadingRecordService;
import com.hncboy.chatgpt.tarot.service.UserCheckInRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.*;

/**
 * zcWu
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LoginManager {

    private final RedisService redisService;
    //    private final SmspleHelper sampleHelper;
    private final UserBaseInfoService userBaseInfoService;
    private final UserBaseInfoMapper userBaseInfoMapper;
    private final SysConfigService sysConfigService;
    private final TarotDailyInsightService tarotDailyInsightService;
    private final TarotReadingRecordService tarotReadingRecordService;
    private final WxPayOrderService wxPayOrderService;
    private final UserPointsLogService userPointsLogService;
    private final UserMergeInfoService userMergeInfoService;
    private final UserCheckInRecordService userCheckInRecordService;
    private final AlOrdersMapper alOrdersMapper;
    private final ISysDictDataService sysDictDataService;


    /**
     * 短信登录
     *
     * @param loginInfoParam
     * @return UserBaseInfoVO
     * @Author: zc.wu
     * @Date: 2024/2/26 0026 下午 02:04
     */
    public UserBaseInfoVO smsLogin(LoginInfoParam loginInfoParam) {
        if (StringUtils.isEmpty(loginInfoParam.getIdCode())) {
            Object idCode = redisService.get(SMS_CODE_VALIDATE_KEY + loginInfoParam.getAccount());
            if (Objects.nonNull(idCode)) {
                loginInfoParam.setIdCode(String.valueOf(idCode));
            } else {
                //先获取验证码
                throw new ServiceException("验证码输入错误");
            }
        }
        //效验验证码
        String phone = checkSmsCode(loginInfoParam);
        loginInfoParam.setAccount(phone);
        UserBaseInfoVO userBaseInfoByAccount = userBaseInfoService.getUserBaseInfoByAccount(phone);
        // 首次登陆时给邀请人增加积分
        Integer parentNum = userBaseInfoByAccount.getParentId();
        if (Objects.isNull(userBaseInfoByAccount.getLoginTime()) &&
                Objects.nonNull(parentNum)) {
            userBaseInfoService.addUserPoints(parentNum, phone, "invite_points");
        }
        userBaseInfoService.updateUserLoginTime(userBaseInfoByAccount.getId());
        return userBaseInfoByAccount;
    }


    /**
     * 登录发送短信
     *
     * @param phone
     * @param parentId
     * @param flag
     * @param request
     * @return String
     * @Author: zc.wu
     * @Date: 2022/6/15 10:51
     */
    public String getSmsCode(String phone, String parentId, String userType, String ipAddress, boolean flag, HttpServletRequest request) {
        if (StringUtils.isEmpty(phone)) {
            throw new ServiceException("手机号输入错误");
        }

        //是否为手机号
        if (!PhoneFormatCheckUtils.isPhoneLegal(phone)) {
            throw new ServiceException("手机号输入错误");
        }

        if (flag) {
            addUser(phone, parentId, userType, ipAddress);
        }
        SysConfig testPhone = null;
        try {
            testPhone = sysConfigService.querySysConfig("test_phone");
        } catch (Exception e) {
            log.error("获取测试账号电话失败", e);
        }

        if (ObjectUtil.isNotNull(testPhone) && testPhone.getConfigValue().contains(phone)) {
            //测试手机号
            //6位随机数
            SysConfig verificationCode = sysConfigService.querySysConfig("verification_code");
            String simpleUUID = IdUtil.simpleUUID();
            //验证码
            redisService.set(simpleUUID, verificationCode.getConfigValue() + "——" + phone, TIME_OUT_900);
            //setRedisPhoneNum(phone, request);
            return simpleUUID;
        } else {
            //如果已经发送过短信 1分钟内有效
            if (redisService.exists(SMS_PHONE_KEY + phone)) {
                throw new ServiceException("请勿重复发送短信");
            }
            if (StringUtils.isEmpty(ipAddress)) {
                //IP效验
                if (!checkIpSendNum(request)) {
                    throw new ServiceException("IP发送次数上限");
                }
            } else {
                //IP效验
                if (!checkIpSendNum2(ipAddress)) {
                    throw new ServiceException("IP发送次数上限");
                }
            }
            //效验次数上限
            if (!checkPhoneSendNum(phone)) {
                throw new ServiceException("手机号发送次数上限");
            }

            //6位随机数
            String verificationCode = "";
            verificationCode = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
            //发送短信
//            boolean send = sampleHelper.sendHySms(phone, verificationCode);
            boolean send = MsgUtil.sendMsgDefault(phone, verificationCode);
            if (!send) {
                throw new ServiceException("短信发送失败，请联系管理员");
            }
            String simpleUUID = IdUtil.simpleUUID();
            //验证码
            redisService.set(simpleUUID, verificationCode + "——" + phone, TIME_OUT_900);
            //手机号防重复记录
            redisService.set(SMS_PHONE_KEY + phone, simpleUUID, TIME_OUT_60);
            redisService.set(SMS_CODE_VALIDATE_KEY + phone, simpleUUID, TIME_OUT_900);
            setRedisPhoneNum(phone, ipAddress, request);
            return simpleUUID;
        }

    }


    public String addUser(LoginInfoParam loginInfoParam) {
        addUser(loginInfoParam.getAccount(), null, loginInfoParam.getUserType(), loginInfoParam.getIpAddress());
        UserBaseInfoVO userBaseInfo = userBaseInfoService.getUserBaseInfoByAccount(loginInfoParam.getAccount());
        AlOrders alOrders = new AlOrders();
        alOrders.setUserId(userBaseInfo.getId().toString());
        AlOrders payHistory = alOrdersMapper.getPayHistory(alOrders);
        if (ObjectUtil.isNull(payHistory)) {
            return null;
        }

        boolean b = userBaseInfoService.checkUserIsVip(userBaseInfo);//wxUserInfoService.checkUserIsVip(userBaseInfo.getId().toString());
        if (!b) {
            //会员已过期
            return null;
        }
        List<SysDictData> sysDictData = sysDictDataService.selectDictLabel("chatoi_groups");
        HashMap<String, String> set = new HashMap<>();
        for (SysDictData sysDictDatum : sysDictData) {
            set.put(sysDictDatum.getDictLabel(), sysDictDatum.getDictValue());
        }
        if (payHistory.getDays() > 0 && payHistory.getDays() <= 30) {
            return set.get(GroupsEnum.ONE_MONTH.getCode());
        } else if (payHistory.getDays() == 90) {
            return set.get(GroupsEnum.THREE_MONTHS.getCode());
        } else if (payHistory.getDays() == 180) {
            return set.get(GroupsEnum.SIX_MONTHS.getCode());
        } else {
            return set.get(GroupsEnum.ONE_YEAR.getCode());
        }

    }

    private void addUser(String phone, String parentId, String userType, String ipAddress) {
        //判断手机号是否存在于系统
        LoginInfoParam loginInfoParam = new LoginInfoParam();
        loginInfoParam.setAccount(phone);
        loginInfoParam.setUserType(userType);
        loginInfoParam.setIpAddress(ipAddress);
        UserBaseInfoVO userBaseInfo = userBaseInfoService.getUserBaseInfoByAccount(phone);
        if (Objects.isNull(userBaseInfo)) {
            //注册用户
            if (StringUtils.isNotEmpty(parentId)) {
                //是被邀请用户
                loginInfoParam.setParentId(parentId);
            }
            userBaseInfoService.initUserInfo(loginInfoParam);
        }
    }

    /**
     * 绑定手机号
     *
     * @param loginInfoParam
     * @return String
     */
    public UserBaseInfoVO bindPhone(LoginInfoParam loginInfoParam) {
        // 当前登陆的用户信息
        UserBaseInfoVO userBaseInfo = userBaseInfoService.queryUserInfoById(CurrentUserUtil.getV2UserId());
        if (Objects.isNull(userBaseInfo)) {
            throw new AuthException("请登录后再试");
        }
        if (StringUtils.isEmpty(loginInfoParam.getIdCode())) {
            Object idCode = redisService.get(SMS_CODE_VALIDATE_KEY + loginInfoParam.getAccount());
            if (Objects.nonNull(idCode)) {
                loginInfoParam.setIdCode(String.valueOf(idCode));
            } else {
                //先获取验证码
                throw new ServiceException("验证码输入错误");
            }
        }
        // 效验验证码
        String phone = checkSmsCode(loginInfoParam);
        loginInfoParam.setAccount(phone);
        // 手机号的账户信息
        UserBaseInfoVO userBaseInfoByAccount = userBaseInfoService.getUserBaseInfoByAccount(phone);
        if (Objects.nonNull(userBaseInfoByAccount) && !Objects.equals(userBaseInfoByAccount.getId(), CurrentUserUtil.getV2UserId())) {
            if (userBaseInfo.getAccount().startsWith("wx_")) {
                UserBaseInfo userInfo = new UserBaseInfo();
                if (userBaseInfo.getUserType().equals("tarot")) {
                    //塔罗用户与智能社用户合并
                    userInfo.setTarotCoins(userBaseInfo.getTarotCoins());
                    tarotDailyInsightService.update(new UpdateWrapper<TarotDailyInsight>()
                            .set("user_id", userBaseInfoByAccount.getId())
                            .eq("user_id", userBaseInfo.getId()));
                    tarotReadingRecordService.update(new UpdateWrapper<TarotReadingRecord>()
                            .set("user_id", userBaseInfoByAccount.getId())
                            .eq("user_id", userBaseInfo.getId()));
                    wxPayOrderService.update(new UpdateWrapper<WxPayOrder>()
                            .set("user_id", userBaseInfoByAccount.getId())
                            .eq("user_id", userBaseInfo.getId()));
                    userPointsLogService.update(new UpdateWrapper<UserPointsLog>()
                            .set("user_id", userBaseInfoByAccount.getId())
                            .eq("user_id", userBaseInfo.getId()));
                    userPointsLogService.update(new UpdateWrapper<UserPointsLog>()
                            .set("rel_order", userBaseInfoByAccount.getAccount())
                            .eq("rel_order", userBaseInfo.getAccount()));
                    userCheckInRecordService.update(new UpdateWrapper<UserCheckInRecord>()
                            .set("user_id", userBaseInfoByAccount.getId())
                            .eq("user_id", userBaseInfo.getId()));


                    //throw new RuntimeException("该微信号已绑定其他账号");
                    UserMergeInfo userMergeInfo = new UserMergeInfo();
                    userMergeInfo.setMergeId(userBaseInfoByAccount.getId().toString());
                    userMergeInfo.setWasMergedId(userBaseInfo.getId().toString());
                    userMergeInfoService.save(userMergeInfo);

                }

                String openId = userBaseInfo.getOpenId();
                // 要删除微信登陆用户的账号
                log.info("删除微信登陆用户的账号");
                userBaseInfoMapper.deleteById(CurrentUserUtil.getV2UserId());
                // 更新手机账号下的openId

                userInfo.setId(userBaseInfoByAccount.getId());
                userInfo.setOpenId(openId);
                userBaseInfoByAccount.setOpenId(openId);
                if (userBaseInfo.getNickName() != null) {
                    userInfo.setNickName(userBaseInfo.getNickName());
                    userBaseInfoByAccount.setNickName(userBaseInfo.getNickName());
                }
                if (userBaseInfo.getHeadSculpture() != null) {
                    userInfo.setHeadSculpture(userBaseInfo.getHeadSculpture());
                    userBaseInfoByAccount.setHeadSculpture(userBaseInfo.getHeadSculpture());
                }
                userInfo.setUpdateTime(new Date());
                //userInfo.setUserType("zns");

                userBaseInfoMapper.updateById(userInfo);
                return userBaseInfoByAccount;
            } else {
                throw new RuntimeException("该手机号已绑定其他账号");
            }
        } else {
            // 更新当前用户的账号为手机号
            userBaseInfoService.updateUserAccountById(CurrentUserUtil.getV2UserId(), phone);
            // 新用户绑定手机号时，才给邀请人增加积分
            if (userBaseInfo.getParentId() != null) {
                userBaseInfoService.addUserPoints(userBaseInfo.getParentId(), phone, "invite_points");
            }
            return userBaseInfo;
        }
    }

    /**
     * 根据手机号效验验证码发送次数
     *
     * @param phone
     * @return boolean
     * @Author: zc.wu
     * @Date: 2022/6/15 10:11
     */
    private boolean checkPhoneSendNum(String phone) {
        String redisKey = SMS_PHONE_NUM_KEY + phone;
        if (!redisService.exists(redisKey)) {
            return true;
        }
        int num = (int) redisService.get(redisKey);
        log.info("根据手机号效验验证码发送次数:手机号:{},当日发送短信次数:{}", phone, num);
        if (num >= CHECK_PHONE_NUM) {
            return false;
        }
        return true;
    }

    /**
     * 根据IP效验短信发送次数
     *
     * @return boolean
     * @Author: zc.wu
     * @Date: 2022/6/15 11:14
     */
    private boolean checkIpSendNum(HttpServletRequest request) {
        String redisKey = SMS_IP_NUM_KEY + WebUtil.getIp();
        if (!redisService.exists(redisKey)) {
            return true;
        }
        int num = (int) redisService.get(redisKey);
        log.info("根据IP效验短信发送次数:IP:{},当日发生短信次数:{}", WebUtil.getIp(), num);
        if (num >= CHECK_IP_NUM) {
            return false;
        }
        return true;
    }


    /**
     * 根据IP效验短信发送次数
     *
     * @return boolean
     * @Author: zc.wu
     * @Date: 2022/6/15 11:14
     */
    private boolean checkIpSendNum2(String ipAddress) {
        String redisKey = SMS_IP_NUM_KEY + ipAddress;
        if (!redisService.exists(redisKey)) {
            return true;
        }
        int num = (int) redisService.get(redisKey);
        log.info("根据IP效验短信发送次数:IP:{},当日发生短信次数:{}", ipAddress, num);
        if (num >= CHECK_IP_NUM) {
            return false;
        }
        return true;
    }


    /**
     * 手机验证吗发送次数，只记录当天的剩余时间
     *
     * @param phone
     * @return void
     * @Author: zc.wu
     * @Date: 2022/6/15 11:13
     */
    private void setRedisPhoneNum(String phone, String ipAdders, HttpServletRequest request) {
        String redisKey = SMS_PHONE_NUM_KEY + phone;
        String ipKey = SMS_IP_NUM_KEY + (StringUtils.isNotEmpty(ipAdders) ? ipAdders : WebUtil.getIp());
        if (!redisService.exists(ipKey)) {
            redisService.set(ipKey, NUMBER_ONE, DateUtils.getLastSeconds());
        } else {
            int numIp = (int) redisService.get(ipKey);
            redisService.set(ipKey, numIp + NUMBER_ONE, DateUtils.getLastSeconds());
        }
        if (!redisService.exists(redisKey)) {
            redisService.set(redisKey, NUMBER_ONE, DateUtils.getLastSeconds());
        } else {
            int num = (int) redisService.get(redisKey);
            redisService.set(redisKey, num + NUMBER_ONE, DateUtils.getLastSeconds());
        }
    }


    /**
     * 登录验证短信验证码
     *
     * @param loginInfoParam
     * @return String
     * @Author: zc.wu
     * @Date: 2022/6/15 11:06
     */
    private String checkSmsCode(LoginInfoParam loginInfoParam) {
        //短信验证码是否存在
        if (!redisService.exists(loginInfoParam.getIdCode())) {
            throw new ServiceException("验证失败，请重新发送");
        }
        String verificationCode = (String) redisService.get(loginInfoParam.getIdCode());
        if (!loginInfoParam.getVerificationCode().equals(verificationCode.split("——")[0])) {
            throw new ServiceException("验证码输入错误");
        }
        String phone = verificationCode.split("——")[1];
        if (!loginInfoParam.getAccount().equals(phone)) {
            throw new ServiceException("验证码输入错误");
        }
        //删除缓存
        redisService.remove(loginInfoParam.getIdCode());
        return phone;
    }
}
