package com.hncboy.chatgpt.front.api.apikey;

import cn.hutool.extra.spring.SpringUtil;
import com.hncboy.chatgpt.front.framework.config.ChatConfig;
import com.unfbx.chatgpt.OpenAiClient;
import com.unfbx.chatgpt.OpenAiStreamClient;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;

import java.net.Proxy;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/3/24 16:09 ApiKey 聊天 Client 构建者
 */
@UtilityClass
@Slf4j
public class ApiKeyChatClientBuilder {

    /**
     * 构建 API 流式请求客户端
     *
     * @return OpenAiStreamClient
     */
    public OpenAiStreamClient buildOpenAiStreamClient(ChatConfig chatConfig,String model) {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
        String apiHost = chatConfig.getOpenaiApiBaseUrl();
        String openaiApiKey = chatConfig.getOpenaiApiKey();
        if (model.startsWith("gpt-4-gizmo")) {
            apiHost = chatConfig.getGptsOpenaiApiBaseUrl();
            openaiApiKey = chatConfig.getGptsOpenaiApiKey();
        } else if (model.startsWith("gpt-4-all")) {
            apiHost = chatConfig.getGpt4allOpenaiApiBaseUrl();
            openaiApiKey = chatConfig.getGpt4allOpenaiApiKey();
        } else if (model.startsWith("gpt-4")) {
            apiHost = chatConfig.getGpt4OpenaiApiBaseUrl();
            openaiApiKey = chatConfig.getGpt4OpenaiApiKey();
        } else if (model.startsWith("gpt-3.5")) {
            apiHost = chatConfig.getGpt35OpenaiApiBaseUrl();
            openaiApiKey = chatConfig.getGpt35OpenaiApiKey();
        }
        log.info("=== current model:   {}", model);
        log.info("=== current hostUrl: {}", apiHost);
        return OpenAiStreamClient.builder().okHttpClient(okHttpClient)
                .apiKey(Arrays.asList(openaiApiKey))
                .apiHost(apiHost).build();
    }

    /**
     * 构建 API 请求客户端
     *
     * @return OpenAiClient
     */
    public OpenAiClient buildOpenAiClient() {
        ChatConfig chatConfig = SpringUtil.getBean(ChatConfig.class);
        OkHttpClient okHttpClient = new OkHttpClient
                .Builder()
//                .proxy(getProxy())
//                .addInterceptor(new OpenAiResponseInterceptor())
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
        return OpenAiClient.builder()
                //支持多key传入，请求时候随机选择
                .apiKey(Arrays.asList(chatConfig.getOpenaiApiKey()))
                .apiHost(chatConfig.getOpenaiApiBaseUrl())
                .okHttpClient(okHttpClient)
                .build();
    }

    /**
     * 获取 Proxy
     *
     * @return Proxy
     */
    private Proxy getProxy() {
        ChatConfig chatConfig = SpringUtil.getBean(ChatConfig.class);
        Proxy proxy = Proxy.NO_PROXY;
//        if (StrUtil.isNotBlank(chatConfig.getHttpProxyHost())) {
//            proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(chatConfig.getHttpProxyHost()
//                    , chatConfig.getHttpProxyPort()));
//        }
        return proxy;
    }
}
