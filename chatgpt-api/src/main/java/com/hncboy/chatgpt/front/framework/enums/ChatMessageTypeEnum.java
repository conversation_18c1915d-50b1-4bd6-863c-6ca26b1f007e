package com.hncboy.chatgpt.front.framework.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/3/26 01:53
 * 聊天消息类型枚举
 */
@AllArgsConstructor
public enum ChatMessageTypeEnum {

    /**
     * 问题
     */
    QUESTION(1),

    /**
     * 回答
     */
    ANSWER(2),

    /**
     * 识别图片
     */
    IDENTIFY_IMAGES(3),

    /**
     * 生成图片
     */
    GENERATE_IMAGES(4);

    @Getter
    @EnumValue
    private final Integer code;
}
