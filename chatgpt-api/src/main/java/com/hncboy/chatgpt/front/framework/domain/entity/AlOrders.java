package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hncboy.chatgpt.tarot.i18n.I18nStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * 充值记录
 *
 * @Version: v1.0.0
 * @Author: zyc
 * @Date: 2024/7/4
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("al_orders")
public class AlOrders implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @TableId(value = "orders_id", type = IdType.AUTO)
    private String ordersId;


    /**
     * 用户ID
     */
    private String userId;

    /**
     * 产品ID
     */
    private BigInteger productId;

    /**
     * 产品类型
     */
    private String productType;


    /**
     * 产品名称
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String productName;


    /**
     * 产品价格
     */
    private Double productPrice;


    /**
     * 数量
     */
    private BigInteger num;


    /**
     * 单位
     */
    private String unit;


    /**
     * 支付状态
     */
    private Integer state;


    /**
     * 支付时间
     */
    private LocalDateTime payTime;


    /**
     * 支付失败原因
     */
    private String reasonFailure;


    /**
     * 过期时间
     */
    private LocalDateTime expiresTime;


    /**
     * 创建时间
     */
    private LocalDateTime createdTime;


    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


    @TableField(exist = false)
    private String Type;
    @TableField(exist = false)
    private String openid;
    @TableField(exist = false)
    private String tradeType;
    @TableField(exist = false)
    private int days;


}
