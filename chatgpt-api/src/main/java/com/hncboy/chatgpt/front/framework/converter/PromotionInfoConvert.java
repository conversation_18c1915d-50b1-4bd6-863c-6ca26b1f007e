package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.dto.PromotionInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.PromotionInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.PromotionInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 促销活动信息 领域对象转换器
 * @Version: v1.0.0
 * @Author: zzd
 * @Date: 2024/6/27 14:06
 */
@Mapper
public interface PromotionInfoConvert {

   PromotionInfoConvert INSTANCE = Mappers.getMapper(PromotionInfoConvert.class);

  /**
   * PromotionInfoDTO 转 PromotionInfo
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  PromotionInfo dtoToEntity(PromotionInfoDTO dto);

  /**
   * PromotionInfo 转 PromotionInfoVO
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  PromotionInfoVO entityToVO(PromotionInfo entity);

  /**
   * List<PromotionInfo> 转 List<PromotionInfoVO>
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  List<PromotionInfoVO> entityListToVOList(List<PromotionInfo> entityList);

  /**
   * 查询DTO转换
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
   PromotionInfo queryDtoToEntity(PromotionInfoDTO dto);


}
