package com.hncboy.chatgpt.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.front.framework.domain.entity.WxUserInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 微信用户信息 Mapper
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/7
 */
@Mapper
public interface WxUserInfoMapper extends BaseMapper<WxUserInfo> {

    /**
     * 更新可用次数
     *
     * @param openId
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:51
     */
    @Update("update wx_user_info set apply_num=apply_num-#{species} where open_id=#{openId}")
    void updateUserNumMp(@Param("openId") String openId, @Param("species") Integer species);


    /**
     * 更新可用次数
     *
     * @param openId
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:51
     */
    @Update("update wx_user_info set apply_num=apply_num+#{species} where open_id=#{openId}")
    void addUserNumMp(@Param("openId") String openId, @Param("species") Integer species);


}
