package com.hncboy.chatgpt.front.api.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hncboy.chatgpt.front.framework.domain.vo.ChatReplyMessageVO;
import com.hncboy.chatgpt.front.framework.domain.vo.DifyChatCompleteResponse;
import com.hncboy.chatgpt.front.framework.domain.vo.OnComplateResVO;
import com.hncboy.chatgpt.front.framework.handler.response.ResultCode;
import com.unfbx.chatgpt.entity.chat.ChatChoice;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import com.unfbx.chatgpt.entity.chat.Message;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;
import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.appError;

import java.io.IOException;
import java.util.Date;
import java.util.Objects;
import java.util.function.Consumer;

import static org.ietf.jgss.GSSException.FAILURE;

@Slf4j
public class DifySSEEventSourceListener extends EventSourceListener{

    private long tokens;
    private long initTime;
    private long startTime;
    private long endTime;

    protected OnComplateResVO onComplateResVO = new OnComplateResVO();
    protected String lastMessage = "";
    /**
     * C当收到所有新消息时调用。
     *
     * @param message the new message
     */
    @Setter
    @Getter
    protected Consumer<OnComplateResVO> onComplate = s -> {

    };

    private ResponseBodyEmitter sseEmitter;

    public DifySSEEventSourceListener(ResponseBodyEmitter sseEmitter) {
        initTime = System.currentTimeMillis();
        this.sseEmitter = sseEmitter;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void onOpen(EventSource eventSource, Response response) {
        startTime = System.currentTimeMillis();
        log.info("Dify建立sse连接...耗时: [{}]", startTime - initTime);
    }

    /**
     * {@inheritDoc}
     */
    @SneakyThrows
    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        log.info("Dify返回数据：{}", data);
        if (!JSONUtil.isTypeJSON(data)) {
            return;
        }
        DifyChatCompleteResponse difyResponseVO = JSONUtil.toBean(data, DifyChatCompleteResponse.class);
        String str;
        ChatCompletionResponse completionResponse = JSONUtil.toBean(data, ChatCompletionResponse.class);
        int index;
        if (onComplateResVO.getCnt() == null) {
            onComplateResVO.setFirstCharTime(new Date());
            index = 0;
            onComplateResVO.setCnt(0);
        } else {
            index = onComplateResVO.getCnt() + 1;
            onComplateResVO.setCnt(index);
        }
        if (("agent_message".equals(difyResponseVO.getEvent()) || "message".equals(difyResponseVO.getEvent())) &&
                difyResponseVO.getAnswer() != null) {
            // 文本消息处理
            str = difyResponseVO.getAnswer();
            buildBody(str, completionResponse, index);
        } else if ("message_file".equals(difyResponseVO.getEvent()) && StringUtil.isNotBlank(difyResponseVO.getUrl())) {
            // 文件处理
            String url;
            if (difyResponseVO.getUrl().startsWith("http")) {
                url = difyResponseVO.getUrl();
            } else {
                url = "http://fdsr.tpddns.cn:8089" + difyResponseVO.getUrl();
            }
            String fileType = null;
            if ("image".equals(difyResponseVO.getType())) {
                fileType = "image";
            }
            str = "\n![" + fileType + "1](" + url + ")\n" + "\n\n[下载1](" + url + ")\n\n";
            buildBody(str, completionResponse, index);
        } else if ("error".equals(difyResponseVO.getEvent())) {
            String msg = appError;
            String errMsg = difyResponseVO.getMessage();
            log.error("=== Dify  sse连接异常data：[{}] ===", errMsg);if (StrUtil.isNotBlank(lastMessage)) {
                onComplateResVO.setMessage(lastMessage);
            }
            onComplateResVO.setResponse(errMsg);
            onComplateResVO.setError(msg);
            onComplate.accept(onComplateResVO);
            sseEmitter.send(ChatReplyMessageVO.buildFailureMessage(msg, ResultCode.FAILURE.getCode()));
            sseEmitter.complete();
        } else if ("message_end".equals(difyResponseVO.getEvent())) {
            log.info("Dify返回数据结束了, 耗时: [{}]", System.currentTimeMillis() - startTime);
            // 模拟OpenAI的结束标志
            sseEmitter.send(("data: [DONE]" + "\n\n"));
            if (StrUtil.isNotBlank(lastMessage)) {
                onComplateResVO.setMessage(lastMessage);
            }
            if (StrUtil.isNotBlank(difyResponseVO.getConversation_id())) {
                onComplateResVO.setConversationId(difyResponseVO.getConversation_id());
            }
            onComplate.accept(onComplateResVO);
            // 传输完成后自动关闭sse
            sseEmitter.complete();
        }
    }

    private void buildBody(String str, ChatCompletionResponse completionResponse, int index) throws IOException {
        ChatChoice choice = new ChatChoice();
        Message message = new Message();
        message.setContent(str);
        choice.setDelta(message);
        choice.setFinishReason(null);
        choice.setIndex(index);
        completionResponse.setChoices(CollUtil.toList(choice));
        String delta = JSONUtil.toJsonStr(completionResponse);
        delta = delta.replace("\"text\":", "\"delta\":");
        lastMessage = lastMessage + str;
        log.info("data: {}", delta);
        sseEmitter.send(("data: " + delta + "\n\n"));
    }

    @Override
    public void onClosed(EventSource eventSource) {
        endTime = System.currentTimeMillis();
        log.info("流式输出返回值总共{}tokens", tokens() - 2);
        log.info("Dify关闭sse连接...耗时: [{}]", endTime - initTime);
    }

    @SneakyThrows
    @Override
    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        long endTime = System.currentTimeMillis();
        String msg;
        String errorMsg = "";
        if (Objects.isNull(response)) {
            log.error("=== Dify  sse连接异常 ===", t);
            msg = "发送异常，请重试！";
            if (t != null) {
                errorMsg = "Exception:[" + t.getMessage() + "]";
            }
        } else {
            msg = appError;
            ResponseBody body = response.body();
            if (Objects.nonNull(body)) {
                errorMsg = body.string();
                log.error("=== Dify  sse连接异常data：[{}]，异常： ===", errorMsg, t);
            } else {
                log.error("=== Dify  sse连接异常data：[{}]，异常： ===", response, t);
            }
            log.error("=== Dify  sse连接异常End ===");
        }
        if (t != null && "ResponseBodyEmitter has already completed".equals(t.getMessage())) {
            Long finalTimes = endTime - initTime;
            String timeStr = String.format("耗时: [" + endTime + "-" + startTime + "-" + initTime + "->" + finalTimes + "]");
            if (finalTimes.compareTo(Objects.requireNonNull(sseEmitter.getTimeout())) < 0) {
                msg = "手工终止";
            } else {
                msg = "接收超时";
            }
            log.error("{}, {}", msg, timeStr);
        }
        onComplateResVO.setMessage(lastMessage);
        onComplateResVO.setT(t);
        onComplateResVO.setError(msg);
        onComplateResVO.setResponse(errorMsg);
        onComplate.accept(onComplateResVO);
        sseEmitter.send(ChatReplyMessageVO.buildFailureMessage(msg, FAILURE));
        sseEmitter.complete();
        eventSource.cancel();
    }

    /**
     * tokens
     *
     * @return
     */
    public long tokens() {
        return tokens;
    }
}
