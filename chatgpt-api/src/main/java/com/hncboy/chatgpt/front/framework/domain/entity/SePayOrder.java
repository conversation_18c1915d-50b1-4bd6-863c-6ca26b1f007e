package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hncboy.chatgpt.tarot.i18n.I18nStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付订单信息 ENTITY
 *
 * @Version：v1.0.0
 * @Author: shy
 * @Date:2025/4/14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("se_pay_order")
@Accessors(chain = true)
public class SePayOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 商品类型
     */
    private String productType;

    /**
     * 单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)
     */
    private String unit;

    /**
     * 数量
     */
    private Long num;

    /**
     * 商品描述
     */
    @JsonSerialize(using = I18nStringSerializer.class)
    private String body;

    /**
     * 商户订单号
     */
    private String orderNo;

    /**
     * 唯一ID
     */
    private String uniqueId;

    /**
     * 转账金额
     */
    private Double amount;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 银行账号
     */
    private String accountNumber;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 生成的QR码URL
     */
    private String qrCodeUrl;

    /**
     * 状态
     */
    private Integer status;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 银行网关名称
     */
    private String gateway;

    /**
     * 交易时间
     */
    private String transactionDate;

    /**
     * 付款代码
     */
    private String code;

    /**
     * 转账内容
     */
    private String content;

    /**
     * 交易类型：进或出
     */
    private String transferType;

    /**
     * 交易金额
     */
    private Double transferAmount;

    /**
     * 累计账户余额
     */
    private Double accumulated;

    /**
     * 子账户
     */
    private String subAccount;

    /**
     * 参考代码
     */
    private String referenceCode;

    /**
     * 内容
     */
    private String description;

    /**
     * 支付完成时间
     */
    private Date timeEnd;

    /**
     * 过期时间
     */
    private Date expiresTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String sePayId;
}