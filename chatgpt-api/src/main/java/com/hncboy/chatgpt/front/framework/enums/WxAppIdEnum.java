package com.hncboy.chatgpt.front.framework.enums;


import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 微信公众号 AppId 枚举
 */
@Getter
@AllArgsConstructor
public enum WxAppIdEnum {

    /**
     * 方顶-服务号
     */
    zns("wx734d3d8a6888e18f"),


    /**
     * 方顶-订阅号
     */
    tarot("wx8aa8b47c44e06a98"),

    /**
     * 塔罗-订阅号
     */
    tarot_main("wx94dd418a44321f70"),

    /**
     * 月信塔罗-订阅号
     */
    tarot_yuexin("wx7360397aa6007725");

    @EnumValue
    private final String code;
}
