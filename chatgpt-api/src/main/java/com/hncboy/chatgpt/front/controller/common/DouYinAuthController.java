package com.hncboy.chatgpt.front.controller.common;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.constant.ByteDanceUrlConstants;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.helper.RestTemplateUtil;
import com.hncboy.chatgpt.front.service.WxPayOrderService;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * zcWu
 */
@AllArgsConstructor
@Tag(name = "抖音授权登录")
@RestController
@RequestMapping("/dy")
@Slf4j
public class DouYinAuthController {

    @Autowired
    private RestTemplateUtil restTemplateUtil;

    @Autowired
    private WxUserInfoService wxUserInfoService;

    @Autowired
    private WxPayOrderService wxPayOrderService;


    @Operation(summary = "抖音小程序登录")
    @PostMapping("/loginBind")
    @ResponseBody
    public R loginBind(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        String code = jsonObject.getStr("code");
        if (null == code) {
            return R.fail("code丢失");
        }
        JSONObject requestObject = new JSONObject();
        requestObject.put("appid", "tt46bc3001d98088b001");
        requestObject.put("secret", "1a0d3cc40a2312b8a66cf727fdaa650d73de48e6");
        requestObject.put("code", code);
        requestObject.put("anonymous_code", "");
        String result = restTemplateUtil.byteDancePostRequest(requestObject, ByteDanceUrlConstants.CODE_2_SESSION);
        if (StringUtils.isNotBlank(result)) {
            JSONObject resultObj = JSONUtil.parseObj(result);
            String err_no = resultObj.getStr("err_no");
            if (null != err_no && "0".equals(err_no)) {
                JSONObject jsonData = resultObj.getJSONObject("data");
                String session_key = jsonData.getStr("session_key");
                String openid = jsonData.getStr("openid");
                String unionid = jsonData.getStr("unionid");
                //处理自己的业务逻辑
                jsonObject.put("openid", openid);
                jsonObject.put("unionid", unionid);
                WxUserInfoVO wxUserInfoVO = wxUserInfoService.saveOrUpdateDyUser(jsonObject);
                StpUtil.login(wxUserInfoVO.getOpenId());
                //会话相关
                StpUtil.getSession().set(ApplicationConstant.USER, wxUserInfoVO);
                wxUserInfoVO.setToken(StpUtil.getTokenValue());
                return R.data(wxUserInfoVO);
            } else {
                return R.fail("解析错误[" + err_no + "]");
            }
        } else {
            return R.fail("解析异常请重试");
        }
    }


    @Operation(summary = "生成抖音支付订单")
    @GetMapping("/createOrder")
    public R<Object> createDouYinOrder(Integer goodsId) {
        try {
            String result = restTemplateUtil.byteDancePostRequest(JSONUtil.parseObj(wxPayOrderService.createDyOrderPay(goodsId)),
                    ByteDanceUrlConstants.CREATE_ORDER);
            if (StringUtils.isNotBlank(result)) {
                JSONObject resultObj = JSONUtil.parseObj(result);
                String err_no = resultObj.getStr("err_no");
                if (null != err_no && "0".equals(err_no)) {
                    JSONObject jsonData = resultObj.getJSONObject("data");
                    return R.data(jsonData);
                } else {
                    return R.fail("订单错误[" + err_no + "]");
                }
            } else {
                return R.fail("订单异常请重试");
            }
        } catch (Exception e) {
            log.error("生成支付订单异常", e);
            return R.fail("生成支付订单异常");
        }
    }


}
