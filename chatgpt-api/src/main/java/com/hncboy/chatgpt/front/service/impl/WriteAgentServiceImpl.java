package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.converter.WriteAgentConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.WriteAgentDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.WriteAgent;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteAgentVO;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.mapper.WriteAgentMapper;
import com.hncboy.chatgpt.front.service.WriteAgentService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【write_agent(写作应用)】的数据库操作Service实现
* @createDate 2024-06-27 21:33:40
*/
@Service
public class WriteAgentServiceImpl extends ServiceImpl<WriteAgentMapper, WriteAgent>
    implements WriteAgentService{


    /**
     * 查询写作应用列表
     *
     * @param dto
     * @return: IPage<WriteAgentVO>
     * @Author: wZhic
     * @Date: 2024/2/28
     */
    @Override
    public List<WriteAgentVO> queryListEntity(WriteAgentDTO dto) {
        LambdaQueryWrapper<WriteAgent> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(dto.getTitle()), WriteAgent::getTitle, dto.getTitle());
        //默认查询启用的
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        wrapper.le(WriteAgent::getStartTime, format);
        wrapper.gt(WriteAgent::getEndTime, format);
        
        wrapper.eq(WriteAgent::getStatus, 0);
        wrapper.orderByDesc(WriteAgent::getHot);
        wrapper.orderByDesc(WriteAgent::getUseCnt);
        List<WriteAgent> agentList = list(wrapper);
        // 创建一个新的列表来存储修改后的记录
        List<WriteAgent> modifiedAgentList = new ArrayList<>();

        // 遍历原始列表
        for (WriteAgent agent : agentList) {
            // 获取 tag 字段
            String tag = agent.getTag();
            if (tag != null && tag.contains("|")) {
                // 拆分 tag 字段
                String[] tags = tag.split("\\|");
                for (String singleTag : tags) {
                    // 创建新的 WriteAgent 对象，并复制原始对象的属性
                    WriteAgent newAgent = new WriteAgent();
                    newAgent.setId(agent.getId()); // 假设需要保持原始 ID，可以根据需要调整
                    newAgent.setTitle(agent.getTitle());
                    newAgent.setImgUrl(agent.getImgUrl());
                    newAgent.setStatus(agent.getStatus());
                    newAgent.setHot(agent.getHot());
                    newAgent.setUseCnt(agent.getUseCnt());
                    newAgent.setTag(singleTag.trim()); // 使用拆分后的单个 tag
                    // 添加到新的列表中
                    modifiedAgentList.add(newAgent);
                }
            } else {
                // 如果不包含 |，直接添加到新的列表中
                modifiedAgentList.add(agent);
            }
        }
        return WriteAgentConvert.INSTANCE.entityListToVOList(modifiedAgentList);
    }

    /**
     * 分页查询智能体信息列表
     *
     * @param dto
     * @return: IPage<WriteAgentVO>
     * @Author: wZhic
     * @Date: 2024/2/28
     */
    @Override
    public IPage<WriteAgentVO> queryListEntityPage(WriteAgentDTO dto) {
        LambdaQueryWrapper<WriteAgent> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotEmpty(dto.getTag()), WriteAgent::getTag, dto.getTag());
        wrapper.like(StrUtil.isNotEmpty(dto.getTitle()), WriteAgent::getTitle, dto.getTitle());
        wrapper.eq(Objects.nonNull(dto.getHot()), WriteAgent::getHot, dto.getHot());
        wrapper.eq(Objects.nonNull(dto.getCharge()), WriteAgent::getCharge, dto.getCharge());
        wrapper.eq(Objects.nonNull(dto.getCreateBy()), WriteAgent::getCreateBy, CurrentUserUtil.getUserId());
        //默认查询启用的
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        wrapper.le(WriteAgent::getStartTime, format);
        wrapper.gt(WriteAgent::getEndTime, format);
        
        wrapper.eq(WriteAgent::getStatus, 0);
        wrapper.orderByDesc(WriteAgent::getHot);
        wrapper.orderByDesc(WriteAgent::getUseCnt);
        IPage<WriteAgent> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<WriteAgent> iPage = this.page(userPage, wrapper);
        IPage<WriteAgentVO> convert = iPage.convert(WriteAgentConvert.INSTANCE::entityToVO);
        return convert;
    }

    /**
     * 查询写作应用分类列表
     *
     * @return List<String>
     * @Author: zzd
     * @Date: 2024/6/27 14:06
     */
    @Override
    public List<String> queryTagList() {
        List<WriteAgentMapper.TagIcon> TagIconlist = this.baseMapper.queryAllTag();
        List<String> list = TagIconlist.stream().map(WriteAgentMapper.TagIcon::getTag).collect(Collectors.toList());
        List<String> tempList = new ArrayList<>();
        list.stream().filter(Objects::nonNull).forEach(item -> {
            if (item.contains(";")) {
                String[] array  = item.split(";");
                tempList.addAll(Arrays.asList(array));
            } else {
                tempList.add(item);
            }
        });
        return tempList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 查询写作应用信息
     *
     * @return WriteAgentVO
     * @Author: zzd
     * @Date: 2024/7/2
     */
    @Override
    public WriteAgentVO getWriteAgentInfoById(Integer id) {
        WriteAgent writeAgent = this.getById(id);
        return WriteAgentConvert.INSTANCE.entityToVO(writeAgent);
    }

    @Override
    public void addUseCnt(Integer id) {
        this.baseMapper.addUseCnt(id);
    }
}




