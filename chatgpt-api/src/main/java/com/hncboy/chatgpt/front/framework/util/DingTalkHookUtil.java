package com.hncboy.chatgpt.front.framework.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;
import com.hncboy.chatgpt.front.framework.domain.entity.SiteInfo;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessV2Request;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;

@Slf4j
@Component
public class DingTalkHookUtil {


    @Value("${spring.profiles.active}")
    private String profile;

    @Value("${dingtalk.secret}")
    private String secret;

    @Value("${dingtalk.webhook}")
    private String webhook;
    @Value("${dingtalk.webhook_tarot_monitor}")
    private String webhookTarotMonitor;
    @Value("${dingtalk.webhook_tarot_exception}")
    private String webhookTarotException;


    /**
     * 发送钉钉消息
     * @param message 消息内容
     * 该方法用于向指定的钉钉群或个人发送消息，实现了与钉钉接口的交互，能够方便地将信息传达给相关人员。
     */
    public void sendDingTalk(String message) {
        sendDingTalk(message,webhook);
    }

    /**
     * 发送异常消息
     * @param message
     */
    public void sendDingTalkForTarotException(String message) {
            sendDingTalk(message, webhookTarotException);
    }

    /**
     * 发送监控消息
     * @param message
     */
    public void sendDingTalkForMonitor(String message) {
        sendDingTalk(message, webhookTarotMonitor);
    }
    public void sendDingTalk(String message,String webhookProfile) {
        try {
            long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = URLEncoder.encode(new String(Base64.getEncoder().encode(signData)), "UTF-8");

            // 拼接URL
            String url = webhookProfile + "&timestamp=" + timestamp;// + "&sign=" + sign;  现在不需要签名
            if(webhookProfile.equals(webhook)){
                url = url + "&sign=" + sign;
            }
            String payload = "{\"msgtype\": \"text\", \"text\": {\"content\": \"" + message + "\"}}";

            // 发送POST请求
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
            httpPost.setEntity(new StringEntity(payload, "UTF-8"));
            CloseableHttpResponse response = httpClient.execute(httpPost);

            // 输出响应
            String result = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.debug("钉钉返回结果：[{}]", result);
            if (JSONUtil.isTypeJSON(result)) {
                JSONObject resObject = JSONUtil.parseObj(result);
                if (resObject.containsKey("errcode") && resObject.getInt("errcode") == 0) {
                    log.info("钉钉消息发送成功");
                } else {
                    log.error("发送钉钉消息失败, 错误信息:[{}-{}], 消息内容: [\n{}]",
                            resObject.getStr("errcode"), resObject.getStr("errmsg"), message);
                }
            }
        } catch (Exception e) {
            log.error("发送钉钉消息失败, 消息内容: [{}], 异常信息:", message, e);
        }
    }
    /**
     * 发送频道异常消息。
     * 此方法用于向指定的聊天频道发送一条异常消息，以提示频道内发生异常事件。
     *
     * @param chatProcessRequest 聊天处理请求对象，包含处理聊天所需的全部信息。
     * @param chatMessageDO 聊天消息数据对象，封装了消息的内容、发送者等信息。
     */
    public void sendChannelExceptionMessage(ChatProcessV2Request chatProcessRequest, ChatMessageDO chatMessageDO,
                                                    UserBaseInfoVO userBaseInfoVO, SiteInfo siteInfo, String response,
                                                    String errMessage) {
        // JSON格式处理
        String requestString = getFormatJsonString(chatProcessRequest.getPrompt());
        String responseString = getFormatJsonString(response);
        String errMessageString = getFormatJsonString(errMessage);

        // 获取本机信息
        String host = "";
        try {
            host = String.valueOf(InetAddress.getLocalHost());
        } catch (UnknownHostException e) {
            log.error("获取本机信息失败", e);
        }
        // 构建消息内容
        String message = "=== 智能社通道异常 ===\n" + "time:\t" + DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss") + "\n"
                + "env:\t\t" + profile + "\n" + "host:\t" + host + "\n"
                + "userid:\t" + userBaseInfoVO.getId() + "\n"
                + "account:\t" + userBaseInfoVO.getAccount() + "\n" + "name:\t" + userBaseInfoVO.getName() + "\n"
                + "gid:\t\t" + chatMessageDO.getModelGid() + "\n" + "app_name:\t" + chatMessageDO.getAgentName() + "\n"
                + "site_name:\t" + siteInfo.getName() + "\n" + "key_name:\t" + siteInfo.getApiKeyName() + "\n"
                + "url:\t\t" + siteInfo.getUrl() + "\n"
                + "request:\t" + requestString + "\n" + "response:\t" + responseString + "\n"
                + "err_info:\t" + errMessageString + "\n"
                + "=== END ===";
        sendDingTalk(message);
    }


    /**
     * 发送频道异常消息。
     * 此方法用于向指定的聊天频道发送一条异常消息，以提示频道内发生异常事件。
     *
     * @param chatProcessRequest 聊天处理请求对象，包含处理聊天所需的全部信息。
     * @param chatMessageDO 聊天消息数据对象，封装了消息的内容、发送者等信息。
     */
    public void sendTarotExceptionMessage(ChatProcessV2Request chatProcessRequest, TarotSpread chatMessageDO,
                                                   UserBaseInfoVO userBaseInfoVO, SiteInfo siteInfo, String response,
                                                   String errMessage) {
        // JSON格式处理
        String requestString = getFormatJsonString(chatProcessRequest.getPrompt());
        String responseString = getFormatJsonString(response);

        String errMessageString = getFormatJsonString(errMessage);

        // 获取本机信息
        String host = "";
        try {
            host = String.valueOf(InetAddress.getLocalHost());
        } catch (UnknownHostException e) {
            log.error("获取本机信息失败", e);
        }
        String nickName = userBaseInfoVO.getNickName();
        if(nickName == null || nickName.isEmpty()){
            nickName="";
        }

        // 构建消息内容
        String message = "=== 塔罗通道异常 ===\n" + "time:\t" + DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss") + "\n"
                + "env:\t\t" + profile + "\n" + "host:\t" + host + "\n"
                + "userid:\t" + userBaseInfoVO.getId() + "\n"
                + "account:\t" + userBaseInfoVO.getAccount() + "\n" + "name:\t" + nickName + "\n"
                + "spread_name:\t" + chatMessageDO.getName() + "\n"
                + "site_name:\t" + siteInfo.getName() + "\n" + "key_name:\t" + siteInfo.getApiKeyName() + "\n"
                + "url:\t\t" + siteInfo.getUrl() + "\n"
                + "request:\t" + requestString + "\n" + "response:\t" + responseString + "\n"
                + "err_info:\t" + errMessageString + "\n"
                + "=== END ===";
        sendDingTalkForTarotException(message);
    }



    public String getFormatJsonString(String str) {
        if (str == null) {
            return "";
        }
        String temp1;
        if (JSONUtil.isTypeJSON(str)) {
            JSONObject jsonObject = JSONUtil.parseObj(str);
            temp1 = jsonObject.toString();
        } else {
            temp1 = str;
        }
        String temp2 = temp1.replace("\\", "\\\\");
        return temp2.replace("\"", "\\\"");
    }

    /**
     * 发送频道恢复消息。
     * 此方法用于向指定的聊天频道发送一条恢复消息，以提示通道已经恢复正常。
     *
     * @param modelGid 模型GID，标识聊天消息所属的模型。
     * @param siteInfo 站点信息对象，包含站点的名称、URL等信息。
     * @param type 类型标识，用于区分恢复消息的类型。
     */
    public void sendChannelRecoverMessage(String modelGid, SiteInfo siteInfo, char type) {
        // 获取本机信息
        String host = "";
        try {
            host = String.valueOf(InetAddress.getLocalHost());
        } catch (UnknownHostException e) {
            log.error("获取本机信息失败", e);
        }
        String title = "=== 智能社通道恢复 ===";
        if (type == '1') {
            title = "=== 智能社通道自动探测恢复 ===";
        }
        // 构建消息内容
        String message = title + "\ntime:\t" + DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss") + "\n"
                + "env:\t\t\t" + profile + "\n" + "host:\t" + host + "\n"
                + "gid:\t\t\t" + modelGid + "\n"
                + "url:\t\t\t" + siteInfo.getUrl() + "\n"
                + "site_name:\t" + siteInfo.getName() + "\n"
                + "key_name:\t" + siteInfo.getApiKeyName() + "\n"
                + "=== END ===";
        sendDingTalk(message);
    }

}
