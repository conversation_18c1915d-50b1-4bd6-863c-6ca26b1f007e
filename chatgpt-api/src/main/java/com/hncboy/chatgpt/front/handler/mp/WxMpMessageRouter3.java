package com.hncboy.chatgpt.front.handler.mp;

import me.chanjar.weixin.common.api.WxErrorExceptionHandler;
import me.chanjar.weixin.common.api.WxMessageDuplicateChecker;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpMessageRouter;
import me.chanjar.weixin.mp.api.WxMpService;

import java.util.concurrent.ExecutorService;

public class WxMpMessageRouter3 extends WxMpMessageRouter {
    public WxMpMessageRouter3(WxMpService wxMpService) {
        super(wxMpService);
    }

    public WxMpMessageRouter3(WxMpService wxMpService, ExecutorService executorService) {
        super(wxMpService, executorService);
    }

    public WxMpMessageRouter3(WxMpService wxMpService, ExecutorService executorService, WxMessageDuplicate<PERSON>he<PERSON> messageDuplicate<PERSON>hecker, WxSessionManager sessionManager, WxErrorExceptionHandler exceptionHandler) {
        super(wxMpService, executorService, messageDuplicateChecker, sessionManager, exceptionHandler);
    }
}
