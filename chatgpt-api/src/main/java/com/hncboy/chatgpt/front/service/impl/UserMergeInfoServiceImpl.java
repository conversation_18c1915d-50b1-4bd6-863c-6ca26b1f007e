package com.hncboy.chatgpt.front.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.UserMergeInfo;
import com.hncboy.chatgpt.front.mapper.UserMergeInfoMapper;
import com.hncboy.chatgpt.front.service.UserMergeInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户基础信息实现
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2024/2/26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserMergeInfoServiceImpl extends ServiceImpl<UserMergeInfoMapper, UserMergeInfo> implements UserMergeInfoService {


}
