package com.hncboy.chatgpt.front.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.constant.OrderConstant;
import com.hncboy.chatgpt.front.framework.domain.entity.Orders;
import com.hncboy.chatgpt.front.framework.domain.entity.Product;
import com.hncboy.chatgpt.front.framework.domain.entity.RechargeLog;
import com.hncboy.chatgpt.front.framework.domain.entity.SePayOrder;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.util.DateUtils;
import com.hncboy.chatgpt.front.framework.util.IdGeneratorUtils;
import com.hncboy.chatgpt.front.framework.util.PayUtil;
import com.hncboy.chatgpt.front.helper.RedisLockHelper;
import com.hncboy.chatgpt.front.mapper.ProductMapper;
import com.hncboy.chatgpt.front.mapper.SePayOrderMapper;
import com.hncboy.chatgpt.front.service.RechargeLogService;
import com.hncboy.chatgpt.front.service.RedisService;
import com.hncboy.chatgpt.front.service.SePayOrderService;
import com.hncboy.chatgpt.tarot.domain.entity.Transaction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 支付订单信息实现
 *
 * @Version：v1.0.0
 * @Author: shy
 * @Date:2025/4/14
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SePayOrderServiceImpl extends ServiceImpl<SePayOrderMapper, SePayOrder> implements SePayOrderService {

    private final RechargeLogService rechargeLogService;
    private final RedisLockHelper lockHelper;
    private final IdGeneratorUtils idGeneratorUtils;
    private final ProductMapper productMapper;
    private final RedisService redisService;
    private final SePayOrderMapper sePayOrderMapper;
    private final PayUtil payUtil;
    private ReentrantLock[] locks;

    {
        locks = new ReentrantLock[20];
        for (int i = 0; i < 20; i++) {
            locks[i] = new ReentrantLock();
        }
    }

    @Value("${se-pay.bankName}")
    String bankName;

    @Value("${se-pay.accountNumber}")
    String accountNumber;

    @Value("${se-pay.se-secret}")
    String seSecret;

    /**
     * 使用 HMAC-SHA256 加密字符串（固定输出）
     *
     * @param secretKey 秘钥
     * @param input     要加密的字符串
     * @return 固定长度的 HEX 字符串
     */
    public String hmacSha256(String secretKey, String input) {
        HMac hmac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes());
        return hmac.digestHex(input);
    }

    /**
     * 构建二维码url
     *
     * @param productId
     * @param httpRequest
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SePayOrder generateSePayQrCode(Long productId, HttpServletRequest httpRequest) {
        final String timestamp = String.valueOf(System.currentTimeMillis());
        String format = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        long currUserId = StpUtil.getLoginIdAsLong();
        if (ObjectUtil.isEmpty(currUserId)) {
            throw new ServiceException("请先登录");
        }
        Date newDate = DateUtils.getDateAfter(new Date(), Calendar.MINUTE, 5);
        //锁前缀
        final String lockPrefix = "ORDER_USER" + currUserId;
        //上锁
        final boolean lock = lockHelper.lock(lockPrefix, timestamp);
        try {
            if (!lock) {
                throw new ServiceException("请勿重复下单");
            }
            final String key = OrderConstant.ORDER_PAY + ApplicationConstant.CHANNEL_SE + "-" + currUserId + "-" + productId;
            if (redisService.exists(key)) {
                //生成BASE64图片给前端
                return (SePayOrder) redisService.get(key);
            }

            final Product product = productMapper.selectOne(new QueryWrapper<Product>()
                    .lambda()
                    .eq(Product::getProductId, productId)
                    .le(Product::getStartTime, format)
                    .gt(Product::getEndTime, format)
                    .eq(Product::getStatus, "0")
                    .select(Product::getProductId, Product::getProductPrice, Product::getNum, Product::getType,
                            Product::getUnit, Product::getProductName, Product::getPackageInfo, Product::getRemark)
            );
            if (product == null) {
                throw new ServiceException("商品不存在");
            }
            final String orderNo = idGeneratorUtils.getOrderNo();
            SePayOrder order = new SePayOrder();
            //基础信息
            order.setProductId(product.getProductId());
            order.setProductType(product.getType());
            order.setUnit(product.getUnit());
            order.setNum(product.getNum());
            order.setBody(product.getProductName());
            order.setUserId(currUserId);
            order.setOrderNo(orderNo);
            order.setCreateTime(newDate);
            order.setExpiresTime(DateUtil.offsetMinute(newDate, 10));
            order.setCreateBy(StpUtil.getLoginIdAsLong());
            order.setStatus(0);
            order.setIpAddress(httpRequest.getRemoteAddr());
            //构建url信息
            order.setBankName(bankName);
            order.setAccountNumber(accountNumber);
            order.setAmount(product.getProductPrice());
            order.setUniqueId(orderNo);
            order.setContent(hmacSha256(seSecret, orderNo));
            //生成二维码链接
            String qrCodeUrl = String.format("https://qr.sepay.vn/img?acc=%s&bank=%s&amount=%s&des=%s&content=%s",
                    order.getAccountNumber(),
                    order.getBankName(),
                    order.getAmount() != null ? order.getAmount() : "",
                    order.getUniqueId() != null ? order.getUniqueId() : "",
                    order.getContent() != null ? order.getContent() : "");
            order.setQrCodeUrl(qrCodeUrl);
            sePayOrderMapper.insert(order);
            redisService.set(key, order, 10 * 60L);

            return order;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成支付二维码失败:{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        } finally {
            lockHelper.unlock(lockPrefix, timestamp);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void callback(Transaction transaction) {
        String content = transaction.getContent();
        SePayOrder order = getOrderByContent(content);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        //收到回调就成功
        final String outTradeNo = order.getOrderNo();
        int hash = Math.abs(outTradeNo.hashCode() % locks.length);

        locks[hash].lock();

        try {
            //重新查询
            order = getOrderByOrderNo(order.getOrderNo());
            if (order.getStatus() == 1) {
                throw new ServiceException("订单已支付");
            }
            order.setGateway(transaction.getGateway());
            order.setTransactionDate(transaction.getTransactionDate());
            order.setAccountNumber(transaction.getAccountNumber());
            order.setCode(transaction.getCode());
            order.setContent(transaction.getContent());
            order.setTransferType(transaction.getTransferType());
            order.setTransferAmount(transaction.getTransferAmount());
            order.setAccumulated(transaction.getAccumulated());
            order.setSubAccount(transaction.getSubAccount());
            order.setReferenceCode(transaction.getReferenceCode());
            order.setDescription(transaction.getDescription());
            order.setStatus(1);
            order.setSePayId(String.valueOf(transaction.getId()));
            order.setTimeEnd(new Date());
            baseMapper.updateById(order);

            //根据单号，修改用户产品使用信息
            // TODO: 逻辑存疑如此处查询是否需要进行启用控制
            final Product product = productMapper.selectOne(new QueryWrapper<Product>().lambda()
                    .eq(Product::getProductId, order.getProductId()));
            //根据情况对绘画 和 对话次数进行添加
            if (product != null) {
                //充值处理
                Orders orders1 = new Orders().setOrdersId(order.getOrderNo())
                        .setProductId(order.getProductId())
                        .setUserId(Math.toIntExact(order.getUserId()))
                        .setUnit(order.getUnit())
                        .setNum(order.getNum());

                payUtil.chargeUp(product, orders1);
                //生成充值记录
                final RechargeLog rechargeLog = new RechargeLog()
                        .setUserId(Math.toIntExact(order.getUserId())).setOrderId(order.getOrderNo())
                        .setChannel(ApplicationConstant.CHANNEL_WX)
                        .setProductId(order.getProductId())
                        .setPackageInfo(product.getPackageInfo())
                        .setRechargeAmt(product.getProductPrice()).setTimes(Math.toIntExact(product.getNum()))
                        .setUnit(order.getUnit()).setStartTime(order.getCreateTime())
                        .setEndTime(order.getUpdateTime())
                        .setCreatedTime(new Date());
                rechargeLogService.insert(rechargeLog);

                //积分记录
                payUtil.addRechargePoints(product, orders1);
            }
            log.info("-------------订单支付成功，已处理-------------");
            Long productId = order.getProductId();
            final Long currUserId = order.getUserId();
            final String key = OrderConstant.ORDER_PAY + ApplicationConstant.CHANNEL_SE + "-" + currUserId + "-" + productId;
            redisService.remove(key);

        } finally {
            locks[hash].unlock();
        }

    }

    /**
     * 根据订单号，获取订单信息
     *
     * @param orderNo
     * @return
     */
    @Override
    public SePayOrder getOrderByOrderNo(String orderNo) {
        return sePayOrderMapper.selectOne(new QueryWrapper<SePayOrder>()
                .lambda()
                .eq(SePayOrder::getOrderNo, orderNo)
        );
    }

    /**
     * 根据订单号，获取订单信息
     *
     * @param content
     * @return
     */
    public SePayOrder getOrderByContent(String content) {
        return sePayOrderMapper.selectOne(new QueryWrapper<SePayOrder>()
                .lambda()
                .eq(SePayOrder::getContent, content)
        );
    }

}
