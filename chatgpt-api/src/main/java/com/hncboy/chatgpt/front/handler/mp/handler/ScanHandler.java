package com.hncboy.chatgpt.front.handler.mp.handler;

import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.enums.WxAppIdEnum;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.front.handler.mp.builder.TextBuilder;
import com.hncboy.chatgpt.front.mapper.UserBaseInfoMapper;
import com.hncboy.chatgpt.front.service.RedisService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.WX_SCENE_ID;
import static com.hncboy.chatgpt.front.handler.wss.GPTSocketServer.wxUserInfoService;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ScanHandler extends AbstractHandler {

    private final RedisService redisService;
    private final UserBaseInfoMapper userBaseInfoMapper;
    private final UserBaseInfoService userBaseInfoService;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMpXmlMessage, Map<String, Object> map,
                                    WxMpService wxMpService, WxSessionManager wxSessionManager) throws WxErrorException {
        log.info("扫码事件：{}", wxMpXmlMessage);
        //扫码登录事件
        if (wxMpXmlMessage.getEventKey().equals(WX_SCENE_ID.toString())) {
            WxUserInfoVO wxUserInfoVO = null;
            WxMpUser userWxInfo = wxMpService.getUserService()
                    .userInfo(wxMpXmlMessage.getFromUser(), null);
            if (userWxInfo != null) {
                wxUserInfoVO = wxUserInfoService.saveOrUpdateUser(userWxInfo, WxAppIdEnum.zns.getCode());
            }
            //半个小时过期
            redisService.set(wxMpXmlMessage.getTicket(), wxUserInfoVO, 1800L);
            return new TextBuilder().build("登录成功", wxMpXmlMessage, wxMpService);
        } else if (wxMpXmlMessage.getEventKey().startsWith("bind")) {
            //绑定微信
            String s = wxMpXmlMessage.getEventKey().replaceAll("bind", "");
            UserBaseInfo userBaseInfo = userBaseInfoMapper.selectById(Integer.valueOf(s));
            //检查该微信是否已经绑定其他微信号
            String openId = wxMpXmlMessage.getFromUser();
            //检查openId是否已绑定
            UserBaseInfoVO userBaseInfoVO = userBaseInfoService.getUserBaseInfoByOpenId(openId);
            if (Objects.nonNull(userBaseInfoVO) && !userBaseInfoVO.getId().equals(userBaseInfo.getId())) {
                return new TextBuilder().build("该微信号已绑定过其他账号，如需帮助，请联系客服。", wxMpXmlMessage, wxMpService);
            }
            WxUserInfoVO wxUserInfoVO = wxUserInfoService.queryUserInfoByOpenId(openId);
            if (Objects.nonNull(wxUserInfoVO)) {
                if(StringUtils.isNotEmpty(wxUserInfoVO.getNickName())) {
                    userBaseInfo.setNickName(wxUserInfoVO.getNickName());
                }
                if(StringUtils.isNotEmpty(wxUserInfoVO.getAvatarUrl())) {
                    userBaseInfo.setHeadSculpture(wxUserInfoVO.getAvatarUrl());
                }
            }
            userBaseInfo.setOpenId(wxMpXmlMessage.getFromUser());
            userBaseInfo.setUpdateTime(new Date());
            userBaseInfoMapper.updateById(userBaseInfo);
            return new TextBuilder().build("绑定成功", wxMpXmlMessage, wxMpService);
        }
        // 扫码事件处理
        return null;
    }
}
