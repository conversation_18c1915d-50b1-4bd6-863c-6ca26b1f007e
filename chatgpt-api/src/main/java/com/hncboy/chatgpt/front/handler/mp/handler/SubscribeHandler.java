package com.hncboy.chatgpt.front.handler.mp.handler;

import cn.hutool.core.util.StrUtil;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxUserInfoVO;
import com.hncboy.chatgpt.front.framework.enums.WxAppIdEnum;
import com.hncboy.chatgpt.front.handler.mp.builder.TextBuilder;
import com.hncboy.chatgpt.front.mapper.UserBaseInfoMapper;
import com.hncboy.chatgpt.front.service.RedisService;
import com.hncboy.chatgpt.front.service.UserBaseInfoService;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.WX_SCENE_ID;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Component
@RequiredArgsConstructor
public class SubscribeHandler extends AbstractHandler {

    private final WxUserInfoService wxUserInfoService;
    private final RedisService redisService;
    private final UserBaseInfoMapper userBaseInfoMapper;
    private final UserBaseInfoService userBaseInfoService;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage, Map<String, Object> context,
                                    WxMpService weixinService, WxSessionManager sessionManager) throws WxErrorException {

        this.logger.info("新关注用户 OPENID: " + wxMessage);
        WxUserInfoVO wxUserInfoVO = null;
        // 获取微信用户基本信息
        try {
            WxMpUser userWxInfo = weixinService.getUserService()
                    .userInfo(wxMessage.getFromUser(), null);
            if (userWxInfo != null) {
                wxUserInfoVO = wxUserInfoService.saveOrUpdateUser(userWxInfo, WxAppIdEnum.zns.getCode());
            }
        } catch (WxErrorException e) {
            if (e.getError().getErrorCode() == 48001) {
                this.logger.info("该公众号没有获取用户信息权限！");
            }
        }
        String scanMsg = this.handleSpecial(wxMessage, wxUserInfoVO);
        if (StrUtil.isNotEmpty(scanMsg)) {
            //先发一条登录成功消息
            WxMpKefuMessage wxMpMassOpenIdsMessage = new WxMpKefuMessage();
            wxMpMassOpenIdsMessage.setToUser(wxMessage.getFromUser());
            wxMpMassOpenIdsMessage.setMsgType(WxConsts.MassMsgType.TEXT);
            wxMpMassOpenIdsMessage.setContent(scanMsg)
            ;
            weixinService.getKefuService().sendKefuMessage(wxMpMassOpenIdsMessage);
        }
        try {
            String msg = "" +
                    "\uD83D\uDC4F欢迎关注我们！\n" +
                    "\n" +
                    "浙江方顶数融科技有限公司专注于构建人工智能应用架构。" +
                    "通过多智能体作业平台及一系列应用产品和工具链，" +
                    "我们帮助企业以可控且可评估的方式实施人工智能技术，" +
                    "从而提升运营效率并提高员工工作效率。" +
                    "此外，我们与企业共同构建领域模型，助力企业转型其产品和服务，创造更大的价值。";
            return new TextBuilder().build(msg, wxMessage, weixinService);
        } catch (Exception e) {
            this.logger.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 处理特殊请求，比如如果是扫码进来的，可以做相应处理
     */
    private String handleSpecial(WxMpXmlMessage wxMessage, WxUserInfoVO wxUserInfoVO) {
        if (StrUtil.isNotEmpty(wxMessage.getEventKey())) {
            String code = wxMessage.getEventKey().replaceAll("qrscene_", "");
            //场景值是扫码登录
            if (code.equals(WX_SCENE_ID.toString())) {
                //半个小时过期
                redisService.set(wxMessage.getTicket(), wxUserInfoVO, 1800L);
                return "扫码登录成功";
            } else if (code.startsWith("bind")) {
                //绑定微信
                String s = code.replaceAll("bind", "");
                UserBaseInfo userBaseInfo = userBaseInfoMapper.selectById(Integer.valueOf(s));
                String openId = wxMessage.getFromUser();
                // 检查openId是否已绑定
                UserBaseInfoVO userBaseInfoVO = userBaseInfoService.getUserBaseInfoByOpenId(openId);
                if (Objects.nonNull(userBaseInfoVO)) {
                    return "当前微信号已绑定过其他账号，如需帮助，请联系客服。";
                }
                userBaseInfo.setOpenId(openId);
                if(StrUtil.isNotBlank(wxUserInfoVO.getNickName())) {
                    userBaseInfo.setNickName(wxUserInfoVO.getNickName());
                }
                if(StrUtil.isNotBlank(wxUserInfoVO.getAvatarUrl())) {
                    userBaseInfo.setHeadSculpture(wxUserInfoVO.getAvatarUrl());
                }
                userBaseInfo.setUpdateTime(new Date());
                userBaseInfoMapper.updateById(userBaseInfo);
                return "绑定成功";
            }
        }
        return null;
    }

}
