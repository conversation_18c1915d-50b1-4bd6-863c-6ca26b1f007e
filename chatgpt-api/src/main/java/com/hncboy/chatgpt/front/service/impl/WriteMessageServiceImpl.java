package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.converter.WriteMessageConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.WriteMessageDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.WriteAgent;
import com.hncboy.chatgpt.front.framework.domain.entity.WriteMessage;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteMessageVO;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.framework.util.StringUtils;
import com.hncboy.chatgpt.front.mapper.WriteMessageMapper;
import com.hncboy.chatgpt.front.service.WriteAgentService;
import com.hncboy.chatgpt.front.service.WriteMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zd.zhong
 * @description 针对表【write_message(写作记录)】的数据库操作Service实现
 * @createDate 2024-07-02 09:26:03
 */
@Service
public class WriteMessageServiceImpl extends ServiceImpl<WriteMessageMapper, WriteMessage>
    implements WriteMessageService{

    @Autowired
    private WriteAgentService writeAgentService;
    
    /**
     * 分页查询写作记录信息列表
     *
     * @param dto
     * @return: IPage<WriteMessageVO>
     * @Author: zd.zhong
     * @Date: 2024/7/2
     */
    @Override
    public IPage<WriteMessageVO> queryListEntityPage(WriteMessageDTO dto) {
        LambdaQueryWrapper<WriteMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WriteMessage::getUserId, CurrentUserUtil.getV2UserId());
        wrapper.eq(WriteMessage::getIsDelete,0);
        wrapper.like(StringUtils.isNotEmpty(dto.getContent()), WriteMessage::getContent, dto.getContent());
        wrapper.like(StringUtils.isNotEmpty(dto.getAgentTitle()), WriteMessage::getAgentTitle, dto.getAgentTitle());
        wrapper.like(StringUtils.isNotEmpty(dto.getTopic()), WriteMessage::getTopic, dto.getTopic());
        wrapper.like(StringUtils.isNotEmpty(dto.getSiteName()), WriteMessage::getSiteName, dto.getSiteName());
        wrapper.orderByDesc(WriteMessage::getCreateTime);
        IPage<WriteMessage> userPage = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<WriteMessage> iPage = this.page(userPage, wrapper);
        IPage<WriteMessageVO> convert = iPage.convert(WriteMessageConvert.INSTANCE::entityToVO);
        Map<Integer, String> collect = writeAgentService.list().stream().collect(Collectors.toMap(writeAgent -> writeAgent.getId(), writeAgent -> writeAgent.getImgUrl()));


        for (WriteMessageVO record : convert.getRecords()) {
            if(collect.containsKey(record.getAgentId())){
                record.setImgUrl(collect.get(record.getAgentId()));
            }
            
        }
        
        
        return convert;
    }

    /**
     * 删除写作记录
     * @param id
     */
    @Override
    public void softDeleteById(Integer id) {
        WriteMessage writeMessage = this.getById(id);
        if (writeMessage != null && writeMessage.getUserId().equals(CurrentUserUtil.getV2UserId())) {
            writeMessage.setStatus("9");
            writeMessage.setIsDelete(1);
            this.updateById(writeMessage);
        }
    }
    /**
     * 查询写作应用信息
     *
     * @return WriteMessageVO
     * @Author: 赵雨晨
     * @Date: 2024/7/17
     */
    @Override
    public WriteMessageVO getWriteMessageInfoById(Integer id) {
        WriteMessage writeMessage = this.getById(id);
        return WriteMessageConvert.INSTANCE.entityToVO(writeMessage);
    }

}




