package com.hncboy.chatgpt.front.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.entity.Model;

/**
 * 模型列表Service接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface IModelService extends IService<Model> {
    /**
     * 查询模型列表
     *
     * @param id 模型列表主键
     * @return 模型列表
     */
    public Model selectModelById(Long id);

    /**
     * 查询模型列表列表
     *
     * @param model 模型列表
     * @return 模型列表集合
     */
    public List<Model> selectModelList(Model model);

    /**
     * 新增模型列表
     *
     * @param model 模型列表
     * @return 结果
     */
    public int insertModel(Model model);

    /**
     * 修改模型列表
     *
     * @param model 模型列表
     * @return 结果
     */
    public int updateModel(Model model);


    /**
     * 删除模型列表信息
     *
     * @param id 模型列表主键
     * @return 结果
     */
    public int deleteModelById(Long id);
}