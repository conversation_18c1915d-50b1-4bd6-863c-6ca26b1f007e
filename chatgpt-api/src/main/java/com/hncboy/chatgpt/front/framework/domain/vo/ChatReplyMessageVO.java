package com.hncboy.chatgpt.front.framework.domain.vo;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2023/3/23 13:53
 * 聊天回复的消息
 */
@Data
@Schema(title = "聊天回复的消息")
@Slf4j
public class ChatReplyMessageVO {


    @Schema(title = "当前消息 id")
    private Long id;

    @Schema(title = "回复的消息")
    private String text;

    @Schema(title = "用户ID")
    private String openId;

    @Schema(title = "状态")
    private Integer status;
    
    public static ResponseBodyEmitter sendAndComplete(ResponseBodyEmitter emitter, String msg,Integer status){
        try {
            String s = buildFailureMessage(msg, status);
            log.error("发生业务异常----{}",s);
            emitter.send(("data: " + s + "\n\n"));
            emitter.send("[DONE]");
            emitter.complete();
        } catch (IOException e) {
            throw new ServiceException("流式回答异常");
        }
        return emitter;
    }


    /**
     *
     * @param msg
     * @param status
     * @return
     */
    public static String buildFailureMessage(String msg,Integer status) {
        ChatReplyMessageVO chatReplyMessageVO = new ChatReplyMessageVO();
        chatReplyMessageVO.setText(msg);
        chatReplyMessageVO.setStatus(status);
        String delta = JSONUtil.toJsonStr(chatReplyMessageVO);
        return delta;
    }


    public static String buildSpeakMessage(String url) {
        ChatReplyMessageVO chatReplyMessageVO = new ChatReplyMessageVO();
        chatReplyMessageVO.setText(url);
        chatReplyMessageVO.setStatus(ChatReplyMessageVO.MsgStatusEnum.speech.getStatus());
        String delta = JSONUtil.toJsonStr(chatReplyMessageVO);
        return delta;
    }

    @AllArgsConstructor
    public enum MsgStatusEnum {

        /**
         * 正常
         */
        normal(0),

        speech(22),

        /**
         * 异常
         */
        error(1);


        @Getter
        @EnumValue
        private final Integer status;
    }
}
