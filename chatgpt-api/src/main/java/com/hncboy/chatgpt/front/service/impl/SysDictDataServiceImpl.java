package com.hncboy.chatgpt.front.service.impl;

import com.hncboy.chatgpt.front.framework.domain.entity.SysDictData;
import com.hncboy.chatgpt.front.mapper.SysDictDataMapper;
import com.hncboy.chatgpt.front.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysDictDataServiceImpl implements ISysDictDataService
{
    @Autowired
    private SysDictDataMapper dictDataMapper;

    /**
     * 根据条件分页查询字典数据
     * 
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData)
    {
        return dictDataMapper.selectDictDataList(dictData);
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     * 
     * @param dictType 字典类型
     * @return 字典标签
     */
    @Override
    public List<SysDictData> selectDictLabel(String dictType)
    {
        return dictDataMapper.selectDictLabel(dictType);
    }
    @Override
    public List<String> selectDictLabelString(String dictType)
    {
        return dictDataMapper.selectDictLabelString(dictType);
    }

    /**
     * 根据字典数据ID查询信息
     * 
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode)
    {
        return dictDataMapper.selectDictDataById(dictCode);
    }

}
