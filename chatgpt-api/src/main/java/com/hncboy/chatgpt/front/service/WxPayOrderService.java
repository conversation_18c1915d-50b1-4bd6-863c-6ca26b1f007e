package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.hncboy.chatgpt.front.framework.domain.dto.WxPayOrderDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.WxPayOrder;
import com.hncboy.chatgpt.front.framework.domain.vo.PayOutComeVo;
import com.hncboy.chatgpt.front.framework.domain.vo.WxPayCodeVO;
import com.hncboy.chatgpt.front.framework.domain.vo.WxPayOrderVO;
import com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord;

import java.util.Map;

/**
 * 支付订单信息 Dao
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/21
 */
public interface WxPayOrderService extends IService<WxPayOrder> {


    /**
     * 根据订单号查询
     *
     * @param order
     * @return WxPayOrderVO
     * @Author: zc.wu
     * @Date: 2023/4/21 14:20
     */
    PayOutComeVo queryOrderBySn(String order);


    /**
     * 更新支付订单信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/21
     */
    boolean updateEntity(WxPayOrder record);

    /**
     * 插入支付订单信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/21
     */
    String insertEntity(WxPayOrderDTO record);


    /**
     * 创建支付订单
     *
     * @param code
     * @return WxPayUnifiedOrderRequest
     * @Author: zc.wu
     * @Date: 2023/4/21 14:16
     */
    WxPayOrderVO createWxOrderPay(WxPayUnifiedOrderRequest request, Integer code);


    /**
     * 更新支付结果
     *
     * @param wxPayOrderNotifyResult
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/21 14:45
     */
    void updateOrderStatus(WxPayOrderNotifyResult wxPayOrderNotifyResult);


    /**
     * 创建抖音支付订单
     *
     * @param goodsId
     * @return Map<String, Object>
     * @Author: zc.wu
     * @Date: 2023/6/25 0025 下午 05:49
     */
    Map<String, Object> createDyOrderPay(Integer goodsId);


    /**
     * 创建支付订单
     *
     * @return WxPayUnifiedOrderRequest
     */
    WxPayCodeVO createOrder(WxPayOrderDTO wxPayOrderDTO) throws WxPayException;
    WxPayCodeVO createPrepayOrder(WxPayOrderDTO wxPayOrderDTO) throws WxPayException;
    //WxPayCodeVO createWxMinOrder(WxPayOrderDTO wxPayOrderDTO) throws WxPayException;
    //WxPayCodeVO createPrepayOrderNoUser(WxPayOrderDTO wxPayOrderDTO) throws WxPayException;

    /**
     * 创建提现订单
     * @param wxPayOrderDTO
     * @return
     * @throws WxPayException
     */
    boolean createTransfer(WxPayOrderDTO wxPayOrderDTO);


}
