package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hncboy.chatgpt.front.framework.converter.IntelligentAgentConvert;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatRoomDO;
import com.hncboy.chatgpt.front.framework.domain.entity.HomeConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.IntelligentAgent;
import com.hncboy.chatgpt.front.framework.domain.vo.ChatRoomVO;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.framework.util.WebUtil;
import com.hncboy.chatgpt.front.mapper.ChatRoomMapper;
import com.hncboy.chatgpt.front.mapper.HomeConfigMapper;
import com.hncboy.chatgpt.front.service.ChatRoomService;
import com.hncboy.chatgpt.front.service.IntelligentAgentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.TYPE_CHAT;

/**
 * 聊天室相关业务实现类
 */
@Service
@RequiredArgsConstructor
public class ChatRoomServiceImpl extends ServiceImpl<ChatRoomMapper, ChatRoomDO> implements ChatRoomService {

    private final IntelligentAgentService intelligentAgentService;

    private final HomeConfigMapper homeConfigMapper;

    @Override
    public ChatRoomDO createChatRoom(ChatRoomDO chatRoomDO) {
        chatRoomDO.setIp(WebUtil.getIp());
        chatRoomDO.setOpenId(CurrentUserUtil.getUserId());
        this.save(chatRoomDO);
        chatRoomDO.setAgentVo(intelligentAgentService.queryInfoById(chatRoomDO.getRoleId()));
        return chatRoomDO;
    }

    @Override
    public boolean updateChatRoom(ChatRoomDO chatRoomDO) {
        if(ObjectUtil.isEmpty(chatRoomDO.getId())) {
            return false;
        }
        ChatRoomDO chatRoom = this.getById(chatRoomDO.getId());
        if(ObjectUtil.isEmpty(chatRoom) || !chatRoom.getOpenId().equals(CurrentUserUtil.getUserId())){
            return false;
        }
        chatRoom.setTitle(chatRoomDO.getTitle());
        return this.updateById(chatRoom);
    }


    /**
     * 公开房间
     *
     * @param chatRoomDO
     * @return void
     * @Author: zc.wu
     * @Date: 2024/1/18 0018 下午 01:07
     */
    private HomeConfig saveHomeConfig(ChatRoomDO chatRoomDO) {
        HomeConfig homeConfig = new HomeConfig();
        homeConfig.setImgUrl(chatRoomDO.getImageUrl());
        homeConfig.setStatus(0);
        homeConfig.setTag("user");
        homeConfig.setTitle(chatRoomDO.getTitle());
        homeConfig.setDescription(chatRoomDO.getDescription());
        homeConfig.setSysContent(chatRoomDO.getSysContent());
        homeConfig.setCreateBy(chatRoomDO.getOpenId());
        homeConfigMapper.insert(homeConfig);
        return homeConfig;
    }


    /**
     * 查询用户聊天室
     *
     * @param
     * @return List<ChatRoomDO>
     * @Author: zc.wu
     * @Date: 2023/7/31 0031 下午 04:25
     */
    @Override
    public List<ChatRoomVO> getChatRoom(ChatRoomDO roomDO) {
        List<ChatRoomDO> list;
        LambdaQueryWrapper<ChatRoomDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ChatRoomDO::getOpenId, CurrentUserUtil.getUserId());
        String type = roomDO.getType();
        if (StrUtil.isBlank(type)) {
            lambdaQueryWrapper.eq(ChatRoomDO::getType, TYPE_CHAT);
        } else {
            lambdaQueryWrapper.eq(ChatRoomDO::getType, type);
        }
        if (ObjectUtil.isNotEmpty(roomDO.getIds())) {
            lambdaQueryWrapper.in(ChatRoomDO::getId, roomDO.getIds());
        }
        lambdaQueryWrapper.orderByDesc(ChatRoomDO::getUpdateTime);
        list = this.list(lambdaQueryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<ChatRoomVO> chatRoomVOS = Lists.newArrayList();
        list.stream().forEach(chatRoomDO -> {
            ChatRoomVO chatRoomVO = new ChatRoomVO();
            BeanUtil.copyProperties(chatRoomDO, chatRoomVO);
            chatRoomVOS.add(chatRoomVO);
        });
        List<Integer> collect = chatRoomVOS.stream().map(ChatRoomVO::getRoleId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            //房间信息统一按最新的查询
            Map<Integer, IntelligentAgent> agentMap = intelligentAgentService.queryListByIds(collect)
                    .stream().collect(Collectors.toMap(IntelligentAgent::getId, Function.identity()));
            chatRoomVOS.stream().forEach(chatRoomVO -> {
                chatRoomVO.setAgentVo(IntelligentAgentConvert.INSTANCE.entityToVO(agentMap.get(chatRoomVO.getRoleId())));
            });
        }
        return chatRoomVOS;
    }
}
