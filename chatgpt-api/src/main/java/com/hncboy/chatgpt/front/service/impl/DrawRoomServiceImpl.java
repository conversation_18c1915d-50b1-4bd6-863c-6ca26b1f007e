package com.hncboy.chatgpt.front.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hncboy.chatgpt.front.framework.domain.entity.DrawRoomDO;
import com.hncboy.chatgpt.front.framework.domain.vo.DrawRoomVO;
import com.hncboy.chatgpt.front.framework.util.CurrentUserUtil;
import com.hncboy.chatgpt.front.framework.util.WebUtil;
import com.hncboy.chatgpt.front.mapper.DrawRoomMapper;
import com.hncboy.chatgpt.front.service.DrawRoomService;
import com.hncboy.chatgpt.front.service.IntelligentAgentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【draw_room(绘图室)】的数据库操作Service实现
* @createDate 2024-03-20 16:46:36
*/
@Service
@RequiredArgsConstructor
public class DrawRoomServiceImpl extends ServiceImpl<DrawRoomMapper, DrawRoomDO>
    implements DrawRoomService {

    private final IntelligentAgentService intelligentAgentService;

    public DrawRoomVO createDrawRoom(DrawRoomDO drawRoomDO) {
        drawRoomDO.setIp(WebUtil.getIp());
        drawRoomDO.setOpenId(CurrentUserUtil.getUserId());
        this.save(drawRoomDO);
        DrawRoomVO drawRoomVO = new DrawRoomVO();
        BeanUtil.copyProperties(drawRoomDO, drawRoomVO);
        return drawRoomVO;
    }

    @Override
    public boolean updateDrawRoom(DrawRoomDO drawRoomDO) {
        if(ObjectUtil.isEmpty(drawRoomDO.getId())) {
            return false;
        }
        DrawRoomDO drawRoom = this.getById(drawRoomDO.getId());
        if(ObjectUtil.isEmpty(drawRoom) || !drawRoom.getOpenId().equals(CurrentUserUtil.getUserId())){
            return false;
        }
        drawRoom.setTitle(drawRoomDO.getTitle());
        return this.updateById(drawRoom);
    }

    /**
     * 查询用户聊天室
     *
     * @param
     * @return List<DrawRoomVO>
     * @Author: zc.wu
     * @Date: 2023/7/31 0031 下午 04:25
     */
    @Override
    public List<DrawRoomVO> getDrawRoom(String idsString) {
        List<DrawRoomDO> list;
        LambdaQueryWrapper<DrawRoomDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DrawRoomDO::getOpenId, CurrentUserUtil.getUserId());
        if (idsString != null) {
            String[] split;
            split = idsString.split(",");
            lambdaQueryWrapper.in(DrawRoomDO::getId, Arrays.asList(split));
        }
        lambdaQueryWrapper.orderByDesc(DrawRoomDO::getUpdateTime);
        list = this.list(lambdaQueryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<DrawRoomVO> drawRoomVOList = Lists.newArrayList();
        list.stream().forEach(drawRoomDO -> {
            DrawRoomVO drawRoomVO = new DrawRoomVO();
            BeanUtil.copyProperties(drawRoomDO, drawRoomVO);
            drawRoomVOList.add(drawRoomVO);
        });
        return drawRoomVOList;
    }

}




