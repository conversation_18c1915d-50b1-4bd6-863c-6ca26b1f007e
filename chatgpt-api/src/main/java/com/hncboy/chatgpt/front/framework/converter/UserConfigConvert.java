package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.dto.UserConfigDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.UserConfig;
import com.hncboy.chatgpt.front.framework.domain.vo.UserConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 用户配置相关信息 领域对象转换器
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Mapper
public interface UserConfigConvert {

    UserConfigConvert INSTANCE = Mappers.getMapper(UserConfigConvert.class);

    /**
     * UserConfigDTO转UserConfig
     *
     * @Author: wzhic
     * @Date:2023/4/19
     */
    UserConfig dtoToEntity(UserConfigDTO dto);

    /**
     * UserConfig 转UserConfigVO
     *
     * @Author: wzhic
     * @Date:2023/4/19
     */
    UserConfigVO entityToVO(UserConfig entity);

    /**
     * List<UserConfig> 转List<UserConfigVO>
     *
     * @Author: wzhic
     * @Date:2023/4/19
     */
    List<UserConfigVO> entityListToVOList(List<UserConfig> entityList);

    /**
     * 查询DTO转换
     *
     * @Author: wzhic
     * @Date:2023/4/19
     */
    UserConfig queryDtoToEntity(UserConfigDTO dto);


}
