package com.hncboy.chatgpt.front.framework.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @描述:APP用户签到 DTO
 * @版本：v1.0.0
 * @作者: zc.wu
 * @时间:2021/10/15
 */
@Data
public class AppSignDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "用户ID")
    private Integer userId;

    @Schema(title = "是否中断")
    private String interruptStatus;

    @Schema(title = "签到日期")
    private Date checkDate;

    @Schema(title = "补签")
    private String repairCheck;

    @Schema(title = "备注")
    private String remark;


}
