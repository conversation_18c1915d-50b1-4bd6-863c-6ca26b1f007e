package com.hncboy.chatgpt.front.service;

import com.hncboy.chatgpt.front.framework.domain.entity.RoleMsgTemplate;

import java.util.List;

/**
 * 角色消息模版 Dao
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/8/2
 */
public interface RoleMsgTemplateService {


    /**
     * 根据配置查询模版
     *
     * @param configId
     * @return List<RoleMsgTemplate>
     * @Author: zc.wu
     * @Date: 2023/8/2 0002 下午 05:53
     */
    List<RoleMsgTemplate> queryRoleMsgTemplateList(Integer configId);


}
