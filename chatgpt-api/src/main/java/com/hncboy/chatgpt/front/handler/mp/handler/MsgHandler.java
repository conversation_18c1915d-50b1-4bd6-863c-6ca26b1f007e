package com.hncboy.chatgpt.front.handler.mp.handler;

import com.hncboy.chatgpt.front.framework.config.ChatConfig;
import com.hncboy.chatgpt.front.framework.util.DownloadURLFile;
import com.hncboy.chatgpt.front.handler.mp.builder.TextBuilder;
import com.hncboy.chatgpt.front.helper.OpenAiMsgHelper;
import com.unfbx.chatgpt.OpenAiClient;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import com.unfbx.chatgpt.entity.chat.Message;
import com.unfbx.chatgpt.entity.images.ImageResponse;
import com.unfbx.chatgpt.interceptor.OpenAILogger;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;


/**
 * 文本消息处理
 */
@Component
@Slf4j
public class MsgHandler extends AbstractHandler {

    @Resource
    private ChatConfig chatConfig;

    @Autowired
    @Lazy
    private WxMpService wxMpService;

    @Autowired
    private OpenAiMsgHelper openAiMsgHelper;

    private OpenAiClient client;


    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage, Map<String, Object> context,
                                    WxMpService weixinService, WxSessionManager sessionManager) {
        String msg = wxMessage.getContent();
        if (msg.contains("照片") || msg.contains("图片")) {
            CompletableFuture.runAsync(() -> imageMsgHandler(wxMessage));
        }
        String msg1 = "" +
                "\uD83D\uDC4F欢迎关注我们！\n" +
                "\n" +
                "浙江方顶数融科技有限公司专注于构建人工智能应用架构。" +
                "通过多智能体作业平台及一系列应用产品和工具链，" +
                "我们帮助企业以可控且可评估的方式实施人工智能技术，" +
                "从而提升运营效率并提高员工工作效率。" +
                "此外，我们与企业共同构建领域模型，助力企业转型其产品和服务，创造更大的价值。";
        return new TextBuilder().build(msg1, wxMessage, weixinService);
    }


    @PostConstruct
    public void getClient() {
        //国内访问需要做代理，国外服务器不需要
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 50482));
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor(
                new OpenAILogger());
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .addInterceptor(httpLoggingInterceptor).connectTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(120, TimeUnit.SECONDS).readTimeout(120, TimeUnit.SECONDS).build();
        client = OpenAiClient.builder()
                .apiKey(Arrays.asList(chatConfig.getOpenaiApiKey()))
                .okHttpClient(okHttpClient).build();
    }


    /**
     * 自然语句处理
     *
     * @param wxMpXmlMessage
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/6 13:47
     */
    public void textMsgHandler(WxMpXmlMessage wxMpXmlMessage) {
        StringBuffer content = new StringBuffer();
        //聊天模型：gpt-3.5
        Message message = Message.builder().role(Message.Role.USER)
                .content(wxMpXmlMessage.getContent()).build();
        //构建会话
        List<Message> msg = openAiMsgHelper.queryMsg(wxMpXmlMessage.getFromUser(), message);
        ChatCompletion chatCompletion = ChatCompletion.builder().messages(msg).build();
        ChatCompletionResponse chatCompletionResponse = null;
        try {
            chatCompletionResponse = client.chatCompletion(chatCompletion);
            chatCompletionResponse.getChoices().forEach(e -> {
                System.out.println(e.getMessage());
                content.append(e.getMessage().getContent());
            });
            if (StringUtils.isNotBlank(content.toString())) {
                openAiMsgHelper.addAnswer(wxMpXmlMessage.getFromUser(), content.toString());
            }
        } catch (Exception e) {
            content.append("请求超时啦，请再试试看呢");
            log.error("请求出错", e);
        }
        String res = content.toString();
        System.out.println(res.length());
        if (res.toString().length() > 600) {
            res = res.toString().substring(0, 600);
        }
        WxMpKefuMessage wxMpMassOpenIdsMessage = new WxMpKefuMessage();
        wxMpMassOpenIdsMessage.setToUser(wxMpXmlMessage.getFromUser());
        wxMpMassOpenIdsMessage.setMsgType(WxConsts.MassMsgType.TEXT);
        wxMpMassOpenIdsMessage.setContent(res);
        try {
            boolean send = wxMpService.getKefuService().sendKefuMessage(wxMpMassOpenIdsMessage);
            log.info("发送客服消息结果:{}", send);
        } catch (WxErrorException e) {
            log.error("发送客服消息错误:{}", e.getMessage());
        }
    }

    /**
     * 根据描述找图片
     *
     * @param wxMpXmlMessage
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/6 13:46
     */
    public void imageMsgHandler(WxMpXmlMessage wxMpXmlMessage) {
        ImageResponse imageResponse = client.genImages(wxMpXmlMessage.getContent());
        imageResponse.getData().forEach(image -> {
            String url = image.getUrl();
            try {
                File down = DownloadURLFile.downloadByUrl(url, "D:\\image\\");
                WxMediaUploadResult uploadResult = wxMpService.getMaterialService()
                        .mediaUpload("image", down);
                WxMpKefuMessage wxMpMassOpenIdsMessage = new WxMpKefuMessage();
                wxMpMassOpenIdsMessage.setToUser(wxMpXmlMessage.getFromUser());
                wxMpMassOpenIdsMessage.setMsgType(WxConsts.MassMsgType.IMAGE);
                wxMpMassOpenIdsMessage.setMediaId(uploadResult.getMediaId());
                wxMpService.getKefuService().sendKefuMessage(wxMpMassOpenIdsMessage);
            } catch (WxErrorException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
