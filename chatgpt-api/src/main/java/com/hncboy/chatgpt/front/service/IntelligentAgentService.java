package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.domain.dto.IntelligentAgentDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.IntelligentAgent;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentAgentVO;

import java.util.List;

/**
 * 智能体信息 Dao
 *
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/2/28
 */
public interface IntelligentAgentService {


    /**
     * 根据ID查询智能体信息
     *
     * @param id
     * @return IntelligentAgentVO
     * @Author: zc.wu
     * @Date: 2024/3/1 0001 上午 11:53
     */
    IntelligentAgentVO queryInfoById(Integer id);


    /**
     * 根据ID查询智能体信息(包含核心系统回复 不能暴漏到前端 只能后台使用)
     *
     * @param id
     * @return IntelligentAgentVO
     * @Author: zc.wu
     * @Date: 2024/3/1 0001 上午 11:53
     */
    IntelligentAgent queryInfoSysContentById(Integer id);


    /**
     * 分页查询智能体信息列表
     *
     * @Param:@param example
     * @return:IPage<IntelligentAgentVO>
     * @Author: wZhic
     * @Date:2024/2/28
     */
    IPage<IntelligentAgentVO> queryListEntityPage(IntelligentAgentDTO dto);


    /**
     * 查询智能体列表
     *
     * @param dto
     * @return List<IntelligentAgentVO>
     * @Author: zc.wu
     * @Date: 2024/2/28 0028 上午 11:16
     */
    List<IntelligentAgentVO> queryListEntityList(IntelligentAgentDTO dto);


    /**
     * 更新智能体信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/2/28
     */
    boolean updateEntity(IntelligentAgentDTO record);

    /**
     * 插入智能体信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/2/28
     */
    boolean insertEntity(IntelligentAgentDTO record);

    /**
     * 删除智能体信息
     *
     * @Param:@param id
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/2/28
     */
    boolean deleteById(Integer id);

    /**
     * 根据模型名称获取默认智能体信息
     *
     * @Author: zd.zhong
     * @Date: 2024/3/18 0001 上午 11:53
     * @param modelName
     * @return IntelligentAgentVO
     */
    IntelligentAgentVO queryIntelligentByModelName(String modelName);

    /**
     * 查询智能体分类列表
     *
     * @return List<String>
     * @Author: zc.wu
     * @Date: 2024/2/28 0028 上午 11:16
     */
    List<String> queryTagList();


    /**
     * 远程接口抓取数据
     *
     * @param
     * @return void
     * @Author: zc.wu
     * @Date: 2024/2/28 0028 上午 11:36
     */
    void saveDataRemotely();

    List<IntelligentAgent> queryListByIds(List<Integer> ids);

    void addUseCnt(Integer id);
}
