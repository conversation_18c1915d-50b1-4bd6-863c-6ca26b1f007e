package com.hncboy.chatgpt.front.controller.common;


import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.hncboy.chatgpt.front.framework.constant.ApplicationConstant;
import com.hncboy.chatgpt.front.framework.converter.WxPayOrderConvert;
import com.hncboy.chatgpt.front.framework.domain.dto.PayCodeDTO;
import com.hncboy.chatgpt.front.framework.domain.dto.WxPayOrderDTO;
import com.hncboy.chatgpt.front.framework.exception.ServiceException;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.framework.util.AESUtil;
import com.hncboy.chatgpt.front.momo.MomoPaymentService;
import com.hncboy.chatgpt.front.service.PayService;
import com.hncboy.chatgpt.front.service.RedisService;
import com.hncboy.chatgpt.front.service.WxPayOrderService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 交易性接口
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/pay")
//@FrontPreAuth
@RequiredArgsConstructor
public class PayController {


    private final PayService payService;
    private final WxPayOrderService wxPayOrderService;
    private final MomoPaymentService momoPaymentService;
    @Value("${ali-pay.domain}")
    private String domain;
    @Value("${ali-pay.appId}")
    private String appId;

    @Value("${ali-pay.alipayPublicKey}")
    private String alipayPublicKey;
    @Value("${ali-pay.privateKey}")
    private String privateKey;
    @Value("${wx.miniapp.configs[0].appid}")
    private String wxAppId;
    private final RedisService redisService;
    /**
     * 生成支付宝支付二维码
     *
     * @param productId the product id
     * @return the R
     */
    @PostMapping(value = "/alipay/pay/{productId}", name = "支付宝支付", produces = MediaType.APPLICATION_JSON_VALUE)
    public R alipayPayQrCode(@PathVariable final Long productId) {
        return R.data(payService.generatePayQrCode(productId));
    }

    /**
     * 生成支付二维码
     * 
     * type:1:支付宝 2：微信
     * @param payCodeDTO
     * @return
     */
    @PostMapping(value = "/payQrCode", name = "获取支付二维码", produces = MediaType.APPLICATION_JSON_VALUE)
    public R PayQrCode(@RequestBody PayCodeDTO payCodeDTO) {
        
        if("1".equals(payCodeDTO.getType())){
            return R.data(payService.generatePayQrCode(payCodeDTO.getProductId()));
        }else{
            WxPayOrderDTO wxPayOrderDTO = WxPayOrderConvert.INSTANCE.queryDtoToEntity(payCodeDTO);
            return R.data(payService.generateWxPayQrCode(wxPayOrderDTO));
        }
        
    }

    /**
     * 生成支付二维码 非智能社渠道
     *
     * type:1:支付宝 2：微信
     * @param payCodeDTO
     * @return
     */
    @PostMapping(value = "/payQrCodeNoUser", name = "获取支付二维码 非智能社渠道", produces = MediaType.APPLICATION_JSON_VALUE)
    public R payQrCodeNoUser(@RequestBody PayCodeDTO payCodeDTO) {

        try {
            String userValue = payCodeDTO.getUserValue();
            String userId = payCodeDTO.getUserId().toString();
            String key = ApplicationConstant.REDIS_USER_KEY + userId;
            String s = (String) redisService.get(key + "_key");
            SecretKeySpec keyFromPassword = AESUtil.getKeyFromPassword(s);
            String encrypt = AESUtil.decrypt(userValue, keyFromPassword);
            if(!encrypt.equals(userId)){
                log.error("用户信息不匹配 ,错误用户id为:{}",encrypt);
                throw new RuntimeException("用户信息不匹配");
            }

        } catch (ServiceException e) {
            log.error("用户id解密失败",e);
            return R.fail("订单时间超时，请重新下单");
        } catch (Exception e) {
            log.error("用户信息解密异常",e);
            return R.fail("用户信息不匹配");
            //throw new ServiceException("用户信息不匹配");
        }

        if("1".equals(payCodeDTO.getType())){
            return R.data(payService.generatePayQrCode(payCodeDTO.getProductId()));
        }else{
            WxPayOrderDTO wxPayOrderDTO = WxPayOrderConvert.INSTANCE.queryDtoToEntity(payCodeDTO);
            return R.data(payService.generateWxPayQrCode(wxPayOrderDTO));
        }

    }

    /**
     * 获取商品列表
     *
     * @return the product list
     */
    @GetMapping(value = "/product/list", name = "获取商品列", produces = MediaType.APPLICATION_JSON_VALUE)
    public R getProductList(final String type,final String channel) {
        return R.data(payService.getProductList(type,channel));
    }

    /**
     * 获取商品详情
     *
     * @return the product
     */
    @GetMapping(value = "/product/{productId}", name = "获取商品详情", produces = MediaType.APPLICATION_JSON_VALUE)
    public R getProductId(@PathVariable final Long productId) {
        return R.data(payService.getProductId(productId));
    }


    /**
     * 支付宝支付状态查询
     *
     * @param orderId the order id
     * @return the R
     */
    @PostMapping(value = "/alipay/status/{orderId}", name = "支付宝支付状态", produces = MediaType.APPLICATION_JSON_VALUE)
    public R alipayIsSucceed(@PathVariable final String orderId) {
        return R.data(payService.paymentStatus(orderId));
    }

    /**
     * 支付宝支付状态查询
     *
     * @return the R
     */
    @PostMapping(value = "/status", name = "获取支付状态", produces = MediaType.APPLICATION_JSON_VALUE)
    public R payIsSucceed(@RequestBody PayCodeDTO payCodeDTO) {

        if("1".equals(payCodeDTO.getType())){
            return R.data(payService.paymentStatus(payCodeDTO.getOrderNo()));
        }else{
            //2-微信, 3-momo
            return R.data(wxPayOrderService.queryOrderBySn(payCodeDTO.getOrderNo()));
        }

    }

    @Operation(summary = "唤起支付")
    @PostMapping("/createPrepayOrder")
    public R createPrepayOrder(@RequestBody PayCodeDTO payCodeDTO,HttpServletRequest request)
    {
        String ip = request.getRemoteAddr();
        payCodeDTO.setSpbillCreateIp(ip);


        try {

            if("1".equals(payCodeDTO.getType())){
                return R.data(payService.createPrepayOrder(payCodeDTO));
            }else if("2".equals(payCodeDTO.getType())){
                WxPayOrderDTO wxPayOrderDTO = WxPayOrderConvert.INSTANCE.queryDtoToEntity(payCodeDTO);
                return R.data(wxPayOrderService.createPrepayOrder(wxPayOrderDTO));
            }else{
                WxPayOrderDTO wxPayOrderDTO = WxPayOrderConvert.INSTANCE.queryDtoToEntity(payCodeDTO);
                return R.data(momoPaymentService.createPrepayOrder(wxPayOrderDTO));
            }

        } catch (WxPayException e) {
            throw new RuntimeException(e);
        }
    }


    @Operation(summary = "唤起支付(海报支付)")
    @PostMapping("/createPrepayOrderNoUser")
    public R payQrCodeNoUser(@RequestBody PayCodeDTO payCodeDTO,HttpServletRequest request)
    {
        String ip = request.getRemoteAddr();
        payCodeDTO.setSpbillCreateIp(ip);

        try {
            String userValue = payCodeDTO.getUserValue();
            String userId = payCodeDTO.getUserId().toString();
            String key = ApplicationConstant.REDIS_USER_KEY + userId;
            String s = (String) redisService.get(key + "_key");
            SecretKeySpec keyFromPassword = AESUtil.getKeyFromPassword(s);
            String encrypt = AESUtil.decrypt(userValue, keyFromPassword);
            if(!encrypt.equals(userId)){
                log.error("用户信息不匹配 ,错误用户id为:{}",encrypt);
                throw new ServiceException("用户信息不匹配");
            }

        } catch (ServiceException e) {
            log.error("解密失败",e);
            return R.fail("订单时间超时，请重新下单");
            //throw new ServiceException("用户信息不匹配");
        } catch (Exception e) {
            log.error("解密失败",e);
            return R.fail("用户信息不匹配");
             //throw new ServiceException("用户信息不匹配");
        }

        try {
            if("1".equals(payCodeDTO.getType())){
                //return R.data(payService.createPrepayOrder(payCodeDTO));

                return R.data(payService.generatePayQrCodeNoUser(payCodeDTO));
            }else{
                WxPayOrderDTO wxPayOrderDTO = WxPayOrderConvert.INSTANCE.queryDtoToEntity(payCodeDTO);
                return R.data(wxPayOrderService.createPrepayOrder(wxPayOrderDTO));
            }

        } catch (WxPayException e) {
            throw new RuntimeException(e);
        }
    }


    //@GetMapping(value = "/pcPay")
    @ResponseBody
    public void pcPay(HttpServletResponse response) {
        try {

            final AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setServerUrl("https://openapi.alipay.com/gateway.do");
            alipayConfig.setFormat("json");
            alipayConfig.setCharset("UTF8");
            alipayConfig.setSignType("RSA2");
            alipayConfig.setAppId(appId);
            alipayConfig.setAlipayPublicKey(alipayPublicKey);
            alipayConfig.setPrivateKey(privateKey);
            //构建支付宝订单
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);

            String totalAmount = "0.01";
            String outTradeNo = "12345678910";//StringUtils.getOutTradeNo();
            log.info("pc outTradeNo>" + outTradeNo);

            String returnUrl = domain + "/callback/order";//aliPayBean.getDomain() + RETURN_URL;
            String notifyUrl = domain + "/callback/order";//aliPayBean.getDomain() + NOTIFY_URL;
            AlipayTradePagePayModel model = new AlipayTradePagePayModel();

            model.setOutTradeNo(outTradeNo);
            model.setProductCode("FAST_INSTANT_TRADE_PAY");
            model.setTotalAmount(totalAmount);
            model.setSubject("Javen PC支付测试");
            model.setBody("Javen IJPay PC支付测试");
            model.setPassbackParams("passback_params");
            model.setQrPayMode("4");
            /**
             * 花呗分期相关的设置,测试环境不支持花呗分期的测试
             * hb_fq_num代表花呗分期数，仅支持传入3、6、12，其他期数暂不支持，传入会报错；
             * hb_fq_seller_percent代表卖家承担收费比例，商家承担手续费传入100，用户承担手续费传入0，仅支持传入100、0两种，其他比例暂不支持，传入会报错。
             */
//            ExtendParams extendParams = new ExtendParams();
//            extendParams.setHbFqNum("3");
//            extendParams.setHbFqSellerPercent("0");
//            model.setExtendParams(extendParams);

            //AliPayApi.tradePage(alipayClient,response, model, notifyUrl, returnUrl);
            // https://opensupport.alipay.com/support/helpcenter/192/201602488772?ant_source=antsupport
            // Alipay Easy SDK（新版）目前只支持输出form表单，不支持打印出url链接。
            // AliPayApi.tradePage(response, "GET", model, notifyUrl, returnUrl);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Operation(summary = "小程序唤起支付")
    @PostMapping("/createWxMinOrder")
    public R createWxMinOrder(@RequestBody PayCodeDTO payCodeDTO,HttpServletRequest request)
    {
        String ip = request.getRemoteAddr();
        payCodeDTO.setSpbillCreateIp(ip);
        try {
            WxPayOrderDTO wxPayOrderDTO = WxPayOrderConvert.INSTANCE.queryDtoToEntity(payCodeDTO);
            wxPayOrderDTO.setAppId(wxAppId);
            return R.data(wxPayOrderService.createPrepayOrder(wxPayOrderDTO));
        } catch (WxPayException e) {
            throw new RuntimeException(e);
        }
    }

}
