package com.hncboy.chatgpt.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.front.framework.domain.entity.HomeConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * Mapper
 *
 * @Version：v1.0.0
 * @Author: wuzhic
 * @Date:2023/4/11
 */
@Mapper
public interface HomeConfigMapper extends BaseMapper<HomeConfig> {


    /**
     * 更新可用次数
     *
     * @param id
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:51
     */
    @Update("update home_config set num=num+1 where id=#{id}")
    void updateConfigNum(@Param("id") Integer id);

}
