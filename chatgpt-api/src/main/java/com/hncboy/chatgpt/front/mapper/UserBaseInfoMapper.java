package com.hncboy.chatgpt.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 用户基础信息 Mapper
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2024/2/26
 */
@Mapper
public interface UserBaseInfoMapper extends BaseMapper<UserBaseInfo> {


    /**
     * 更新绘画可用次数
     *
     * @param id
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:51
     */
    @Update("update user_base_info set draw_num=draw_num-#{drawNum} where id=#{id} and draw_num>0")
    void updateUserDrawNumMp(@Param("id") Integer id, @Param("drawNum") Integer drawNum);

    /**
     * 更新音乐可用次数
     *
     * @param id
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/4/25 14:51
     */
    @Update("update user_base_info set music_num=music_num-#{musicNum} where id=#{id} and music_num>0")
    void updateUserMusicNumMp(@Param("id") Integer id, @Param("musicNum") Integer musicNum);

    /**
     * 更新写作可用次数
     *
     * @param id
     * @return void
     * @Author: zd.zhong
     * @Date: 2024/7/2 14:51
     */
    @Update("update user_base_info set write_num=write_num-#{writeNum} where id=#{id} and write_num>0")
    void updateUserWriteNumMp(@Param("id") Integer id, @Param("writeNum") Integer writeNum);

    /**
     * 更新塔罗牌可用次数
     * @param id
     * @param tarotNum
     */
    @Update("update user_base_info set tarot_coins=tarot_coins-#{writeNum} where id=#{id} and user_base_info.tarot_coins>#{writeNum}")
    void updateUserTarotNumMp(@Param("id") Integer id, @Param("writeNum") Integer tarotNum);

    /**
     * 更新免费可用次数
     *
     * @param id
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:51
     */
    @Update("update user_base_info set free_num=free_num-#{species} where id=#{id} and free_num>0")
    void updateUserFreeNumMp(@Param("id") Integer id, @Param("species") Integer species);


    /**
     * 更新充值可用次数
     *
     * @param id
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:51
     */
    @Update("update user_base_info set use_num=use_num-#{species} where id=#{id} and use_num>0")
    void updateUserNumMp(@Param("id") Integer id, @Param("species") Integer species);


    /**
     * 增加可用次数
     *
     * @param id
     * @return void
     * @Author: zc.wu
     * @Date: 2023/4/14 14:51
     */
    @Update("update user_base_info set use_num=use_num+#{species} where id=#{id}")
    void addUserNumMp(@Param("id") Integer id, @Param("species") Integer species);

    /**
     * 添加用户积分
     * @param userId
     * @param newPoints
     */
    @Update("update user_base_info set points=#{add} where id=#{id}")
    void addPoints(@Param("id") Integer userId, @Param("add") Integer newPoints);

    /**
     * 添加用户塔罗币
     * @param userId
     * @param tarotCoins
     */
    @Update("update user_base_info set tarot_coins=#{add} where id=#{id}")
    void addTarotCoins(@Param("id") Integer userId, @Param("add") Integer tarotCoins);

    /**
     * 添加用户绘画次数
     * @param userId
     * @param times
     */
    @Update("update user_base_info set draw_num=draw_num+#{addDrawNum} where id=#{id}")
    void addUserDrawNum(@Param("id") Integer userId, @Param("addDrawNum") Integer times);

    /**
     * 添加用户音乐创作次数
     * @param userId
     * @param times
     */
    @Update("update user_base_info set music_num=music_num+#{addMusicNum} where id=#{id}")
    void addUserMusicNum(@Param("id") Integer userId, @Param("addMusicNum") Integer times);

    /**
     * 添加用户写作点数
     * @param userId
     * @param times
     */
    @Update("update user_base_info set write_num=write_num+#{addWriteNum} where id=#{id}")
    void addUserWriteNum(@Param("id") Integer userId, @Param("addWriteNum") Integer times);

    /**
     * 添加用户塔罗币点数
     * @param userId
     * @param times
     */
    @Update("update user_base_info set tarot_coins=tarot_coins+#{addWriteNum} where id=#{id}")
    void addUserTarotNumMp(@Param("id") Integer userId, @Param("addWriteNum") Integer times);

    /**
     * 根据id设置父级id
     * @param id
     * @param parentId
     */
    @Update("update user_base_info set parent_id=#{parentId} where id=#{id}")
    void setParentId(@Param("id") Integer id, @Param("parentId") Integer parentId);

    /**
     * 根据id更新用户昵称
     * @param id
     * @param nickName
     */
    @Update("update user_base_info set nick_name=#{nickName}, head_sculpture=#{headSculpture} where id=#{id}")
    void updateUserInfoById(@Param("id") Integer id, @Param("nickName") String nickName, @Param("headSculpture") String headSculpture);

    /**
     * 根据id更新用户账号
     * @param id
     * @param account
     */
    @Update("update user_base_info set account=#{account} where id=#{id}")
    void updateUserAccountById(@Param("id") Integer id, @Param("account") String account);
}
