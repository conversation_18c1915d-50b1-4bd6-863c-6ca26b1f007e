package com.hncboy.chatgpt.front.framework.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户配置相关信息 DTO
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Data
public class UserConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "用户ID")
    private String openid;

    @Schema(title = "配置信息")
    private String content;

    @Schema(title = "配置类型1系统2用户广告3用户配置")
    private Integer type;


}
