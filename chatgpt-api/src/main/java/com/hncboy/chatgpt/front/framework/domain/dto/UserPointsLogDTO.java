package com.hncboy.chatgpt.front.framework.domain.dto;

import com.hncboy.chatgpt.front.framework.domain.query.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


@EqualsAndHashCode(callSuper = true)
@Data
public class UserPointsLogDTO extends PageParam implements Serializable {

    private Integer id;

    @Schema(title = "用户id")
    private Integer userId;

    @Schema(title = "关联订单")
    private String relOrder;

    @Schema(title = "积分")
    private Integer points;

    @Schema(title = "积分类型")
    private String pointsType;

    @Schema(title = "备注")
    private String remark;

    @Schema(title = "创建人")
    private String createBy;

    @Schema(title = "创建时间")
    private String createTime;

    @Schema(title = "更新人")
    private String updateBy;

    @Schema(title = "更新时间")
    private String updateTime;

}
