package com.hncboy.chatgpt.front.framework.domain.request;

import com.unfbx.chatgpt.entity.chat.Content;
import com.unfbx.chatgpt.entity.chat.Message;
import lombok.Builder;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;


@Data
@Builder
public class BuildChatProcessRequest {


    /**
     * 消息体
     */
    LinkedList<Message> messages;


    List<Content> contentList;


    /**
     * 消息ID
     */
    private Long msgId;


    /**
     * tokens
     */
    private Integer tokens;


}
