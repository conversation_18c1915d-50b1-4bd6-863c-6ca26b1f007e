package com.hncboy.chatgpt.front.framework.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PayOutComeVo {
    private Long productId;

    /**
     * 金额
     */
    private Double productPrice;
    /**
     * 名称
     */
    private String productName;

    /**
     * 类型(CHAT-对话;DRAW-绘图;COMMON-通用)
     */
    private String type;


    /**
     * 单位(DAY-天;MONTH-月;YEAR-年;TIMES-次数)
     */
    private String unit;
    /**
     * 描述
     */
    private String remark;

    /**
     * 数量
     */
    private Long num;


    //付款状态
    private String payStatus;
}
