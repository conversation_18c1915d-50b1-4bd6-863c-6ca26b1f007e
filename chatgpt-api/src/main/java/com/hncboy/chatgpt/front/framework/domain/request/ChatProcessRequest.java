package com.hncboy.chatgpt.front.framework.domain.request;

import com.hncboy.chatgpt.front.framework.domain.entity.ChatRoomDO;
import com.unfbx.chatgpt.entity.images.Image;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2023/3/23 13:17 消息处理请求
 */
@Data
@Schema(title = "消息处理请求")
public class ChatProcessRequest {

    @Size(min = 1, max = 5000, message = "问题字数范围[1, 5000]")
    @Schema(title = "问题")
    private String prompt;

    @Schema(title = "text | ping | file")
    private String msgType;

    @Schema(title = "聊天室")
    private ChatRoomDO chatRoomDO;

    @Schema(title = "openId")
    private String openId;

    @Schema(title = "filePath")
    private String filePath;

    @Schema(title = "ip")
    private String ip;

    @Schema(title = "发音人")
    private String deftSpeakUser;

    @Schema(title = "消息ID")
    private Long msgId;

    @Schema(title = "图片参数")
    private Image image;

    @Schema(title = "语音对话")
    private Boolean speech = false;

    @Schema(title = "系统")
    private String sysContent;


}
