package com.hncboy.chatgpt.front.framework.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 提词器信息 DTO
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/5/8
 */
@Data
public class PrompterInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "行业")
    private String industry;

    @Schema(title = "行业名称")
    private String industryName;

    @Schema(title = "提词器")
    private String keyWord;

    @Schema(title = "状态")
    private Integer status;

    private Integer pageNo = 1;

    private Integer pageSize = 20;


}
