package com.hncboy.chatgpt.front.controller.chatoi;

import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.helper.ChannelConfigHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.STATUS_DISABLE;

/**
 * @Description:
 * @Version：v1.0.0
 * @Author: zc.wu
 * @Date:2023/4/10
 */

@AllArgsConstructor
@Tag(name = "ChatOI相关接口")
@RestController
@RequestMapping("/chatOi")
@Slf4j
public class ChatOiController {

    private final ChannelConfigHelper channelConfigHelper;

    @Operation(summary = "修改通道状态")
    @PostMapping("/updateChannelStatus")
    public R getUserInfoPhone(Integer channelConfigId) {
        channelConfigHelper.updateChannelStatusById2(channelConfigId, STATUS_DISABLE);
        return R.success();
    }

}
