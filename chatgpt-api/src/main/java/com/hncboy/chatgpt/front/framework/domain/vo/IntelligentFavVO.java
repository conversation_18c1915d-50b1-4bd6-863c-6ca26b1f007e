package com.hncboy.chatgpt.front.framework.domain.vo;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 智能体收藏 VO
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/1
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IntelligentFavVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "智能体ID")
    private Integer agentId;

    @Schema(title = "创建者")
    private String createBy;

    @Schema(title = "创建时间")
    private Date createTime;

    @Schema(title = "智能体信息")
    private IntelligentAgentVO agentVo;


}
