package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.entity.WriteAgent;
import com.hncboy.chatgpt.front.framework.domain.vo.WriteAgentVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 写作应用信息 领域对象转换器
 * @Version: v1.0.0
 * @Author: zzd
 * @Date: 2024/6/27 14:06
 */
@Mapper
public interface WriteAgentConvert {

   WriteAgentConvert INSTANCE = Mappers.getMapper(WriteAgentConvert.class);

  /**
   * WriteAgent转WriteAgent
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  WriteAgent dtoToEntity(WriteAgent dto);

  /**
   * WriteAgent 转WriteAgentVO
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  WriteAgentVO entityToVO(WriteAgent entity);

  /**
   * List<WriteAgent> 转List<WriteAgentVO>
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
  List<WriteAgentVO> entityListToVOList(List<WriteAgent> entityList);

  /**
   * 查询DTO转换
   * @Author: zzd
   * @Date: 2024/6/27 14:06
   */
   WriteAgent queryDtoToEntity(WriteAgent dto);


}
