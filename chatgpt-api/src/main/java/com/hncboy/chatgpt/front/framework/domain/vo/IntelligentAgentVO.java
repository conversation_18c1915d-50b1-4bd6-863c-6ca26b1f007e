package com.hncboy.chatgpt.front.framework.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 智能体信息 VO
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/2/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class IntelligentAgentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "描述")
    private String description;

    @Schema(title = "分类")
    private String tag;

    @Schema(title = "输入提示")
    private String inputExample;

    @Schema(title = "图标")
    private String imgUrl;

    @Schema(title = "是否热门")
    private Integer hot;

    @Schema(title = "是否精选推荐")
    private Integer featRecs;

    @Schema(title = "是否收费(0不是1是)")
    private Integer charge;

    @Schema(title = "模型名称")
    private String modelName;

    @Schema(title = "模型ID")
    private String gid="gpt-3.5-turbo";

    @Schema(title = "回复数")
    private Integer maxToken=2048;

    @Schema(title = "随机数")
    private Double temperature=0.2;

    @Schema(title = "上下文数量")
    private Integer numContexts=5;

    @Schema(title = "可用次数")
    private Integer useCnt;

    @Schema(title = "0启用1禁用")
    private Integer status;

    @Schema(title = "创建者")
    private String createBy;

    @Schema(title = "创建时间")
    private LocalDateTime createTime;

    @Schema(title = "更新者")
    private String updateBy;

    @Schema(title = "更新时间")
    private LocalDateTime updateTime;

    @Schema(title = "备注")
    private String remark;


}
