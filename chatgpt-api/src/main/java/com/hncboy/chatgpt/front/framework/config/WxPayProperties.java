package com.hncboy.chatgpt.front.framework.config;

import com.hncboy.chatgpt.front.framework.util.JsonUtils;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * wxpay pay properties.
 *
 * <AUTHOR> Wang
 */
@Data
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayProperties {

    /**
     * 多个微信商户配置信息
     */
    private List<PayConfig> configs;

    @Override
    public String toString() {
        return JsonUtils.toJson(this);
    }

    @Data
    public static class PayConfig {
        /**
         * 设置微信微信商户或者小程序等的appid
         */
        private String appId;

        /**
         * 微信支付商户号
         */
        private String mchId;

        /**
         * 微信支付商户密钥
         */
        private String mchKey;
        /**
         * 微信支付V3密钥
         */
        private String apiV3Key;

        /**
         * 支付回调地址
         */
        private String notifyUrl;

        /**
         * 服务商模式下的子商户公众账号ID，普通模式请不要配置，请在配置文件中将对应项删除
         */
        private String subAppId;

        /**
         * 服务商模式下的子商户号，普通模式请不要配置，最好是请在配置文件中将对应项删除
         */
        private String subMchId;

        /**
         * apiclient_cert.p12文件的绝对路径，或者如果放在项目中，请以classpath:开头指定
         */
        private String keyPath;
        private String privateKeyPath;
        private String privateCertPath;
    }

}
