package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.domain.dto.PayCodeDTO;
import com.hncboy.chatgpt.front.framework.domain.dto.WxPayOrderDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.Product;
import com.hncboy.chatgpt.front.framework.domain.entity.TransferInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.AlipayPayCodeVo;
import com.hncboy.chatgpt.front.framework.domain.vo.PayOutComeVo;
import com.hncboy.chatgpt.front.framework.domain.vo.ProductVo;
import com.hncboy.chatgpt.front.framework.domain.vo.WxPayCodeVO;
import com.hncboy.chatgpt.front.framework.handler.response.R;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 雨纷纷旧故里草木深
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface PayService {

    /**
     * 获取产品列表。
     * @param type 类型
     * @return the product list
     */
    List<Product> getProductList(String type,String channel);
    Product getProductId(final Long id);


    /**
     * 根据ID删除产品
     *
     * @param id the id
     */
    void deleteProductById(final Long id);


    /**
     * 分页获取产品数据
     */
    IPage<ProductVo> getProductPageVo(final int pageNum, final String prompt);


    /**
     * 生成支付宝支付二维码
     *
     * @param productId the product id
     * @return the alipay pay code vo
     */
    AlipayPayCodeVo generatePayQrCode(final Long productId);
    AlipayPayCodeVo generatePayQrCodeNoUser(PayCodeDTO payCodeDTO);

    /**
     * 生成微信支付二维码
     * @param wxPayOrderDTO
     * @return
     */
    WxPayCodeVO generateWxPayQrCode(WxPayOrderDTO wxPayOrderDTO);


    /**
     * 支付宝回调
     *
     * @param request the request
     * @return the string
     */
    String alipayPullback(final HttpServletRequest request);

    void wxPullback(Map body, HttpServletRequest request);


    /**
     * Payment status string.
     *获取订单状态
     * @param orderNo the order no
     * @return the string
     */
    PayOutComeVo paymentStatus(final String orderNo);

    /**
     * 发起支付
     * @param payCodeDTO
     * @return
     */
    PayOutComeVo createPrepayOrder(PayCodeDTO payCodeDTO);

    /**
     * 创建提现订单
     * @param transferInfo
     * @return
     */
    R createPayouts(TransferInfo transferInfo);

    /**
     * 提现
     * @return
     */
    void payouts();
    void checkPayoutsOrder();
    //PayOutComeVo createPrepayOrderNoUser(PayCodeDTO payCodeDTO);

}
