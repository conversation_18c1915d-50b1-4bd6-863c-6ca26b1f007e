package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hncboy.chatgpt.front.framework.domain.dto.IntelligentFavDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.IntelligentFavVO;

import java.util.List;
import java.math.BigDecimal;

/**
 * 智能体收藏 Dao
 * @Version：v1.0.0
 * @Author: wZhic
 * @Date:2024/3/1
 */
public interface IntelligentFavService  {

    /**
     * 根据ID查询智能体收藏
     * @Param:@param id
     * @return:com.zhongjia.infrastructure.db.entity.IntelligentFav
     * @Author: wZhic
     * @Date:2024/3/1
     */
    IntelligentFavVO queryEntityById(Integer id);

    /**
     * 分页查询智能体收藏列表
     * @Param:@param example
     * @return:List<com.zhongjia.infrastructure.db.entity.IntelligentFav>
     * @Author: wZhic
     * @Date:2024/3/1
     */
    IPage<IntelligentFavVO> queryListEntityPage(IntelligentFavDTO dto);

    /**
     * 查询单条智能体收藏
     * @Param:@param dto
     * @return:List<com.zhongjia.infrastructure.db.entity.IntelligentFav>
     * @Author: wZhic
     * @Date:2024/3/1
     */
    IntelligentFavVO queryEntityByDto(IntelligentFavDTO dto);

    /**
     * 更新智能体收藏
     * @Param:@param record
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/3/1
     */
    boolean updateEntity(IntelligentFavDTO record);

    /**
     * 插入智能体收藏
     * @Param:@param record
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/3/1
     */
    boolean insertEntity(IntelligentFavDTO record);

    /**
     * 删除智能体收藏
     * @Param:@param id
     * @return:boolean
     * @Author: wZhic
     * @Date:2024/3/1
     */
    boolean deleteById(Integer id);
    }