package com.hncboy.chatgpt.front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hncboy.chatgpt.front.framework.domain.dto.AppSignDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.AppSign;

import java.math.BigDecimal;
import java.util.List;

/**
 * @描述:APP用户签到 Dao
 * @版本：v1.0.0
 * @作者: zc.wu
 * @时间:2021/10/15
 */
public interface AppSignService extends IService<AppSign> {

    /**
     * @描述:根据ID查询APP用户签到
     * @参数:@param id
     * @返回:infrastructure.db.dataobject.AppSign
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    AppSign queryEntityById(BigDecimal id);

    /**
     * @描述:查询APP用户签到列表
     * @参数:@param example
     * @返回:List<infrastructure.db.dataobject.AppSign>
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    List<AppSign> queryListEntityPage(AppSignDTO dto);

    /**
     * @描述:查询单条APP用户签到
     * @参数:@param dto
     * @返回:List<infrastructure.db.dataobject.AppSign>
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    AppSign queryEntityByDto(AppSignDTO dto);

    /**
     * @描述:更新APP用户签到
     * @参数:@param record
     * @返回:boolean
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    boolean updateEntity(AppSign record);

    /**
     * @描述:插入APP用户签到
     * @参数:@param record
     * @返回:boolean
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    boolean insertEntity(AppSign record);

    /**
     * @描述:删除APP用户签到
     * @参数:@param id
     * @返回:boolean
     * @作者: zc.wu
     * @时间:2021/10/15
     */
    boolean deleteById(BigDecimal id);

    /**
     * @param userId
     * @描述:签到
     * @返回:boolean
     * @作者: zc.wu
     * @时间:2021/10/15 12:02 下午
     */
    boolean signByUser(String userId);

    /**
     * @param userId
     * @描述:计算连续签到天数
     * @返回:int
     * @作者: zc.wu
     * @时间:2021/10/15 3:54 下午
     */
    int querySignByUser(String userId);

    /**
     * @param userId
     * @描述:今日是否签到
     * @返回:int
     * @作者: zc.wu
     * @时间:2021/10/15 3:54 下午
     */
    boolean toDaySign(String userId);

    /**
     * @param userId
     * @描述:获取签到详情
     * @返回:int
     * @作者: zc.wu
     * @时间:2021/10/15 3:54 下午
     */
    List<AppSign> querySignMonthByUser(String userId, int type);
}
