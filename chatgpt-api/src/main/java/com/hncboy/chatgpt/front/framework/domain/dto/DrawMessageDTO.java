package com.hncboy.chatgpt.front.framework.domain.dto;

import com.hncboy.chatgpt.front.framework.domain.query.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @create 2023-06-01 20:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DrawMessageDTO extends PageQuery {

    private static final long serialVersionUID = 5833974878287829455L;

    private Integer id;

    @Schema(title = "open_id")
    private String openId;

    @Schema(title = "任务id")
    private String taskId;

    @Schema(title = "提示词")
    private String prompt;

    @Schema(title = "提示词-英文")
    private String promptEn;

    @Schema(title = "任务描述")
    private String description;

    @Schema(title = "是否热门")
    private String hot;

    @Schema(title = "自定义参数")
    private String state;

    @Schema(title = "提交时间")
    private LocalDateTime submitTime;

    @Schema(title = "图片地址")
    private String imageUrl;

    @Schema(title = "开始时间")
    private LocalDateTime startTime;

    @Schema(title = "结束时间")
    private LocalDateTime finishTime;
}
