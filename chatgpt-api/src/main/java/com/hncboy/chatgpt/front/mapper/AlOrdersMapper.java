package com.hncboy.chatgpt.front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hncboy.chatgpt.front.framework.domain.entity.AlOrders;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【ali_orders(支付记录)】的数据库操作Mapper
* @createDate 2024-07-04 13:08:01
* @Entity com.hncboy.chatgpt.front.framework.domain.entity.AlOrders
*/
public interface AlOrdersMapper extends BaseMapper<AlOrders> {

    IPage<AlOrders> page(Page<AlOrders> page,@Param("e") AlOrders orders);
    AlOrders getPayHistory(AlOrders orders);
}




