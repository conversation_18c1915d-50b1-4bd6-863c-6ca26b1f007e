package com.hncboy.chatgpt.front.service;

import com.hncboy.chatgpt.front.framework.domain.dto.CategoryInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.vo.CategoryInfoVO;

import java.util.List;

/**
 * 分类信息 Dao
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
public interface CategoryInfoService {

    /**
     * 根据ID查询分类信息
     *
     * @Param:@param id
     * @return:jialand.account.infrastructure.db.entity.CategoryInfo
     * @Author: wzhic
     * @Date:2023/4/19
     */
    CategoryInfoVO queryEntityById(Integer id);

    /**
     * 查询分类信息列表
     *
     * @Param:@param example
     * @return:List<jialand.account.infrastructure.db.entity.CategoryInfo>
     * @Author: wzhic
     * @Date:2023/4/19
     */
    List<CategoryInfoVO> queryListEntityPage(Integer type);

    /**
     * 查询单条分类信息
     *
     * @Param:@param dto
     * @return:List<jialand.account.infrastructure.db.entity.CategoryInfo>
     * @Author: wzhic
     * @Date:2023/4/19
     */
    CategoryInfoVO queryEntityByDto(CategoryInfoDTO dto);

    /**
     * 更新分类信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/19
     */
    boolean updateEntity(CategoryInfoDTO record);

    /**
     * 插入分类信息
     *
     * @Param:@param record
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/19
     */
    boolean insertEntity(CategoryInfoDTO record);

    /**
     * 删除分类信息
     *
     * @Param:@param id
     * @return:boolean
     * @Author: wzhic
     * @Date:2023/4/19
     */
    boolean deleteById(Integer id);
}
