package com.hncboy.chatgpt.front.controller.common;


import com.hncboy.chatgpt.front.framework.domain.entity.TransferInfo;
import com.hncboy.chatgpt.front.framework.handler.response.R;
import com.hncboy.chatgpt.front.service.ITransferInfoService;
import com.hncboy.chatgpt.front.service.PayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 定时任务接口
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/Job")
//@FrontPreAuth
@RequiredArgsConstructor
public class JobController {


    private final PayService payService;
    //private final ITransferInfoService iTransferInfoService;


    /**
     * 定时提现
     * @return
     */
    @PostMapping(value = "/payouts")
    @ResponseBody
    public R payouts() {
        payService.payouts();
        return R.success();
    }

    /**
     * 定时检查提现状态
     * @return
     */
    @PostMapping(value = "/checkPayoutsOrder")
    @ResponseBody
    public R checkPayoutsOrder() {
        payService.checkPayoutsOrder();
        return R.success();
    }





}
