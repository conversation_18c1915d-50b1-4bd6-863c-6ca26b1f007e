package com.hncboy.chatgpt.front.framework.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 写作记录 ENTITY
 *
 * @Version: v1.0.0
 * @Author: zd.zhong
 * @Date: 2024/7/1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("write_message")
public class WriteMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


    /**
     * 用户ID
     */
    private Integer userId;


    /**
     * 主题
     */
    private String topic;


    /**
     * 输入
     */
    private String inputs;


    /**
     * 内容
     */
    private String content;


    /**
     * 模型
     */
    private String model;


    /**
     * 应用名称
     */
    private String agentName;


    /**
     * 应用标题
     */
    private String agentTitle;


    /**
     * 应用ID
     */
    private Integer agentId;


    /**
     * 消耗点数
     */
    private Integer consume;


    /**
     * 站点ID
     */
    private Integer siteId;


    /**
     * 站点名称
     */
    private String siteName;


    /**
     * 站点URL
     */
    private String siteUrl;


    /**
     * 合计回复数
     */
    private Long total_tokens;

    /**
     * 状态 0初始化 1已完成
     */
    private String status;

    /**
     * ip
     */
    private String ip;


    /**
     * 备注
     */
    private String remark;

    /**
     * 删除状态 0未删除 1已删除
     */
    private int isDelete;


    /**
     * 首个字符出现时间
     */
    private Date firstCharTime;

    /**
     * 创建者
     */
    private String createBy;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 更新者
     */
    private String updateBy;


    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
