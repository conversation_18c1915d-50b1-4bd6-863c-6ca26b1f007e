package com.hncboy.chatgpt.front.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hncboy.chatgpt.front.framework.domain.entity.ChatMessageDO;
import com.hncboy.chatgpt.front.framework.domain.entity.ExceptionLog;
import com.hncboy.chatgpt.front.framework.domain.entity.SiteInfo;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessV2Request;
import com.hncboy.chatgpt.front.framework.domain.vo.UserBaseInfoVO;
import com.hncboy.chatgpt.front.mapper.ExceptionLogMapper;
import com.hncboy.chatgpt.front.service.ExceptionLogService;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.TYPE_CHAT;
import static com.hncboy.chatgpt.front.framework.constant.ApplicationConstant.TYPE_TAROT;

/**
* <AUTHOR>
* @description 针对表【error_log】的数据库操作Service实现
* @createDate 2024-04-15 14:58:04
*/
@Service
@RequiredArgsConstructor
public class ExceptionLogServiceImpl extends ServiceImpl<ExceptionLogMapper, ExceptionLog>
    implements ExceptionLogService {


    /**
     * 保存聊天处理过程中的异常日志。
     *
     * @param chatProcessRequest 聊天处理的请求对象，包含处理过程中的所有必要信息。
     * @param e 发生的异常对象，用于记录具体的异常信息。
     */
    @Override
    public void saveChatExceptionLog(ChatProcessV2Request chatProcessRequest, Throwable e,
                                     ChatMessageDO chatMessageDO, UserBaseInfoVO userBaseInfoVO,
                                     SiteInfo siteInfo, String response, String errMessage) {
        saveExceptionLog(TYPE_CHAT, chatProcessRequest, e, chatMessageDO,
                userBaseInfoVO, siteInfo, response, errMessage);
    }

    @Override
    public void saveTarotExceptionLog(ChatProcessV2Request chatProcessRequest, Throwable e,
                                      ChatMessageDO chatMessageDO, UserBaseInfoVO userBaseInfoVO,
                                      SiteInfo siteInfo, String response, String errMessage) {
        saveExceptionLog(TYPE_TAROT, chatProcessRequest, e, chatMessageDO,
                userBaseInfoVO, siteInfo, response, errMessage);
    }

    /**
     * 保存异常日志。
     * 该方法用于记录特定消息类型、聊天请求和遇到的服务异常到日志系统中。
     *
     * @param messageType 消息类型，用于分类日志。
     * @param chatProcessRequest 聊天处理请求对象，包含请求的详细信息，用于日志记录。
     * @param t 服务异常对象，记录了发生的异常信息。
         */
    @Override
    public void saveExceptionLog(String messageType, ChatProcessV2Request chatProcessRequest, Throwable t,
                                     ChatMessageDO chatMessageDO, UserBaseInfoVO userBaseInfoVO,
                                     SiteInfo siteInfo, String response, String errMessage) {
        ExceptionLog exceptionLog = new ExceptionLog();
        exceptionLog.setMessageType(messageType);
        exceptionLog.setMessageId(chatMessageDO.getId());
        exceptionLog.setUserId(userBaseInfoVO.getId());
        exceptionLog.setAccount(userBaseInfoVO.getAccount());
        exceptionLog.setName(userBaseInfoVO.getName());
        exceptionLog.setModelGid(chatMessageDO.getModelGid());
        exceptionLog.setModelName(chatMessageDO.getAgentName());
        exceptionLog.setSiteId(siteInfo.getId());
        exceptionLog.setUrl(siteInfo.getUrl());
        exceptionLog.setApiKey(siteInfo.getApiKey());
        exceptionLog.setRequest(chatProcessRequest.getPrompt());
        exceptionLog.setResMessage(errMessage);
        exceptionLog.setResponse(response);
        if (t != null) {
            exceptionLog.setException(t.toString());
        }
        // 保存异常日志
        save(exceptionLog);
    }
   /* @Override
    public void saveExceptionLog2(String messageType, ChatProcessV2Request chatProcessRequest,
                                    TarotSpread tarotSpread, UserBaseInfoVO userBaseInfoVO,
                                     SiteInfo siteInfo, String response, String errMessage) {
        ExceptionLog exceptionLog = new ExceptionLog();
        exceptionLog.setMessageType(messageType);
        //exceptionLog.setMessageId(Long.parseLong(tarotSpread.getId().toString()));
        exceptionLog.setUserId(userBaseInfoVO.getId());
        exceptionLog.setAccount(userBaseInfoVO.getAccount());
        exceptionLog.setName(userBaseInfoVO.getName());
        exceptionLog.setModelGid(tarotSpread.getGid());
        exceptionLog.setModelName(tarotSpread.getName());
        exceptionLog.setSiteId(siteInfo.getId());
        exceptionLog.setUrl(siteInfo.getUrl());
        exceptionLog.setApiKey(siteInfo.getApiKey());
        exceptionLog.setRequest(chatProcessRequest.getPrompt());
        exceptionLog.setResMessage(errMessage);
        exceptionLog.setResponse(response);
       *//* if (t != null) {
            exceptionLog.setException(t.toString());
        }*//*
        // 保存异常日志
        save(exceptionLog);
    }*/

}




