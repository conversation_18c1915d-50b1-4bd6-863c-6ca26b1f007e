package com.hncboy.chatgpt.front.framework.converter;

import com.hncboy.chatgpt.front.framework.domain.dto.CategoryInfoDTO;
import com.hncboy.chatgpt.front.framework.domain.entity.CategoryInfo;
import com.hncboy.chatgpt.front.framework.domain.vo.CategoryInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 分类信息 领域对象转换器
 *
 * @Version：v1.0.0
 * @Author: wzhic
 * @Date:2023/4/19
 */
@Mapper
public interface CategoryInfoConvert {

    CategoryInfoConvert INSTANCE = Mappers.getMapper(CategoryInfoConvert.class);

    /**
     * CategoryInfoDTO转CategoryInfo
     *
     * @Author: wzhic
     * @Date:2023/4/19
     */
    CategoryInfo dtoToEntity(CategoryInfoDTO dto);

    /**
     * CategoryInfo 转CategoryInfoVO
     *
     * @Author: wzhic
     * @Date:2023/4/19
     */
    CategoryInfoVO entityToVO(CategoryInfo entity);

    /**
     * List<CategoryInfo> 转List<CategoryInfoVO>
     *
     * @Author: wzhic
     * @Date:2023/4/19
     */
    List<CategoryInfoVO> entityListToVOList(List<CategoryInfo> entityList);

    /**
     * 查询DTO转换
     *
     * @Author: wzhic
     * @Date:2023/4/19
     */
    CategoryInfo queryDtoToEntity(CategoryInfoDTO dto);


}
