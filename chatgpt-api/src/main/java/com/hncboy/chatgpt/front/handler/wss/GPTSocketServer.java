package com.hncboy.chatgpt.front.handler.wss;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hncboy.chatgpt.front.api.listener.OpenAIWebSocketEventSourceListener;
import com.hncboy.chatgpt.front.framework.domain.request.BuildChatProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.request.ChatProcessRequest;
import com.hncboy.chatgpt.front.framework.domain.vo.ChatReplyMessageVO;
import com.hncboy.chatgpt.front.framework.handler.response.ResultCode;
import com.hncboy.chatgpt.front.framework.util.WebsocketUtil;
import com.hncboy.chatgpt.front.handler.config.SensitiveWordHandler;
import com.hncboy.chatgpt.front.handler.config.WebSocketConfig;
import com.hncboy.chatgpt.front.helper.ChatMsgBuildHelper;
import com.hncboy.chatgpt.front.helper.SensitiveWordEmitterChain;
import com.hncboy.chatgpt.front.service.WxUserInfoService;
import com.unfbx.chatgpt.OpenAiStreamClient;
import com.unfbx.chatgpt.entity.Tts.TtsVoice;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import com.unfbx.chatgpt.entity.chat.ChatCompletionWithPicture;
import com.unfbx.chatgpt.entity.chat.Message;
import com.unfbx.chatgpt.entity.chat.MessagePicture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR>
 */
@ServerEndpoint(value = "/gpt/socket/{token}", configurator = WebSocketConfig.class)
@Slf4j
@Component
public class GPTSocketServer {


    /**
     * 为了保存在线用户信息，在方法中新建一个list存储一下【实际项目依据复杂度，可以存储到数据库或者缓存】
     */
    private final static List<Session> SESSIONS = Collections.synchronizedList(new ArrayList<>());
    public static ChatMsgBuildHelper chatMsgBuildHelper;

    public static WxUserInfoService wxUserInfoService;
    private static OpenAiStreamClient openAiStreamClient;
    //在线总数
    private static int onlineCount;
    private static CopyOnWriteArraySet<GPTSocketServer> webSocketSet = new CopyOnWriteArraySet<>();
    /**
     * 用来存放每个客户端对应的GPTSocketServer对象
     */
    private static ConcurrentHashMap<String, GPTSocketServer> webSocketMap = new ConcurrentHashMap();
    //当前会话
    private Session session;
    //用户id
    private String token;

    /**
     * 获取当前连接数
     *
     * @return
     */
    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    /**
     * 当前连接数加一
     */
    public static synchronized void addOnlineCount() {
        GPTSocketServer.onlineCount++;
    }

    /**
     * 当前连接数减一
     */
    public static synchronized void subOnlineCount() {
        GPTSocketServer.onlineCount--;
    }

    @Autowired
    public void setOrderService(OpenAiStreamClient openAiStreamClient) {
        this.openAiStreamClient = openAiStreamClient;
    }

    /**
     * 建立连接
     *
     * @param session
     * @param token
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token) {
        this.session = session;
        this.token = token;
        webSocketSet.add(this);
        SESSIONS.add(session);
        if (webSocketMap.containsKey(token)) {
            webSocketMap.remove(token);
            webSocketMap.put(token, this);
        } else {
            webSocketMap.put(token, this);
            addOnlineCount();
        }
        log.info("[连接ID:{}] 建立连接, 当前连接数:{}", this.token, getOnlineCount());
    }

    /**
     * 断开连接
     */
    @OnClose
    public void onClose() {
        webSocketSet.remove(this);
        if (webSocketMap.containsKey(token)) {
            webSocketMap.remove(token);
            subOnlineCount();
        }
        log.info("[连接ID:{}] 断开连接, 当前连接数:{}", token, getOnlineCount());
    }

    /**
     * 发送错误
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.info("[连接ID:{}] 错误原因:{}", this.token, error.getMessage());
        error.printStackTrace();
    }

    /**
     * 接收到客户端消息
     *
     * @param msg
     */
    @OnMessage
    public void onMessage(String msg) throws IOException {
        log.info("[连接ID:{}] 收到消息:{}", this.token, msg);
        //接受参数
        ChatProcessRequest chatProcessRequest = JSONUtil.toBean(msg, ChatProcessRequest.class);
        if (StrUtil.isNotBlank(chatProcessRequest.getMsgType()) &&
                chatProcessRequest.getMsgType().equals("ping")) {
            //跳过Ping
            return;
        }
        Object loginIdByToken = StpUtil.getLoginIdByToken(this.token);
        if (Objects.isNull(loginIdByToken)) {
            session.getBasicRemote().sendText(ChatReplyMessageVO.buildFailureMessage("登录失效，请重新登录",ResultCode.UN_AUTHORIZED.getCode()));
            return;
        }
        //放入用户ID
        chatProcessRequest.setOpenId(loginIdByToken.toString());
        boolean b = SensitiveWordEmitterChain.doChain(chatProcessRequest);
        if (!b) {
            List<String> prompts = SensitiveWordHandler.checkWord(chatProcessRequest.getPrompt());
            session.getBasicRemote().sendText(ChatReplyMessageVO.buildFailureMessage("您的输入包含敏感词【" +
                    CollUtil.join(prompts, ",") + "】，请重新输入",ResultCode.FAILURE.getCode()));
            return;
        }
        if (StrUtil.isEmpty(chatProcessRequest.getOpenId()) || StrUtil.isEmpty(chatProcessRequest.getPrompt())) {
            log.error("参数为空---：{}", chatProcessRequest);
            return;
        }
        Boolean gpt = wxUserInfoService.checkUserByType(chatProcessRequest.getOpenId(), "gpt");
        if (!gpt) {
            session.getBasicRemote().sendText(ChatReplyMessageVO.buildFailureMessage("您的可用次数已用完，请联系客服",ResultCode.UN_AUTHORIZED.getCode()));
            return;
        }
        OpenAIWebSocketEventSourceListener eventSourceListener = null;
        String ip = WebsocketUtil.getHeader(session, "ip");
        chatProcessRequest.setIp(ip);
        BuildChatProcessRequest body = null;
        if (StrUtil.isNotEmpty(chatProcessRequest.getFilePath())) {
            //构建图片识别消息
            body = chatMsgBuildHelper.imageRecognition(chatProcessRequest);
            MessagePicture message = MessagePicture.builder().role(Message.Role.USER).content(body.getContentList()).build();
            ChatCompletionWithPicture chatCompletion = ChatCompletionWithPicture
                    .builder()
                    .messages(Collections.singletonList(message))
                    .model(ChatCompletion.Model.GPT_4_VISION_PREVIEW.getName())
                    .build();
            chatProcessRequest.setMsgId(body.getMsgId());
            eventSourceListener = new OpenAIWebSocketEventSourceListener(this.session, chatProcessRequest);
            //发送消息
            openAiStreamClient.streamChatCompletion(chatCompletion, eventSourceListener);
        } else {
            //构建对话消息
            body = chatMsgBuildHelper.buildMessageBody(chatProcessRequest);
            //构建参数
            ChatCompletion chatCompletion = ChatCompletion.builder()
                    .model(ChatCompletion.Model.GPT_3_5_TURBO_16K.getName())
                    .messages(body.getMessages())
                    .temperature(0.2)
                    .build();
            chatProcessRequest.setMsgId(body.getMsgId());
            eventSourceListener = new OpenAIWebSocketEventSourceListener(this.session, chatProcessRequest);
            //发送消息
            openAiStreamClient.streamChatCompletion(chatCompletion, eventSourceListener);
        }
        BuildChatProcessRequest finalBody = body;
        eventSourceListener.setOnComplate(s -> {
            log.info("回答完成：{}", s);
            CompletableFuture.runAsync(() -> {
                //回答完成，保存系统回答
                chatProcessRequest.setMsgId(finalBody.getMsgId());
                chatMsgBuildHelper.saveSysChatMsg(s, chatProcessRequest);
                wxUserInfoService.updateUserNum(chatProcessRequest.getOpenId(), 1);
            });
            //如果是语音对话 生成对应语音
            if (chatProcessRequest.getSpeech()) {
                //异步执行convertTextToSpeech
                CompletableFuture.runAsync(() -> {
                    chatMsgBuildHelper.convertTextToSpeech(s,
                            StrUtil.isEmpty(chatProcessRequest.getDeftSpeakUser()) ? TtsVoice.NOVA.getName() : chatProcessRequest.getDeftSpeakUser()
                            , session);
                });
            }
        });
    }
}

