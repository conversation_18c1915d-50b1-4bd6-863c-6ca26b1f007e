spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: super_gpt
    password: F4etrzBRayCNKePd
    url: ${JDBC_URL:****************************************************************************************************************************************************************************************************************************************}
  redis:
    host: 127.0.0.1
    password: redispassword
    port: 5388
    timeout: 20000
    lettuce:
      pool:
        max-active: 30
        max-idle: 7
        min-idle: 2
        max-wait: 20000
    database: 4

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mj:
  discord:
    guild-id: 1133302321749700670
    channel-id: 1133302322483707917
    user-token: ODI2NDA0MDI1MzU2MjU1Mjkz.GD3MqO.-SL1-8a7uZqyoYWQGfVsU4mWwWexPLXOBdURog
    user-wss: true
    user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
    bot-token: MTEzMzMwMzI3NzY0ODM2MzU2MQ.GZO0tl.-UVvhFjNjcMxEwu14m_qJ24jzklXSJ1Gx7X9Vc
  task-store:
    type: redis
    timeout: 1d
  translate-way: null
  id-prefix: "["
  id-suffix: "]"
  queue:
    timeout-minutes: 35
    core-size: 8
    queue-size: 3
  openai:
    gpt-api-key: ***************************************************
  ngDiscord:
    cdn: http://image.alwzc.com/discord

chat:
  # HTTP 代理
  http_proxy_host:
  # HTTP 代理
  http_proxy_port:

  # 默认配置
  openai_api_model: gpt-3.5-turbo-instruct
  openai_api_base_url: https://k.29qg.com/
  openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

  # 该通道的3.5模型免费
  gpt35_openai_api_base_url: https://k.29qg.com/
  gpt35_openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

  # 该通道的4模型价格便宜
  gpt4_openai_api_base_url: https://k.29qg.com/
  gpt4_openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

  # 该通道支持4-all模型，能处理附件
  gpt4all_openai_api_base_url: https://k.29qg.com/
  gpt4all_openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

  # 该通道支持gpts模型
  gpts_openai_api_base_url: https://k.29qg.com/
  gpts_openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO

dingtalk:
  secret: SECb68c6eac3b7330ddb5704147d6f16025d3db2a067092cebf109f322b1361cbd1
  webhook: https://oapi.dingtalk.com/robot/send?access_token=b5dae4c051d7b6fb72ddc78c3bc424f213dde811de83b3dafdf9e7c35cc6d192


chatOI:
  url: https://chatoi.zjfdsr.com/api/v1/