-- 超级智能社重构版初始化数据脚本

-- ================================
-- 初始化联合登录配置
-- ================================
INSERT INTO `user_joint_config` (`login_type`, `config_name`, `client_id`, `client_secret`, `enabled`, `sort_order`) VALUES
('WECHAT', '微信开放平台', 'your_wechat_app_id', 'your_wechat_app_secret', 1, 1),
('GOOGLE', 'Google OAuth2', 'your_google_client_id', 'your_google_client_secret', 1, 2),
('FACEBOOK', 'Facebook Login', 'your_facebook_app_id', 'your_facebook_app_secret', 1, 3),
('PHONE', '手机号登录', '', '', 1, 4),
('EMAIL', '邮箱登录', '', '', 1, 5),
('PASSWORD', '密码登录', '', '', 1, 6),
('FINGERPRINT', '浏览器指纹', '', '', 1, 7);

-- ================================
-- 初始化支付渠道配置 - 按业务场景区分
-- ================================

-- 塔罗牌业务场景支付配置
INSERT INTO `payment_channel_config` VALUES 
-- 支付宝配置 (塔罗牌场景)
(1, 'ALIPAY', '支付宝', 'SCAN', 'tarot', '塔罗牌业务',
 'your_tarot_alipay_app_id', 'your_tarot_alipay_pid', NULL,
 'your_tarot_alipay_private_key', 'alipay_public_key', NULL,
 'https://openapi.alipay.com/gateway.do', 
 'https://yourdomain.com/api/payment/callback/alipay/tarot',
 'https://yourdomain.com/payment/return/alipay/tarot',
 'RSA2', '["CNY","USD"]', 1, 1, 'prod', 0.006, 0.01, 50000,
 '{"timeout":"15m","product_code":"FAST_INSTANT_TRADE_PAY"}',
 '塔罗牌支付宝配置', 0, NOW(), NOW()),

-- 微信支付配置 (塔罗牌场景)
(2, 'WECHAT', '微信支付', 'SCAN', 'tarot', '塔罗牌业务',
 'your_tarot_wechat_app_id', 'your_tarot_wechat_mch_id', 'your_tarot_wechat_mch_key',
 'your_tarot_wechat_private_key', NULL, '/path/to/tarot/wechat/cert.p12',
 'https://api.mch.weixin.qq.com', 
 'https://yourdomain.com/api/payment/callback/wechat/tarot',
 'https://yourdomain.com/payment/return/wechat/tarot',
 'MD5', '["CNY"]', 1, 2, 'prod', 0.006, 0.01, 50000,
 '{"trade_type":"NATIVE","timeout":"15m"}',
 '塔罗牌微信支付配置', 0, NOW(), NOW()),

-- 紫微斗数业务场景支付配置
-- 支付宝配置 (紫微斗数场景)
(3, 'ALIPAY', '支付宝', 'SCAN', 'zns', '紫微斗数业务',
 'your_zns_alipay_app_id', 'your_zns_alipay_pid', NULL,
 'your_zns_alipay_private_key', 'alipay_public_key', NULL,
 'https://openapi.alipay.com/gateway.do', 
 'https://yourdomain.com/api/payment/callback/alipay/zns',
 'https://yourdomain.com/payment/return/alipay/zns',
 'RSA2', '["CNY","USD"]', 1, 1, 'prod', 0.006, 0.01, 50000,
 '{"timeout":"15m","product_code":"FAST_INSTANT_TRADE_PAY"}',
 '紫微斗数支付宝配置', 0, NOW(), NOW()),

-- AI对话业务场景支付配置
-- 支付宝配置 (AI对话场景)
(4, 'ALIPAY', '支付宝', 'SCAN', 'chatoi', 'AI对话业务',
 'your_chatoi_alipay_app_id', 'your_chatoi_alipay_pid', NULL,
 'your_chatoi_alipay_private_key', 'alipay_public_key', NULL,
 'https://openapi.alipay.com/gateway.do', 
 'https://yourdomain.com/api/payment/callback/alipay/chatoi',
 'https://yourdomain.com/payment/return/alipay/chatoi',
 'RSA2', '["CNY","USD"]', 1, 1, 'prod', 0.006, 0.01, 50000,
 '{"timeout":"15m","product_code":"FAST_INSTANT_TRADE_PAY"}',
 'AI对话支付宝配置', 0, NOW(), NOW()),

-- SE支付配置 (所有场景通用)
(5, 'SEPAY', 'SE支付', 'BANK_TRANSFER', 'tarot', '塔罗牌业务',
 NULL, NULL, NULL, NULL, NULL, NULL,
 'https://sepay.vn/api', 
 'https://yourdomain.com/api/payment/callback/sepay/tarot',
 'https://yourdomain.com/payment/return/sepay/tarot',
 NULL, '["VND"]', 1, 3, 'prod', 0.01, 10000, ********,
 '{"bank_code":"VCB","timeout":"30m"}',
 'SE支付越南本地', 0, NOW(), NOW()),

-- Momo支付配置 (所有场景通用)
(6, 'MOMO', 'Momo支付', 'WALLET', 'tarot', '塔罗牌业务',
 'your_momo_app_id', 'your_momo_partner_code', 'your_momo_secret_key',
 NULL, NULL, NULL,
 'https://payment.momo.vn/v2/gateway/api',
 'https://yourdomain.com/api/payment/callback/momo/tarot',
 'https://yourdomain.com/payment/return/momo/tarot',
 'HMAC_SHA256', '["VND"]', 1, 4, 'prod', 0.015, 10000, ********,
 '{"version":"2.0","timeout":"15m"}',
 'Momo支付越南', 0, NOW(), NOW());

-- ================================
-- 初始化缓存配置
-- ================================
INSERT INTO `cache_config` (`cache_name`, `cache_type`, `ttl_seconds`, `max_size`, `description`) VALUES
('user_info', 'BOTH', 1800, 10000, '用户信息缓存'),
('model_channels', 'REDIS', 300, 1000, '模型通道缓存'),
('sys_config', 'BOTH', 3600, 500, '系统配置缓存'),
('payment_channels', 'REDIS', 600, 100, '支付渠道缓存'),
('i18n_messages', 'LOCAL', 7200, 5000, '国际化消息缓存'),
('user_joint_login', 'REDIS', 1800, 5000, '用户联合登录缓存'),
('payment_config', 'BOTH', 3600, 200, '支付配置缓存');

-- ================================
-- 初始化国际化消息
-- ================================

-- 用户状态翻译
INSERT INTO `i18n_message` VALUES 
('正常', 'zh_CN', '正常', 'user', '用户状态-正常'),
('正常', 'en_US', 'Normal', 'user', 'User Status-Normal'),
('正常', 'vi_VN', 'Bình thường', 'user', 'Trạng thái người dùng-Bình thường'),

('禁用', 'zh_CN', '禁用', 'user', '用户状态-禁用'),
('禁用', 'en_US', 'Disabled', 'user', 'User Status-Disabled'),
('禁用', 'vi_VN', 'Bị vô hiệu hóa', 'user', 'Trạng thái người dùng-Bị vô hiệu hóa'),

-- 积分消息翻译 (支持参数)
('user.points.message', 'zh_CN', '您有{{points}}个积分', 'user', '积分消息'),
('user.points.message', 'en_US', 'You have {{points}} points', 'user', 'Points message'),
('user.points.message', 'vi_VN', 'Bạn có {{points}} điểm', 'user', 'Thông báo điểm'),

-- VIP到期消息翻译 (支持参数)
('user.vip.expire', 'zh_CN', 'VIP还有{{days}}天到期', 'user', 'VIP到期提醒'),
('user.vip.expire', 'en_US', 'VIP expires in {{days}} days', 'user', 'VIP expiration reminder'),
('user.vip.expire', 'vi_VN', 'VIP hết hạn sau {{days}} ngày', 'user', 'Nhắc nhở hết hạn VIP'),

-- 支付相关翻译
('支付成功', 'zh_CN', '支付成功', 'payment', '支付状态'),
('支付成功', 'en_US', 'Payment Successful', 'payment', 'Payment Status'),
('支付成功', 'vi_VN', 'Thanh toán thành công', 'payment', 'Trạng thái thanh toán'),

('待支付', 'zh_CN', '待支付', 'payment', '支付状态'),
('待支付', 'en_US', 'Pending Payment', 'payment', 'Payment Status'),
('待支付', 'vi_VN', 'Chờ thanh toán', 'payment', 'Trạng thái thanh toán'),

-- 业务场景翻译
('tarot', 'zh_CN', '塔罗牌', 'business', '业务场景'),
('tarot', 'en_US', 'Tarot', 'business', 'Business Scene'),
('tarot', 'vi_VN', 'Tarot', 'business', 'Kịch bản kinh doanh'),

('zns', 'zh_CN', '紫微斗数', 'business', '业务场景'),
('zns', 'en_US', 'Zi Wei Dou Shu', 'business', 'Business Scene'),
('zns', 'vi_VN', 'Tử Vi Đẩu Số', 'business', 'Kịch bản kinh doanh'),

('chatoi', 'zh_CN', 'AI对话', 'business', '业务场景'),
('chatoi', 'en_US', 'AI Chat', 'business', 'Business Scene'),
('chatoi', 'vi_VN', 'Trò chuyện AI', 'business', 'Kịch bản kinh doanh');

-- ================================
-- 初始化汇率配置 (汇率设为1，暂不支持转换)
-- ================================
INSERT INTO `exchange_rate` (`from_currency`, `to_currency`, `rate`) VALUES
('CNY', 'USD', 1.0),
('CNY', 'VND', 1.0),
('USD', 'CNY', 1.0),
('USD', 'VND', 1.0),
('VND', 'CNY', 1.0),
('VND', 'USD', 1.0);

-- ================================
-- 数据迁移脚本 - 将现有数据迁移到新表
-- ================================

-- 迁移微信用户数据到user_joint_login表
INSERT INTO `user_joint_login` (
    `user_id`, `login_type`, `third_party_id`, `third_party_username`, 
    `third_party_avatar`, `wechat_open_id`, `wechat_union_id`, `wechat_session_key`,
    `gender`, `country`, `province`, `city`, `language`, `is_primary`, `status`,
    `bind_time`, `last_use_time`, `create_time`, `update_time`
)
SELECT 
    w.user_id, 'WECHAT', w.open_id, w.nick_name,
    w.avatar_url, w.open_id, w.union_id, w.session_key,
    w.gender, w.country, w.province, w.city, w.language, 1, 1,
    w.create_time, w.update_time, w.create_time, w.update_time
FROM wx_user_info w
WHERE w.user_id IS NOT NULL
ON DUPLICATE KEY UPDATE
    `third_party_username` = VALUES(`third_party_username`),
    `third_party_avatar` = VALUES(`third_party_avatar`),
    `update_time` = NOW();

-- 迁移第三方用户数据到user_joint_login表
INSERT INTO `user_joint_login` (
    `user_id`, `login_type`, `third_party_id`, `fb_id`, `google_id`, `finb_id`,
    `referrer_id`, `lucky_coins`, `is_primary`, `status`,
    `bind_time`, `create_time`, `update_time`
)
SELECT 
    u.id, 
    CASE 
        WHEN u.fb_id IS NOT NULL THEN 'FACEBOOK'
        WHEN u.google_id IS NOT NULL THEN 'GOOGLE'
        WHEN u.finb_id IS NOT NULL THEN 'FINGERPRINT'
        ELSE 'OTHER'
    END,
    COALESCE(u.fb_id, u.google_id, u.finb_id, CONCAT('user_', u.id)),
    u.fb_id, u.google_id, u.finb_id,
    u.referrer_id, u.lucky_coins, 0, 1,
    u.create_time, u.create_time, u.update_time
FROM users u
WHERE u.id IS NOT NULL
ON DUPLICATE KEY UPDATE
    `referrer_id` = VALUES(`referrer_id`),
    `lucky_coins` = VALUES(`lucky_coins`),
    `update_time` = NOW();
