<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.tarot.mapper.TarotSpreadMapper">

    <select id="selectTarotSpreadList" parameterType="com.hncboy.chatgpt.tarot.domain.entity.TarotSpread" resultType="com.hncboy.chatgpt.tarot.domain.entity.TarotSpread">
        select * from tarot_spread
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="spreadLayout != null  and spreadLayout != ''"> and spread_layout = #{spreadLayout}</if>
            <if test="spreadDiagramUrl != null  and spreadDiagramUrl != ''"> and spread_diagram_url = #{spreadDiagramUrl}</if>
            <if test="summary != null  and summary != ''"> and summary = #{summary}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="inputExample != null  and inputExample != ''"> and input_example = #{inputExample}</if>
            <if test="consume != null "> and consume = #{consume}</if>
        </where>
        order by sort
    </select>
</mapper>