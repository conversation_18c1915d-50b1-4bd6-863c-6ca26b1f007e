<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.front.mapper.ModelMapper">

    <select id="selectModelList" parameterType="com.hncboy.chatgpt.front.framework.domain.entity.Model" resultType="com.hncboy.chatgpt.front.framework.domain.entity.Model">
        select * from model
        <where>
            <if test="channel != null  and channel != ''"> and channel = #{channel}</if>
            <if test="gid != null  and gid != ''"> and gid = #{gid}</if>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="ownedBy != null  and ownedBy != ''"> and owned_by = #{ownedBy}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectGidList"  resultType="java.lang.String">
        select gid  from model where detection=1
    </select>
</mapper>