<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.front.mapper.UserPointsLogMapper">

    <select id="pageQueryPointsByUserId" resultType="com.hncboy.chatgpt.front.framework.domain.vo.UserPointsLogVO">
        SELECT log.*,
        (CASE WHEN log.points_type = 'invite_points' THEN log.rel_order
        WHEN log.points_type = 'payouts_points' then user2.account
        ELSE user.account END) AS account,
        (CASE WHEN log.points_type = 'charge_points' THEN ord.product_name
        WHEN log.points_type = 'payouts_points' then "奖励金提现"
        ELSE "邀请奖励" END) as product_name  FROM user_points_log log
        LEFT JOIN ( select orders_id,user_id,product_id,product_type,product_name,product_price,num,unit,state,pay_time,expires_time,created_time,update_time,"支付宝" as type,'' as trade_type,'' as openid  from al_orders
        union all
        select out_trade_no as orders_id,user_id,goods_id as product_id,product_type,body as product_name,total_fee as product_price,num,unit,status,create_time,expires_time,create_time,update_time,"微信" as type,trade_type, openid from wx_pay_order)
        ord ON log.rel_order = ord.orders_id
        LEFT JOIN user_base_info user ON ord.user_id = user.id
        LEFT JOIN user_base_info user2 ON log.user_id = user2.id
        WHERE log.user_id = #{userId}
        and log.points_type in ('invite_points','charge_points','payouts_points')
        <if test='type != null and type != ""'>
            AND points_type = #{type}
        </if>
        ORDER BY log.create_time DESC

    </select>

    <select id="pageQueryTarotByUserId" resultType="com.hncboy.chatgpt.front.framework.domain.vo.UserPointsLogVO">
        SELECT log.*,
        user.account AS account,
        user.nick_name AS nickName
        FROM user_points_log log
        LEFT JOIN user_base_info  user ON log.rel_order = user.account
        WHERE log.user_id = #{userId}
        <if test='type != null and type != ""'>
            AND points_type = #{type}
        </if>
        ORDER BY log.create_time DESC
    </select>
</mapper>