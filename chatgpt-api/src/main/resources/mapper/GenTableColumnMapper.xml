<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.front.mapper.AlOrdersMapper">



    <select id="page"  resultType="com.hncboy.chatgpt.front.framework.domain.entity.AlOrders">
        SELECT
        *
        FROM
        (
        SELECT
        orders_id,
        user_id,
        product_id,
        product_type,
        product_name,
        product_price,
        num,
        unit,
        state,
        pay_time,
        expires_time,
        created_time,
        update_time,
        "支付宝" AS type,
        '' AS trade_type,
        '' AS openid
        FROM
        al_orders UNION ALL
        SELECT
        id AS orders_id,
        user_id,
        goods_id AS product_id,
        product_type,
        CONCAT('充值', '-', body) AS product_name,
        total_fee AS product_price,
        num,
        unit,
        STATUS,
        create_time AS created_time,
        expires_time,
        create_time,
        update_time,
        CASE
            WHEN trade_channel IS NULL OR trade_channel = '' THEN '微信'
            WHEN trade_channel = 'momo' THEN 'MoMo'
            ELSE trade_channel
            END AS type,
        trade_type,
        openid
        FROM
        wx_pay_order UNION ALL
        SELECT
        id AS orders_id,
        user_id,
        NULL,
        'TAROT' AS product_type,
        "公众号赠币" AS product_name,
        0 AS product_price,
        times AS num,
        unit,
        '1' AS state,
        created_time,
        recharge_time AS expires_time,
        NULL,
        update_time,
        "关注奖励" AS type,
        NULL,
        NULL
        FROM
        recharge_log
        WHERE
        channel = 'bestow' UNION ALL
        SELECT
        id AS orders_id,
        user_id,
        NULL,
        'TAROT' AS product_type,
        CONCAT(remark, '-', points, '塔罗币') AS product_name,
        0 AS product_price,
        points AS num,
        NULL,
        '1' AS state,
        create_time AS pay_time,
        create_time AS expires_time,
        create_time,
        update_time,
        points_type AS type,
        NULL,
        NULL
        FROM
        user_points_log
        WHERE
        points_type IN ('tarot_bestow', 'tarot_reward', 'tarot_invite_points', 'new_user_tarot')) a
        WHERE
        1 =1
        <if test="e.productType != null and e.productType != ''">
            and product_type=#{e.productType}
        </if>
        <if test="e.userId != null and e.userId != ''">
            and user_id=#{e.userId}
        </if>
        <if test="e.state != null and e.state != ''">
            and state=#{e.state}
        </if>
        <![CDATA[  and (state=1 or (state=0 and expires_time>=#{e.expiresTime}))]]>
        order by created_time desc
	</select>

    <select id="getPayHistory"  resultType="com.hncboy.chatgpt.front.framework.domain.entity.AlOrders">
        select 	orders_id,user_id,product_name,num,unit,pay_time,
                  num * (CASE when unit = 'day' then  1 when unit='MONTH' then 30 else  365 end ) as days
        from
            (select orders_id,user_id,product_type,product_name,num,unit,state,pay_time,update_time  from al_orders
             union all
             select id as orders_id,user_id,product_type,body as product_name,num,unit,status,time_end as pay_time,update_time from wx_pay_order) a
        where 1=1
        and product_type='CHAT'
        and user_id=#{userId}
        and state=1
        order by pay_time desc
        limit 1
	</select>


</mapper>
