<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.api.mapper.UserJointLoginMapper">

    <!-- 根据用户ID查询所有登录方式 -->
    <select id="selectByUserId" resultType="com.hncboy.chatgpt.api.domain.entity.UserJointLogin">
        SELECT * FROM user_joint_login 
        WHERE user_id = #{userId} AND deleted = 0
        ORDER BY is_primary DESC, create_time ASC
    </select>

    <!-- 根据登录类型和第三方ID查询 -->
    <select id="selectByTypeAndThirdPartyId" resultType="com.hncboy.chatgpt.api.domain.entity.UserJointLogin">
        SELECT * FROM user_joint_login 
        WHERE login_type = #{loginType} AND third_party_id = #{thirdPartyId} AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据微信OpenID查询 -->
    <select id="selectByWechatOpenId" resultType="com.hncboy.chatgpt.api.domain.entity.UserJointLogin">
        SELECT * FROM user_joint_login 
        WHERE wechat_open_id = #{openId} AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据用户ID和登录类型查询 -->
    <select id="selectByUserIdAndType" resultType="com.hncboy.chatgpt.api.domain.entity.UserJointLogin">
        SELECT * FROM user_joint_login 
        WHERE user_id = #{userId} AND login_type = #{loginType} AND deleted = 0
        LIMIT 1
    </select>

</mapper>
