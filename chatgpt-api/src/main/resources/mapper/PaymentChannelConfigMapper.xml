<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.api.mapper.PaymentChannelConfigMapper">

    <!-- 根据渠道代码和业务场景查询 -->
    <select id="selectByChannelAndScene" resultType="com.hncboy.chatgpt.api.domain.entity.PaymentChannelConfig">
        SELECT * FROM payment_channel_config 
        WHERE channel_code = #{channelCode} 
        AND business_scene = #{businessScene} 
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据业务场景查询所有启用的渠道 -->
    <select id="selectEnabledByScene" resultType="com.hncboy.chatgpt.api.domain.entity.PaymentChannelConfig">
        SELECT * FROM payment_channel_config 
        WHERE business_scene = #{businessScene} 
        AND enabled = 1 
        AND deleted = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>

    <!-- 根据渠道代码查询所有场景的配置 -->
    <select id="selectByChannelCode" resultType="com.hncboy.chatgpt.api.domain.entity.PaymentChannelConfig">
        SELECT * FROM payment_channel_config 
        WHERE channel_code = #{channelCode} 
        AND deleted = 0
        ORDER BY business_scene ASC, sort_order ASC
    </select>

</mapper>
