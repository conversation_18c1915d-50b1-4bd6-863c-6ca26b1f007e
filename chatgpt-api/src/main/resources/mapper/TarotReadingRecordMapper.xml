<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.tarot.mapper.TarotReadingRecordMapper">

    <select id="page" resultType="com.hncboy.chatgpt.tarot.domain.entity.TarotReadingRecord">
        select * from tarot_reading_record
        <where>
            <if test="e.spreadId != null  and e.spreadId != ''"> and spread_id = #{e.spreadId}</if>
            <if test="e.userId != null  and e.userId != ''"> and user_id = #{e.userId}</if>
            <if test="e.question != null  and e.question != ''"> and question = #{e.question}</if>
            <if test="e.status != null  and e.status != ''"> and `status` = #{e.status}</if>
            <if test="e.answer != null  and e.answer != ''"> and answer = #{e.answer}</if>
            <if test="e.drawResult != null  and e.drawResult != ''"> and draw_result = #{e.drawResult}</if>
            <if test="e.consume != null "> and consume = #{e.consume}</if>
                 and deleted = 0
        </where>
        order by create_time desc
    </select>
    <select id="selectById" resultType="com.hncboy.chatgpt.tarot.domain.vo.TarotReadingRecordVO">
        select *,s.spread_layout from tarot_reading_record r
            left join tarot_spread s on r.spread_id = s.id
        where r.id = #{id} and r.deleted = 0
    </select>
</mapper>