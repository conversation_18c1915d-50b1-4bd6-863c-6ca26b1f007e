<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.front.mapper.SysDictDataMapper">
	

	
	<sql id="selectDictDataVo">
        select dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark 
		from sys_dict_data
    </sql>

	<select id="selectDictDataList" parameterType="com.hncboy.chatgpt.front.framework.domain.entity.SysDictData" resultType="com.hncboy.chatgpt.front.framework.domain.entity.SysDictData">
	    <include refid="selectDictDataVo"/>
		<where>
		    <if test="dictType != null and dictType != ''">
				AND dict_type = #{dictType}
			</if>
			<if test="dictLabel != null and dictLabel != ''">
				AND dict_label like concat('%', #{dictLabel}, '%')
			</if>
			<if test="status != null and status != ''">
				AND status = #{status}
			</if>
		</where>
		order by dict_sort asc
	</select>
	
	<select id="selectDictDataByType" parameterType="String" resultType="com.hncboy.chatgpt.front.framework.domain.entity.SysDictData">
		<include refid="selectDictDataVo"/>
		where status = '0' and dict_type = #{dictType} order by dict_sort asc
	</select>
	
	<select id="selectDictLabel" resultType="com.hncboy.chatgpt.front.framework.domain.entity.SysDictData">
		select * from sys_dict_data
		where dict_type = #{dictType}
		  and status = '0'
	</select>

	<select id="selectDictLabelString" resultType="java.lang.String">
		select dict_label from sys_dict_data
		where dict_type = #{dictType}
		and status = '0'
	</select>
	
	<select id="selectDictDataById" parameterType="Long" resultType="com.hncboy.chatgpt.front.framework.domain.entity.SysDictData">
		<include refid="selectDictDataVo"/>
		where dict_code = #{dictCode}
	</select>
	
	<select id="countDictDataByType" resultType="Integer">
	    select count(1) from sys_dict_data where dict_type=#{dictType}  
	</select>

	
</mapper> 