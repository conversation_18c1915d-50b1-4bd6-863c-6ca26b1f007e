<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.api.mapper.I18nMessageMapper">

    <!-- 根据消息键和语言区域查询 -->
    <select id="selectByKeyAndLocale" resultType="com.hncboy.chatgpt.api.domain.entity.I18nMessage">
        SELECT * FROM i18n_message 
        WHERE message_key = #{messageKey} AND locale = #{locale}
        LIMIT 1
    </select>

    <!-- 根据消息键列表和语言区域查询 -->
    <select id="selectByKeysAndLocale" resultType="com.hncboy.chatgpt.api.domain.entity.I18nMessage">
        SELECT * FROM i18n_message 
        WHERE message_key IN 
        <foreach collection="messageKeys" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
        AND locale = #{locale}
    </select>

    <!-- 根据语言区域查询所有消息 -->
    <select id="selectByLocale" resultType="com.hncboy.chatgpt.api.domain.entity.I18nMessage">
        SELECT * FROM i18n_message 
        WHERE locale = #{locale}
        ORDER BY module, message_key
    </select>

</mapper>
