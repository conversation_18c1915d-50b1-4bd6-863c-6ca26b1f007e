<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.tarot.mapper.TarotDailyInsightMapper">



    <select id="selectTarotDailyInsightById"  resultType="com.hncboy.chatgpt.tarot.domain.vo.TarotDailyInsightVO">
        select *,m.guidance_text,m.advice,m.discouraged,m.meaning,m.name,m.card_front_url from tarot_daily_insight t
        left join tarot_card_meaning m on t.card_id = m.id
        where t.id = #{id}
	</select>

    <select id="selectDayTarotDailyInsight"  resultType="com.hncboy.chatgpt.tarot.domain.vo.TarotDailyInsightVO">
        select *,m.guidance_text,m.advice,m.discouraged,m.meaning,m.name,m.card_front_url from tarot_daily_insight t
        left join tarot_card_meaning m on t.card_id = m.id
        where 1=1
          and t.user_id = #{userId}
          <![CDATA[ and t.create_time >=#{startDate} ]]>
          <![CDATA[ and t.create_time <=#{endDate} ]]>
    </select>


</mapper>