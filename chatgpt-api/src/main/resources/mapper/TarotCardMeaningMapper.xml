<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.tarot.mapper.TarotCardMeaningMapper">

    <select id="selectTarotCardMeaningList" parameterType="com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning" resultType="com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning">
        select * from tarot_card_meaning
        <where>
            <if test="spreadId != null  and spreadId != ''"> and spread_id = #{spreadId}</if>
            <if test="question != null  and question != ''"> and question = #{question}</if>
            <if test="answer != null  and answer != ''"> and answer = #{answer}</if>
            <if test="drawResult != null  and drawResult != ''"> and draw_result = #{drawResult}</if>
            <if test="consume != null "> and consume = #{consume}</if>
        </where>
        order by sort asc
    </select>
    <select id="getById" resultType="com.hncboy.chatgpt.tarot.domain.vo.TarotCardMeaningVO">
        select * from tarot_card_meaning where id = #{id}
    </select>
</mapper>