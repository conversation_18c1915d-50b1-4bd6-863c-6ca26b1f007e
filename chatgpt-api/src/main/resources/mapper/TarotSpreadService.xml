<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hncboy.chatgpt.tarot.mapper.UserCheckInRecordMapper">

    <select id="selectUserCheckInRecordList" parameterType="com.hncboy.chatgpt.tarot.domain.dto.UserCheckInRecordDTO"
            resultType="com.hncboy.chatgpt.tarot.domain.vo.UserCheckInRecordVO">
        select * from user_check_in_record
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="checkInDate != null  and checkInDate != ''"> and check_in_date = #{checkInDate}</if>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="isMakeUp != null  and isMakeUp != ''"> and is_make_up = #{isMakeUp}</if>
            <if test="awarded != null "> and awarded = #{awarded}</if>
            <if test="startDate != null and startDate != ''"> and check_in_date >= #{startDate}</if>
            <if test="endDate != null and endDate != ''"><![CDATA[ and check_in_date <= #{endDate}]]></if>
        </where>
    </select>

    <select id="selectDayUserCheck" parameterType="com.hncboy.chatgpt.tarot.domain.entity.UserCheckInRecord"
            resultType="com.hncboy.chatgpt.tarot.domain.entity.UserCheckInRecord">
        select * from user_check_in_record
            where 1=1
            and user_id = #{userId}
            and check_in_date = #{checkInDate}
    </select>
</mapper>