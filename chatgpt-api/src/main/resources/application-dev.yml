spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
#    username: super_gpt_dev
#    password: LLTt7Chbm5cR4rGf
#    url: ${JDBC_URL:*******************************************************************************************************************************************************************************************************************************************************}

#   公网测试环境
#    username: super_gpt_test
#    password: PDKn5KABLDis7ncM
#    url: ${JDBC_URL:***************************************************************************************************************************************************************************************************************************************************}

    # 越南准生产环境
    username: super_gpt
    password: F4etrzBRayCNKePd
    url: ${JDBC_URL:*********************************************************************************************************************************************************************************************************************************************}



  redis:
    host: *************
    password: 123456
    port: 8115
    timeout: 20000
    lettuce:
      pool:
        max-active: 30
        max-idle: 7
        min-idle: 2
        max-wait: 20000
    database: 1

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  mapper-locations: classpath*:mapper/**/*.xml

# Sa-Token配置
sa-token:
  token-name: Authorization
  token-prefix: Bearer
  timeout: 2592000
  activity-timeout: 1800
  is-concurrent: true
  is-share: false
  token-style: uuid
  is-log: true

mj:
  proxy:
    host: 127.0.0.1
    port: 50483
  discord:
    guild-id: 1133302321749700670
    channel-id: 1133302322483707917
    user-token: ODI2NDA0MDI1MzU2MjU1Mjkz.GD3MqO.-SL1-8a7uZqyoYWQGfVsU4mWwWexPLXOBdURog
    user-wss: true
    user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
    bot-token: MTEzMzMwMzI3NzY0ODM2MzU2MQ.GZO0tl.-UVvhFjNjcMxEwu14m_qJ24jzklXSJ1Gx7X9Vc
  task-store:
    type: redis
    timeout: 1d
  translate-way: null
  id-prefix: "["
  id-suffix: "]"
  queue:
    timeout-minutes: 120
    core-size: 8
    queue-size: 10
  openai:
    gpt-api-key: ***************************************************
  ngDiscord:
    cdn: http://image.alwzc.com/discord


chat:
  # HTTP 代理
  http_proxy_host: 127.0.0.1
  # HTTP 代理
  http_proxy_port: 50483

  # 默认配置
  openai_api_model: gpt-3.5-turbo-instruct
  openai_api_base_url: https://k.29qg.com/
  openai_api_key: sk-ugqVOAurlSnAL6juEa2d45D97f9e45CcA80aE4A2A638F2E1

  # 该通道的3.5模型免费
  gpt35_openai_api_base_url: https://29qg.com/
  gpt35_openai_api_key: sk-FRpBbKy7prilAA6U406a8e64D89746DcA387245fD95a9023

  # 该通道的4模型价格便宜
  gpt4_openai_api_base_url: https://29qg.com/
  gpt4_openai_api_key: sk-FRpBbKy7prilAA6U406a8e64D89746DcA387245fD95a9023

  # 该通道支持4-all模型，能处理附件
  gpt4all_openai_api_base_url: https://mjapi.cn/
  gpt4all_openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

  # 该通道支持gpts模型
  gpts_openai_api_base_url: https://mjapi.cn/
  gpts_openai_api_key: sk-pNEBhzEbxwPP0Gay0563237b48364604903245Af64Dd75E5

logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO
    com.hncboy.chatgpt: DEBUG

dingtalk:
  secret: SEC3820f48f9abccf43765e3a26ccefa16ca3f8ca21171fd153062058db9081c4d1
  webhook: https://oapi.dingtalk.com/robot/send?access_token=159b03ad6fab176638304567645e14d750d596468facf9bbab98f3d1f23bb3af
  webhook_tarot_monitor: https://oapi.dingtalk.com/robot/send?access_token=159b03ad6fab176638304567645e14d750d596468facf9bbab98f3d1f23bb3af
  webhook_tarot_exception: https://oapi.dingtalk.com/robot/send?access_token=159b03ad6fab176638304567645e14d750d596468facf9bbab98f3d1f23bb3af
  schedule_enabled: false

wx:
  pay:
    configs:
      - appId: wx734d3d8a6888e18f # 第一个公众号的appid
        mchId: 1683747767 #微信支付商户号
        mchKey: MDZBRDM5NzU0OTg0NkMwMUMzRThFQkQy #微信支付商户密钥
        subAppId: #服务商模式下的子商户公众账号ID
        subMchId: #服务商模式下的子商户号
        keyPath: /www/wwwroot/jar/zns/apiclient_cert.p12 #p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
        privateKeyPath: /www/wwwroot/jar/zns/apiclient_key.pem
        privateCertPath: /www/wwwroot/jar/zns/apiclient_cert.pem #
        notifyUrl: https://zns.zjfdsr.com/tarot_test/api/notify/order/zns # 支付回调地址
        apiV3Key: MDZBRDM5NzU0OTg0NkMwMUMzRThFQkQy #微信支付V3密钥
      - appId: wx734d3d8a6888e18f # 第一个公众号的appid
        mchId: 1717772719 #微信支付商户号
        mchKey: MDZBRDM5NzUFdsR0NkMwMUMzRThFQkQy #微信支付商户密钥
        subAppId: #服务商模式下的子商户公众账号ID
        subMchId: #服务商模式下的子商户号
        keyPath: /www/wwwroot/jar/tarot/apiclient_cert.p12 #p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
        privateKeyPath: /www/wwwroot/jar/tarot/apiclient_key.pem
        privateCertPath: /www/wwwroot/jar/tarot/apiclient_cert.pem #
        notifyUrl: https://zns.zjfdsr.com/tarot_test/api/notify/order/tarot # 支付回调地址
        apiV3Key: MDZBRDM5NzUFdsR0NkMwMUMzRThFQkQy #微信支付V3密钥
  miniapp:
    configs:
      - appid: wxfe365e97c4eb6f9f
        secret: e494427b392b2e1f5adfbe61e828dbd9
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
  mp:
    configs:
      - appId: wx734d3d8a6888e18f # 第一个公众号的appid
        secret: e54bde3b6a97b435cf43526f3f5e0fb5 # 公众号的appsecret
        token: cxfqsqn1yolgogsaqmtxi0ek3tgjyrus # 接口配置里的Token值
        aesKey: 3hl1lhoiGGTYODDFDqK5ZYSSLjfDoHWBJz8nnrh6jrF # 接口配置里的EncodingAESKey值
      - appId: wx8aa8b47c44e06a98        # 第一个公众号的appid
        secret: 73f75b816c313814d4230b75e855c07c # 公众号的appsecret
        token: cxfqsqn1yolgogsaqmtxi0ek3tgjyrus # 接口配置里的Token值
        aesKey: 3hl1lhoiGGTYODDFDqK5ZYSSLjfDoHWBJz8nnrh6jrF # 接口配置里的EncodingAESKey值
      - appId: wx94dd418a44321f70        # 第一个公众号的appid
        secret: cd81945f300e0d90b6a0446756878765 # 公众号的appsecret
        token: cxfqsqn1yolgogsaqmtxi0ek3tgjyrus # 接口配置里的Token值
        aesKey: 3hl1lhoiGGTYODDFDqK5ZYSSLjfDoHWBJz8nnrh6jrF # 接口配置里的EncodingAESKey值

chatOI:
  url: http://*************:8080/api/v1/

# momo 支付配置
PROD_ACCESS_KEY: key
PROD_PARTNER_CODE: key
PROD_SECRET_KEY: key
momo:
  return-url: https://zns.zjfdsr.com/tarot_test/return/momo
  notify-url: https://zns.zjfdsr.com/tarot_test/api/notify/momo

# 重构后的应用配置
app:
  # AI配置
  ai:
    default-model: gpt-3.5-turbo
    max-tokens: 2000
    temperature: 0.7
    timeout: 30000

  # 支付配置
  pay:
    # 支付宝配置
    alipay:
      app-id: 2021001234567890
      private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
      public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      gateway-url: https://openapi.alipay.com/gateway.do
      notify-url: /api/pay/callback/alipay
      return-url: /api/pay/return/alipay

    # 微信支付配置
    wechat:
      app-id: wx1234567890abcdef
      mch-id: 1234567890
      api-key: abcdef1234567890abcdef1234567890
      cert-path: classpath:cert/wechat/apiclient_cert.p12
      notify-url: /api/pay/callback/wechat

    # SE支付配置
    sepay:
      merchant-id: SE123456
      secret-key: se_secret_key_123456
      gateway-url: https://api.sepay.vn/v1
      notify-url: /api/pay/callback/sepay

  # 认证配置
  auth:
    # 微信登录配置
    wechat:
      app-id: wx1234567890abcdef
      app-secret: abcdef1234567890abcdef1234567890
      redirect-uri: /api/auth/wechat/callback

    # 短信配置
    sms:
      provider: aliyun
      access-key: LTAI5t...
      secret-key: abc123...
      sign-name: 超级智能社
      template-code: SMS_123456789

  # 国际化配置
  i18n:
    default-language: zh_CN
    supported-languages:
      - zh_CN
      - en_US
      - vi_VN
      - ja_JP
      - ko_KR

  # 缓存配置
  cache:
    # Redis缓存配置
    redis:
      default-ttl: 1800
      key-prefix: "chatgpt:api:"

    # Caffeine本地缓存配置
    caffeine:
      maximum-size: 10000
      expire-after-write: 900
      expire-after-access: 600

  # 业务配置
  business:
    # 用户配置
    user:
      default-free-count: 10
      default-points: 100
      daily-free-count: 3

    # AI对话配置
    chat:
      max-context-length: 10
      max-message-length: 2000

    # 塔罗牌配置
    tarot:
      default-tarot-coins: 5
      max-daily-readings: 10
