package com.hncboy.chatgpt.tarot.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hncboy.chatgpt.front.framework.domain.entity.RechargeLog;
import com.hncboy.chatgpt.front.framework.domain.entity.SysConfig;
import com.hncboy.chatgpt.front.framework.domain.entity.UserBaseInfo;
import com.hncboy.chatgpt.front.framework.domain.entity.UserPointsLog;
import com.hncboy.chatgpt.front.mapper.RechargeLogMapper;
import com.hncboy.chatgpt.front.mapper.UserBaseInfoMapper;
import com.hncboy.chatgpt.front.mapper.UserPointsLogMapper;
import com.hncboy.chatgpt.front.service.SysConfigService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * recharge_log 表数据迁移
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class RechargeLogDataMigration {

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private UserPointsLogMapper userPointsLogMapper;

    @Resource
    private UserBaseInfoMapper userBaseInfoMapper;

    @Resource
    private RechargeLogMapper rechargeLogMapper;

    @Test
    public void testMigration() {
        //执行时要 去掉  create_time  和 update_time  的注解，防止赋值默认值
//        List<Integer> userIds= Arrays.asList(21);
        List<Integer> userIds = Arrays.asList();
        // 执行签到业务逻辑
        SysConfig tarotReward = sysConfigService.querySysConfig("tarot_bestow");

        LambdaQueryWrapper<RechargeLog> bestow = new QueryWrapper<RechargeLog>().lambda().eq(RechargeLog::getChannel, "bestow");
        if (!userIds.isEmpty()) {
            bestow.in(RechargeLog::getUserId, userIds);
        }
        List<RechargeLog> rechargeLogs = rechargeLogMapper.selectList(bestow);

        List<UserBaseInfo> userBaseInfos = userBaseInfoMapper.selectList(new QueryWrapper<UserBaseInfo>());
        Map<Integer, UserBaseInfo> collect = userBaseInfos.stream().collect(Collectors.toMap(UserBaseInfo::getId, v -> v));

        for (RechargeLog rechargeLog : rechargeLogs) {
            if (collect.containsKey(rechargeLog.getUserId())) {
                Integer userId = rechargeLog.getUserId();
                UserBaseInfo userBaseInfo = collect.get(userId);
                UserPointsLog userPointsLog = new UserPointsLog()
                        .setUserId(userBaseInfo.getId())
                        .setRelOrder(userBaseInfo.getAccount())
                        .setPoints(Integer.valueOf(tarotReward.getConfigValue()))
                        .setPointsType(tarotReward.getConfigKey())
                        .setRemark(tarotReward.getRemark())
                        .setCreateTime(rechargeLog.getUpdateTime())
                        .setUpdateTime(rechargeLog.getUpdateTime())
                        .setUpdateBy(String.valueOf(rechargeLog.getId()))
                        .setCreateBy(String.valueOf(userBaseInfo.getId()));
                userPointsLogMapper.insert(userPointsLog);
                rechargeLogMapper.deleteById(rechargeLog.getId());
            }
        }
    }
}
