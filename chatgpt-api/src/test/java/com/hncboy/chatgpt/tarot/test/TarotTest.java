package com.hncboy.chatgpt.tarot.test;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hncboy.chatgpt.front.framework.domain.entity.Product;
import com.hncboy.chatgpt.front.mapper.ProductMapper;
import com.hncboy.chatgpt.tarot.domain.entity.TarotCardMeaning;
import com.hncboy.chatgpt.tarot.domain.entity.TarotI18n;
import com.hncboy.chatgpt.tarot.domain.entity.TarotSpread;
import com.hncboy.chatgpt.tarot.enums.LanguageCode;
import com.hncboy.chatgpt.tarot.service.TarotCardMeaningService;
import com.hncboy.chatgpt.tarot.service.TarotI18nService;
import com.hncboy.chatgpt.tarot.service.TarotSpreadService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class TarotTest {

    @Resource
    TarotCardMeaningService tarotCardMeaningService;

    @Resource
    TarotSpreadService tarotSpreadService;

    @Resource
    TarotI18nService tarotI18nService;

    @Resource
    ProductMapper productMapper;

    @Resource
    StringRedisTemplate stringRedisTemplate;


    @Test
    void _生成需要生成的文件() {


        String filePath = "src/main/resources/i18n/中文.txt";
        File file = new File(filePath);
        // 如果目标文件存在，删除它（可选，确保每次写入都是全新的）
        if (file.exists()) {
            FileUtil.del(file);
        }
        Set<String> set = cretaeSet();

        // 新的集合用于存放分割后的结果
        Set<String> newSet = new HashSet<>();
        // 遍历原始集合
        for (String str : set) {
            //去除括号
            if (StrUtil.isBlank(str)) {
                continue;
            }
            // 使用正则表达式分割字符串
            String[] splitStrings = splitString(str);
            // 将分割后的字符串添加到新的集合中
            for (String s : splitStrings) {
                // 只添加非空字符串
                if (!s.trim().isEmpty()) {
                    newSet.add(s.trim());
                }
            }
        }
        List<String> sortedList = CollUtil.sort(newSet, Comparator.comparingInt(String::length));
        // 写入文件，每个元素一行
        FileUtil.writeLines(sortedList, file, "UTF-8");
        System.out.println("成功将数据写入文件: " + filePath);

    }


    public Set<String> cretaeSet() {
        Set<String> set = new HashSet<>();
        List<TarotCardMeaning> tarotCardMeanings = tarotCardMeaningService.list();
        for (TarotCardMeaning tarotCardMeaning : tarotCardMeanings) {
            //name
            //meaning
            //tag
            //guidance_text
            //advice
            //discouraged
            set.add(tarotCardMeaning.getName());
            set.add(tarotCardMeaning.getMeaning());
            set.add(tarotCardMeaning.getTag());
            set.add(tarotCardMeaning.getGuidanceText());
            set.add(tarotCardMeaning.getAdvice());
            set.add(tarotCardMeaning.getDiscouraged());
        }
        List<TarotSpread> tarotSpreads = tarotSpreadService.list();
        //name
        //summary
        //description
        //input_example
        for (TarotSpread tarotSpread : tarotSpreads) {
            set.add(tarotSpread.getName());
            set.add(tarotSpread.getSummary());
            set.add(tarotSpread.getDescription());
            set.add(tarotSpread.getInputExample());
        }

        List<Product> products = productMapper.selectList(null);
        for (Product product : products) {
            set.add(product.getProductName());
            set.add(product.getRemark());
            set.add(product.getButtonName());
        }
        //系统异常导入
        List<String> strings = FileUtil.readUtf8Lines("C:\\software\\shy\\superai-end\\chatgpt-api\\src\\main\\resources\\i18n\\系统异常.txt");
        set.addAll(strings);
        return set;
    }


    public static String[] splitString(String input) {
        // 匹配所有非中文、非英文大小写、非数字的字符（正则表达式需转义反斜杠）
//        return input.split("[^a-zA-Z0-9\\u4e00-\\u9fff]");
        return input.split("\\r?\\n");
    }

    void _读取需要翻译的中文字段(LanguageCode langCodeWord, LanguageCode langCode) {

        Set<String> set = cretaeSet();

        tarotI18nService.remove(new LambdaQueryWrapper<TarotI18n>().eq(TarotI18n::getLang, langCode.getCode()));

        //查询整合为
        String lang = langCodeWord.getCode();

        List<TarotI18n> list = tarotI18nService.list(new LambdaQueryWrapper<TarotI18n>().eq(TarotI18n::getLang, lang));
        Map<String, String> collect = list.stream().collect(Collectors.toMap(TarotI18n::getCode, TarotI18n::getValue, (v1, v2) -> v1));
        // 将 Map 转换为 List<Entry<String, String>>
        List<Map.Entry<String, String>> mapList = new ArrayList<>(collect.entrySet());

        // 对 List 进行排序，根据键的长度升序
        mapList.sort(Comparator.comparingInt(entry -> entry.getKey().length()));

        // 将排序后的 List 转换回 LinkedHashMap
        Map<String, String> sortedMap = new LinkedHashMap<>();
        ListIterator<Map.Entry<String, String>> iterator = mapList.listIterator(mapList.size());
        while (iterator.hasPrevious()) {
            Map.Entry<String, String> entry = iterator.previous();
            sortedMap.put(entry.getKey(), entry.getValue());
        }

//        if(true){
//            sortedMap.forEach((k,v)->{
//                System.out.println(k);
//            });
//            return;
//        }

        List<TarotI18n> saveList = new ArrayList<>();
        AtomicReference<String> finalStr = new AtomicReference<>();
        for (String str : set) {
            TarotI18n tarotI18n = new TarotI18n();
            tarotI18n.setLang(langCode.getCode());
            if (StrUtil.isBlank(str)) {
                continue;
            }
            finalStr.set(str);
            sortedMap.forEach((key, value) -> {
                if (finalStr.get().contains(key)) {
                    finalStr.set(finalStr.get().replace(key, value));
                }
            });

            tarotI18n.setCode(str);
            tarotI18n.setValue(finalStr.get());
            saveList.add(tarotI18n);
        }
        tarotI18nService.saveBatch(saveList);

        //删除 过程数据
        tarotI18nService.remove(new LambdaQueryWrapper<TarotI18n>().eq(TarotI18n::getLang, langCodeWord.getCode()));
    }


    public void _翻译拼接(LanguageCode langCodeWord, String file) {

        tarotI18nService.remove(new LambdaQueryWrapper<TarotI18n>().eq(TarotI18n::getLang, langCodeWord.getCode()));

        Set<String> set = new HashSet<>();

        List<String> strings = FileUtil.readUtf8Lines("C:\\software\\shy\\superai-end\\chatgpt-api\\src\\main\\resources\\i18n\\中文.txt");
        List<String> strings2 = FileUtil.readUtf8Lines(file);
        List<TarotI18n> saveList = new ArrayList<>();
        for (int i = 0; i < strings.size(); i++) {
            String code = strings.get(i);
            if (StrUtil.isBlank(code)) {
                continue;
            }
            if (!set.add(code)) {
                continue;
            }

            String value = strings2.get(i);
            TarotI18n tarotI18n = new TarotI18n();
            tarotI18n.setLang(langCodeWord.getCode());
            tarotI18n.setCode(code);
            tarotI18n.setValue(value);
            saveList.add(tarotI18n);
        }

        tarotI18nService.saveBatch(saveList);
    }


    /**
     * 读取文件，将每行的第一个字符转换为大写，并更新文件内容
     *
     * @param filePath 文件路径
     */
    public static void _首字符大写处理(String filePath) {
        // 读取文件所有行
        List<String> lines = FileUtil.readLines(filePath, "UTF-8");

        // 处理每一行
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            if (StrUtil.isNotBlank(line)) { // 判断是否非空行
                // 将第一个字符转换为大写
                line = StrUtil.upperFirst(line);
            }
            lines.set(i, line); // 更新行内容
        }

        // 将处理后的内容写回文件
        FileUtil.writeLines(lines, new File(filePath), "UTF-8");
    }

    @Test
    public void _test() {

//        越南
        LanguageCode vietnameseWoed = LanguageCode.VIETNAMESE_WOED;
        LanguageCode vietnamese = LanguageCode.VIETNAMESE;
        String file = "C:\\software\\shy\\superai-end\\chatgpt-api\\src\\main\\resources\\i18n\\越南.txt";


//        //英文
//        LanguageCode vietnameseWoed = LanguageCode.ENGLISH_WOED;
//        LanguageCode vietnamese = LanguageCode.ENGLISH;
//        String file="C:\\software\\shy\\superai-end\\chatgpt-api\\src\\main\\resources\\i18n\\英文.txt";


//        _首字符大写处理(file);
        _翻译拼接(vietnameseWoed, file);
        _读取需要翻译的中文字段(vietnameseWoed, vietnamese);

    }
}