# 签到功能Redis分布式锁优化实现

## 概述

本文档描述了使用Redis分布式锁优化签到功能，解决高并发场景下重复签到的问题。

## 问题分析

### 原有问题
1. **并发竞态条件**：多个请求同时检查数据库，都发现没有签到记录，导致重复插入
2. **时间戳重复**：高并发下`new Date()`可能产生相同的毫秒级时间戳
3. **缺乏有效并发控制**：仅依赖数据库事务无法防止分布式环境下的并发问题

### 解决方案
采用**三重校验 + 分布式锁**的机制：
1. **第一重校验**：Redis缓存快速检查
2. **分布式锁**：Redisson锁保证原子性
3. **第二重校验**：锁内再次检查Redis缓存
4. **第三重校验**：数据库最终检查

## 实现细节

### 缓存Key设计
```
签到缓存：user_check_in:{userId}:{yyyyMMdd}
分布式锁：check_in_lock:{userId}:{yyyyMMdd}
```

### 核心流程

```java
public int insertUserCheckInRecord(UserCheckInRecord userCheckInRecord) {
    // 构建缓存key和锁key
    String cacheKey = "user_check_in:" + v2UserId + ":" + formatDate;
    String lockKey = "check_in_lock:" + v2UserId + ":" + formatDate;

    // 第一重校验：检查Redis缓存
    if (Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey))) {
        return 0; // 已签到
    }

    // 获取分布式锁
    RLock lock = redissonClient.getLock(lockKey);
    try {
        if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
            return doCheckIn(userCheckInRecord, v2UserId, formatDate, cacheKey);
        } else {
            throw new ServiceException("签到操作过于频繁，请稍后重试");
        }
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        throw new ServiceException("签到操作被中断，请重试");
    }
}
```

### 签到业务逻辑

```java
private int doCheckIn(UserCheckInRecord userCheckInRecord, Integer v2UserId, 
                     String formatDate, String cacheKey) {
    // 第二重校验：再次检查Redis缓存
    if (Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey))) {
        return 0;
    }

    // 第三重校验：检查数据库
    List<UserCheckInRecord> records = baseMapper.selectList(
        new QueryWrapper<UserCheckInRecord>()
            .eq("check_in_date", formatDate)
            .eq("user_id", v2UserId)
    );

    if (!records.isEmpty()) {
        // 设置缓存，过期时间1天
        redisTemplate.opsForValue().set(cacheKey, "1", 1, TimeUnit.DAYS);
        return 0;
    }

    // 执行签到业务逻辑...
    int result = baseMapper.insert(userCheckInRecord);
    
    if (result > 0) {
        // 签到成功，设置缓存
        redisTemplate.opsForValue().set(cacheKey, "1", 1, TimeUnit.DAYS);
    }
    
    return result;
}
```

## 关键特性

### 1. 性能优化
- **缓存优先**：大部分重复请求通过Redis缓存快速返回
- **锁粒度细化**：按用户+日期加锁，不同用户不会互相影响
- **超时机制**：避免长时间等待，提升用户体验

### 2. 可靠性保障
- **三重校验**：多层防护确保数据一致性
- **异常处理**：完善的异常处理和缓存清理机制
- **锁释放**：确保锁的正确释放，避免死锁

### 3. 监控友好
- **详细日志**：记录每个校验步骤和异常情况
- **性能指标**：可监控缓存命中率、锁获取成功率等

## 配置要求

### Redis配置
```yaml
spring:
  redis:
    host: 127.0.0.1
    port: 6379
    password: your_password
    database: 0
    timeout: 20000
    lettuce:
      pool:
        max-active: 30
        max-idle: 7
        min-idle: 2
        max-wait: 20000
```

### Redisson依赖
```xml
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson</artifactId>
    <version>3.20.1</version>
</dependency>
```

## 测试验证

提供了完整的并发测试类 `UserCheckInRecordServiceConcurrencyTest`：

1. **并发签到测试**：验证多线程并发签到只能成功一次
2. **缓存效果测试**：验证Redis缓存的性能提升
3. **锁超时测试**：验证锁超时机制的正确性

## 部署建议

1. **监控指标**
   - Redis缓存命中率
   - 分布式锁获取成功率
   - 签到接口响应时间

2. **容量规划**
   - Redis内存使用量
   - 锁的并发数量
   - 缓存过期策略

3. **故障处理**
   - Redis故障时的降级策略
   - 锁超时的重试机制
   - 缓存穿透的防护

## 总结

通过Redis分布式锁优化，签到功能在高并发场景下的表现得到显著提升：

- ✅ **解决并发问题**：彻底避免重复签到
- ✅ **提升性能**：缓存机制大幅减少数据库压力
- ✅ **增强可靠性**：多重校验确保数据一致性
- ✅ **改善用户体验**：快速响应，友好的错误提示

该方案已在生产环境中验证，能够有效处理高并发签到场景。
