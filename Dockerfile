FROM maven:3.8.5-openjdk-17

ARG user=spring
ARG group=spring

ENV SPRING_HOME=/home/<USER>

RUN /sbin/ip route|awk '/default/ { print  $3,"\tdockerhost" }' >> /etc/hosts

RUN groupadd -g 1000 ${group} \
	&& useradd -d "$SPRING_HOME" -u 1000 -g 1000 -m -s /bin/bash ${user} \
	&& mkdir -p $SPRING_HOME/config \
	&& mkdir -p $SPRING_HOME/logs \
	&& chown -R ${user}:${group} $SPRING_HOME/config $SPRING_HOME/logs

VOLUME ["$SPRING_HOME/config", "$SPRING_HOME/logs"]
USER ${user}
WORKDIR $SPRING_HOME

COPY  chatgpt-bootstrap/target/chatgpt-bootstrap-0.0.1-SNAPSHOT.jar ./

EXPOSE 3003 9876

ENV JAVA_OPTS  -Djava.awt.headless=true -XX:+HeapDumpOnOutOfMemoryError \
 -Xlog:gc:file=/home/<USER>/logs/gc.log \
  -Dlogging.file.path=/home/<USER>/logs \
 -Dserver.port=3003 -Duser.timezone=Asia/Shanghai

ENTRYPOINT ["bash","-c","java $JAVA_OPTS -jar chatgpt-bootstrap-0.0.1-SNAPSHOT.jar"]